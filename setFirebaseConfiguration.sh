#!/usr/bin/env bash

set -e

# ------------------------------
# 1. Get the environment
# ------------------------------
ENV=$1

if [ -z "$ENV" ]; then
  echo "❌ NODE_ENV not provided. Usage: ./setFirebaseConfiguration.sh <env>"
  exit 1
fi

# Supported environments
SUPPORTED_ENVS=("qa" "prod")

# Check if ENV is supported
if [[ " ${SUPPORTED_ENVS[@]} " =~ " $ENV " ]]; then
  RESOLVED_ENV="$ENV"
else
  echo "⚠️ Environment \"$ENV\" not supported. Falling back to \"qa\"."
  RESOLVED_ENV="qa"
fi

echo "📦 Applying Firebase configuration for environment: $RESOLVED_ENV"

# ------------------------------
# 2. Set paths
# ------------------------------
ANDROID_SRC="firebaseConfigurations/android/$RESOLVED_ENV/google-services.json"
ANDROID_DEST="android/app/google-services.json"

IOS_SRC="firebaseConfigurations/ios/$RESOLVED_ENV/GoogleService-Info.plist"
IOS_DEST="ios/GoogleService-Info.plist"

# ------------------------------
# 3. Copy files recursively
# ------------------------------
copy_file() {
  local src="$1"
  local dest="$2"

  cp "$src" "$dest"
}

echo "🔄 Coppying GoogleService-Info.plist"
copy_file "$IOS_SRC" "$IOS_DEST"

echo "🔄 Coppying google-services.json"
copy_file "$ANDROID_SRC" "$ANDROID_DEST"

echo "✅ Google Service configuration has benn updated successfully"
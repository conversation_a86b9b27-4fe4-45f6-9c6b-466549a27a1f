/**
 * @format
 */
import React from 'react';
import { AppRegistry, Platform } from 'react-native';
import App from './app/_layout';
import { name as appName } from './app.json';
import DeviceInfo from 'react-native-device-info';
import { Instrumentation } from '@appdynamics/react-native-agent';
import { startAppDInstrumentation } from './analytics/AnalyticsUtils';

  
startAppDInstrumentation();  

AppRegistry.registerComponent(appName, () => App);
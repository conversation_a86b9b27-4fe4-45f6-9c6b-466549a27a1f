// Import required modules
const fs = require('fs');
const path = require('path');

// Get the current environment from the NODE_ENV environment variable
let env = process.env.NODE_ENV;

// Check if NODE_ENV is set, otherwise exit the script
if (!env) {
    console.error("NODE_ENV is not set. Aborting.");
    process.exit(1);
}

// Define supported environments for app icons
const supportedEnvs = ['qa1', 'qa2', 'prod'];

// Resolve the environment: use 'dev' if the provided environment is not supported
const resolvedEnv = supportedEnvs.includes(env) ? env : 'dev';

// Warn the user if the provided environment is not supported
if (env !== resolvedEnv) {
    console.warn(`Environment "${env}" not supported for icons. Falling back to "dev".`);
}

// Define source and target paths for Android icons
const androidSourceDir = path.join(__dirname, `appIcons/android/${resolvedEnv}`);
const androidTargetDir = path.join(__dirname, 'android/app/src/main/res');

// Define source and target paths for iOS icons
const iosSourceDir = path.join(__dirname, `appIcons/iOS/${resolvedEnv}/AppIcon.appiconset`);
const iosTargetDir = path.join(__dirname, 'ios/AssociateApp/Images.xcassets/AppIcon.appiconset'); // Replace AssociateApp if needed

/**
 * Recursively copies files and directories from the source path to the destination path.
 * 
 * @param {string} src - The source directory path.
 * @param {string} dest - The destination directory path.
 */
function copyRecursiveSync(src, dest) {
    // Check if the source path exists
    if (!fs.existsSync(src)) {
        console.warn(`Source path not found: ${src}`);
        return;
    }

    // Create the destination directory if it does not exist
    if (!fs.existsSync(dest)) {
        fs.mkdirSync(dest, { recursive: true });
    }

    // Iterate through the items in the source directory
    fs.readdirSync(src).forEach(item => {
        const srcPath = path.join(src, item); // Full path of the source item
        const destPath = path.join(dest, item); // Full path of the destination item

        // If the item is a directory, recursively copy its contents
        if (fs.statSync(srcPath).isDirectory()) {
            copyRecursiveSync(srcPath, destPath);
        } else {
            // If the item is a file, copy it to the destination
            fs.copyFileSync(srcPath, destPath);
        }
    });
}

// Copy Android icons for the resolved environment
console.log(`Copying Android icons for ${resolvedEnv}...`);
copyRecursiveSync(androidSourceDir, androidTargetDir);

// Copy iOS icons for the resolved environment
console.log(`Copying iOS icons for ${resolvedEnv}...`);
copyRecursiveSync(iosSourceDir, iosTargetDir);

// Log success message
console.log(`✅ ${resolvedEnv} App icons are applied.`);

**JIRA Identifier**

**Areas Impacted**

- ADD THE AREA IMPACTED DETAILS HERE

**Developer Checks**

<!--- Put an `[x]` in the boxes that apply -->

- [ ] Unit Testing
- [ ] Accessibility
- [ ] UI changes verified on bigger and smaller device
- [ ] UI changes verified on Tablet and Phones
- [ ] UI changes verified on couple of banners Safeway & Albertsons
- [ ] Verified the areas impacted
- [ ] Verified with and without the feature flag
- [ ] Analytics
- [ ] Documentation

**How did you test?**
- [ ] Manual Testing
- [ ] Unit Testing

**Attachments**
- [ ] Accessibility Video
- [ ] UI screenshots for banners
- [ ] UI screenshots for devices
- [ ] Unit Testing coverage
- [ ] Feature flow video
- [ ] Other attachments



# Primary Review Checklist

<!--- Put an `x` in the boxes that apply, remove checkboxes from the remaining -->

- [ ] PR owner created the PR properly with all required information above
- [ ] I have reviewed the Jira ticket, and know the requirements
- [ ] All the acceptance criteria is met with code changes
- [ ] Thought about other features potentially might impacted by these changes 
- [ ] ADA and unit testing is completed
- [ ] Bitrise test is passing and there is no conflict
- [ ] Code quality, optimized algorithms, and logic are of the highest importance

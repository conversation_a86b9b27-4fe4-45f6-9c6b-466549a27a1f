#!/bin/bash

set -e

# ------------------------------
# 1. Get the environment
# ------------------------------
ENV=$1

if [ -z "$ENV" ]; then
  echo "❌ NODE_ENV not provided. Usage: ./setAppIcons.sh <env>"
  exit 1
fi

# Supported environments
SUPPORTED_ENVS=("qa1" "qa2" "prod")

# Check if ENV is supported
if [[ " ${SUPPORTED_ENVS[@]} " =~ " $ENV " ]]; then
  RESOLVED_ENV="$ENV"
else
  echo "⚠️ Environment \"$ENV\" not supported for icons. Falling back to \"dev\"."
  RESOLVED_ENV="dev"
fi

echo "📦 Applying app icons for environment: $RESOLVED_ENV"

# ------------------------------
# 2. Set paths
# ------------------------------
ANDROID_SRC="appIcons/android/$RESOLVED_ENV"
ANDROID_DEST="android/app/src/main/res"

IOS_SRC="appIcons/iOS/$RESOLVED_ENV/AppIcon.appiconset"
IOS_DEST="ios/AssociateApp/Images.xcassets/AppIcon.appiconset" # Adjust if needed

# ------------------------------
# 3. Copy files recursively
# ------------------------------
copy_dir() {
  local src="$1"
  local dest="$2"

  if [ ! -d "$src" ]; then
    echo "⚠️ Source path not found: $src"
    return
  fi

  mkdir -p "$dest"
  cp -R "$src/"* "$dest"
}

# Copy Android icons
echo "🔄 Copying Android icons..."
copy_dir "$ANDROID_SRC" "$ANDROID_DEST"

# Copy iOS icons
echo "🔄 Copying iOS icons..."
copy_dir "$IOS_SRC" "$IOS_DEST"

echo "✅ App icons applied for $RESOLVED_ENV"

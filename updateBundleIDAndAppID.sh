set -e

# ------------------------------
# 1. Get the environment
# ------------------------------
ENV=$1

if [ -z "$ENV" ]; then
  echo "❌ NODE_ENV not provided. Usage: ./updateBunldeIDAndAppID.sh <env>"
  exit 1
fi

# Supported environments
SUPPORTED_ENVS=("qa" "prod")

# Check if <PERSON>NV is supported
if [[ " ${SUPPORTED_ENVS[@]} " =~ " $ENV " ]]; then
  RESOLVED_ENV="$ENV"
else
  echo "⚠️ Environment \"$ENV\" not supported. Falling back to \"qa\"."
  RESOLVED_ENV="qa"
fi

echo "📦 Applying iOS Bunlde ID and Android applicationId for environment: $RESOLVED_ENV"

# Get the bundle id to update
new_app_id="com.associateapp.diaa.entqa"
if [ $RESOLVED_ENV = "prod" ]; then
  new_app_id="com.associateapp.diaa.entprod"
fi

echo "🔄 Updating Android applicationId"
# Path to your app's build.gradle file
gradle_file="android/app/build.gradle"
# Get the old application ID
old_app_id=$(grep -o 'applicationId "[^"]*"' "$gradle_file" | sed 's/applicationId "//;s/"//g')
# Use sed to replace the old application ID with the new one
sed -i.bak "s/applicationId \"$old_app_id\"/applicationId \"$new_app_id\"/" "$gradle_file"
# Remove the backup file created by sed
rm "$gradle_file.bak"
echo "✅ Android applicationId updated successfully from $old_app_id to $new_app_id"

echo "🔄 Updating iOS bundleId"
# Update project.pbxproj (optional, for targets)
sed -i '' "s/PRODUCT_BUNDLE_IDENTIFIER = .*/PRODUCT_BUNDLE_IDENTIFIER = $new_app_id;/g" ios/AssociateApp.xcodeproj/project.pbxproj
echo "✅ iOS Bundle ID updated successfully to: $new_app_id"
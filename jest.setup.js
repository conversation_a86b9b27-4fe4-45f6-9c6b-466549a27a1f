jest.mock('react-native-reanimated', () => {
  const Reanimated = require('react-native-reanimated/mock');
  Reanimated.default.call = () => { };
  return Reanimated;
});
// jest.mock('react-native/Libraries/Animated/NativeAnimatedHelper');

jest.mock('react-native-gesture-handler', () => {
  const View = require('react-native/Libraries/Components/View/View');
  return {
    Swipeable: View,
    DrawerLayout: View,
    State: {},
    ScrollView: View,
    Slider: View,
    Switch: View,
    TextInput: View,
    ToolbarAndroid: View,
    ViewPagerAndroid: View,
    DrawerLayoutAndroid: View,
    WebView: View,
    NativeViewGestureHandler: View,
    TapGestureHandler: View,
    FlingGestureHandler: View,
    ForceTouchGestureHandler: View,
    LongPressGestureHandler: View,
    PanGestureHandler: View,
    PinchGestureHandler: View,
    RotationGestureHandler: View,
    /* Buttons */
    RawButton: View,
    BaseButton: View,
    RectButton: View,
    BorderlessButton: View,
    /* Other */
    FlatList: View,
    gestureHandlerRootHOC: jest.fn((component) => component),
    Directions: {},
  };
});

jest.mock('react-native-safe-area-context', () => ({
  SafeAreaProvider: function SafeAreaProvider(props) {
    return props.children;
  },
  SafeAreaConsumer: function SafeAreaConsumer(props) {
    return props.children({ top: 0, bottom: 0, left: 0, right: 0 });
  },
  SafeAreaView: function SafeAreaView(props) {
    const React = require('react');
    const { View } = require('react-native');
    return React.createElement(View, props, props.children);
  },
  useSafeAreaInsets: jest.fn(() => ({ top: 0, bottom: 0, left: 0, right: 0 })),
}));

jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key) => key,
  }),
}));

jest.mock('@react-native-cookies/cookies', () => ({
  clearAll: jest.fn(),
  get: jest.fn(() => Promise.resolve({})),
  set: jest.fn(() => Promise.resolve()),
}));

jest.mock("@react-native-community/geolocation", () => ({
  getCurrentPosition: jest.fn((success, error) => {
    success({
      coords: {
        latitude: 37.7749,
        longitude: -122.4194,
        altitude: 0,
        accuracy: 1,
        altitudeAccuracy: 1,
        heading: 0,
        speed: 0,
      },
      timestamp: Date.now(),
    });
  }),
  watchPosition: jest.fn(),
  clearWatch: jest.fn(),
  stopObserving: jest.fn(),
}));

jest.mock("react-native-calendar-events", () => ({
  requestPermissions: jest.fn(),
  checkPermissions: jest.fn(),
  fetchAllEvents: jest.fn(),
  removeEvent: jest.fn(),
}));

jest.mock('@react-native-firebase/analytics', () => () => ({
  logEvent: jest.fn(),
  setUserId: jest.fn(),
  setUserProperties: jest.fn(),
  logScreenView: jest.fn(),
}));

jest.mock("react-native-permissions", () => ({
  checkNotifications: jest.fn().mockResolvedValue({ status: 'granted' }),
  PERMISSIONS: {
    ANDROID: {},
    IOS: {},
  },
  RESULTS: {
    GRANTED: 'granted',
    DENIED: 'denied',
    BLOCKED: 'blocked',
    UNAVAILABLE: 'unavailable',
  },
  check: jest.fn(),
  request: jest.fn(),
}));

jest.mock('react-native-keychain', () => ({
  setGenericPassword: jest.fn(() => Promise.resolve(true)),
  getGenericPassword: jest.fn(() => Promise.resolve({ username: 'test-user', password: 'test-password' })),
  resetGenericPassword: jest.fn(() => Promise.resolve(true)),
  hasInternetCredentials: jest.fn(() => Promise.resolve(false)),
  setInternetCredentials: jest.fn(() => Promise.resolve(true)),
  getInternetCredentials: jest.fn(() => Promise.resolve({ username: 'test-user', password: 'test-password' })),
  resetInternetCredentials: jest.fn(() => Promise.resolve(true)),
}));

jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(() => Promise.resolve(null)),
  setItem: jest.fn(() => Promise.resolve(null)),
}));
jest.mock('@appdynamics/react-native-agent', () => ({
  Instrumentation: {
    start: jest.fn(),
    setUserData: jest.fn(),
    removeUserData: jest.fn(),
    reportError: jest.fn(),
    reportMetric: jest.fn(),
    leaveBreadcrumb: jest.fn(),
    startTimer: jest.fn(),
    stopTimer: jest.fn(),
    startNextSession: jest.fn(),
  },
  LoggingLevel: { VERBOSE: 'VERBOSE', INFO: 'INFO' },
  ErrorSeverityLevel: { WARNING: 'WARNING', CRITICAL: 'CRITICAL' },
  BreadcrumbVisibility: { CRASHES_AND_SESSIONS: 'CRASHES_AND_SESSIONS' },
}));

jest.mock('react-native-device-info', () => ({
  isEmulator: jest.fn(),
  getUniqueId: jest.fn(() => Promise.resolve('test-device-id')),
  getVersion: jest.fn(() => Promise.resolve('1.0.0')),
}));

jest.mock('react-native-fs', () => ({
  readDir: jest.fn(() => Promise.resolve([])),
  exists: jest.fn(() => Promise.resolve(true)),
  unlink: jest.fn(() => Promise.resolve()),
  TemporaryDirectoryPath: '/tmp',
  CachesDirectoryPath: '/cache',
}));

jest.mock('@react-native-community/netinfo', () => ({
  fetch: jest.fn(() => Promise.resolve({ isConnected: true, isInternetReachable: true })),
  addEventListener: jest.fn(() => () => { }),
}));

// jest.setup.js
process.env.IS_TEST = 'true';
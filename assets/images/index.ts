const LDAP_ICON = require("./ldap_logo.png");
const LUNCH_SHIFT = require("./Classic.png");
const FOR_U = require("./for-U.png");

const ACME = {
  ICON: require("./acme/home-logo.png"),
  LOGO: require("./acme/logo.png"),
};
const ALBERTSONS = {
  ICON: require("./albertsons/home-logo.png"),
  LOGO: require("./albertsons/logo.png"),
};
const ANDRONICOS = {
  ICON: require("./andronicos/home-logo.png"),
  LOGO: require("./andronicos/logo.png"),
};
const BALDUCCIS = {
  ICON: require("./balduccis/home-logo.png"),
  LOGO: require("./balduccis/logo.png"),
};
const CARRS = {
  ICON: require("./carrs/home-logo.png"),
  LOGO: require("./carrs/logo.png"),
};
const HAGGEN = {
  ICON: require("./haggen/home-logo.png"),
  LOGO: require("./haggen/logo.png"),
};
const JEWEL_OSCO = {
  ICON: require("./jewelOsco/home-logo.png"),
  LOGO: require("./jewelOsco/logo.png"),
};
const KINGS = {
  ICON: require("./kings/home-logo.png"),
  LOGO: require("./kings/logo.png"),
};
const PAVILIONS = {
  ICON: require("./pavilions/home-logo.png"),
  LOGO: require("./pavilions/logo.png"),
};
const RANDALLS = {
  ICON: require("./randalls/home-logo.png"),
  LOGO: require("./randalls/logo.png"),
};
const SAFEWAY = {
  ICON: require("./safeway/home-logo.png"),
  LOGO: require("./safeway/logo.png"),
};
const SHAWS = {
  ICON: require("./shaws/home-logo.png"),
  LOGO: require("./shaws/logo.png"),
};
const START_MARKET = {
  ICON: require("./starMarket/home-logo.png"),
  LOGO: require("./starMarket/logo.png"),
};
const TOMTHUMB = {
  ICON: require("./tomthumb/home-logo.png"),
  LOGO: require("./tomthumb/logo.png"),
};
const VONS = {
  ICON: require("./vons/home-logo.png"),
  LOGO: require("./vons/logo.png"),
};

const UNDERLINE = require("./underLine.png");

const ARROW_DOWN = require("./arrow-down.png");

const LANGUAGES = {
  EN: require("./usa.png"),
  ES: require("./esflag.png"),
};

const ANNOUNCEMENTS_ERROR_IMAGE = require("./announcements_error.png");

const RESOURCES_IMAGE = require('../images/resources-image.png')

export default {
  LDAP_ICON,
  LUNCH_SHIFT,
  ACME,
  ALBERTSONS,
  ANDRONICOS,
  BALDUCCIS,
  CARRS,
  HAGGEN,
  JEWEL_OSCO,
  KINGS,
  PAVILIONS,
  RANDALLS,
  SAFEWAY,
  SHAWS,
  START_MARKET,
  TOMTHUMB,
  VONS,
  UNDERLINE,
  LANGUAGES,
  LOGIN_BG: require("./static_login_bg.png"),
  PORTRAIT_TABLET_BG: require("./portrait_tablet_static_login_bg.png"),
  LANDSCAPE_TABLET_BG: require("./landscape_tablet_static_login_bg.png"),
  POINTS: require("./points.png"),
  ARROW_DOWN,
  FOR_U,
  ANNOUNCEMENTS_ERROR_IMAGE,
  RESOURCES_IMAGE,
};

export const noShiftScheduled = `<svg width="152" height="152" fill="none" xmlns="http://www.w3.org/2000/svg"><g filter="url(#a)"><g clip-path="url(#b)"><rect x="12" y="10" width="128" height="128" rx="64" fill="#FAF8F7"/><path d="M48.978 100.504c0 .296.064.588.188.861.125.273.308.521.538.73.23.209.504.375.805.488.3.113.623.171.95.171h49.083c.325 0 .648-.058.949-.171.301-.113.574-.279.804-.488.231-.209.414-.457.538-.73.125-.273.189-.565.189-.861V60.291H48.978v40.213Z" fill="#fff"/><path d="M100.478 48.707H51.454c-.657 0-1.287.215-1.751.598-.465.383-.725.902-.725 1.444v9.538h54.043v-9.482a1.738 1.738 0 0 0-.174-.812 2.066 2.066 0 0 0-.55-.689 2.569 2.569 0 0 0-.835-.453 2.95 2.95 0 0 0-.984-.144Z" fill="#397CBF"/><path d="M66.33 64.142h-7.701v7.701h7.7v-7.7Zm0 13.518h-7.701v7.701h7.7v-7.7Zm0 13.526h-7.701v7.701h7.7v-7.7Z" fill="#D8D4D2"/><path d="M79.85 64.142h-7.701v7.701h7.7v-7.7Z" fill="#397CBF"/><path d="M79.85 77.66h-7.701v7.701h7.7v-7.7Zm-.002 13.502h-7.7v7.701h7.7v-7.7Z" fill="#D8D4D2"/><path d="M93.37 64.142h-7.7v7.701h7.7v-7.7Zm0 13.518h-7.7v7.701h7.7v-7.7Z" fill="#397CBF"/><path d="M93.37 91.186h-7.7v7.701h7.7v-7.7Z" fill="#D8D4D2"/><rect x="62.488" y="44.844" width="3.86" height="11.581" rx="1.93" fill="#0D2B49"/><rect x="85.651" y="44.844" width="3.86" height="11.581" rx="1.93" fill="#0D2B49"/><path fill="#fff" d="M102.311 83.244h4.978v28.445h-4.978z"/><path fill-rule="evenodd" clip-rule="evenodd" d="M108.779 82.704c-1.769-3.072-6.191-3.072-7.959 0L87.289 106.2c-1.77 3.072.442 6.911 3.98 6.911h27.061c3.538 0 5.749-3.839 3.98-6.911l-13.531-23.496Zm-3.98 6.91c-.39 0-.712.142-.965.425a1.378 1.378 0 0 0-.413.99v9.672c0 .378.138.708.413.991.253.283.575.425.965.425.391 0 .724-.142 1-.425a1.44 1.44 0 0 0 .379-.991V91.03a1.44 1.44 0 0 0-.379-.991 1.345 1.345 0 0 0-1-.425Zm0 14.643c-.39 0-.712.13-.965.389a1.421 1.421 0 0 0-.413 1.027v.955c0 .377.138.708.413.991.253.283.575.424.965.424.391 0 .724-.141 1-.424.253-.283.379-.614.379-.991v-.955c0-.401-.126-.744-.379-1.027a1.409 1.409 0 0 0-1-.389Z" fill="#C63939"/></g></g><defs><clipPath id="b"><rect x="12" y="10" width="128" height="128" rx="64" fill="#fff"/></clipPath><filter id="a" x="0" y="0" width="152" height="152" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/><feOffset dy="2"/><feGaussianBlur stdDeviation="6"/><feComposite in2="hardAlpha" operator="out"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/><feBlend in2="BackgroundImageFix" result="effect1_dropShadow_3990_130226"/><feBlend in="SourceGraphic" in2="effect1_dropShadow_3990_130226" result="shape"/></filter></defs></svg>`;

export const noSchedulesReleased = `<svg width="152" height="152" fill="none" xmlns="http://www.w3.org/2000/svg"><g filter="url(#a)"><g clip-path="url(#b)"><g clip-path="url(#c)"><rect x="12" y="10" width="128" height="128" rx="64" fill="#FAF8F7"/><mask id="d" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="12" y="10" width="128" height="128"><rect x="12" y="10" width="128" height="128" rx="64" fill="#C4C4C4"/></mask><g mask="url(#d)"><path d="M65.207 51.698c1.304-.511 2.519-2.282 2.71-4.16.279-2.789-1.817-4.633-2.325-5.077-.385-.339-2.497-2.107-5.18-1.164-1.839.648-2.886 2.202-3.218 2.738 2.133 5.744 5.652 8.59 8.013 7.663Z" fill="#E6BA5B"/><path d="M61.992 45.2c.037-.55.149-2.003-1.067-3.128-1.849-1.71-5.68-1.663-7.868-1.3-4.114.68-6.347 3.122-7.22 4.079a10.172 10.172 0 0 0-2.335 4.183c-.515 1.859-.346 2.07-.114 5.926.321 5.266.389 5.717.248 6.78-.285 2.14-1.058 4.012-1.985 5.602a48.273 48.273 0 0 0 6.353 1.832c5.033 1.087 9.492 1.232 12.757 1.124.583-12.19.956-21.255 1.23-25.099Z" fill="#E6BA5B"/><path d="M45.57 73.972c1.15-.965 5.724-6.741 7.062-8.997a18.23 18.23 0 0 0 2.381-6.815c-.816.214-1.67.002-2.17-.558-.882-.99-.078-2.395-.035-2.47.119-.199.536-.906 1.4-1.072.768-.149 1.598.187 2.136.863 1.19-.863 2.94-2.255 4.566-4.287.975-1.22 3.122-4.15 2.844-5.283 0 .002 1.626.407 1.626.407 4.47 1.625 6.77 7.346 7.52 9.528.448 1.3 2.08 6.481-.035 11.4-1.086 2.523-3.09 2.996-3.38 3.095-1.004.347-1.575.04-4.251-.583a46.763 46.763 0 0 1-2.17-.558 9.072 9.072 0 0 0-.351 3.042c.13 2.314.713 7.584 1.268 8.394-3.033 3.105-7.798 3.79-11.377 1.772-2.994-1.69-7.324-4.383-7.033-7.878Z" fill="#D46F54"/><path d="M118.928 83.442c.149.789.374 2.23-.22 3.718-.568 1.434-1.656 2.316-2.157 2.716a8.343 8.343 0 0 1-2.819 1.465c-1.339.411-4.917 1.343-10.095 2.626a1344.212 1344.212 0 0 1-15.183 3.65 127.917 127.917 0 0 0-.139 16.569 128.066 128.066 0 0 0 4.148 25.85 34.703 34.703 0 0 1-7.37 5.563c-7.132 4.005-13.953 4.431-17.702 4.348-12.859-.574-25.716-1.144-38.574-1.718-1.682-15.45-1.404-25.283-.763-31.56.238-2.33.779-6.667-.002-12.407-.936-6.859-2.642-7.526-3.138-12.719-.058-.627-1.218-14.423 6.36-18.694 4.378-2.47 9.759-.677 10.946-.282 3.086 1.029 3.016 2.072 6.396 3.816 6.87 3.544 15.977 3.775 18.995.456.331-.363 1.472-1.742 3.354-3.407 1.13-1 1.691-1.5 2.315-1.827 2.312-1.225 4.818-.065 7.623.82 1.692.533 4.455.935 9.987 1.743 3.738.544 7.087.548 8.824.597 1.809.054 3.343.051 4.485.041a146.6 146.6 0 0 1-4.635-21.16l-.315-1.776a8.54 8.54 0 0 1-3.907.297c-3.999-.65-6.156-4.056-6.553-4.705 7.863-6.651 10.888-7.403 12.048-6.47.749.6.739 1.912 2.726 5.447.546.969.97 1.622 1.245 2.056 1.745 2.695 12.459 26.234 14.12 34.947Z" fill="#D8D4D2"/><path d="M118.927 83.435c.148.79.373 2.23-.22 3.72-.568 1.433-1.657 2.314-2.157 2.715a8.342 8.342 0 0 1-2.819 1.465c-1.339.41-4.917 1.343-10.095 2.626a184.136 184.136 0 0 0-1.373-7.757c-.951-4.765-1.708-8.506-2.55-11.445 1.808.054 3.343.05 4.485.041a146.573 146.573 0 0 1-4.635-21.16l-.316-1.777a8.54 8.54 0 0 1-3.906.297c-4-.65-6.156-4.055-6.553-4.704 7.863-6.652 10.888-7.403 12.048-6.47.748.6.739 1.911 2.726 5.446.546.97.97 1.623 1.245 2.057 1.745 2.695 12.459 26.234 14.12 34.946Z" fill="#D46F54"/><path d="M74.754 54.057a5.586 5.586 0 0 1 4.39-5.891l30.51-6.59 1.505 19.446-31.16-1.819a5.586 5.586 0 0 1-5.245-5.146Z" fill="#D8D4D2"/><path d="M113.648 51.044c.201 2.596-.104 4.944-.726 6.633-.648 1.763-1.509 2.506-2.23 2.562-.722.056-1.687-.546-2.599-2.188-.874-1.574-1.537-3.847-1.737-6.443-.201-2.595.104-4.944.725-6.633.649-1.762 1.51-2.506 2.231-2.562.721-.056 1.687.547 2.599 2.189.874 1.573 1.536 3.846 1.737 6.442Z" fill="#fff" stroke="#7C7575" stroke-width="1.625"/><path d="M63.418 54.997a4.763 4.763 0 0 1 3.635-5.074l31.258-7.472 1.505 19.446-32.04-2.593a4.763 4.763 0 0 1-4.358-4.307Z" fill="#D8D4D2"/><path d="M103.113 51.86c.201 2.596-.104 4.944-.726 6.634-.648 1.762-1.51 2.506-2.231 2.562-.72.056-1.686-.547-2.598-2.189-.874-1.573-1.537-3.846-1.738-6.442-.2-2.596.105-4.944.726-6.633.649-1.763 1.51-2.507 2.231-2.562.721-.056 1.687.546 2.598 2.188.874 1.574 1.537 3.847 1.738 6.443Z" fill="#fff" stroke="#7C7575" stroke-width="1.625"/><path d="M87.722 99.82c-1.584 1.855-3.406 2.781-5.52 3.672-3.366 1.413-8.666 3.255-16.185 4.375.8-5.749.425-3.377.69-7.28.369-5.586.65-9.976.585-13.508a70.4 70.4 0 0 0 3.144.798 224.323 224.323 0 0 1 .503-5.699c.168-1.627.283-7.24.255-20.878-.78-.63-2.213-1.982-2.969-4.168-1.52-4.396 1.04-8.288 1.367-8.775 1.043-.708 2.687-1.63 4.86-2.06 3.553-.703 7.01.207 7.007.685-.002.35-1.86.026-2.43 1.03-1.087 1.906 3.984 6.306 2.793 8.45-.154.282-.594.84-.7 1.737-.083.702.078 1.253.132 1.438 2.062 7.11 4.308 14.187 5.899 21.42 2.198 8.052 5.31 13.198.57 18.763Z" fill="#D46F54"/><path d="M69.02 58.924s.453.773.812 1.219c.423.524 1.626 1.219 1.626 1.219v13.003s-.02 6.356-.407 9.346c-.207 1.605-.334 2.853-.406 4.47-.093 2.085.406 4.876.406 4.876m-21.184 13.011s4.787 1.746 7.927 2.645c2.809.804 7.271 1.779 7.271 1.779s.4-.694.67-1.132c.302-.489.51-1.373.51-1.373" stroke="#130B06" stroke-width="1.067"/></g></g></g></g><defs><clipPath id="b"><rect x="12" y="10" width="128" height="128" rx="64" fill="#fff"/></clipPath><clipPath id="c"><rect x="12" y="10" width="128" height="128" rx="64" fill="#fff"/></clipPath><filter id="a" x="0" y="0" width="152" height="152" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/><feOffset dy="2"/><feGaussianBlur stdDeviation="6"/><feComposite in2="hardAlpha" operator="out"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/><feBlend in2="BackgroundImageFix" result="effect1_dropShadow_3990_130238"/><feBlend in="SourceGraphic" in2="effect1_dropShadow_3990_130238" result="shape"/></filter></defs></svg>`;
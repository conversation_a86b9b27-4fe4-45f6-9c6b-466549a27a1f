export const ScheduleEmptyImage = `<svg width="201" height="160" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill="" d="M0 0h201v160H0z"/><path d="M-2288-1068c0-1.1.9-2 2-2H386c1.104 0 2 .9 2 2V248a2 2 0 0 1-2 2h-2672c-1.1 0-2-.895-2-2v-1316Z" fill="#FAF9F8"/><path d="M-2286-1070v1H386v-2h-2672v1Zm2674 2h-1V248h2v-1316h-1Zm-2 1318v-1h-2672v2H386v-1Zm-2674-2h1v-1316h-2V248h1Zm2 2v-1c-.55 0-1-.448-1-1h-2c0 1.657 1.34 3 3 3v-1Zm2674-2h-1a1 1 0 0 1-1 1v2a3 3 0 0 0 3-3h-1Zm-2-1318v1c.552 0 1 .45 1 1h2c0-1.66-1.343-3-3-3v1Zm-2672 0v-1c-1.66 0-3 1.34-3 3h2c0-.55.45-1 1-1v-1Z" fill="#000" fill-opacity=".1"/><g opacity=".6" clip-path="url(#a)"><path d="M76.457 58.75c-4.105-.283-5.11-12.137-13.187-21.618-8.078-9.48-34.44-24.708-48.064-42.567C1.58-23.295 21.5-21.69 43.596-15.234 58.415-1.94 62.593 16.435 71.31 21.034c20.352 10.738 38.979-2.51 42.44 2.471 1.169 1.676-15.45 7.55-15.45 7.55 2.841 1.826 20.727.513 20.296 3.203-.431 2.691-20.097 2.832-20.097 2.832 5.925 2.8 23.181 5.05 19.583 7.104-3.599 2.054-17.434-.22-17.434-.22a10.31 10.31 0 0 1-.71 12.063.612.612 0 0 1-1.147-.426c1.183-7.734-7.363-15.09-14.755-13.017-7.009 1.964-7.431 11.873-6.668 15.731.014.179-.148.514-.91.425Z" fill="#8A8A8A"/><path d="M100.846 148.306c55.467 0 100.433-17.382 100.433-38.824 0-21.441-44.966-38.823-100.433-38.823C45.379 70.659.414 88.04.414 109.482s44.965 38.824 100.432 38.824Z" fill="#F1F1F1"/><path d="M143.276 123.459c.447-2.471 1.867-9.601 16.632-6.948 6.621 1.19 21.792 2.327 20.528 9.321-.523 2.893-7.924 8.748-23.306 7.283-6.701-1.204-14.9-3.872-13.854-9.656Zm-46.216-1.488c.094-2.048.493-7.971 15.48-7.294 6.721.304 21.658 1.497 21.394 7.294-.109 2.397-5.45 7.699-21.799 6.446-6.802-.307-15.293-1.651-15.074-6.446Z" fill="#D6D6D6"/><path d="M87.62 52.565s-7.512.073-10.947 6.484c-3.38 2.202-4.739 3.761-5.901 7.493-1.163 3.732-2.706 9.458 4.626 15.861 7.332 6.404 22.006 12.042 33.305 10.984 6.724-.64 11.296-2.596 14.414-8.723 3.117-6.127 2.108-11.118.229-13.952.164-3.343-.416-6.021-4.617-10.287-1.303-2.267-2.955-4.186-6.802-6.494-6.407-3.843-16.468-6.546-24.307-1.366Z" fill="#AFAFAF"/><path d="M76.674 59.046s-3.71 3.676-3.612 7.658c.099 3.981 4.814 11.877 15.041 15.853 8.605 3.346 16.933 5.9 27.738 3.086 3.89-2.041 7.485-3.475 9.329-8.274-.56-3.698-.414-4.678-1.821-6.658-.501 2.249-.63 4.605-2.776 6.748-2.146 2.142-8.213 8.818-25.2 3.988-8.81-2.448-25.424-10.746-18.7-22.4Z" fill="#AFAFAF"/><path d="M76.508 59.344c.028-.18-1.43 3.358-1.316 6.278.117 2.921 4.496 11.535 21.26 16.123 7.682 1.82 18.596 3.541 25.436-6.005 1.061-1.93 1.239-3.978 1.439-4.915.005-.022-.027-.034-.038-.017-2.269 3.483-6.684 13.49-23.374 9.894-5.856-1.33-13.586-4.144-18.513-8.383-3.667-3.493-5.93-6.35-4.894-12.975Z" fill="#494949"/><path d="M86.552 53.36c.077-.114-.045-.26-.187-.213-3.526 1.157-6.29 3.504-6.402 7.587-.12 4.247 5.25 10.683 14.21 14.012 9.503 3.531 16.727 3.092 21.947-.093 3.877-2.151 7.419-7.6 3.381-12.687-.039-.048-.121-.021-.114.038.413 3.442-1.714 7.777-5.416 9.641-4.413 2.22-12.08 2.142-17.444-.171-11.689-5.038-14.56-11.438-9.975-18.115Z" fill="#C7C7C7"/><path d="M86.244 53.83s-6.47 7.913 5.44 15.184c3.797 2.32 11.903 5.746 19.6 3.61 5.552-1.596 8.728-6.604 8.01-10.733-.003-.023-.047-.031-.052-.009-.617 2.477-1.611 7.97-10.124 9.447-6.323 1.098-14.84-1.45-19.352-5.254-5.126-4.104-4.504-10.567-3.522-12.245Z" fill="#494949"/><path d="M96.055 50.56s-5.43-.225-6.506 2.906c-1.076 3.133 2.56 7.13 8.007 9.386 5.446 2.255 12.452 3.662 15.764 1.884 3.312-1.777 3.969-4.427 2.367-6.412-1.602-1.985-5.44-5.326-11.401-6.73-5.962-1.403-6.895.905-7.288 2.19-.395 1.286 1.733 3.618 5.755 4.81 4.022 1.192 6.351.442 6.613-1.355.246-1.7-2.951-3.802-6.142-3.61-.174.01-.227.221-.074.302 1.576.835 1.621 1.247 1.165 1.862-.5.675-4.515-.63-3.364-2.36.993-1.494 3.806-.718 5.525.075 1.72.793 5.167 2.653 3.329 6.25-1.592 3.115-7.689 1.42-8.997.932-7.996-2.981-8.053-7.3-4.753-10.13Z" fill="#C7C7C7"/><path d="M95.864 50.73c.036-.034.097.005.076.046-.425.877-2.024 3.785 1.11 6.505 1.337 1.162 9.814 6.15 13.21 1.054.006-.008.018-.005.015.004-.487 1.688-1.221 4.236-7.616 2.881-7.303-1.943-8.434-5.1-8.63-6.342-.184-1.186.542-2.944 1.835-4.149Z" fill="#494949"/><path d="M88.936 56.624c-.023-.104-2.066 2.958-.908 5.635 1.157 2.677 5.338 3.186 5.52 1.317.183-1.87-3.742-3.28-4.612-6.952Zm-8.69 8.957c-.026-.074-1.309 2.121-.134 4.045 1.72 2.817 5.236 3.567 5.3 2.23.067-1.333-4.277-3.607-5.166-6.275Zm-5.658 8.045c-.026-.074-1.31 2.12-.135 4.044 1.72 2.818 5.236 3.567 5.3 2.232.065-1.336-4.277-3.608-5.165-6.276Z" fill="#C7C7C7"/><path d="M70.296 68.18c.04-.16-1.744 9.283 11.9 17.658 8.268 4.29 19.858 6.993 28.183 5.65 5.407-1.23 9.228-3.253 12.296-6-2.818 3.682-4.749 8.528-19.751 7.901-9.967-1.52-19.257-5.04-24.583-8.727-5.794-4.11-10.036-8.487-8.045-16.481Z" fill="#494949"/><path d="M100.11 69.105c.453.038.85-.262.885-.672.035-.409-.304-.772-.758-.81-.454-.039-.85.262-.885.67-.035.41.304.773.758.811Zm11.447-6.207c.531.045.994-.306 1.035-.784.041-.479-.356-.903-.886-.948-.53-.045-.994.306-1.035.785-.041.478.356.902.886.947ZM91.027 73.95c.53.046.993-.305 1.034-.784.041-.478-.355-.902-.886-.947-.53-.045-.993.306-1.034.784-.041.478.355.903.886.948Zm14.794 5.074c.385.033.722-.222.751-.57.03-.347-.258-.655-.643-.688-.385-.033-.721.222-.751.57-.03.347.258.655.643.688Zm-23.647 1.671c.437.037.819-.252.853-.646.033-.395-.294-.745-.73-.782-.438-.037-.82.253-.854.647-.033.394.294.744.73.781Zm-9.343-11.647c.437.037.82-.252.853-.647.034-.394-.293-.743-.73-.78-.438-.038-.82.252-.853.646-.034.394.293.744.73.781Zm22.913 19.946c.454.039.85-.262.885-.67.035-.41-.304-.773-.758-.811-.453-.039-.85.261-.885.67-.035.41.305.773.758.811Zm13.25-2.742c.53.045.993-.306 1.034-.785.041-.478-.356-.902-.886-.947-.53-.045-.993.306-1.035.784-.041.478.356.903.887.948Zm10.984-2.279c.53.045.994-.306 1.035-.785.041-.478-.356-.903-.887-.948-.53-.045-.993.307-1.034.785-.041.478.356.903.886.948Zm-3.732-8.693c.351.03.658-.203.685-.52.027-.316-.236-.597-.586-.626-.351-.03-.658.202-.685.519-.027.316.235.597.586.627Zm.347-10.788c.351.03.657-.202.684-.519.027-.316-.235-.597-.586-.627-.351-.03-.657.203-.684.52-.028.316.235.596.586.626Zm-6.127-9.296c.35.03.657-.203.684-.52.027-.316-.235-.596-.586-.626-.351-.03-.657.202-.685.519-.027.316.236.597.587.627ZM97.56 55.08c.35.03.657-.202.684-.519.027-.316-.235-.597-.586-.627-.35-.03-.657.203-.684.52-.028.316.235.596.586.626Zm-17.1 5.132c.351.03.658-.203.685-.52.027-.316-.236-.596-.586-.626-.351-.03-.658.203-.685.519-.027.316.236.597.586.627Zm10.709-3.422c.35.03.657-.203.684-.519.027-.316-.235-.597-.586-.627-.35-.03-.657.203-.684.52-.028.316.235.596.586.626Zm16.54 11.187c.351.03.658-.203.685-.52.027-.316-.236-.597-.586-.626-.351-.03-.658.202-.685.519-.027.316.235.597.586.627Zm-9.354 9.618c.351.03.657-.202.685-.519.027-.316-.236-.597-.587-.627-.35-.03-.657.203-.684.52-.027.316.235.597.586.626Zm-5.113-14.44c.53.045.994-.306 1.035-.785.041-.478-.356-.902-.886-.947-.53-.045-.994.306-1.035.784-.04.479.356.903.886.948Zm12.17-6.219c.347.03.65-.2.677-.513.027-.313-.233-.59-.579-.62-.347-.03-.65.2-.677.513-.027.313.233.59.579.62Z" fill="#fff"/><path d="M77.914 80.753c-3.82-1.777-11.902-2.105-20.98-.572-9.077 1.532-16.594 4.494-19.603 7.424l-1.84.31.458 2.662c.633 3.679 11.054 4.987 23.277 2.923 12.222-2.063 21.618-6.719 20.985-10.396l-.458-2.661-1.84.31Z" fill="#353535"/><path d="M58.898 90.817C46.68 92.879 36.591 94.29 35.445 87.63c-.405-2.353 4.66-5.291 12.266-7.497.693-.7 2.39-1.87 3.632-.95a84.354 84.354 0 0 1 5.234-1.052c.985-.166 1.869-.826 2.825-.954 1.256-.168 1.38.114 1.62.237l.327.048c3.959-.438 7.441-2.447 10.444-.846 4.538.317 8.035 1.529 8.262 5.111.226 3.582-1.316 3.037-4.206 4.542-1.883.982-4.397 1.425-7.205 2.282-2.669 2.386-6.207 1.667-9.746 2.264Z" fill="#494949"/><path d="M40.283 88.038c-1.02.589-.935 1.238-.605 1.9.04.082.094.164.213.217.166.074.424.074.664.064a10.51 10.51 0 0 0 2.776-.5c.355-.117.7-.258.922-.44a.77.77 0 0 0 .306-.62c-.003-.343-.215-.674-.68-.884-.796-.36-2.675-.269-3.596.263Z" fill="#777"/><path d="M41.051 88.044c-.484.2-.829.492-.923.785-.03.093-.034.19.041.266.076.077.245.13.426.106.108-.015.205-.052.297-.09.425-.175.827-.377 1.306-.5.24-.063.52-.114.662-.232.14-.116.081-.258-.083-.338-.164-.08-.407-.107-.649-.113a3.515 3.515 0 0 0-1.077.116Z" fill="#A1A1A1"/><path d="M44.575 84.121c-.221.075-.5.023-.608-.079-.108-.101-.086-.234-.038-.36.105-.276.316-.548.546-.815.376-.437.824-.878 1.51-1.221.686-.343 1.66-.573 2.483-.48.36.041.667.139.936.256.255.112.483.245.58.415.188.326-.152.73-.687 1.012-.535.28-1.232.457-1.924.597-.691.14-1.4.252-2.06.439l-.738.236Z" fill="#949494"/><path d="M46.05 82.09c-.393.226-.445.325-.765.575-.087.069-.173.152-.122.218.04.05.157.072.272.066a1.37 1.37 0 0 0 .332-.068c.476-.15.86-.39 1.038-.653.064-.092.1-.194.028-.273-.072-.078-.453-.037-.612.023l-.17.113Z" fill="#D1D1D1"/><path d="M56.244 80.645c-.614.271-1.267.576-1.464.957-.157.304.022.607.367.823.345.216.837.357 1.352.466.413.09.853.161 1.327.161s.994-.08 1.382-.257c.35-.159.565-.383.658-.604.093-.22.07-.439.017-.65a1.935 1.935 0 0 0-.469-.873 1.216 1.216 0 0 0-.553-.353c-.338-.105-.801-.102-1.228-.035-.427.067-.83.194-1.214.328l-.175.037Z" fill="#8E8E8E"/><path d="M65.883 84.092c-.613.271-1.266.576-1.464.957-.156.304.023.606.368.823.344.216.837.357 1.352.466.413.09.853.16 1.327.161.474 0 .994-.08 1.382-.257.35-.159.565-.384.657-.604.093-.22.07-.44.018-.65a1.936 1.936 0 0 0-.47-.874 1.216 1.216 0 0 0-.552-.352c-.339-.105-.801-.102-1.228-.035-.427.067-.83.194-1.214.328l-.175.037Z" fill="#949494"/><path d="M57.704 80.687c-.44.067-.859.216-1.157.407-.102.067-.195.158-.125.22a.328.328 0 0 0 .148.054c.217.045.472.047.725.03a4.57 4.57 0 0 0 1.31-.289c.09-.035.189-.085.177-.137-.005-.024-.036-.044-.064-.061-.11-.07-.222-.14-.367-.19-.144-.05-.33-.08-.52-.061l-.127.027Z" fill="#B0B0B0"/><path d="M67.344 84.134c-.44.068-.86.216-1.157.408-.103.066-.196.157-.126.22a.327.327 0 0 0 .148.054c.217.044.473.047.726.029a4.565 4.565 0 0 0 1.31-.289c.09-.034.188-.085.176-.137-.005-.024-.035-.043-.064-.06-.11-.07-.221-.141-.366-.191-.145-.05-.33-.08-.521-.061l-.126.027Z" fill="#D1D1D1"/><path d="M62.77 77.293c-.122.382-.233.799.146 1.09.303.234.878.342 1.47.34.592-.002 1.202-.103 1.782-.229.467-.101.934-.221 1.326-.397.394-.174.71-.411.78-.657.064-.22-.074-.43-.312-.59-.238-.162-.565-.28-.908-.382a8.98 8.98 0 0 0-1.628-.33 3.812 3.812 0 0 0-.96 0 2.383 2.383 0 0 0-1.065.433c-.26.196-.414.417-.54.636l-.092.086Z" fill="#959595"/><path d="M64.037 76.78c-.27.203-.405.442-.38.663.01.076.062.162.208.172a.712.712 0 0 0 .2-.023 2.36 2.36 0 0 0 .64-.25c.332-.189.569-.417.676-.65.025-.053.035-.119-.048-.144a.482.482 0 0 0-.14-.012c-.19 0-.381.001-.573.026a1.345 1.345 0 0 0-.518.157l-.065.062Z" fill="#BDBDBD"/><path d="M52.019 86.802c-.759.504-1.191 1.125-1.14 1.7.05.575.599 1.095 1.5 1.377.215.067.45.121.706.148.744.078 1.59-.08 2.33-.297.331-.097.666-.215.867-.382.193-.16.238-.351.164-.514-.076-.162-.26-.3-.488-.41a4.11 4.11 0 0 0-.934-.296c-.24-.05-.496-.09-.69-.173-.477-.204-.452-.593-.62-.91a.489.489 0 0 0-.24-.234c-.166-.075-.409-.093-.64-.11-.195-.013-.416-.023-.595.042l-.22.06Z" fill="#CBCBCB"/><path d="M51.755 86.944c.017.13-.041.127-.082.26-.113.366-.008.756.502.963a.94.94 0 0 0 .35.073c.27.001.532-.13.627-.267.094-.137.065-.277.024-.409-.076-.245-.19-.493-.493-.671a.942.942 0 0 0-.367-.125.92.92 0 0 0-.466.058l-.095.118Z" fill="#E9E9E9"/><path d="M72.585 78.68a3.403 3.403 0 0 0-1.602.33c-.442.221-.724.512-.862.803-.104.218-.12.46.127.615.305.192.898.19 1.428.17.966-.036 1.94-.093 2.91-.2.218-.025.44-.052.641-.11.203-.06.384-.158.432-.27.042-.092-.015-.18-.077-.26-.691-.9-2.996-1.079-2.996-1.079Z" fill="#777"/><path d="M71.66 79.088c-.118.04-.239.082-.335.136-.096.054-.164.126-.153.19.006.032.032.061.085.075a.45.45 0 0 0 .101.008c.288.003.586-.041.877-.06.307-.018.606-.01.907-.009.107 0 .233-.005.309-.05.102-.06.037-.143-.05-.194-.29-.171-.787-.242-1.275-.184.003 0-.346.047-.467.088Z" fill="#A1A1A1"/><path d="M129.595 120.373c-3.012-2.053-9.875-3.467-17.851-3.431-7.976.035-14.821 1.51-17.808 3.59l-1.616.007.014 2.337c.02 3.23 8.743 5.809 19.483 5.761 10.739-.047 19.429-2.705 19.409-5.934l-.014-2.337-1.617.007Z" fill="#C7C7C7"/><path d="M111.791 125.262c-10.736.048-19.454-2.179-19.471-4.974-.013-2.067 4.733-3.865 11.546-4.682.691-.5 2.307-1.262 3.239-.301a72.831 72.831 0 0 1 4.623-.165c.865-.004 1.714-.443 2.55-.418 1.097.033 1.163.29 1.351.429l.273.087c3.446.181 6.707-1.046 9.047.743 3.834.907 6.241 2.667 6.25 4.133.008 1.202-1.592 2.763-4.275 3.643-1.749.574-3.961.6-6.482.938-2.619 1.663-5.542.553-8.651.567Z" fill="#C7C7C7"/><path d="M96.534 119.671c-.955.361-.974.927-.787 1.539a.36.36 0 0 0 .152.215c.131.086.352.123.559.148a9.148 9.148 0 0 0 2.444-.037c.319-.051.634-.123.85-.248a.669.669 0 0 0 .349-.487c.046-.293-.088-.606-.456-.85-.63-.418-2.248-.605-3.11-.28Z" fill="#494949"/><path d="M97.189 119.784c-.442.102-.778.304-.9.541-.039.075-.056.157-.003.233.054.076.19.144.349.149.094.003.182-.015.266-.034.39-.09.761-.206 1.188-.245.215-.019.46-.024.599-.105.135-.08.105-.209-.023-.3-.129-.092-.333-.148-.539-.188a3.04 3.04 0 0 0-.937-.051Z" fill="#696969"/><path d="M99.534 117.492c-.199.033-.43-.051-.508-.153-.078-.102-.04-.212.019-.313.129-.221.348-.424.582-.62.383-.32.828-.634 1.463-.831.635-.197 1.501-.256 2.19-.061.302.085.551.212.764.349.202.131.378.277.437.436.115.305-.233.603-.73.768-.498.164-1.118.217-1.73.24-.61.023-1.232.02-1.823.086l-.664.099Z" fill="#494949"/><path d="M101.029 116.253c-.518.192-.599.301-1.033.539-.119.065-.239.148-.19.236.038.069.174.118.313.133.138.016.279.003.413-.016.6-.086 1.108-.299 1.375-.579.095-.098.159-.213.088-.321-.071-.109-.536-.134-.739-.094l-.227.102Z" fill="#696969"/><path d="M111.087 117.237c-.563.146-1.165.314-1.387.612-.178.238-.067.522.197.755.264.233.665.422 1.089.588.341.134.707.257 1.112.323.405.067.861.071 1.218-.025.321-.087.537-.249.647-.424.111-.175.122-.366.107-.553a1.673 1.673 0 0 0-.277-.812 1.048 1.048 0 0 0-.422-.379c-.275-.137-.671-.199-1.045-.202a5.982 5.982 0 0 0-1.085.11l-.154.007Z" fill="#494949"/><path d="M112.329 117.477c-.386-.004-.764.064-1.046.187-.097.042-.189.106-.138.169a.28.28 0 0 0 .118.068c.179.068.398.106.616.126.396.036.804.014 1.161-.062.082-.018.173-.047.17-.093-.001-.021-.024-.042-.046-.061a1.481 1.481 0 0 0-.286-.214 1.078 1.078 0 0 0-.437-.125l-.112.005Z" fill="#696969"/><path d="M117.138 115.289c-.158.31-.312.65-.03.953.226.242.703.415 1.21.496.505.081 1.041.08 1.555.054.414-.021.829-.058 1.189-.153.361-.094.665-.252.761-.452.085-.18-.004-.378-.184-.549-.18-.171-.443-.317-.722-.453a7.744 7.744 0 0 0-1.345-.51 3.255 3.255 0 0 0-.82-.134 2.065 2.065 0 0 0-.972.22 1.767 1.767 0 0 0-.552.467l-.09.061Z" fill="#494949"/><path d="M118.294 115.029c-.259.135-.408.32-.418.513-.002.066.029.147.153.176a.627.627 0 0 0 .173.009c.217-.013.413-.062.584-.125.31-.114.545-.276.669-.46.029-.042.047-.096-.021-.129a.393.393 0 0 0-.117-.03c-.162-.026-.327-.053-.494-.059-.167-.006-.34.01-.465.062l-.064.043Z" fill="#696969"/><path d="M106.845 121.132c-.72.325-1.178.795-1.216 1.293-.037.498.357 1.02 1.088 1.387.174.087.367.167.582.226.625.171 1.371.155 2.033.073.298-.037.601-.091.796-.205.188-.11.253-.267.213-.416-.042-.15-.18-.293-.359-.419a3.514 3.514 0 0 0-.756-.384c-.199-.076-.412-.146-.566-.244-.378-.242-.302-.571-.401-.866a.422.422 0 0 0-.172-.233c-.131-.088-.336-.137-.532-.183-.164-.039-.352-.079-.514-.048l-.196.019Z" fill="#494949"/><path d="M106.361 121.988c-.004.114-.054.103-.108.211-.148.297-.113.645.293.893a.822.822 0 0 0 .29.113c.229.038.472-.037.572-.141.101-.104.095-.228.079-.346-.029-.22-.092-.448-.326-.643a.805.805 0 0 0-.296-.158.795.795 0 0 0-.407-.016l-.097.087Z" fill="#696969"/><path d="M120.398 120.671c-.492.018-.885.203-1.187.389-.302.187-.512.408-.713.628-.111.121-.226.247-.426.336-.206.09-.48.131-.719.198s-.461.179-.448.32c.013.128.215.238.435.308.508.163 1.132.179 1.713.155.48-.02.97-.067 1.353-.203.524-.186.776-.508 1.215-.741.232-.123.516-.22.833-.284.273-.057.593-.097.747-.222.129-.105.102-.251-.027-.365-.13-.114-.345-.2-.576-.263-.379-.103-.905-.302-2.2-.256Z" fill="#494949"/><path d="M119.577 121.317c-.251.142-.458.309-.511.501-.008.027-.01.059.029.081a.328.328 0 0 0 .153.026c.225-.001.423-.059.6-.122.266-.093.511-.199.756-.305a2.36 2.36 0 0 0 .479-.25c.046-.035.086-.073.094-.114.007-.042-.023-.089-.093-.115a.634.634 0 0 0-.15-.028 2.626 2.626 0 0 0-1.357.326Z" fill="#696969"/><path d="M125.332 117.854a2.951 2.951 0 0 0-1.416.059c-.409.126-.692.335-.851.564-.12.173-.168.376.021.544.234.206.741.289 1.197.346.831.104 1.671.192 2.515.236.191.01.384.018.565-.004.181-.022.35-.081.407-.169.049-.073.012-.156-.03-.233-.463-.866-2.408-1.343-2.408-1.343Z" fill="#494949"/><path d="M124.483 118.074c-.106.018-.215.036-.305.069-.09.033-.158.084-.158.141.001.028.019.057.062.075a.36.36 0 0 0 .086.022c.245.043.506.047.757.072.265.027.519.076.777.119.091.015.199.028.271.001.096-.038.052-.117-.014-.173-.224-.187-.64-.318-1.065-.336.002.001-.302-.009-.411.01Z" fill="#696969"/><path d="M105.996 107.812c-2.318-1.057-6.063-4.791-6.961-7.94-.899-3.148-1.067-9.664 3.032-12.34 4.547-3.019 12.575-5.954 19.425-5.997 6.849-.043 10.217.13 15.663 1.812 5.446 1.683 11.621 5.092 11.847 10.787.224 5.696-3.823 11.047-7.636 13.678-4.279 2.951-11.004 4.79-20.885 3.667-9.882-1.123-9.377-1.339-14.485-3.667Z" fill="#8E8E8E"/><path d="M100.551 99.985c-.278-1.612.038-3.252.547-4.83.444-1.372 1.041-2.724 1.963-3.947 2.004-2.657 5.434-4.538 9.119-5.724 3.685-1.188 7.65-1.746 11.581-2.298l3.995-.562c1.271-.178 2.56-.358 3.849-.275 1.457.094 2.84.518 4.165.994 1.281.46 2.552.984 3.539 1.763 2.255 1.78 2.605 4.514 2.379 6.992-.334 3.654-1.653 7.252-3.849 10.501-.924 1.37-2.019 2.691-3.466 3.762-3.087 2.281-7.546 3.212-11.827 3.144-4.281-.067-8.444-1.04-12.485-2.127-2.372-.639-4.828-1.374-6.483-2.827 0-.002-2.748-2.954-3.027-4.566Z" fill="#A1A1A1"/><path d="M146.749 102.106c.378-.729-2.059-.055-2.798.488-.738.543-1.181 1.274-1.717 1.943-.537.669-1.243 1.323-2.226 1.54-1.069.235-2.206-.099-3.316-.059-.917.032-1.812.331-2.47.822-.649.484-1.123 1.172-1.986 1.378-1.355.324-2.622-.732-4.037-.823-1.597-.102-2.836 1.01-4.305 1.5-1.253.417-2.758.361-3.951-.15-1.421-.607-2.498-1.827-4.121-1.899-1.179-.054-2.412.535-3.483.154-.88-.314-1.247-1.17-2.103-1.522-1.137-.469-2.558.136-3.815-.089-.741-.132-1.341-.538-1.898-.935-.558-.397-1.138-.814-1.871-.976-.731-.161-2.422-.679-1.974-.207 1.947 2.718 3.972 4.405 6.237 5.441 2.266 1.035 4.634 1.952 6.503 2.242 1.87.291 12.526 1.989 18.386.78 5.858-1.207 11.939-3.847 14.945-9.628Zm-13.551-9.434c-.305.61-.879 1.134-1.559 1.526-.817.47-1.782.765-2.761.98-3.076.678-6.426.603-9.445-.212-.86-.233-1.707-.53-2.403-.983-.339-.222-1.003-.768-1.101-1.183-.062-.257.096-.463.676-.513.377-.032.749.074 1.107.173.356.098.735.197 1.109.15 1.037-.124 1.433-1.196 2.42-1.469.6-.164 1.277.014 1.789.307.51.292.905.687 1.359 1.032.454.341 1.015.643 1.649.672.793.037 1.493-.352 2.17-.672.674-.32 1.525-.59 2.238-.319.426.163.712.496 1.142.653.357.127.915.244 1.61-.142Z" fill="#C7C7C7"/><path d="M133.347 92.304a2.226 2.226 0 0 1-.149.368c-.694.386-1.253.27-1.611.143-.43-.155-.718-.488-1.143-.652-.71-.272-1.563-.002-2.237.318-.677.32-1.377.71-2.17.672-.634-.029-1.195-.33-1.649-.672-.455-.343-.849-.738-1.359-1.032-.511-.292-1.189-.472-1.79-.306-.986.272-1.383 1.344-2.419 1.469-.375.046-.753-.052-1.109-.15-.359-.1-.731-.204-1.107-.173-.581.05-.739.254-.677.512l-.038.005a1.856 1.856 0 0 1-.202-.603c-.128-.82.354-1.646 1.085-2.25.735-.605 1.697-1.013 2.678-1.35 1.217-.415 2.504-.735 3.831-.777 1.397-.044 2.594-.107 3.981.134 1.287.225 2.538.577 3.705 1.048.522.21 1.037.447 1.449.772.84.65 1.203 1.637.931 2.524Z" fill="#777"/><path d="M134.32 100.552a.419.419 0 0 0-.306-.272 5.621 5.621 0 0 1-2.463-1.217 5.64 5.64 0 0 1-1.23-1.494.417.417 0 0 0-.57-.157 1.96 1.96 0 0 0-.975 1.533.427.427 0 0 0 .07.278 7.783 7.783 0 0 0 2.204 2.192 7.674 7.674 0 0 0 2.479 1.058.42.42 0 0 0 .401-.123 1.77 1.77 0 0 0 .48-.985 1.736 1.736 0 0 0-.09-.813Zm-22.971-10.49a.42.42 0 0 0-.306-.272 5.617 5.617 0 0 1-2.463-1.217 5.638 5.638 0 0 1-1.23-1.493.417.417 0 0 0-.57-.157 1.96 1.96 0 0 0-.975 1.533.428.428 0 0 0 .07.278 7.79 7.79 0 0 0 2.204 2.192 7.668 7.668 0 0 0 2.479 1.058.42.42 0 0 0 .401-.123c.178-.188.418-.517.48-.985a1.74 1.74 0 0 0-.09-.814Zm28.085 2.17a.421.421 0 0 0 .389-.128 5.617 5.617 0 0 1 2.289-1.519 5.642 5.642 0 0 1 1.909-.313.417.417 0 0 0 .422-.414 1.97 1.97 0 0 0-.142-.743 1.999 1.999 0 0 0-.692-.87.427.427 0 0 0-.276-.08 7.775 7.775 0 0 0-3.001.804 7.663 7.663 0 0 0-2.16 1.613.421.421 0 0 0-.096.408c.073.249.237.622.61.91.266.206.544.294.748.332Z" fill="#CDCDCD"/><path d="M110.289 100.37a.417.417 0 0 0 .388-.127 5.618 5.618 0 0 1 2.29-1.52 5.642 5.642 0 0 1 1.909-.312.416.416 0 0 0 .421-.414 1.933 1.933 0 0 0-.142-.744 1.999 1.999 0 0 0-.691-.87.427.427 0 0 0-.276-.08 7.693 7.693 0 0 0-5.162 2.417.423.423 0 0 0-.095.409c.072.248.237.622.61.91.266.206.544.294.748.331Z" fill="#fff"/><path d="M113.997 87.913a.31.31 0 0 0 .286-.094 4.137 4.137 0 0 1 1.688-1.12 4.157 4.157 0 0 1 1.406-.23.307.307 0 0 0 .311-.305 1.426 1.426 0 0 0-.105-.549 1.467 1.467 0 0 0-.509-.64.315.315 0 0 0-.204-.06 5.667 5.667 0 0 0-3.804 1.781.312.312 0 0 0-.07.302 1.3 1.3 0 0 0 .449.67c.197.153.401.217.552.245Zm25.134 8.999a.31.31 0 0 0 .22.206 4.142 4.142 0 0 1 1.79.945c.428.386.703.8.877 1.124.081.15.266.208.417.127.135-.073.291-.185.43-.355a1.47 1.47 0 0 0 .318-.755.312.312 0 0 0-.046-.206 5.654 5.654 0 0 0-3.386-2.486.311.311 0 0 0-.298.082 1.306 1.306 0 0 0-.373.716c-.039.245.003.456.051.602Z" fill="#CBCBCB"/><path d="M101.103 92.931a.31.31 0 0 0 .22.207 4.14 4.14 0 0 1 1.791.944c.427.387.703.8.877 1.125.08.15.266.208.416.127.135-.073.291-.185.431-.355.229-.28.296-.58.318-.755a.318.318 0 0 0-.046-.207 5.748 5.748 0 0 0-1.581-1.658 5.648 5.648 0 0 0-1.805-.828.31.31 0 0 0-.298.083 1.3 1.3 0 0 0-.373.716c-.039.245.002.456.05.601Zm39.982-.973a.309.309 0 0 0 .294.067 4.136 4.136 0 0 1 2.022-.092 4.163 4.163 0 0 1 1.325.526c.145.09.334.045.423-.1a1.43 1.43 0 0 0 .192-.525 1.478 1.478 0 0 0-.107-.812.32.32 0 0 0-.144-.155 5.738 5.738 0 0 0-2.202-.63 5.647 5.647 0 0 0-1.976.2.31.31 0 0 0-.215.222c-.049.184-.086.482.04.806.091.231.233.392.348.493Zm-25.104 10.796a.314.314 0 0 0 .294.067 4.138 4.138 0 0 1 2.023-.092 4.166 4.166 0 0 1 1.325.526.306.306 0 0 0 .423-.101 1.43 1.43 0 0 0 .192-.524 1.477 1.477 0 0 0-.107-.812.317.317 0 0 0-.145-.155 5.644 5.644 0 0 0-4.177-.431.31.31 0 0 0-.215.222c-.049.185-.086.483.04.807.09.231.233.392.347.493Zm0-18.313a.31.31 0 0 0 .294.067 4.141 4.141 0 0 1 2.023-.091 4.16 4.16 0 0 1 1.325.526.307.307 0 0 0 .423-.102 1.43 1.43 0 0 0 .192-.524 1.478 1.478 0 0 0-.107-.812.316.316 0 0 0-.145-.155 5.65 5.65 0 0 0-4.177-.43.31.31 0 0 0-.215.222c-.049.184-.086.483.04.806.09.232.233.392.347.493Z" fill="#D7D7D7"/><path d="M130.885 85.81a.338.338 0 0 0 .282.17 4.534 4.534 0 0 1 2.131.608c.545.317.933.698 1.192 1.006a.336.336 0 0 0 .475.042 1.58 1.58 0 0 0 .551-1.358.341.341 0 0 0-.096-.21 6.186 6.186 0 0 0-4.187-1.898.339.339 0 0 0-.3.155 1.429 1.429 0 0 0-.238.852c.014.27.106.487.19.632Zm-.591 13.645a.42.42 0 0 0-.409-.01 5.62 5.62 0 0 1-2.666.661 5.645 5.645 0 0 1-1.903-.347.416.416 0 0 0-.536.248 1.965 1.965 0 0 0-.116.749c.021.49.222.854.359 1.051a.426.426 0 0 0 .234.168 7.769 7.769 0 0 0 3.097.251 7.674 7.674 0 0 0 2.576-.792.423.423 0 0 0 .227-.353 1.777 1.777 0 0 0-.269-1.063 1.737 1.737 0 0 0-.594-.563Z" fill="#fff"/><g clip-path="url(#b)"><path d="M37.482 122.044c-.524-3.028 4.288-8.807 31.023-20.979a168.387 168.387 0 0 1 32.836 1.405c0 2.731 3.002 4.67 4.169 5.446-13.496 10.715-36.734 19.149-47.473 19.011-6.136-.079-19.752-.253-20.555-4.883Z" fill="#D6D6D6"/><path d="M13.146 119.869c-2.186-4.655-2.61-18.19 15.63-20.841 5.925-5.613 13.08-8.54 22.023-7.379A25.656 25.656 0 0 1 72.09 86.04s13.478-8.82 25.556.684c3.892 3.476 7.829 9.124 2.627 18.081-5.203 8.958-24.41 18.438-48.099 24.071-23.69 5.633-36.196-2.923-39.028-9.007Z" fill="#C7C7C7"/><path d="M19.004 105.705c1.82-.416 3.714-.4 5.527.045 5.776 1.1 11.419 3.629 15.395 7.95a6.033 6.033 0 0 1 1.556 2.592 2.523 2.523 0 0 1-.927 2.724 3.767 3.767 0 0 1-2.21.35 40.552 40.552 0 0 1-12.766-2.527 24.947 24.947 0 0 1-8.188-5.143c-.98-.946-2.405-1.931-1.813-3.344a5.285 5.285 0 0 1 3.426-2.647Zm9.803-6.816a36.5 36.5 0 0 1 10.382 5.204c5.923 3.964 12.196 7.905 19.272 8.819 1.627.209 8.843 2.069 3.264-6.427a25.891 25.891 0 0 0-6.717-6.983c-5.697-4.01-12.96-5.39-20.027-5.023a34.423 34.423 0 0 0-6.174 4.41Zm21.992-7.239c7.652.292 16.718 8.718 24.564 12.045 8.523 3.607 4.08-5.767 1.17-7.986-5.658-4.32-13.107-8.044-19.552-7.965 0 0-3.979 1.763-6.182 3.906Z" fill="#E9E9E9"/><path d="M53.867 89.413c-.226 0 3.114-1.669 3.114-1.669a25.731 25.731 0 0 1 14.33 4.466.141.141 0 0 1 .07.186.141.141 0 0 1-.187.07c-6.243-1.527-10.158-3.023-17.327-3.052Z" fill="#DADADA"/><path d="M72.076 86.072s7.963 3.102 12.539 7.074 7.907 6.268 7.402 2.567c-.505-3.702-1.85-10.523-15.884-11.637l-4.057 1.996Z" fill="#E9E9E9"/><path d="M13.146 119.877s5.152 5.967 16.447 6.631c11.295.665 40.636-6.452 54.494-14.643 17.347-10.257 15.809-22.797 15.809-22.797 4.436 4.05 5.655 14.192-6.405 22.794s-33.089 18.083-58.933 18.773c-11.17-.524-18.393-5.605-21.412-10.758Z" fill="#AFAFAF"/><path d="M72.162 86.002a13.84 13.84 0 0 1 3.971-1.95c3.707-.324 7.41.667 10.454 2.8a.108.108 0 0 1-.014.184.11.11 0 0 1-.072.01c-4.686-1.148-8.959-2.83-14.339-1.044Zm-55.868 21.302c11.163-.864 16.534 3.876 22.675 5.426-3.891-3.739-9.106-5.996-14.438-6.98a11.974 11.974 0 0 0-5.527-.045 6.077 6.077 0 0 0-2.71 1.599ZM30.77 97.243c12.728-2.278 20.533 4.123 28.597 6.2a25.593 25.593 0 0 0-4.358-3.879 27.166 27.166 0 0 0-1.805-1.14 31.44 31.44 0 0 0-18.394-3.875 47.072 47.072 0 0 0-4.04 2.694Z" fill="#DADADA"/></g><path d="M133.285 105.612a.334.334 0 0 0-.313-.084 4.44 4.44 0 0 1-2.171.017 4.465 4.465 0 0 1-1.399-.617.33.33 0 0 0-.458.092 1.531 1.531 0 0 0-.227.554c-.074.381.013.698.082.875a.34.34 0 0 0 .149.172 6.072 6.072 0 0 0 4.461.629.33.33 0 0 0 .239-.23 1.39 1.39 0 0 0-.011-.866 1.367 1.367 0 0 0-.352-.542Z" fill="#ACACAC"/><path d="M171.043 121.286c-.755 1.988-1.517 3.989-2.668 5.812-1.152 1.824-2.73 3.48-4.792 4.478-1.085.525-2.28.856-3.489 1.099-5.284 1.06-11.008.428-15.912-1.755-.937-.418-1.864-.904-2.558-1.606-.655-.666-1.067-1.49-1.448-2.306a56.092 56.092 0 0 1-4.616-15.062c-.087-.562-.167-1.13-.095-1.691.163-1.289 1.084-2.376 1.973-3.39 1.046 2.93 3.37 5.448 6.219 7.199 2.847 1.752 5.937 3.178 9.329 3.664 4.601.66 9.703-.066 13.846-1.869a22.883 22.883 0 0 0 3.03-1.621c.5-.315.987-.642 1.467-.981.353-.248 2.08-2.561 2.555-2.606 1.251 1.625-1.103 6.067-1.364 6.755-.494 1.293-.985 2.587-1.477 3.88Z" fill="#3F3F3F"/><path d="m170.447 119.048-4.44 10.817s-1.398 1.256-2.412 1.7c-1.014.446 3.798-10.963 3.798-10.963s.264-.18.535-.546c.632-.855 1.088-1.908 2.519-1.008Z" fill="#353535"/><path d="M173.772 110.851s1.223 2.593-.312 3.955c-1.535 1.362-5.936 7.723-18.712 6.273-12.776-1.45-15.545-4.405-17.369-6.765-1.823-2.36-2.312-7.283.688-8.715 3.689 5.306 14.656 12.876 23.663 10.974 6.581-1.388 2.454.498 12.042-5.722Z" fill="#494949"/><path d="m163.198 121.565-3.1 11.078-2.751.418 3.021-10.865s.343-.119.772-.47c.488-.403.619-1.025 2.058-.161Zm-6.219.156-2.052 11.438-2.378-.103.952-12.068c.006-.066.089-.093.135-.044.395.425 2.103 1.83 3.343.777Zm-6.788.048.025 10.978-2.493-.583-1.315-12.105s1.203-.571 2.346.747c.777.897 1.073.792 1.437.963Z" fill="#353535"/><path d="M173.769 110.863c-1.028 1.671-2.543 3.069-4.251 4.211-4.614 3.083-10.751 4.302-16.477 3.271-5.725-1.03-10.947-4.296-14.017-8.764-.598-.869-1.119-1.79-1.377-2.778-.345-1.327-.141-2.703.159-4.03.627-2.765 2.249-4.22 3.745-5.933.642-.734 2.728-3.007 3.777-2.933 1.163.083.108 2.713.851 3.526 1.281 1.405 2.989 2.498 4.886 3.124 2.119.699 4.491.843 6.376 1.964 1.005.597 1.845 1.503 3.048 1.961 1.233.47 2.622.343 3.95.262a48.01 48.01 0 0 1 7.381.121c.787.074 2.295-.001 2.946.489.633.476.333 1.705.229 2.32a9.01 9.01 0 0 1-1.226 3.189Z" fill="#FAF8F7"/><path d="M169.052 107.695c-1.373.39-2.623 1.073-4.022 1.386-3.469.772-7.124-.849-10.699-.597-.404.028-.869.12-1.042.447-.121.231-.05.514.089.743.36.596 1.09.928 1.784 1.201 2.725 1.069 5.638 1.814 8.592 1.875 2.954.062 5.958-.593 8.337-2.116.421-.269.828-.573 1.073-.981 1.391-2.307-2.686-2.362-4.112-1.958Z" fill="#fff"/><path d="M174.254 109.848s-5.963 4.596-10.608 5.343c-4.645.746-11.099.479-15.739-2.054-4.205-2.516-7.83-3.939-9.304-12.717-.855 2.345-1.841 4.233-.594 7.525 1.247 3.291 5.977 7.913 12.917 9.397 7.062 1.511 12.257-.032 14.271-.849 2.017-.819 7.755-3.7 9.057-6.645Z" fill="#F1F0EE"/><path d="M145.498 131.458c.159.057-3.771-13.168-3.771-13.168s-2.576.398-2.808-1.745c1.389 5.734 4.286 13.87 4.286 13.87s1.261.666 2.293 1.043Z" fill="#353535"/><path d="M164.193 107.034c1.049-.201 2.059-.554 3.128-.666 2.415-.255 4.067 1.507 6.373.833.5-.146 1.324-.203 1.55-.627.558-1.048.358-2.241.136-2.812-1.56-4.008-4.158-5.825-5.31-6.77-.965-.791-2.083-1.417-3.193-2.036-2.298-1.283-4.668-2.592-7.319-3.026-2.455-.402-4.954-.022-7.422.208-1.494.14-3.007.227-4.439.614-1.432.386-3.612 1.334-4.376 2.478-.839 1.253-.992 2.791-.213 4.102.779 1.311 3.25 2.15 4.873 2.442 1.566.282 3.214-.003 4.754.387 2.941.744 2.635 2.857 5.495 3.832 0 0 2.428 1.719 5.963 1.041Z" fill="#3F3F3F"/><path d="M167.41 101.246c-.845.667-2.026.918-3.187.852-1.16-.065-2.31-.423-3.402-.871-.848-.349-1.686-.761-2.368-1.359-.679-.597-1.19-1.405-1.228-2.237-.013-.303.035-.6.101-.891.068-.298.165-.606.413-.795.375-.288.957-.209 1.451-.06 1.242.37 2.375 1.075 3.629 1.407.894.236 1.812.272 2.722.374.652.074 1.305.188 1.931.42.326.12.78.244.945.564.45.865-.377 2.098-1.007 2.596Z" fill="#2D2D2D"/><path d="M167.195 98.353c-.304 1.622-1.518 3.005-3.165 3.395-1.009.24-2.119.114-3.169-.198-1.051-.313-2.051-.806-3.035-1.314-.711-.368-1.451-.773-1.91-1.419-.406-.57-.543-1.256-.587-1.913-.11-1.654.316-3.28 1.2-4.597.392-.584.89-1.122 1.553-1.414.662-.291 1.51-.307 2.187.101.302.181.553.434.812.675.651.61 1.407 1.186 2.268 1.347 1.492.281 2.45.093 3.226 1.626.591 1.178.849 2.498.62 3.711Z" fill="#777"/><path d="M156.753 94.67c.176.257.439.456.703.64.738.515 1.539.958 2.399 1.258.303.107.625.195.94.162.315-.034.617-.224.67-.505.07-.37-.299-.716-.673-.906-.698-.357-1.22-.404-1.646-1.02-.42-.611.174-2.27-1.169-1.942-.992.242-1.777 1.503-1.224 2.314Z" fill="#A1A1A1"/><path d="M163.828 89.485a4.52 4.52 0 0 0-.965-1.573c-.124-.13-.266-.255-.436-.3-.171-.047-.373.018-.411.176-.043.174.115.349.252.49a5.02 5.02 0 0 1 1.212 2.112c.088.32.14.647.121.966-.023.376-.143.73-.293 1.065-.297.66-1.198 1.564-1.239 1.756-.042.192.38.209.478.192.146-.026.299-.238.394-.336.27-.279.497-.597.676-.94a4.433 4.433 0 0 0 .484-2.249 4.607 4.607 0 0 0-.273-1.359Z" fill="#130B06"/><path d="M152.713 106.62c.084.113.154.238.144.363-.015.251-.338.414-.645.495-.515.134-1.086.138-1.632.051-.498-.077-.999-.236-1.352-.524-.324-.261-.507-.634-.393-.967.512-1.494 3.271-.25 3.878.582Z" fill="#fff"/><path d="M174.728 111.897c-.036.44-.338.866-.197 1.292.116.345.473.631.455.989-.029.62-1.085.776-1.3 1.361-.162.434.203.955-.038 1.357-.268.45-1.035.372-1.494.674-.418.273-.5.788-.66 1.229-.16.441-.585.91-1.092.793-.242-.057-.427-.237-.661-.318-.623-.217-1.22.322-1.553.831-.332.508-.724 1.118-1.386 1.14-.61.018-1.165-.504-1.76-.391-.776.149-1.094 1.283-1.876 1.142-.345-.062-.586-.376-.931-.441-.764-.146-1.135.923-1.899 1.04-.834.13-1.475-.932-2.311-.821-.454.061-.739.444-1.101.694-.55.377-1.354.438-1.981.147-.712-.331-1.193-1.061-1.978-1.192-1.097-.182-2.094.918-3.17.645-.973-.248-1.352-1.483-2.345-1.655-.89-.153-1.734.667-2.607.439-.839-.22-1.053-1.218-1.738-1.707-.572-.41-1.366-.4-2.093-.433-.725-.036-1.557-.198-1.909-.773-.361-.589-.07-1.404-.565-1.912-.474-.487-1.358-.372-1.985-.694-.563-.29-.838-.966-.611-1.501.075-.175.196-.337.224-.523.076-.504-.516-.869-1.001-1.148-.485-.278-.994-.803-.703-1.237.16-.241.518-.338.655-.589.276-.505-.531-1.141-.22-1.628.209-.329.77-.308 1.069-.575.323-.286.224-.764.17-1.172-.055-.409.058-.937.51-1.011.394-.064.841-.306.862.054.021.36-.267 1.317-.418 1.647-1.015 2.232-.193 4.917 1.525 6.826 1.718 1.908 4.201 3.149 6.735 4.064 6.732 2.433 14.433 2.848 21.148.615 2.011-.67 3.96-1.589 5.42-2.985.927-.886 1.635-1.976 1.965-3.162a5.3 5.3 0 0 0 .174-.914c.026-.251-.284-1.209-.175-1.434.348-.713.922.362.846 1.237Z" fill="#2D2D2D"/><ellipse cx=".467" cy=".466" rx=".467" ry=".466" transform="matrix(.9999 -.00021 -.0002 1.0001 147.559 103.616)" fill="#949494"/><ellipse cx=".467" cy=".466" rx=".467" ry=".466" transform="matrix(.9999 -.00021 -.0002 1.0001 162.352 109.828)" fill="#949494"/><ellipse cx=".467" cy=".466" rx=".467" ry=".466" transform="matrix(.9999 -.00021 -.0002 1.0001 151.452 104.393)" fill="#8E8E8E"/><ellipse cx=".467" cy=".466" rx=".467" ry=".466" transform="matrix(.9999 -.00021 -.0002 1.0001 143.667 102.063)" fill="#8E8E8E"/><ellipse cx=".467" cy=".466" rx=".467" ry=".466" transform="matrix(.9999 -.00021 -.0002 1.0001 144.445 107.498)" fill="#CBCBCB"/><ellipse cx=".467" cy=".466" rx=".467" ry=".466" transform="matrix(.9999 -.00021 -.0002 1.0001 166.245 109.052)" fill="#999"/><ellipse cx=".467" cy=".466" rx=".467" ry=".466" transform="matrix(.9999 -.00021 -.0002 1.0001 165.466 113.71)" fill="#999"/><ellipse cx=".467" cy=".466" rx=".467" ry=".466" transform="matrix(.9999 -.00021 -.0002 1.0001 171.694 109.052)" fill="#CBCBCB"/><ellipse cx=".467" cy=".466" rx=".467" ry=".466" transform="matrix(.9999 -.00021 -.0002 1.0001 160.794 116.816)" fill="#CBCBCB"/><ellipse cx=".467" cy=".466" rx=".467" ry=".466" transform="matrix(.9999 -.00021 -.0002 1.0001 138.995 101.287)" fill="#CBCBCB"/><ellipse cx=".467" cy=".466" rx=".467" ry=".466" transform="matrix(.9999 -.00021 -.0002 1.0001 138.217 104.393)" fill="#949494"/><ellipse cx=".467" cy=".466" rx=".467" ry=".466" transform="matrix(.9999 -.00021 -.0002 1.0001 167.802 112.934)" fill="#8E8E8E"/><ellipse cx=".467" cy=".466" rx=".467" ry=".466" transform="matrix(.9999 -.00021 -.0002 1.0001 148.338 107.498)" fill="#8E8E8E"/><ellipse cx=".467" cy=".466" rx=".467" ry=".466" transform="matrix(.9999 -.00021 -.0002 1.0001 156.123 112.157)" fill="#8E8E8E"/><ellipse cx=".467" cy=".466" rx=".467" ry=".466" transform="matrix(.9999 -.00021 -.0002 1.0001 161.573 113.71)" fill="#949494"/><ellipse cx=".467" cy=".466" rx=".467" ry=".466" transform="matrix(.9999 -.00021 -.0002 1.0001 156.123 106.722)" fill="#949494"/><ellipse cx=".467" cy=".466" rx=".467" ry=".466" transform="matrix(.9999 -.00021 -.0002 1.0001 140.553 102.84)" fill="#999"/><ellipse cx=".467" cy=".466" rx=".467" ry=".466" transform="matrix(.9999 -.00021 -.0002 1.0001 149.895 112.157)" fill="#999"/><ellipse cx=".467" cy=".466" rx=".467" ry=".466" transform="matrix(.9999 -.00021 -.0002 1.0001 141.331 98.957)" fill="#949494"/><ellipse cx=".467" cy=".466" rx=".467" ry=".466" transform="matrix(.9999 -.00021 -.0002 1.0001 160.016 108.275)" fill="#CBCBCB"/></g><defs><clipPath id="a"><path fill="#fff" transform="translate(.5)" d="M0 0h200v160H0z"/></clipPath><clipPath id="b"><path fill="#fff" transform="matrix(1.00019 -.00027 -.00028 .9998 12.106 82.332)" d="M0 0h93.394v48.125H0z"/></clipPath></defs></svg>`;


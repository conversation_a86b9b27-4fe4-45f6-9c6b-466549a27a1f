export const clockIcon = `<svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M6.99999 2.99996C4.10049 2.99996 1.74999 5.35047 1.74999 8.24996C1.74999 11.1495 4.10049 13.5 6.99999 13.5C9.89948 13.5 12.25 11.1495 12.25 8.24996C12.25 5.35047 9.89948 2.99996 6.99999 2.99996ZM0.749988 8.24996C0.749988 4.79818 3.54821 1.99996 6.99999 1.99996C10.4518 1.99996 13.25 4.79818 13.25 8.24996C13.25 11.7017 10.4518 14.5 6.99999 14.5C3.54821 14.5 0.749988 11.7017 0.749988 8.24996Z" fill="#1F1E1E"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M9.81354 1.02613C9.79909 0.750366 10.0109 0.5151 10.2867 0.500648C12.0332 0.409117 13.5232 1.75074 13.6147 3.49724C13.6292 3.77301 13.4174 4.00827 13.1416 4.02273C12.8658 4.03718 12.6306 3.82534 12.6161 3.54958C12.5535 2.3546 11.534 1.43665 10.339 1.49928C10.0633 1.51373 9.82799 1.30189 9.81354 1.02613Z" fill="#1F1E1E"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M4.22843 1.04354C4.2525 0.768449 4.049 0.525932 3.77391 0.501865C2.03167 0.349438 0.495729 1.63824 0.343302 3.38049C0.319235 3.65558 0.52273 3.8981 0.797822 3.92216C1.07291 3.94623 1.31543 3.74274 1.3395 3.46764C1.44379 2.27558 2.49469 1.39377 3.68676 1.49806C3.96185 1.52213 4.20436 1.31863 4.22843 1.04354Z" fill="#1F1E1E"/>
<path d="M7.49999 7.75997V4.99994C7.49999 4.7238 7.27613 4.49994 6.99999 4.49994C6.72385 4.49994 6.49999 4.7238 6.49999 4.99994V8.24996L6.50001 8.25497L6.49999 8.25997C6.49999 8.53611 6.72385 8.75997 6.99999 8.75997H9.66665C9.9428 8.75997 10.1667 8.53611 10.1667 8.25997C10.1667 7.98383 9.9428 7.75997 9.66665 7.75997H7.49999Z" fill="#1F1E1E"/>
</svg>`

export const getClockIcon = (width = 24, height = 24, color = "#1F1E1E") => `
  <svg width="${width}" height="${height}" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M6.99999 2.99996C4.10049 2.99996 1.74999 5.35047 1.74999 8.24996C1.74999 11.1495 4.10049 13.5 6.99999 13.5C9.89948 13.5 12.25 11.1495 12.25 8.24996C12.25 5.35047 9.89948 2.99996 6.99999 2.99996ZM0.749988 8.24996C0.749988 4.79818 3.54821 1.99996 6.99999 1.99996C10.4518 1.99996 13.25 4.79818 13.25 8.24996C13.25 11.7017 10.4518 14.5 6.99999 14.5C3.54821 14.5 0.749988 11.7017 0.749988 8.24996Z" fill="${color}"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M9.81354 1.02613C9.79909 0.750366 10.0109 0.5151 10.2867 0.500648C12.0332 0.409117 13.5232 1.75074 13.6147 3.49724C13.6292 3.77301 13.4174 4.00827 13.1416 4.02273C12.8658 4.03718 12.6306 3.82534 12.6161 3.54958C12.5535 2.3546 11.534 1.43665 10.339 1.49928C10.0633 1.51373 9.82799 1.30189 9.81354 1.02613Z" fill="${color}"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M4.22843 1.04354C4.2525 0.768449 4.049 0.525932 3.77391 0.501865C2.03167 0.349438 0.495729 1.63824 0.343302 3.38049C0.319235 3.65558 0.52273 3.8981 0.797822 3.92216C1.07291 3.94623 1.31543 3.74274 1.3395 3.46764C1.44379 2.27558 2.49469 1.39377 3.68676 1.49806C3.96185 1.52213 4.20436 1.31863 4.22843 1.04354Z" fill="${color}"/>
    <path d="M7.49999 7.75997V4.99994C7.49999 4.7238 7.27613 4.49994 6.99999 4.49994C6.72385 4.49994 6.49999 4.7238 6.49999 4.99994V8.24996L6.50001 8.25497L6.49999 8.25997C6.49999 8.53611 6.72385 8.75997 6.99999 8.75997H9.66665C9.9428 8.75997 10.1667 8.53611 10.1667 8.25997C10.1667 7.98383 9.9428 7.75997 9.66665 7.75997H7.49999Z" fill="${color}"/>
  </svg>
`;

export const getDinnerIcon = (width = 24, height = 24, color = "#1F1E1E") => `
  <svg width="${width}" height="${height}" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Classic">
<g id="Icon">
<path fill-rule="evenodd" clip-rule="evenodd" d="M10 2.08335C10.6904 2.08335 11.25 2.643 11.25 3.33335V10.5427C11.25 11.3936 11.9398 12.0834 12.7907 12.0834H12.9166V4.30239C12.9166 3.61203 13.4762 3.05239 14.1666 3.05239C14.8569 3.05239 15.4166 3.61203 15.4166 4.30239V12.0834H15.5426C16.3935 12.0834 17.0833 11.3936 17.0833 10.5427V3.33335C17.0833 2.643 17.643 2.08335 18.3333 2.08335C19.0237 2.08335 19.5833 2.643 19.5833 3.33335V10.5427C19.5833 12.7743 17.7743 14.5834 15.5426 14.5834H15.4166V21.783H16.9033C17.417 21.783 17.8335 22.1994 17.8335 22.7132V35.5427C17.8335 37.0839 16.5841 38.3333 15.0428 38.3333H13.2909C11.7496 38.3333 10.5002 37.0839 10.5002 35.5427V22.7132C10.5002 22.1994 10.9167 21.783 11.4304 21.783H12.9166V14.5834H12.7907C10.5591 14.5834 8.75 12.7743 8.75 10.5427V3.33335C8.75 2.643 9.30964 2.08335 10 2.08335ZM13.0002 35.5427V24.283H15.3335V35.5427C15.3335 35.7032 15.2034 35.8333 15.0428 35.8333H13.2909C13.1303 35.8333 13.0002 35.7032 13.0002 35.5427Z" fill="${color}"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M32.2761 0.575296C32.6716 0.796141 32.9167 1.21369 32.9167 1.66669V35.5698C32.9167 37.2727 31.5362 38.6532 29.8333 38.6532C28.1305 38.6532 26.75 37.2727 26.75 35.5698V20.2732L24.1486 17.8533C23.8944 17.6168 23.75 17.2852 23.75 16.938V10.45C23.75 7.11081 25.4812 4.0104 28.3238 2.25851L31.0109 0.602542C31.3965 0.364875 31.8805 0.354451 32.2761 0.575296ZM30.4167 3.90536L29.6355 4.3868C27.5314 5.68351 26.25 7.97838 26.25 10.45V16.3936L28.8514 18.8135C29.1056 19.05 29.25 19.3815 29.25 19.7287V35.5698C29.25 35.892 29.5112 36.1532 29.8333 36.1532C30.1555 36.1532 30.4167 35.892 30.4167 35.5698V3.90536Z" fill="${color}"/>
</g>
</g>
</svg>
`;

export const nextShiftIcon = `<svg width="15" height="16" viewBox="0 0 15 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M13.6753 4.52795C13.7478 4.2306 13.8159 3.82051 13.7941 3.3714C13.7718 2.9101 13.6535 2.3904 13.331 1.91404C13.0051 1.43262 12.4958 1.03282 11.7567 0.776959C11.014 0.519866 10.3621 0.52646 9.80507 0.715183C9.25516 0.901493 8.84008 1.25074 8.53712 1.61064C8.23482 1.96977 8.03114 2.35455 7.90372 2.64358C7.90255 2.64623 7.90139 2.64887 7.90025 2.65152L6.75731 2.28015C6.35458 2.1493 5.91683 2.33372 5.72915 2.71332L4.76335 4.66668H1.99996C1.72382 4.66668 1.49996 4.89053 1.49996 5.16668C1.49996 5.44282 1.72382 5.66668 1.99996 5.66668H5.99996C6.2761 5.66668 6.49996 5.44282 6.49996 5.16668C6.49996 4.89053 6.2761 4.66668 5.99996 4.66668H5.87891L6.56921 3.2705L13.9862 5.68042L12.6292 13.2625C12.5318 13.8069 11.976 14.1399 11.45 13.969L11.2765 13.9126C11.0139 13.8273 10.7318 13.971 10.6465 14.2337C10.5611 14.4963 10.7048 14.7784 10.9675 14.8637L11.141 14.9201C12.2439 15.2784 13.4093 14.5802 13.6136 13.4387L14.9931 5.73042C15.0682 5.31106 14.8223 4.90062 14.4171 4.76897L13.6753 4.52795ZM11.4295 1.72194C10.8723 1.52906 10.4496 1.55264 10.126 1.6623C9.79518 1.77437 9.52348 1.99171 9.30216 2.25463C9.10613 2.4875 8.9599 2.74424 8.85724 2.96246L12.7208 4.21781C12.7707 3.9921 12.8094 3.71189 12.7953 3.4198C12.7793 3.08926 12.6967 2.76088 12.5029 2.47461C12.3126 2.19341 11.9903 1.91606 11.4295 1.72194Z" fill="#1F1E1E"/>
<path d="M4.36929 6.59182C4.61683 6.71421 4.71828 7.01409 4.59589 7.26163L4.46155 7.53333H5.33329C5.60943 7.53333 5.83329 7.75719 5.83329 8.03333C5.83329 8.30948 5.60943 8.53333 5.33329 8.53333H0.666626C0.390484 8.53333 0.166626 8.30948 0.166626 8.03333C0.166626 7.75719 0.390484 7.53333 0.666626 7.53333H3.346L3.69947 6.81842C3.82186 6.57088 4.12175 6.46943 4.36929 6.59182Z" fill="#1F1E1E"/>
<path d="M3.16987 10.1458C3.29226 9.89829 3.1908 9.59841 2.94326 9.47602C2.69572 9.35363 2.39584 9.45508 2.27345 9.70262L2.25829 9.73328C1.74159 10.7783 2.27251 12.0385 3.38126 12.3988L4.0006 12.6H0.666626C0.390484 12.6 0.166626 12.8239 0.166626 13.1C0.166626 13.3762 0.390484 13.6 0.666626 13.6H9.33329C9.60944 13.6 9.83329 13.3762 9.83329 13.1C9.83329 12.8239 9.60944 12.6 9.33329 12.6H7.23667L3.69028 11.4477C3.16149 11.2759 2.90828 10.6749 3.15471 10.1765L3.16987 10.1458Z" fill="#1F1E1E"/>
<path d="M10.9293 6.79011C11.0716 6.3524 11.5417 6.11286 11.9794 6.25508C12.4171 6.3973 12.6567 6.86743 12.5144 7.30514C12.3722 7.74286 11.9021 7.9824 11.4644 7.84018C11.0267 7.69796 10.7871 7.22783 10.9293 6.79011Z" fill="#1F1E1E"/>
</svg>`

export const exceptionIcon = `<svg width="16" height="15" viewBox="0 0 16 15" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M5.93061 1.67309C6.87312 0.040612 9.2294 0.0406097 10.1719 1.67309L15.6734 11.2019C16.6159 12.8344 15.4378 14.875 13.5527 14.875H2.54978C0.66476 14.875 -0.513381 12.8344 0.429131 11.2019L5.93061 1.67309ZM8.87288 2.42309C8.50772 1.79061 7.59481 1.79061 7.22965 2.42309L1.72817 11.9519C1.36301 12.5844 1.81946 13.375 2.54978 13.375H13.5527C14.2831 13.375 14.7395 12.5844 14.3744 11.9519L8.87288 2.42309ZM8 4.34603C8.41421 4.34603 8.75 4.68182 8.75 5.09603V8.80001C8.75 9.21423 8.41421 9.55001 8 9.55001C7.58578 9.55001 7.25 9.21423 7.25 8.80001V5.09603C7.25 4.68182 7.58578 4.34603 8 4.34603ZM8 10.3934C8.41421 10.3934 8.75 10.7292 8.75 11.1434V11.687C8.75 12.1012 8.41421 12.437 8 12.437C7.58578 12.437 7.25 12.1012 7.25 11.687V11.1434C7.25 10.7292 7.58578 10.3934 8 10.3934Z" fill="#B00000"/>
</svg>`
export const ProfileBackground = (color: string) => `<svg width="260" height="238" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#a)" fill="${color}"><path d="M96.042 3.34c.856 7.72 1.712 15.438 2.568 23.172 2.039 18.407 4.097 36.814 6.135 55.221 2.467 22.237 4.953 44.489 7.42 66.726l6.42 57.685c1.04 9.367 1.937 18.763 3.139 28.115 0 .134.021.267.041.386.204 1.796 1.957 3.34 4.586 3.34 2.324 0 4.79-1.529 4.586-3.34-.856-7.719-1.712-15.438-2.568-23.172l-6.135-55.221c-2.467-22.237-4.953-44.489-7.42-66.726l-6.42-57.685c-1.04-9.367-1.936-18.763-3.139-28.115 0-.134-.02-.267-.041-.386-.204-1.796-1.956-3.34-4.586-3.34-2.323 0-4.79 1.529-4.586 3.34Z"/><path d="M172.558 10.732c-4.198 7.11-8.418 14.236-12.616 21.347-10.029 16.967-20.057 33.92-30.085 50.886-12.128 20.5-24.235 40.986-36.363 61.486-10.476 17.724-20.953 35.433-31.43 53.157-5.116 8.64-10.232 17.264-15.327 25.919-.062.118-.143.237-.204.356-.979 1.663-.673 3.577 1.65 4.572 1.937.831 5.3.46 6.279-1.202 4.198-7.111 8.418-14.236 12.616-21.347 10.029-16.967 20.057-33.919 30.085-50.886 12.128-20.5 24.235-40.985 36.363-61.486 10.476-17.724 20.953-35.433 31.43-53.157 5.116-8.64 10.232-17.264 15.327-25.919.062-.118.143-.237.204-.356.979-1.662.673-3.577-1.651-4.572-1.936-.831-5.299-.46-6.278 1.203Z"/><path d="M246.344 54.657c-9.03 4.097-18.039 8.209-27.068 12.306L154.744 96.31a268432.663 268432.663 0 0 1-77.984 35.45C54.3 141.972 31.817 152.2 9.355 162.412c-10.945 4.973-22.074 9.783-32.857 14.949-.142.074-.306.133-.448.208-4.994 2.271-.387 8.06 4.627 5.774 9.03-4.097 18.038-8.209 27.068-12.306 21.503-9.782 43.007-19.565 64.531-29.347 25.988-11.816 51.976-23.633 77.984-35.449 22.462-10.213 44.944-20.44 67.405-30.653 10.946-4.973 22.075-9.783 32.857-14.949.143-.074.306-.133.448-.208 4.994-2.27.388-8.06-4.626-5.774Z"/><path d="M257.677 168.929c-9.478-3.533-18.936-7.051-28.414-10.584-22.584-8.402-45.147-16.804-67.731-25.221a184105.72 184105.72 0 0 1-81.857-30.475C56.093 93.861 32.49 85.088 8.907 76.3c-11.496-4.275-22.91-8.714-34.487-12.84-.164-.06-.327-.12-.49-.178-5.238-1.96-9.885 3.815-4.627 5.774 9.478 3.533 18.936 7.051 28.414 10.584 22.584 8.402 45.147 16.804 67.731 25.221 27.293 10.153 54.564 20.307 81.857 30.475 23.582 8.788 47.186 17.561 70.768 26.349 11.496 4.275 22.91 8.714 34.488 12.84l.489.178c5.238 1.96 9.885-3.815 4.627-5.774Z"/></g><defs><clipPath id="a"><path fill="#fff" transform="translate(-33)" d="M0 0h293v238H0z"/></clipPath></defs></svg>`;

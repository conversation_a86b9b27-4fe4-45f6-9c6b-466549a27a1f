export const calendar = `<svg width="25" height="24" viewBox="0 0 25 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
<path d="M11.5625 10.9062C11.5625 10.354 11.1148 9.90625 10.5625 9.90625C10.0102 9.90625 9.5625 10.354 9.5625 10.9062V11.4688C9.5625 12.021 10.0102 12.4688 10.5625 12.4688C11.1148 12.4688 11.5625 12.021 11.5625 11.4688V10.9062Z" fill="#4B4B49"/>
<path d="M8.1875 14.2812C8.1875 13.729 7.73978 13.2812 7.1875 13.2812C6.63522 13.2812 6.1875 13.729 6.1875 14.2812V14.8438C6.1875 15.396 6.63522 15.8438 7.1875 15.8438C7.73978 15.8438 8.1875 15.396 8.1875 14.8438V14.2812Z" fill="#4B4B49"/>
<path d="M11.5625 14.2812C11.5625 13.729 11.1148 13.2812 10.5625 13.2812C10.0102 13.2812 9.5625 13.729 9.5625 14.2812V14.8438C9.5625 15.396 10.0102 15.8438 10.5625 15.8438C11.1148 15.8438 11.5625 15.396 11.5625 14.8438V14.2812Z" fill="#4B4B49"/>
<path d="M14.9375 14.2812C14.9375 13.729 14.4898 13.2812 13.9375 13.2812C13.3852 13.2812 12.9375 13.729 12.9375 14.2812V14.8438C12.9375 15.396 13.3852 15.8438 13.9375 15.8438C14.4898 15.8438 14.9375 15.396 14.9375 14.8438V14.2812Z" fill="#4B4B49"/>
<path d="M18.3125 14.2812C18.3125 13.729 17.8648 13.2812 17.3125 13.2812C16.7602 13.2812 16.3125 13.729 16.3125 14.2812V14.8438C16.3125 15.396 16.7602 15.8438 17.3125 15.8438C17.8648 15.8438 18.3125 15.396 18.3125 14.8438V14.2812Z" fill="#4B4B49"/>
<path d="M14.9375 17.6562C14.9375 17.104 14.4898 16.6562 13.9375 16.6562C13.3852 16.6562 12.9375 17.104 12.9375 17.6562V18.2188C12.9375 18.771 13.3852 19.2188 13.9375 19.2188C14.4898 19.2188 14.9375 18.771 14.9375 18.2188V17.6562Z" fill="#4B4B49"/>
<path d="M11.5625 17.6562C11.5625 17.104 11.1148 16.6562 10.5625 16.6562C10.0102 16.6562 9.5625 17.104 9.5625 17.6562V18.2188C9.5625 18.771 10.0102 19.2188 10.5625 19.2188C11.1148 19.2188 11.5625 18.771 11.5625 18.2188V17.6562Z" fill="#4B4B49"/>
<path d="M8.1875 17.6562C8.1875 17.104 7.73978 16.6562 7.1875 16.6562C6.63522 16.6562 6.1875 17.104 6.1875 17.6562V18.2188C6.1875 18.771 6.63522 19.2188 7.1875 19.2188C7.73978 19.2188 8.1875 18.771 8.1875 18.2188V17.6562Z" fill="#4B4B49"/>
<path d="M14.9375 10.9062C14.9375 10.354 14.4898 9.90625 13.9375 9.90625C13.3852 9.90625 12.9375 10.354 12.9375 10.9062V11.4688C12.9375 12.021 13.3852 12.4688 13.9375 12.4688C14.4898 12.4688 14.9375 12.021 14.9375 11.4688V10.9062Z" fill="#4B4B49"/>
<path d="M18.3125 10.9062C18.3125 10.354 17.8648 9.90625 17.3125 9.90625C16.7602 9.90625 16.3125 10.354 16.3125 10.9062V11.4688C16.3125 12.021 16.7602 12.4688 17.3125 12.4688C17.8648 12.4688 18.3125 12.021 18.3125 11.4688V10.9062Z" fill="#4B4B49"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.75 1.71875C8.16421 1.71875 8.5 2.05454 8.5 2.46875V3.25H16V2.46875C16 2.05454 16.3358 1.71875 16.75 1.71875C17.1642 1.71875 17.5 2.05454 17.5 2.46875V3.25H19.5625C20.9087 3.25 22 4.34131 22 5.6875V19.1125C22 20.4587 20.9087 21.55 19.5625 21.55H4.9375C3.59131 21.55 2.5 20.4587 2.5 19.1125V5.6875C2.5 4.34131 3.59131 3.25 4.9375 3.25H7V2.46875C7 2.05454 7.33579 1.71875 7.75 1.71875ZM7 4.75H4.9375C4.41973 4.75 4 5.16973 4 5.6875V7.25H20.5V5.6875C20.5 5.16973 20.0803 4.75 19.5625 4.75H17.5V5.84375C17.5 6.25796 17.1642 6.59375 16.75 6.59375C16.3358 6.59375 16 6.25796 16 5.84375V4.75H8.5V5.84375C8.5 6.25796 8.16421 6.59375 7.75 6.59375C7.33579 6.59375 7 6.25796 7 5.84375V4.75ZM20.5 8.75H4V19.1125C4 19.6303 4.41973 20.05 4.9375 20.05H19.5625C20.0803 20.05 20.5 19.6303 20.5 19.1125V8.75Z" fill="#4B4B49"/>
</svg>`

export const calendarSelected = `<svg width="25" height="24" viewBox="0 0 25 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.75 1.71875C8.16421 1.71875 8.5 2.05454 8.5 2.46875V3.25H16V2.46875C16 2.05454 16.3358 1.71875 16.75 1.71875C17.1642 1.71875 17.5 2.05454 17.5 2.46875V3.25H19.5625C20.9087 3.25 22 4.34131 22 5.6875V19.1125C22 20.4587 20.9087 21.55 19.5625 21.55H4.9375C3.59131 21.55 2.5 20.4587 2.5 19.1125V5.6875C2.5 4.34131 3.59131 3.25 4.9375 3.25H7V2.46875C7 2.05454 7.33579 1.71875 7.75 1.71875ZM7 4.75H4.9375C4.41973 4.75 4 5.16973 4 5.6875V7.25H20.5V5.6875C20.5 5.16973 20.0803 4.75 19.5625 4.75H17.5V5.84375C17.5 6.25796 17.1642 6.59375 16.75 6.59375C16.3358 6.59375 16 6.25796 16 5.84375V4.75H8.5V5.84375C8.5 6.25796 8.16421 6.59375 7.75 6.59375C7.33579 6.59375 7 6.25796 7 5.84375V4.75ZM10.5625 9.90625C11.1148 9.90625 11.5625 10.354 11.5625 10.9062V11.4688C11.5625 12.021 11.1148 12.4688 10.5625 12.4688C10.0102 12.4688 9.5625 12.021 9.5625 11.4688V10.9062C9.5625 10.354 10.0102 9.90625 10.5625 9.90625ZM7.1875 13.2812C7.73978 13.2812 8.1875 13.729 8.1875 14.2812V14.8438C8.1875 15.396 7.73978 15.8438 7.1875 15.8438C6.63522 15.8438 6.1875 15.396 6.1875 14.8438V14.2812C6.1875 13.729 6.63522 13.2812 7.1875 13.2812ZM10.5625 13.2812C11.1148 13.2812 11.5625 13.729 11.5625 14.2812V14.8438C11.5625 15.396 11.1148 15.8438 10.5625 15.8438C10.0102 15.8438 9.5625 15.396 9.5625 14.8438V14.2812C9.5625 13.729 10.0102 13.2812 10.5625 13.2812ZM13.9375 13.2812C14.4898 13.2812 14.9375 13.729 14.9375 14.2812V14.8438C14.9375 15.396 14.4898 15.8438 13.9375 15.8438C13.3852 15.8438 12.9375 15.396 12.9375 14.8438V14.2812C12.9375 13.729 13.3852 13.2812 13.9375 13.2812ZM17.3125 13.2812C17.8648 13.2812 18.3125 13.729 18.3125 14.2812V14.8438C18.3125 15.396 17.8648 15.8438 17.3125 15.8438C16.7602 15.8438 16.3125 15.396 16.3125 14.8438V14.2812C16.3125 13.729 16.7602 13.2812 17.3125 13.2812ZM13.9375 16.6562C14.4898 16.6562 14.9375 17.104 14.9375 17.6562V18.2188C14.9375 18.771 14.4898 19.2188 13.9375 19.2188C13.3852 19.2188 12.9375 18.771 12.9375 18.2188V17.6562C12.9375 17.104 13.3852 16.6562 13.9375 16.6562ZM10.5625 16.6562C11.1148 16.6562 11.5625 17.104 11.5625 17.6562V18.2188C11.5625 18.771 11.1148 19.2188 10.5625 19.2188C10.0102 19.2188 9.5625 18.771 9.5625 18.2188V17.6562C9.5625 17.104 10.0102 16.6562 10.5625 16.6562ZM7.1875 16.6562C7.73978 16.6562 8.1875 17.104 8.1875 17.6562V18.2188C8.1875 18.771 7.73978 19.2188 7.1875 19.2188C6.63522 19.2188 6.1875 18.771 6.1875 18.2188V17.6562C6.1875 17.104 6.63522 16.6562 7.1875 16.6562ZM13.9375 9.90625C14.4898 9.90625 14.9375 10.354 14.9375 10.9062V11.4688C14.9375 12.021 14.4898 12.4688 13.9375 12.4688C13.3852 12.4688 12.9375 12.021 12.9375 11.4688V10.9062C12.9375 10.354 13.3852 9.90625 13.9375 9.90625ZM17.3125 9.90625C17.8648 9.90625 18.3125 10.354 18.3125 10.9062V11.4688C18.3125 12.021 17.8648 12.4688 17.3125 12.4688C16.7602 12.4688 16.3125 12.021 16.3125 11.4688V10.9062C16.3125 10.354 16.7602 9.90625 17.3125 9.90625Z"/>
</svg>`

export const home = `<svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_10319_1026)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M11.4828 0.956897C11.7724 0.681034 12.2276 0.681034 12.5172 0.956897L22.5517 10.5135C22.9976 10.9382 23.25 11.5271 23.25 12.1429V21C23.25 22.2426 22.2426 23.25 21 23.25H15.3636C14.6733 23.25 14.1136 22.6904 14.1136 22V15.25H9.88636V22C9.88636 22.6904 9.32672 23.25 8.63636 23.25H3C1.75736 23.25 0.75 22.2426 0.75 21V12.1429C0.75 11.5271 1.00237 10.9382 1.44828 10.5135L11.4828 0.956897ZM12 2.53571L2.48276 11.5998C2.33412 11.7413 2.25 11.9376 2.25 12.1429V21C2.25 21.4142 2.58579 21.75 3 21.75H8.38636V15C8.38636 14.3096 8.94601 13.75 9.63636 13.75H14.3636C15.054 13.75 15.6136 14.3096 15.6136 15V21.75H21C21.4142 21.75 21.75 21.4142 21.75 21V12.1429C21.75 11.9376 21.6659 11.7413 21.5172 11.5998L12 2.53571Z" fill="#4B4B49"/>
</g>
<defs>
<clipPath id="clip0_10319_1026">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>
</svg>`

export const homeSelected = `<svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_10319_3388)">
<path d="M12.5172 0.956897C12.2276 0.681034 11.7724 0.681034 11.4828 0.956897L1.44828 10.5135C1.00237 10.9382 0.75 11.5271 0.75 12.1429V21C0.75 22.2426 1.75736 23.25 3 23.25H8.63636C9.32672 23.25 9.88636 22.6904 9.88636 22V15.25H14.1136V22C14.1136 22.6904 14.6733 23.25 15.3636 23.25H21C22.2426 23.25 23.25 22.2426 23.25 21V12.1429C23.25 11.5271 22.9976 10.9382 22.5517 10.5135L12.5172 0.956897Z"/>
</g>
<defs>
<clipPath id="clip0_10319_3388">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>
</svg>`

export const profile = `<svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_10319_3404)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M0.25 12C0.25 5.51065 5.51065 0.25 12 0.25C18.4893 0.25 23.75 5.51065 23.75 12C23.75 18.4893 18.4893 23.75 12 23.75C5.51065 23.75 0.25 18.4893 0.25 12ZM12 1.75C6.33908 1.75 1.75 6.33908 1.75 12C1.75 14.1357 2.40319 16.1189 3.52069 17.7606C3.61124 17.5837 3.73147 17.3628 3.88309 17.112C4.70739 15.7484 6.55415 13.3543 9.71656 12.5346C8.679 11.812 8 10.6103 8 9.25C8 7.04086 9.79086 5.25 12 5.25C14.2091 5.25 16 7.04086 16 9.25C16 10.6103 15.321 11.812 14.2834 12.5346C17.4459 13.3543 19.2926 15.7484 20.1169 17.112C20.2686 17.3628 20.3888 17.5837 20.4793 17.7606C21.5968 16.1189 22.25 14.1357 22.25 12C22.25 6.33908 17.6609 1.75 12 1.75ZM19.4641 19.025C19.2 18.5 19.0459 18.2398 18.8332 17.888C17.9218 16.3802 15.7879 13.75 12 13.75C8.21213 13.75 6.07826 16.3802 5.16678 17.888C4.95391 18.2401 4.8 18.5 4.53582 19.0249C6.40515 21.0104 9.05781 22.25 12 22.25C14.9422 22.25 17.5948 21.0104 19.4641 19.025ZM12 6.75C10.6193 6.75 9.5 7.86929 9.5 9.25C9.5 10.6307 10.6193 11.75 12 11.75C13.3807 11.75 14.5 10.6307 14.5 9.25C14.5 7.86929 13.3807 6.75 12 6.75Z" fill="#4B4B49"/>
</g>
<defs>
<clipPath id="clip0_10319_3404">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>
</svg>`

export const profileSelected = `<svg width="24" height="25" viewBox="0 0 24 25" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_10319_6623)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M0.25 12.5C0.25 6.01065 5.51065 0.75 12 0.75C18.4893 0.75 23.75 6.01065 23.75 12.5C23.75 18.9893 18.4893 24.25 12 24.25C5.51065 24.25 0.25 18.9893 0.25 12.5ZM8 9.75C8 7.54086 9.79086 5.75 12 5.75C14.2091 5.75 16 7.54086 16 9.75C16 11.1103 15.321 12.312 14.2834 13.0346C17.4459 13.8543 19.2927 16.2484 20.1169 17.612C20.3312 17.9665 20.2176 18.4276 19.8631 18.6418C19.5086 18.8561 19.0475 18.7425 18.8333 18.388C17.9218 16.8802 15.7879 14.25 12 14.25C8.21214 14.25 6.07827 16.8802 5.1668 18.388C4.95252 18.7425 4.49144 18.8561 4.13696 18.6418C3.78248 18.4276 3.66883 17.9665 3.88311 17.612C4.7074 16.2484 6.55417 13.8543 9.71657 13.0346C8.679 12.312 8 11.1103 8 9.75ZM12 7.25C10.6193 7.25 9.5 8.36929 9.5 9.75C9.5 11.1307 10.6193 12.25 12 12.25C13.3807 12.25 14.5 11.1307 14.5 9.75C14.5 8.36929 13.3807 7.25 12 7.25Z"/>
</g>
<defs>
<clipPath id="clip0_10319_6623">
<rect width="24" height="24" fill="white" transform="translate(0 0.5)"/>
</clipPath>
</defs>
</svg>`

export const resource = `<svg width="25" height="24" viewBox="0 0 25 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
<path d="M9.75 8.5C9.75 8.08579 10.0858 7.75 10.5 7.75H17.5C17.9142 7.75 18.25 8.08579 18.25 8.5C18.25 8.91421 17.9142 9.25 17.5 9.25H10.5C10.0858 9.25 9.75 8.91421 9.75 8.5Z" fill="#1F1E1E"/>
<path d="M9.75 15C9.75 14.5858 10.0858 14.25 10.5 14.25H17.5C17.9142 14.25 18.25 14.5858 18.25 15C18.25 15.4142 17.9142 15.75 17.5 15.75H10.5C10.0858 15.75 9.75 15.4142 9.75 15Z" fill="#1F1E1E"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M3.75 4C3.75 2.48122 4.98122 1.25 6.5 1.25H20C21.5188 1.25 22.75 2.48122 22.75 4V20C22.75 21.5188 21.5188 22.75 20 22.75H6.5C4.98122 22.75 3.75 21.5188 3.75 20V18.75H1.75C1.33579 18.75 1 18.4142 1 18C1 17.5858 1.33579 17.25 1.75 17.25H3.75V12.75H1.75C1.33579 12.75 1 12.4142 1 12C1 11.5858 1.33579 11.25 1.75 11.25H3.75V6.75H1.75C1.33579 6.75 1 6.41421 1 6C1 5.58579 1.33579 5.25 1.75 5.25H3.75V4ZM5.25 20V18.75H7.25C7.66421 18.75 8 18.4142 8 18C8 17.5858 7.66421 17.25 7.25 17.25H5.25V12.75H7.25C7.66421 12.75 8 12.4142 8 12C8 11.5858 7.66421 11.25 7.25 11.25H5.25V6.75H7.25C7.66421 6.75 8 6.41421 8 6C8 5.58579 7.66421 5.25 7.25 5.25H5.25V4C5.25 3.30964 5.80964 2.75 6.5 2.75H20C20.6904 2.75 21.25 3.30964 21.25 4V20C21.25 20.6904 20.6904 21.25 20 21.25H6.5C5.80964 21.25 5.25 20.6904 5.25 20Z" fill="#1F1E1E"/>
</svg>`

export const resourceSelected = `<svg width="25" height="24" viewBox="0 0 25 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M6.5 1.25C4.98122 1.25 3.75 2.48122 3.75 4V5.25H1.75C1.33579 5.25 1 5.58579 1 6C1 6.41421 1.33579 6.75 1.75 6.75H3.75V11.25H1.75C1.33579 11.25 1 11.5858 1 12C1 12.4142 1.33579 12.75 1.75 12.75H3.75V17.25H1.75C1.33579 17.25 1 17.5858 1 18C1 18.4142 1.33579 18.75 1.75 18.75H3.75V20C3.75 21.5188 4.98122 22.75 6.5 22.75H20C21.5188 22.75 22.75 21.5188 22.75 20V4C22.75 2.48122 21.5188 1.25 20 1.25H6.5ZM9.75 8.5C9.75 8.08579 10.0858 7.75 10.5 7.75H17.5C17.9142 7.75 18.25 8.08579 18.25 8.5C18.25 8.91421 17.9142 9.25 17.5 9.25H10.5C10.0858 9.25 9.75 8.91421 9.75 8.5ZM9.75 15C9.75 14.5858 10.0858 14.25 10.5 14.25H17.5C17.9142 14.25 18.25 14.5858 18.25 15C18.25 15.4142 17.9142 15.75 17.5 15.75H10.5C10.0858 15.75 9.75 15.4142 9.75 15Z"/>
</svg>`

export const bulletin = `<svg width="25" height="24" viewBox="0 0 25 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M5.7625 8.20417C5.7625 7.78995 6.09829 7.45417 6.5125 7.45417H19.0542C19.4684 7.45417 19.8042 7.78995 19.8042 8.20417C19.8042 8.61838 19.4684 8.95417 19.0542 8.95417H6.5125C6.09829 8.95417 5.7625 8.61838 5.7625 8.20417Z" fill="#1F1E1E"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M5.7625 11.9667C5.7625 11.5525 6.09829 11.2167 6.5125 11.2167H11.5292C11.9434 11.2167 12.2792 11.5525 12.2792 11.9667C12.2792 12.3809 11.9434 12.7167 11.5292 12.7167H6.5125C6.09829 12.7167 5.7625 12.3809 5.7625 11.9667Z" fill="#1F1E1E"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M2 5C2 3.48122 3.23122 2.25 4.75 2.25H20.75C22.2688 2.25 23.5 3.48122 23.5 5V15C23.5 16.5188 22.2688 17.75 20.75 17.75H11.889L8.15217 21.9965C7.54259 22.6892 6.4 22.258 6.4 21.3353V17.75H4.75C3.23122 17.75 2 16.5188 2 15V5ZM4.75 3.75C4.05964 3.75 3.5 4.30964 3.5 5V15C3.5 15.6904 4.05964 16.25 4.75 16.25H6.52292C7.28346 16.25 7.9 16.8665 7.9 17.6271V20.0124L10.7997 16.7173C11.0611 16.4203 11.4377 16.25 11.8335 16.25H20.75C21.4404 16.25 22 15.6904 22 15V5C22 4.30964 21.4404 3.75 20.75 3.75H4.75Z" fill="#1F1E1E"/>
</svg>`


export const bulletinSelected = `<svg width="25" height="24" viewBox="0 0 25 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M2 5C2 3.48122 3.23122 2.25 4.75 2.25H20.75C22.2688 2.25 23.5 3.48122 23.5 5V15C23.5 16.5188 22.2688 17.75 20.75 17.75H11.889L8.15217 21.9965C7.54259 22.6892 6.4 22.258 6.4 21.3353V17.75H4.75C3.23122 17.75 2 16.5188 2 15V5Z"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M5.7627 8.2041C5.7627 7.78989 6.09848 7.4541 6.5127 7.4541H19.0544C19.4686 7.4541 19.8044 7.78989 19.8044 8.2041C19.8044 8.61832 19.4686 8.9541 19.0544 8.9541H6.5127C6.09848 8.9541 5.7627 8.61832 5.7627 8.2041Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M5.7627 11.9666C5.7627 11.5524 6.09848 11.2166 6.5127 11.2166H11.5294C11.9436 11.2166 12.2794 11.5524 12.2794 11.9666C12.2794 12.3808 11.9436 12.7166 11.5294 12.7166H6.5127C6.09848 12.7166 5.7627 12.3808 5.7627 11.9666Z" fill="white"/>
</svg>
`
export const NoNetwork = `<svg width="129" height="128" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#a)"><rect x=".5" width="128" height="128" rx="64" fill="#fff"/><path d="M67.262 48.324v20.272a2.466 2.466 0 0 1-2.457 2.457h-.72a2.467 2.467 0 0 1-2.458-2.457V48.324a2.466 2.466 0 0 1 2.457-2.457h.72a2.466 2.466 0 0 1 2.458 2.457ZM64.805 73.44h-.72a2.467 2.467 0 0 0-2.458 2.457v2.961a2.466 2.466 0 0 0 2.457 2.457h.72a2.467 2.467 0 0 0 2.458-2.457V75.9a2.466 2.466 0 0 0-2.457-2.458Z" fill="#C74E4E"/><path d="m79.216 73.792-2.56 2.804a.923.923 0 0 1-1.242.106 9.238 9.238 0 0 0-5.38-1.848v-5.602a14.752 14.752 0 0 1 9.075 3.191.923.923 0 0 1 .107 1.35Zm12.6-15.33a33.483 33.483 0 0 0-21.783-8.11v5.588a27.935 27.935 0 0 1 18.014 6.711.923.923 0 0 0 1.27-.087l2.545-2.772a.923.923 0 0 0-.046-1.33Zm-21.783 1.386v5.589c4.28.038 8.417 1.55 11.714 4.281a.924.924 0 0 0 1.26-.097l2.55-2.799a.924.924 0 0 0-.097-1.335 24.087 24.087 0 0 0-15.427-5.658v.019Zm-20.36 13.944 2.56 2.804a.924.924 0 0 0 1.242.106 9.237 9.237 0 0 1 5.38-1.848v-5.602a14.753 14.753 0 0 0-9.075 3.191.925.925 0 0 0-.107 1.35Zm-12.6-15.33a33.482 33.482 0 0 1 21.783-8.11v5.588a27.935 27.935 0 0 0-18.014 6.711.924.924 0 0 1-1.27-.087l-2.545-2.772a.922.922 0 0 1 .046-1.33Zm21.783 1.386v5.589a18.623 18.623 0 0 0-11.714 4.281.924.924 0 0 1-1.26-.097l-2.55-2.799a.925.925 0 0 1 .097-1.335 24.087 24.087 0 0 1 15.427-5.658v.019Z" fill="#B0B0B0"/></g><defs><clipPath id="a"><rect x=".5" width="128" height="128" rx="64" fill="#fff"/></clipPath></defs></svg>`;

export const NoNetworkConnection = `<svg width="65" height="65" viewBox="0 0 129 128" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_18125_1903)">
<rect x="0.5" width="128" height="128" rx="64" fill="#FAF8F7"/>
<path d="M67.2621 48.3239V68.5961C67.2597 69.247 67 69.8706 66.5397 70.3309C66.0794 70.7912 65.4558 71.0508 64.8048 71.0533H64.0843C63.4334 71.0508 62.8098 70.7912 62.3495 70.3309C61.8892 69.8706 61.6295 69.247 61.6271 68.5961V48.3239C61.6295 47.673 61.8892 47.0494 62.3495 46.5891C62.8098 46.1288 63.4334 45.8691 64.0843 45.8667H64.8048C65.4558 45.8691 66.0794 46.1288 66.5397 46.5891C67 47.0494 67.2597 47.673 67.2621 48.3239ZM64.8048 73.4412H64.0843C63.4334 73.4437 62.8098 73.7033 62.3495 74.1636C61.8892 74.6239 61.6295 75.2475 61.6271 75.8985V78.8592C61.6295 79.5101 61.8892 80.1337 62.3495 80.594C62.8098 81.0543 63.4334 81.3139 64.0843 81.3164H64.8048C65.4558 81.3139 66.0794 81.0543 66.5397 80.594C67 80.1337 67.2597 79.5101 67.2621 78.8592V75.8985C67.2597 75.2475 67 74.6239 66.5397 74.1636C66.0794 73.7033 65.4558 73.4437 64.8048 73.4412Z" fill="#C74E4E"/>
<path d="M79.2156 73.7921L76.6567 76.5957C76.5003 76.7655 76.2861 76.8705 76.0562 76.8901C75.8262 76.9098 75.5972 76.8427 75.4143 76.702C73.8597 75.5354 71.9767 74.8888 70.0333 74.8544V69.2518C73.3303 69.2715 76.5257 70.3952 79.1094 72.4434C79.2088 72.5223 79.291 72.6208 79.3506 72.7329C79.4103 72.8449 79.4463 72.968 79.4562 73.0946C79.4662 73.2212 79.45 73.3484 79.4085 73.4684C79.3671 73.5884 79.3015 73.6986 79.2156 73.7921ZM91.8159 58.4621C85.7565 53.2442 78.0297 50.3671 70.0333 50.3514V55.9402C76.6427 55.9758 83.0254 58.3538 88.0469 62.6514C88.2294 62.8025 88.4631 62.8777 88.6995 62.8613C88.9359 62.845 89.157 62.7384 89.317 62.5637L91.862 59.7923C91.9485 59.7031 92.0161 59.5973 92.0607 59.4814C92.1054 59.3654 92.1262 59.2416 92.1218 59.1174C92.1175 58.9932 92.0882 58.8712 92.0356 58.7586C91.983 58.646 91.9083 58.5451 91.8159 58.4621ZM70.0333 59.8478V65.4366C74.3138 65.4752 78.4504 66.9872 81.7467 69.7183C81.9298 69.8655 82.1622 69.9374 82.3964 69.9194C82.6307 69.9014 82.8492 69.7948 83.0077 69.6213L85.5573 66.8222C85.6415 66.7296 85.7059 66.6208 85.7468 66.5025C85.7876 66.3842 85.8041 66.2589 85.795 66.134C85.7859 66.0092 85.7515 65.8875 85.694 65.7763C85.6365 65.6652 85.557 65.5669 85.4603 65.4874C81.1401 61.8514 75.6799 59.8488 70.0333 59.8293V59.8478Z" fill="#B0B0B0"/>
<path d="M49.6735 73.7921L52.2324 76.5957C52.3888 76.7654 52.603 76.8704 52.833 76.8901C53.0629 76.9098 53.2919 76.8426 53.4748 76.7019C55.0294 75.5353 56.9124 74.8888 58.8558 74.8544V69.2517C55.5588 69.2715 52.3634 70.3952 49.7797 72.4433C49.6803 72.5222 49.5982 72.6208 49.5385 72.7328C49.4788 72.8449 49.4428 72.968 49.4329 73.0946C49.4229 73.2211 49.4392 73.3484 49.4806 73.4684C49.522 73.5884 49.5876 73.6986 49.6735 73.7921ZM37.0733 58.4621C43.1326 53.2441 50.8594 50.3671 58.8558 50.3514V55.9402C52.2464 55.9758 45.8638 58.3537 40.8423 62.6514C40.6597 62.8025 40.4261 62.8776 40.1896 62.8613C39.9532 62.845 39.7321 62.7384 39.5721 62.5636L37.0271 59.7923C36.9406 59.7031 36.873 59.5973 36.8284 59.4813C36.7837 59.3654 36.763 59.2416 36.7673 59.1174C36.7716 58.9932 36.8009 58.8711 36.8535 58.7585C36.9061 58.6459 36.9808 58.5451 37.0733 58.4621ZM58.8558 59.8477V65.4366C54.5753 65.4752 50.4387 66.9872 47.1424 69.7182C46.9593 69.8654 46.7269 69.9374 46.4927 69.9193C46.2584 69.9013 46.0399 69.7947 45.8815 69.6212L43.3318 66.8222C43.2477 66.7296 43.1832 66.6208 43.1423 66.5025C43.1015 66.3842 43.0851 66.2588 43.0941 66.134C43.1032 66.0092 43.1376 65.8875 43.1951 65.7763C43.2526 65.6652 43.3321 65.5668 43.4288 65.4874C47.749 61.8514 53.2092 59.8488 58.8558 59.8293V59.8477Z" fill="#B0B0B0"/>
</g>
<defs>
<clipPath id="clip0_18125_1903">
<rect x="0.5" width="160" height="160" rx="64" fill="#FAF8F7"/>
</clipPath>
</defs>
</svg>
`

import React from "react";
import { SvgXml } from "react-native-svg";

export const ErrorMinus = ({ size, color }: { size: number, color: string }) => {

    const errorMinus = `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Error" clip-path="url(#clip0_9643_6939)">
<path id="Icon" fill-rule="evenodd" clip-rule="evenodd" d="M0.25 8C0.25 3.71979 3.71979 0.25 8 0.25C12.2802 0.25 15.75 3.71979 15.75 8C15.75 12.2802 12.2802 15.75 8 15.75C3.71979 15.75 0.25 12.2802 0.25 8ZM4.75 7C4.19772 7 3.75 7.44772 3.75 8C3.75 8.55229 4.19772 9 4.75 9H11.25C11.8023 9 12.25 8.55229 12.25 8C12.25 7.44772 11.8023 7 11.25 7H4.75Z" fill="${color}"/>
</g>
<defs>
<clipPath id="clip0_9643_6939">
<rect width="16" height="16" fill="white"/>
</clipPath>
</defs>
</svg>
`

    return <SvgXml xml={errorMinus} width={size} height={size} />;
};

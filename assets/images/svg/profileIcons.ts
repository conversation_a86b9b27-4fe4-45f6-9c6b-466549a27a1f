export const notification = `<svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M8.14437 4.58807C7.05512 5.48799 6.25 6.91135 6.25 9.05982C6.25 10.5553 6.02889 11.8515 5.42746 13.1177C4.86561 14.3005 3.99352 15.4112 2.75 16.6164V17.75H21.25V16.6447C20.0044 15.5714 19.1321 14.493 18.5704 13.2902C17.9683 12.0008 17.75 10.6253 17.75 9.05982C17.75 6.98395 16.9813 5.55446 15.9243 4.63465C14.8496 3.69938 13.4276 3.25 12.0834 3.25C10.7232 3.25 9.25642 3.66932 8.14437 4.58807ZM7.18899 3.43168C8.61861 2.25055 10.4435 1.75 12.0834 1.75C13.7392 1.75 15.5255 2.29915 16.909 3.5031C18.3103 4.72251 19.25 6.57293 19.25 9.05982C19.25 10.4838 19.4483 11.625 19.9296 12.6556C20.4112 13.6869 21.2029 14.6647 22.4776 15.7177C22.6501 15.8602 22.75 16.0722 22.75 16.2959V17.8943C22.75 18.6439 22.1422 19.25 21.3939 19.25H2.60606C1.85778 19.25 1.25 18.6439 1.25 17.8943V16.2959C1.25 16.0895 1.33506 15.8922 1.48516 15.7505C2.78228 14.526 3.58583 13.4987 4.07254 12.4741C4.55444 11.4596 4.75 10.3964 4.75 9.05982C4.75 6.48819 5.73656 4.63164 7.18899 3.43168Z"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M8.25 21C8.25 20.5858 8.58579 20.25 9 20.25H15C15.4142 20.25 15.75 20.5858 15.75 21C15.75 21.4142 15.4142 21.75 15 21.75H9C8.58579 21.75 8.25 21.4142 8.25 21Z"/>
</svg>`;

export const locationPin = `<svg width="18" height="24" viewBox="0 0 18 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M9 1.75C4.83594 1.75 1.5 5.01752 1.5 9C1.5 11.234 2.78621 13.8853 4.42076 16.3183C6.03428 18.72 7.89544 20.777 8.86689 21.7944C8.90999 21.8395 8.9576 21.8554 9 21.8554C9.0424 21.8554 9.09001 21.8395 9.13311 21.7944C10.1046 20.777 11.9657 18.72 13.5792 16.3183C15.2138 13.8853 16.5 11.234 16.5 9C16.5 5.01752 13.1641 1.75 9 1.75ZM0 9C0 4.14592 4.05136 0.25 9 0.25C13.9486 0.25 18 4.14592 18 9C18 11.711 16.4843 14.6839 14.8243 17.1548C13.1433 19.657 11.2172 21.7837 10.218 22.8302C9.54941 23.5304 8.45059 23.5304 7.78202 22.8302C6.78275 21.7837 4.85669 19.657 3.17565 17.1548C1.51566 14.6839 0 11.711 0 9Z"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M9 4.75C6.51472 4.75 4.5 6.76472 4.5 9.25C4.5 11.7353 6.51472 13.75 9 13.75C11.4853 13.75 13.5 11.7353 13.5 9.25C13.5 6.76472 11.4853 4.75 9 4.75ZM3 9.25C3 5.93629 5.68629 3.25 9 3.25C12.3137 3.25 15 5.93629 15 9.25C15 12.5637 12.3137 15.25 9 15.25C5.68629 15.25 3 12.5637 3 9.25Z"/>
</svg>`;

export const settings = (color: string): string => {
  return `<svg width="22" height="21" viewBox="0 0 22 21" fill="${color}" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M9.14944 1.16132C9.35002 0.76017 9.81085 0.25 10.5654 0.25H11.683C12.0536 0.25 12.3454 0.429164 12.5281 0.585786C12.7194 0.749709 12.8721 0.95596 12.9747 1.16132C12.984 1.17992 12.9926 1.19889 13.0003 1.21819L13.497 2.46002C13.5025 2.47366 13.5075 2.48746 13.5122 2.50139C13.7432 3.19463 14.6279 3.60658 15.3385 3.30105L16.3149 2.81284C16.3467 2.79697 16.3795 2.78337 16.4132 2.77215C16.6318 2.69928 16.9115 2.62698 17.2144 2.66063C17.5513 2.69807 17.8269 2.85445 18.0499 3.07751L18.795 3.82261C19.3227 4.3503 19.2931 5.09083 19.0597 5.55763C19.0535 5.57004 19.047 5.58228 19.0401 5.59433L18.5459 6.45916C18.1989 7.0885 18.4864 7.8841 19.2957 8.25392L20.2553 8.61376C20.5606 8.71838 20.84 8.90242 21.0273 9.20218C21.2082 9.49151 21.25 9.80491 21.25 10.0654V11.183C21.25 11.9375 20.7398 12.3983 20.3387 12.5989C20.3152 12.6107 20.2912 12.6211 20.2666 12.6304L19.2731 13.0029C19.2687 13.0046 19.2643 13.0062 19.2598 13.0077C19.2556 13.0093 19.2513 13.0107 19.247 13.0122C18.5537 13.2432 18.1418 14.1279 18.4473 14.8385L18.9355 15.8149C18.9514 15.8467 18.965 15.8795 18.9762 15.9132C19.0491 16.1318 19.1214 16.4115 19.0877 16.7144C19.0503 17.0513 18.8939 17.3269 18.6709 17.5499L17.9258 18.295C17.3981 18.8227 16.6575 18.7931 16.1907 18.5597C16.1017 18.5152 15.9785 18.4457 15.8691 18.384C15.8124 18.3521 15.7595 18.3222 15.7168 18.2989C15.5574 18.212 15.4177 18.1444 15.2955 18.1037C15.2515 18.089 15.209 18.0703 15.1684 18.0478C14.5386 17.6978 13.7407 17.9849 13.3703 18.7957L13.0103 19.7556C12.7375 20.5477 11.9712 20.75 11.5588 20.75H10.4412C9.68666 20.75 9.22583 20.2398 9.02526 19.8387C9.01353 19.8152 9.00304 19.7912 8.99383 19.7666L8.62128 18.7731C8.61803 18.7645 8.61494 18.7558 8.61202 18.747C8.38462 18.0648 7.52436 17.655 6.8198 17.9333C6.50846 18.1301 6.14423 18.2883 5.94201 18.3762C5.88111 18.4026 5.83491 18.4227 5.80927 18.4355C5.59857 18.5409 5.32319 18.6259 5.00828 18.5865C4.68522 18.5461 4.41432 18.3867 4.19843 18.1709C4.18485 18.1573 4.17181 18.1432 4.15932 18.1286L3.42982 17.2775C2.93517 16.7606 2.95783 16.051 3.17707 15.5903L3.54661 14.728C3.85263 14.0395 3.59725 13.223 2.82821 12.7534C2.42744 12.6497 2.07767 12.5383 1.79694 12.4142C1.41364 12.3533 1.1359 12.1211 0.972661 11.8599C0.80276 11.5881 0.75 11.2857 0.75 11.0588V9.94118C0.75 9.60676 0.898082 9.29711 1.05523 9.08758C1.17529 8.92751 1.42358 8.66569 1.79127 8.59084L2.80978 8.13817C2.83182 8.12837 2.85432 8.11965 2.87721 8.11202C3.5594 7.88462 3.96919 7.02436 3.69093 6.3198C3.49408 6.00845 3.33585 5.64421 3.248 5.44199C3.22155 5.3811 3.20148 5.33491 3.18866 5.30927C3.08331 5.09857 2.99829 4.82319 3.03765 4.50828C3.07804 4.18522 3.23744 3.91432 3.45333 3.69843L4.19843 2.95333C4.40606 2.7457 4.66806 2.58535 4.94789 2.5014C5.2199 2.4198 5.58523 2.39036 5.93345 2.56447C5.94586 2.57068 5.9581 2.57723 5.97014 2.58411L6.80317 3.06013C7.54082 3.36789 8.42027 3.03358 8.74891 2.33909L9.10875 1.25956C9.11997 1.22589 9.13357 1.19306 9.14944 1.16132ZM10.5097 1.80013L10.1592 2.85155C10.1516 2.87444 10.1429 2.89694 10.1331 2.91898C9.46152 4.42997 7.64388 5.07568 6.16272 4.41738C6.13966 4.40714 6.11713 4.39573 6.09522 4.38321L5.34264 3.95316C5.3364 3.95635 5.32976 3.96002 5.32279 3.96424C5.29951 3.97833 5.27754 3.99554 5.25909 4.01399L4.56422 4.70886C4.60142 4.78839 4.64234 4.8822 4.67899 4.96622C4.70335 5.02204 4.72581 5.07354 4.74405 5.11367C4.8135 5.26646 4.88756 5.41226 4.98025 5.5513C5.00381 5.58663 5.02432 5.62391 5.04157 5.66272C5.71271 7.17279 4.9024 8.99289 3.38863 9.52238L2.30134 10.0056C2.28446 10.0131 2.26733 10.02 2.25 10.0262V10.9738C2.27798 10.9838 2.30541 10.9956 2.33214 11.0089C2.50661 11.0962 2.81615 11.2046 3.29628 11.3247C3.35995 11.3406 3.42125 11.3648 3.47861 11.3967C4.88283 12.1768 5.59317 13.8098 4.91955 15.3324L4.54884 16.1974C4.54305 16.2109 4.53687 16.2242 4.5303 16.2374C4.53147 16.235 4.53098 16.2361 4.53098 16.2361C4.53098 16.2361 4.52978 16.2401 4.52915 16.2435C4.52838 16.2477 4.52801 16.2517 4.52793 16.2552C4.53655 16.2643 4.54494 16.2736 4.5531 16.2831L5.21606 17.0566C5.29402 17.0203 5.38473 16.9807 5.46622 16.9452C5.52204 16.9208 5.57354 16.8984 5.61367 16.8801C5.76646 16.8107 5.91226 16.7366 6.0513 16.6439C6.08663 16.6204 6.12391 16.5999 6.16272 16.5826C7.68037 15.9081 9.51117 16.73 10.0303 18.2584L10.3802 19.1917C10.3859 19.2008 10.3919 19.2094 10.3982 19.2171C10.4095 19.2313 10.4192 19.24 10.4255 19.2448C10.4284 19.2469 10.4316 19.2488 10.4316 19.2488L10.4328 19.2494C10.4328 19.2494 10.4363 19.25 10.4412 19.25H11.5588L11.5611 19.2499L11.5673 19.2493C11.5725 19.2487 11.5787 19.2476 11.5853 19.246C11.5914 19.2445 11.5968 19.2429 11.6012 19.2413L11.9742 18.2465C11.9794 18.2325 11.9851 18.2188 11.9911 18.2052C12.6099 16.8128 14.2541 15.8713 15.8362 16.7037C16.0612 16.7851 16.2692 16.8916 16.4351 16.9821C16.5316 17.0347 16.6043 17.0761 16.6653 17.1108C16.7411 17.1539 16.7988 17.1867 16.8616 17.2181C16.8592 17.2169 16.8603 17.2174 16.8603 17.2174C16.8603 17.2174 16.8643 17.2186 16.8677 17.2192C16.8718 17.22 16.8756 17.2203 16.879 17.2204L17.5886 16.5108C17.5842 16.491 17.5777 16.4658 17.5681 16.4342L17.0972 15.4923C17.0921 15.4821 17.0872 15.4718 17.0826 15.4615C16.4081 13.9438 17.23 12.113 18.7584 11.5939L19.6917 11.2439C19.7008 11.2383 19.7094 11.2322 19.7171 11.226C19.7313 11.2147 19.74 11.205 19.7448 11.1987C19.7479 11.1945 19.7494 11.1914 19.7494 11.1914C19.7494 11.1914 19.75 11.1874 19.75 11.183V10.0654C19.75 10.0504 19.7496 10.0372 19.749 10.0259C19.746 10.0248 19.743 10.0236 19.7399 10.0225L18.7465 9.64996C18.7325 9.64474 18.7188 9.63911 18.7052 9.63307C17.295 9.00631 16.3473 7.32788 17.2365 5.72727L17.2409 5.71937L17.7186 4.8835L17.7192 4.88067C17.72 4.8766 17.7203 4.87272 17.7204 4.86932L17.0108 4.15974C16.991 4.16412 16.9658 4.17066 16.9342 4.18027L15.9923 4.65121C15.9821 4.65629 15.9718 4.66114 15.9615 4.66575C14.4466 5.33903 12.6197 4.52141 12.0968 2.99833L11.6225 1.8127C11.6147 1.79924 11.6051 1.78487 11.5942 1.77066C11.5884 1.763 11.5826 1.75611 11.5772 1.75H10.5654C10.5571 1.75 10.557 1.75058 10.557 1.75058C10.557 1.75058 10.5555 1.75085 10.5497 1.75523C10.5434 1.75997 10.5337 1.76868 10.5223 1.78286C10.518 1.78826 10.5138 1.79404 10.5097 1.80013ZM2.25836 9.98374L2.25722 9.98503L2.25836 9.98374Z" />
<path fill-rule="evenodd" clip-rule="evenodd" d="M11 7.75C9.48122 7.75 8.25 8.98122 8.25 10.5C8.25 12.0188 9.48122 13.25 11 13.25C12.5188 13.25 13.75 12.0188 13.75 10.5C13.75 8.98122 12.5188 7.75 11 7.75ZM6.75 10.5C6.75 8.15279 8.65279 6.25 11 6.25C13.3472 6.25 15.25 8.15279 15.25 10.5C15.25 12.8472 13.3472 14.75 11 14.75C8.65279 14.75 6.75 12.8472 6.75 10.5Z"/>
</svg>`;
};

export const user = (color: string): string => {
  return `<svg width="24" height="24" viewBox="0 0 24 24" fill="${color}" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1037_17384)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M11.9999 0.75C8.98997 0.75 6.54993 3.19005 6.54993 6.2C6.54993 8.27943 7.71451 10.0869 9.42703 11.0057C7.85242 11.3251 6.30974 11.9554 4.96726 12.9608C2.8779 14.5257 1.32454 16.9626 0.879783 20.4071C0.671834 22.0176 2.01272 23.25 3.49997 23.25H20.5C21.9872 23.25 23.3281 22.0176 23.1202 20.4071C22.6754 16.9626 21.122 14.5257 19.0327 12.9608C17.6902 11.9554 16.1475 11.3251 14.5728 11.0056C16.2854 10.0868 17.4499 8.27942 17.4499 6.2C17.4499 3.19005 15.0099 0.75 11.9999 0.75ZM8.04993 6.2C8.04993 4.01848 9.8184 2.25 11.9999 2.25C14.1815 2.25 15.9499 4.01848 15.9499 6.2C15.9499 8.38152 14.1815 10.15 11.9999 10.15C9.8184 10.15 8.04993 8.38152 8.04993 6.2ZM2.36743 20.5992C2.76298 17.5359 4.11775 15.4712 5.86647 14.1614C7.63122 12.8397 9.84843 12.25 12 12.25C14.1515 12.25 16.3687 12.8397 18.1335 14.1614C19.8822 15.4712 21.237 17.5359 21.6325 20.5992C21.7075 21.1797 21.2219 21.75 20.5 21.75H3.49997C2.77808 21.75 2.29248 21.1797 2.36743 20.5992Z" />
</g>
<defs>
<clipPath id="clip0_1037_17384">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>
</svg>`;
};

export const wallet = (color: string): string => {
  return `<svg width="24" height="19" viewBox="0 0 24 19" fill="${color}" xmlns="http://www.w3.org/2000/svg">
<path d="M12 10.3999C12.6904 10.3999 13.25 9.8403 13.25 9.14994C13.25 8.45959 12.6904 7.89994 12 7.89994C11.3096 7.89994 10.75 8.45959 10.75 9.14994C10.75 9.8403 11.3096 10.3999 12 10.3999Z"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M4.24112 1.78851C7.07835 1.78851 9.91544 1.78255 12.7516 1.77658L12.7545 1.77657C15.5918 1.77061 18.4283 1.76464 21.2646 1.76465C21.6789 1.76465 22.0146 1.42886 22.0146 1.01465C22.0146 0.600437 21.6789 0.264648 21.2646 0.264648C18.4265 0.264641 15.5884 0.27061 12.7513 0.276578L12.7492 0.276582C9.91253 0.28255 7.07676 0.288515 4.24112 0.288515C2.97004 0.288515 1.98523 1.33375 1.98523 2.58852C1.98523 2.85849 2.03021 3.11547 2.11293 3.35289H1.5C1.08579 3.35289 0.75 3.68867 0.75 4.10289V7.24574C0.75 7.88805 0.944772 8.47778 1.25 8.98352V16.4999C1.25 17.7426 2.25736 18.7499 3.5 18.7499H20.5C21.7426 18.7499 22.75 17.7426 22.75 16.4999V9.06575C23.0613 8.54061 23.25 7.9199 23.25 7.24675V4.10289C23.25 3.68867 22.9142 3.35289 22.5 3.35289H4.24304L4.24112 3.35288C3.81627 3.35288 3.48523 3.03296 3.48523 2.58852C3.48523 2.12106 3.83899 1.78851 4.24112 1.78851ZM20.8851 10.597C21.0086 10.5572 21.1305 10.5087 21.25 10.4522V16.4999C21.25 16.9142 20.9142 17.2499 20.5 17.2499H3.5C3.08579 17.2499 2.75 16.9142 2.75 16.4999V10.4262C2.85458 10.4835 2.95942 10.5341 3.06346 10.5776C3.07743 10.5835 3.09157 10.5889 3.10586 10.5939L11.7529 13.611C11.9129 13.6668 12.0871 13.6668 12.2471 13.611L20.8851 10.597ZM2.25 4.85289V7.24574C2.25 8.08448 2.92981 8.88624 3.62332 9.18574L12 12.1085L20.4 9.17761L20.4197 9.17105C21.0885 8.95824 21.75 8.20138 21.75 7.24675V4.85289H2.25Z"/>
</svg>`;
};

export const edit = `<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_10654_454)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M5.54393 14.6513L15.2351 4.92482C15.7767 4.38342 15.8379 3.67716 15.6606 3.04363C15.4867 2.42246 15.0779 1.82197 14.5871 1.34732C14.096 0.872374 13.4794 0.481461 12.8453 0.324226C12.1994 0.164064 11.489 0.241886 10.9439 0.786497L1.46658 10.5512C1.40492 10.6148 1.36123 10.6936 1.33998 10.7795L0.49441 14.201C0.299699 14.9888 0.997291 15.7085 1.79083 15.5383L5.29453 14.7873C5.38906 14.767 5.47569 14.7198 5.54393 14.6513ZM10.1329 3.05791L11.6531 1.49157C11.8835 1.26309 12.1977 1.19392 12.6047 1.29483C13.0249 1.39903 13.4915 1.67886 13.8919 2.06615C14.2927 2.45374 14.5839 2.90697 14.6976 3.31319C14.8078 3.70692 14.744 4.00191 14.5283 4.21737L14.5275 4.21818L13.0252 5.72598L10.1329 3.05791ZM9.43618 3.77574L2.27834 11.1507L1.4652 14.4409C1.44778 14.5114 1.51021 14.5758 1.58122 14.5606L4.94009 13.8405L12.3188 6.4349L9.43618 3.77574Z"/>
</g>
<defs>
<clipPath id="clip0_10654_454">
<rect width="16" height="16" fill="white"/>
</clipPath>
</defs>
</svg>
`;
export const receipt = `<svg width="19" height="24" viewBox="0 0 19 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
<path d="M2.76842 8.61113C2.76842 8.19692 3.10421 7.86113 3.51842 7.86113H9.81473C10.2289 7.86113 10.5647 8.19692 10.5647 8.61113C10.5647 9.02534 10.2289 9.36113 9.81473 9.36113H3.51842C3.10421 9.36113 2.76842 9.02534 2.76842 8.61113Z" />
<path d="M3.51842 4.08337C3.10421 4.08337 2.76842 4.41915 2.76842 4.83337C2.76842 5.24758 3.10421 5.58337 3.51842 5.58337H6.03695C6.45116 5.58337 6.78695 5.24758 6.78695 4.83337C6.78695 4.41915 6.45116 4.08337 6.03695 4.08337H3.51842Z" />
<path d="M2.76842 12.3889C2.76842 11.9747 3.10421 11.6389 3.51842 11.6389H6.03695C6.45116 11.6389 6.78695 11.9747 6.78695 12.3889C6.78695 12.8031 6.45116 13.1389 6.03695 13.1389H3.51842C3.10421 13.1389 2.76842 12.8031 2.76842 12.3889Z" />
<path d="M11.0741 15.4167C10.6599 15.4167 10.3241 15.7525 10.3241 16.1667C10.3241 16.5809 10.6599 16.9167 11.0741 16.9167H12.3333C12.7475 16.9167 13.0833 16.5809 13.0833 16.1667C13.0833 15.7525 12.7475 15.4167 12.3333 15.4167H11.0741Z" />
<path d="M10.3241 12.3889C10.3241 11.9747 10.6599 11.6389 11.0741 11.6389H12.3333C12.7475 11.6389 13.0833 11.9747 13.0833 12.3889C13.0833 12.8031 12.7475 13.1389 12.3333 13.1389H11.0741C10.6599 13.1389 10.3241 12.8031 10.3241 12.3889Z" />
<path d="M3.51842 15.4167C3.10421 15.4167 2.76842 15.7525 2.76842 16.1667C2.76842 16.5809 3.10421 16.9167 3.51842 16.9167H6.03695C6.45116 16.9167 6.78695 16.5809 6.78695 16.1667C6.78695 15.7525 6.45116 15.4167 6.03695 15.4167H3.51842Z" />
<path fill-rule="evenodd" clip-rule="evenodd" d="M16.8909 0.255519C16.8609 0.251865 16.8306 0.25 16.8 0.25H2.88889C1.43147 0.25 0.25 1.43147 0.25 2.88889V23C0.25 23.2951 0.423056 23.5628 0.692156 23.6839C0.961257 23.805 1.27638 23.7571 1.49729 23.5614L3.13933 22.107L4.11846 23.4433C4.24938 23.622 4.45258 23.7336 4.67359 23.7483C4.8946 23.7631 5.11081 23.6793 5.26427 23.5196L6.60982 22.1191L7.76608 23.4847C7.90859 23.6529 8.11793 23.75 8.33846 23.75C8.55898 23.75 8.76833 23.6529 8.91083 23.4847L10.0319 22.1606L11.1531 23.4847C11.2956 23.6529 11.5049 23.75 11.7254 23.75C11.946 23.75 12.1553 23.6529 12.2978 23.4847L13.4728 22.097L14.9594 23.5384C15.1757 23.7481 15.4964 23.8081 15.7739 23.6907C16.0513 23.5733 16.2315 23.3012 16.2315 23V9.30557H18.0001C18.4143 9.30557 18.7501 8.96978 18.7501 8.55557V2.25926C18.7501 1.20005 17.9305 0.332271 16.8909 0.255519ZM2.88889 1.75C2.2599 1.75 1.75 2.2599 1.75 2.88889V21.3338L2.7607 20.4386C2.91862 20.2987 3.12816 20.2318 3.33792 20.2543C3.54768 20.2768 3.73828 20.3865 3.86297 20.5567L4.80093 21.8368L6.10413 20.4804C6.25122 20.3273 6.45631 20.2437 6.66851 20.2504C6.88071 20.257 7.08015 20.3533 7.21734 20.5153L8.33846 21.8394L9.45957 20.5153C9.60208 20.3471 9.81142 20.25 10.0319 20.25C10.2525 20.25 10.4618 20.3471 10.6043 20.5153L11.7254 21.8394L12.8466 20.5153C12.9808 20.3568 13.1747 20.2611 13.3821 20.2509C13.5896 20.2407 13.7919 20.317 13.941 20.4616L14.7315 21.2281V4.57719C14.7315 4.57139 14.7315 4.5656 14.7316 4.55981V2.25926C14.7316 2.08331 14.7542 1.91264 14.7967 1.75H2.88889ZM16.2316 2.25926C16.2316 1.978 16.4596 1.75 16.7408 1.75C17.0221 1.75 17.2501 1.978 17.2501 2.25926V7.80557H16.2316V2.25926Z" />
</svg>`;

export const scanPay = `<svg width="18" height="24" viewBox="0 0 18 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M3.78528 0.5C2.16423 0.5 0.850098 1.81413 0.850098 3.43518V20.5648C0.850098 22.1859 2.16423 23.5 3.78528 23.5H14.2149C15.836 23.5 17.1501 22.1859 17.1501 20.5648V3.43519C17.1501 1.81413 15.836 0.5 14.2149 0.5H3.78528ZM2.3501 3.43518C2.3501 2.64255 2.99265 2 3.78528 2H6.60873C6.7741 2.32425 7.11118 2.5463 7.50012 2.5463H10.5001C10.8891 2.5463 11.2261 2.32425 11.3915 2H14.2149C15.0075 2 15.6501 2.64256 15.6501 3.43519V20.5648C15.6501 21.3574 15.0075 22 14.2149 22H10.6853C10.5683 21.7381 10.3055 21.5555 10.0001 21.5555H8.00012C7.69472 21.5555 7.43196 21.7381 7.31498 22H3.78528C2.99265 22 2.3501 21.3574 2.3501 20.5648V3.43518Z" />
<path fill-rule="evenodd" clip-rule="evenodd" d="M15.0391 18C15.0391 18.6903 14.4795 19.25 13.7891 19.25H12.4317C12.0175 19.25 11.6817 18.9142 11.6817 18.5C11.6817 18.0858 12.0175 17.75 12.4317 17.75H13.5391V16.6426C13.5391 16.2284 13.8749 15.8926 14.2891 15.8926C14.7033 15.8926 15.0391 16.2284 15.0391 16.6426V18Z" />
<path fill-rule="evenodd" clip-rule="evenodd" d="M4.30012 19.25C3.60977 19.25 3.05012 18.6903 3.05012 18V16.6426C3.05012 16.2284 3.38591 15.8926 3.80012 15.8926C4.21434 15.8926 4.55012 16.2284 4.55012 16.6426V17.75H5.65753C6.07174 17.75 6.40753 18.0858 6.40753 18.5C6.40753 18.9142 6.07174 19.25 5.65753 19.25H4.30012Z" />
<path fill-rule="evenodd" clip-rule="evenodd" d="M13.7891 4.24999C14.4795 4.24999 15.0391 4.80964 15.0391 5.49999V6.87301C15.0391 7.28722 14.7033 7.62301 14.2891 7.62301C13.8749 7.62301 13.5391 7.28722 13.5391 6.87301V5.74999H12.4161C12.0019 5.74999 11.6661 5.41421 11.6661 4.99999C11.6661 4.58578 12.0019 4.24999 12.4161 4.24999H13.7891Z" />
<path fill-rule="evenodd" clip-rule="evenodd" d="M3.05012 5.49999C3.05012 4.80964 3.60977 4.24999 4.30012 4.24999H5.65753C6.07174 4.24999 6.40753 4.58578 6.40753 4.99999C6.40753 5.41421 6.07174 5.74999 5.65753 5.74999H4.55012V6.8574C4.55012 7.27161 4.21434 7.6074 3.80012 7.6074C3.38591 7.6074 3.05012 7.27161 3.05012 6.8574V5.49999Z" />
<path d="M9.18795 17.0616C8.99586 17.0616 8.83701 16.9951 8.71141 16.8621C8.58581 16.7365 8.52301 16.5813 8.52301 16.3966V15.4879L8.82223 15.7538C8.31244 15.7391 7.86915 15.6763 7.49235 15.5654C7.11555 15.4546 6.80155 15.3327 6.55035 15.1997C6.3878 15.1184 6.27329 15.0076 6.20679 14.8672C6.14769 14.7269 6.12552 14.5828 6.1403 14.435C6.15507 14.2799 6.20679 14.1469 6.29545 14.0361C6.38411 13.9178 6.49863 13.8403 6.639 13.8033C6.78677 13.7664 6.953 13.7959 7.13771 13.892C7.31503 13.988 7.56254 14.0878 7.88023 14.1912C8.19793 14.2873 8.58581 14.3353 9.04388 14.3353C9.38374 14.3353 9.65711 14.2983 9.86398 14.2245C10.0708 14.1432 10.2186 14.0398 10.3073 13.9142C10.4033 13.7886 10.4513 13.6445 10.4513 13.4819C10.4513 13.3489 10.4181 13.2307 10.3516 13.1273C10.2925 13.0239 10.1854 12.9352 10.0302 12.8613C9.88245 12.7801 9.68296 12.7136 9.43176 12.6618L8.2127 12.3959C7.53298 12.2481 7.02689 11.9969 6.69442 11.6423C6.36194 11.2802 6.19571 10.8148 6.19571 10.2459C6.19571 9.80257 6.30284 9.40729 6.5171 9.06004C6.73875 8.71279 7.04166 8.43204 7.42585 8.21778C7.81743 8.00352 8.2755 7.87422 8.80007 7.82989L8.52301 8.00721V7.16495C8.52301 6.98024 8.58581 6.82509 8.71141 6.69948C8.83701 6.56649 8.99586 6.5 9.18795 6.5C9.37266 6.5 9.52781 6.56649 9.65341 6.69948C9.77901 6.82509 9.84181 6.98024 9.84181 7.16495V8.00721L9.56475 7.80773C9.86767 7.8225 10.1928 7.88161 10.54 7.98505C10.8946 8.0811 11.2013 8.22147 11.4598 8.40618C11.6002 8.49484 11.6963 8.60566 11.748 8.73865C11.8071 8.87164 11.8219 9.00463 11.7923 9.13762C11.7701 9.27061 11.7147 9.38882 11.6261 9.49226C11.5374 9.58831 11.4192 9.65111 11.2714 9.68066C11.1311 9.70283 10.9648 9.66588 10.7727 9.56984C10.5732 9.47379 10.3553 9.39252 10.1189 9.32602C9.88245 9.25953 9.59061 9.22628 9.24336 9.22628C8.80746 9.22628 8.4676 9.31494 8.22378 9.49226C7.98736 9.66958 7.86915 9.89862 7.86915 10.1794C7.86915 10.3862 7.93934 10.5525 8.07971 10.6781C8.22748 10.8037 8.48237 10.9071 8.8444 10.9884L10.0745 11.2544C10.7764 11.4095 11.2936 11.6607 11.6261 12.008C11.9659 12.3552 12.1359 12.8059 12.1359 13.36C12.1359 13.7959 12.0287 14.1838 11.8145 14.5237C11.6002 14.8562 11.3047 15.1258 10.9279 15.3327C10.5511 15.5396 10.1115 15.6726 9.60908 15.7317L9.84181 15.4768V16.3966C9.84181 16.5813 9.77901 16.7365 9.65341 16.8621C9.5352 16.9951 9.38005 17.0616 9.18795 17.0616Z" />
</svg>`;

export const accountSecurity = `
<svg width="22" height="23" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M11.609.062 11.607.06V.059a3.557 3.557 0 0 0 .408.415 5.1 5.1 0 0 0 1.451.863c1.325.526 3.505.817 6.765-.437a.75.75 0 0 1 1.019.7v9.9c0 3.963-2.625 6.66-5.08 8.316a20.22 20.22 0 0 1-4.922 2.393l-.024.007-.007.002h-.003L11 21.5c-.213.72-.214.719-.214.719h-.003l-.007-.003-.024-.008a8.992 8.992 0 0 1-.408-.135 20.222 20.222 0 0 1-4.513-2.257C3.376 18.16.75 15.463.75 11.5V1.6A.75.75 0 0 1 1.77.9c3.26 1.254 5.438.963 6.764.437A5.089 5.089 0 0 0 9.986.473a3.557 3.557 0 0 0 .408-.414h-.001l-.001.002M11 21.501l-.214.718c.14.041.288.041.427 0L11 21.5Zm0-.789.16-.055a18.718 18.718 0 0 0 4.17-2.085c2.295-1.548 4.42-3.85 4.42-7.072V2.657c-3.043.96-5.287.689-6.837.075A6.587 6.587 0 0 1 11 1.579l-.034.03a6.586 6.586 0 0 1-1.88 1.123c-1.549.614-3.793.885-6.836-.075V11.5c0 3.222 2.124 5.524 4.42 7.072a18.718 18.718 0 0 0 4.33 2.14ZM11.606.06l.003.003-.003-.003Z"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M16.96 6.408a.75.75 0 0 1 .133 1.052l-6.21 8a.75.75 0 0 1-1.09.1l-3.791-3.368a.75.75 0 1 1 .996-1.121l3.192 2.836 5.718-7.367a.75.75 0 0 1 1.052-.132Z"/>
</svg>`;

export const refresh = `
<svg width="24" height="24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
  <path d="M20.009 10.734C19.75 1.99 7.849.266 4.345 7.852a.75.75 0 1 1-1.36-.628C6.943-1.35 20.27.034 21.441 9.802l.835-1.469a.75.75 0 1 1 1.304.741l-2.305 4.058a.75.75 0 0 1-1.247.087l-2.847-3.697a.75.75 0 1 1 1.188-.916l1.639 2.128ZM3.97 10.78a.75.75 0 0 0-1.246.088L.42 14.926a.75.75 0 1 0 1.304.74l.835-1.469c1.172 9.768 14.498 11.153 18.458 2.579a.75.75 0 1 0-1.361-.628c-3.504 7.586-15.406 5.86-15.664-2.882l1.639 2.127a.75.75 0 1 0 1.188-.915l-2.847-3.697Z"/>
</svg>`;

export const retry = `<svg width="25" height="25" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#a)"><path d="M12.102 2.174a10.428 10.428 0 0 1 9.503 7.793l1.248-2.677a.75.75 0 0 1 1.36.633l-2.209 4.738a.75.75 0 0 1-1.177.244l-4.07-3.613a.75.75 0 0 1 .997-1.121l2.384 2.117a8.929 8.929 0 1 0-1.497 7.671.75.75 0 1 1 1.198.903 10.429 10.429 0 1 1-7.737-16.688Z" fill="#fff"/></g><defs><clipPath id="a"><path fill="#fff" transform="translate(.5 .5)" d="M0 0h24v24H0z"/></clipPath></defs></svg>`;

export const help = (color: string): string => {
  return `<svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Help">
<g id="Icon">
<path d="M11.3731 14.142V14.0341C11.3806 13.3301 11.4507 12.77 11.5831 12.3536C11.7194 11.9373 11.9124 11.6005 12.1622 11.3431C12.412 11.0857 12.7129 10.8511 13.0649 10.6391C13.292 10.4953 13.4964 10.3344 13.678 10.1566C13.8597 9.97867 14.0035 9.77429 14.1095 9.54341C14.2155 9.31254 14.2685 9.05707 14.2685 8.77699C14.2685 8.44014 14.189 8.14871 14.03 7.9027C13.871 7.65669 13.6591 7.46745 13.3942 7.33498C13.133 7.19873 12.8416 7.1306 12.5199 7.1306C12.2284 7.1306 11.9503 7.19116 11.6853 7.31227C11.4204 7.43338 11.2009 7.62262 11.0268 7.87999C10.8527 8.13357 10.7524 8.46096 10.7259 8.86215C10.7259 8.86215 10.5338 9.57227 9.81434 9.57227C9.09493 9.57227 9 8.86215 9 8.86215C9.0265 8.18088 9.19871 7.60559 9.51663 7.13628C9.83455 6.66318 10.2547 6.30551 10.777 6.06328C11.3031 5.82106 11.884 5.69995 12.5199 5.69995C13.2163 5.69995 13.8256 5.83052 14.3479 6.09167C14.8702 6.34904 15.2752 6.71049 15.5629 7.17602C15.8543 7.63776 16 8.1771 16 8.79402C16 9.21792 15.9338 9.60019 15.8013 9.94082C15.6688 10.2777 15.4796 10.5786 15.2336 10.8435C14.9914 11.1084 14.6999 11.3431 14.3593 11.5475C14.0376 11.7481 13.7764 11.9562 13.5758 12.172C13.379 12.3877 13.2352 12.6432 13.1444 12.9384C13.0535 13.2336 13.0043 13.5988 12.9968 14.0341V14.142C12.9968 14.142 12.8519 14.848 12.1325 14.848C11.413 14.848 11.3731 14.142 11.3731 14.142Z" fill="${color}"/>
<path d="M12.1963 16.1056C11.9277 16.1056 11.7065 16.1946 11.5327 16.3726C11.3431 16.5667 11.2483 16.8013 11.2483 17.0764V17.3694C11.2483 17.6283 11.3431 17.8548 11.5327 18.049C11.7065 18.2431 11.9277 18.3402 12.1963 18.3402C12.465 18.3402 12.6941 18.2431 12.8837 18.049C13.0575 17.8548 13.1444 17.6283 13.1444 17.3694V17.0764C13.1444 16.8013 13.0575 16.5667 12.8837 16.3726C12.6941 16.1946 12.465 16.1056 12.1963 16.1056Z" fill="${color}"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M12.303 3.25C7.36176 3.25 3.35606 7.25569 3.35606 12.197C3.35606 13.6465 3.7002 15.0135 4.31055 16.2227C4.4087 16.4171 4.40419 16.6098 4.40287 16.666L4.40282 16.668C4.40075 16.7576 4.38938 16.8476 4.37676 16.926C4.35109 17.0853 4.30705 17.2752 4.2549 17.4748C4.1495 17.8784 3.99287 18.3898 3.82226 18.9173C3.60866 19.5778 3.36733 20.2812 3.16389 20.8622C3.81151 20.72 4.61228 20.5501 5.36906 20.4055C5.95862 20.2928 6.53357 20.1932 6.99276 20.1347C7.22068 20.1056 7.43587 20.0846 7.61701 20.0793C7.70703 20.0766 7.80374 20.0772 7.89726 20.0863C7.97247 20.0935 8.12452 20.1123 8.27735 20.1895C9.48655 20.7998 10.8535 21.1439 12.303 21.1439C17.2443 21.1439 21.25 17.1382 21.25 12.197C21.25 7.25569 17.2443 3.25 12.303 3.25ZM1.85606 12.197C1.85606 6.42727 6.53333 1.75 12.303 1.75C18.0727 1.75 22.75 6.42727 22.75 12.197C22.75 17.9667 18.0727 22.6439 12.303 22.6439C10.6528 22.6439 9.08995 22.2608 7.70055 21.5779C7.68942 21.5779 7.67643 21.5782 7.66146 21.5786C7.55088 21.5819 7.3905 21.5961 7.18236 21.6226C6.76943 21.6753 6.23029 21.768 5.65059 21.8788C4.49459 22.0997 3.22527 22.3828 2.58479 22.5288C1.82582 22.7019 1.17744 21.9687 1.43971 21.2371C1.65295 20.6423 2.06014 19.4913 2.39505 18.4557C2.56307 17.9363 2.70919 17.4572 2.80359 17.0958C2.84426 16.94 2.87214 16.8181 2.8884 16.7302C2.22664 15.3582 1.85606 13.8199 1.85606 12.197ZM7.75301 21.5793C7.76107 21.5801 7.75932 21.5805 7.74997 21.5791L7.75301 21.5793Z" fill="${color}"/>
</g>
</g>
</svg>
`;
};

export const language = (color: string): string => {
  return `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_18447_16879)">
<path d="M11.99 2.00003C6.47 2.00003 2 6.48003 2 12C2 17.52 6.47 22 11.99 22C17.52 22 22 17.52 22 12C22 6.48003 17.52 2.00003 11.99 2.00003ZM18.92 8.00003H15.97C15.65 6.75003 15.19 5.55003 14.59 4.44003C16.43 5.07003 17.96 6.35003 18.92 8.00003ZM12 4.04003C12.83 5.24003 13.48 6.57003 13.91 8.00003H10.09C10.52 6.57003 11.17 5.24003 12 4.04003ZM4.26 14C4.1 13.36 4 12.69 4 12C4 11.31 4.1 10.64 4.26 10H7.64C7.56 10.66 7.5 11.32 7.5 12C7.5 12.68 7.56 13.34 7.64 14H4.26ZM5.08 16H8.03C8.35 17.25 8.81 18.45 9.41 19.56C7.57 18.93 6.04 17.66 5.08 16ZM8.03 8.00003H5.08C6.04 6.34003 7.57 5.07003 9.41 4.44003C8.81 5.55003 8.35 6.75003 8.03 8.00003ZM12 19.96C11.17 18.76 10.52 17.43 10.09 16H13.91C13.48 17.43 12.83 18.76 12 19.96ZM14.34 14H9.66C9.57 13.34 9.5 12.68 9.5 12C9.5 11.32 9.57 10.65 9.66 10H14.34C14.43 10.65 14.5 11.32 14.5 12C14.5 12.68 14.43 13.34 14.34 14ZM14.59 19.56C15.19 18.45 15.65 17.25 15.97 16H18.92C17.96 17.65 16.43 18.93 14.59 19.56ZM16.36 14C16.44 13.34 16.5 12.68 16.5 12C16.5 11.32 16.44 10.66 16.36 10H19.74C19.9 10.64 20 11.31 20 12C20 12.69 19.9 13.36 19.74 14H16.36Z" fill="#004488"/>
</g>
<defs>
<clipPath id="clip0_18447_16879">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>
</svg>
`;
};

export const learning = (color: string): string => {
  return `
<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Learning">
<path d="M17.5 13.75C17.9142 13.75 18.25 14.0858 18.25 14.5V16.75H20.5C20.9142 16.75 21.25 17.0858 21.25 17.5C21.25 17.9142 20.9142 18.25 20.5 18.25H18.25V20.5C18.25 20.9142 17.9142 21.25 17.5 21.25C17.0858 21.25 16.75 20.9142 16.75 20.5V18.25H14.5C14.0858 18.25 13.75 17.9142 13.75 17.5C13.75 17.0858 14.0858 16.75 14.5 16.75H16.75V14.5C16.75 14.0858 17.0858 13.75 17.5 13.75Z" fill="${color}"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M5.75 6.2C5.75 3.19005 8.19005 0.75 11.2 0.75C14.21 0.75 16.65 3.19005 16.65 6.2C16.65 8.29402 15.469 10.1122 13.7368 11.0249C14.3284 11.1585 14.9146 11.3429 15.485 11.5819C16.1172 11.3667 16.795 11.25 17.5 11.25C20.9518 11.25 23.75 14.0482 23.75 17.5C23.75 20.9518 20.9518 23.75 17.5 23.75C16.629 23.75 15.7997 23.5718 15.0464 23.25H3.49997C2.0101 23.25 0.677729 22.0178 0.870204 20.4131C1.28136 16.9853 2.71722 14.5493 4.66644 12.9772C5.8795 11.9988 7.26951 11.3724 8.69371 11.0408C6.94485 10.1335 5.75 8.30628 5.75 6.2ZM11.25 12.25C12.0599 12.25 12.8804 12.3479 13.6788 12.5538C12.2013 13.6969 11.25 15.4874 11.25 17.5C11.25 19.1413 11.8826 20.6348 12.9174 21.75H3.49997C2.78071 21.75 2.28891 21.1805 2.35953 20.5918C2.7289 17.5123 3.99349 15.447 5.60813 14.1448C7.23494 12.8327 9.27397 12.25 11.25 12.25ZM11.2 2.25C9.01847 2.25 7.25 4.01848 7.25 6.2C7.25 8.38152 9.01847 10.15 11.2 10.15C13.3815 10.15 15.15 8.38152 15.15 6.2C15.15 4.01848 13.3815 2.25 11.2 2.25ZM17.5 12.75C14.8766 12.75 12.75 14.8766 12.75 17.5C12.75 20.1234 14.8766 22.25 17.5 22.25C20.1234 22.25 22.25 20.1234 22.25 17.5C22.25 14.8766 20.1234 12.75 17.5 12.75Z" fill="${color}"/>
</g>
<defs>
<clipPath id="clip0_17327_63288">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>
</svg>
`;
};

export const learningList = (color: string): string => {
  return `
<svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M9.25 9C9.25 8.58579 9.58579 8.25 10 8.25H17C17.4142 8.25 17.75 8.58579 17.75 9C17.75 9.41421 17.4142 9.75 17 9.75H10C9.58579 9.75 9.25 9.41421 9.25 9Z" fill="${color}"/>
  <path d="M9.25 15.5C9.25 15.0858 9.58579 14.75 10 14.75H17C17.4142 14.75 17.75 15.0858 17.75 15.5C17.75 15.9142 17.4142 16.25 17 16.25H10C9.58579 16.25 9.25 15.9142 9.25 15.5Z" fill="${color}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M3.25 4.5C3.25 2.98122 4.48122 1.75 6 1.75H19.5C21.0188 1.75 22.25 2.98122 22.25 4.5V20.5C22.25 22.0188 21.0188 23.25 19.5 23.25H6C4.48122 23.25 3.25 22.0188 3.25 20.5V19.25H1.25C0.835786 19.25 0.5 18.9142 0.5 18.5C0.5 18.0858 0.835786 17.75 1.25 17.75H3.25V13.25H1.25C0.835786 13.25 0.5 12.9142 0.5 12.5C0.5 12.0858 0.835786 11.75 1.25 11.75H3.25V7.25H1.25C0.835786 7.25 0.5 6.91421 0.5 6.5C0.5 6.08579 0.835786 5.75 1.25 5.75H3.25V4.5ZM4.75 20.5V19.25H6.75C7.16421 19.25 7.5 18.9142 7.5 18.5C7.5 18.0858 7.16421 17.75 6.75 17.75H4.75V13.25H6.75C7.16421 13.25 7.5 12.9142 7.5 12.5C7.5 12.0858 7.16421 11.75 6.75 11.75H4.75V7.25H6.75C7.16421 7.25 7.5 6.91421 7.5 6.5C7.5 6.08579 7.16421 5.75 6.75 5.75H4.75V4.5C4.75 3.80964 5.30964 3.25 6 3.25H19.5C20.1904 3.25 20.75 3.80964 20.75 4.5V20.5C20.75 21.1904 20.1904 21.75 19.5 21.75H6C5.30964 21.75 4.75 21.1904 4.75 20.5Z" fill="${color}"/>
</svg>`;
};

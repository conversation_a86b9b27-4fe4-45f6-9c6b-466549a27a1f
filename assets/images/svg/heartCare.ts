export const HeartCare = `<svg width="120" height="120" viewBox="0 0 127 127" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_15522_2935" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="-1" y="0" width="128" height="127">
<circle cx="63.498" cy="63.5" r="63.5" fill="#C4C4C4"/>
</mask>
<g mask="url(#mask0_15522_2935)">
<circle cx="63.498" cy="63.5" r="63.5" fill="#FFF2F2"/>
<path d="M108.989 63.0112C110.953 49.128 105.062 31.072 83.6282 31.072C77.9728 31.072 67.4573 35.8634 65.7587 44.5036C65.5427 45.5934 64.4823 45.5542 64.1485 44.5036C61.8117 36.914 53.9669 31.0916 43.0292 31.0916C35.361 31.0916 19.2 38.3867 20.3389 58.5831C21.8117 84.5822 48.3606 103.679 60.9674 112.486C61.3786 112.822 61.887 113.017 62.4174 113.042C62.9479 113.066 63.4722 112.92 63.9129 112.624C70.3439 109.158 105.229 89.7761 108.989 63.0112Z" fill="#E84646"/>
<path d="M10.7961 27.2142C10.7961 27.2142 9.15647 44.6811 26.9572 51.1317C30.2623 52.2508 33.8407 52.2715 37.1585 51.1906C41.3804 49.659 45.1801 46.1735 48.3514 44.1116C49.7653 43.9644 51.297 47.224 47.5267 50.1499C44.1197 52.9285 45.7987 53.5667 49.402 54.2344C53.4865 55.0002 62.0874 55.8544 61.2528 58.4464C60.6244 60.4101 48.2434 56.6792 47.8703 58.3974C47.4972 59.6836 57.6887 62.7568 61.0957 63.827C63.0594 64.2393 63.5012 67.1063 60.7717 66.5564C58.0422 66.0066 47.1831 62.2462 46.7903 63.7877C46.3976 65.3291 54.164 67.9507 56.7364 69.0209C58.7001 69.8162 58.9946 71.9664 56.0491 71.5835C53.1036 71.2005 44.915 66.8313 44.5026 68.1961C44.2179 69.1779 48.0961 71.0336 51.0318 72.9286C52.2886 73.7435 52.7501 76.365 48.7736 74.7253C44.3848 72.9089 33.7712 66.6055 27.075 64.1509C20.3789 61.6963 1.24283 56.9246 -2.27215 41.1759L10.7961 27.2142Z" fill="#F9C0A4"/>
<path d="M120.192 29.482C120.192 29.482 114.765 58.2882 96.9645 64.7389C93.6582 65.8541 90.0802 65.8714 86.7633 64.788C82.5414 63.2662 78.7416 59.7708 75.5703 57.7089C74.1565 57.5617 72.6248 60.8312 76.3951 63.757C79.8021 66.5356 78.1231 67.1641 74.5198 67.8415C70.4353 68.5975 61.8344 69.4615 62.669 72.0536C63.2974 74.0173 75.6784 70.2765 76.0515 72.0536C76.4246 73.3398 66.233 76.413 62.8261 77.4734C60.8624 77.8955 60.4206 80.7625 63.1501 80.2127C65.8796 79.6628 76.7387 75.8926 77.1315 77.434C77.5242 78.9755 69.7578 81.6069 67.1854 82.6673C65.2218 83.4626 64.9272 85.6128 67.8727 85.2298C70.8182 84.8469 79.0067 80.4876 79.4191 81.8426C79.7038 82.8244 75.8256 84.6898 72.8899 86.5848C71.6332 87.3997 71.1717 90.0212 75.1481 88.3717C79.5369 86.5652 90.1506 80.2519 96.8467 77.8071C103.543 75.3624 125.749 67.9191 129.264 52.1606L120.192 29.482Z" fill="#F9C0A4"/>
<path d="M92.0921 65.594C92.0921 65.594 92.6456 63.6991 93.6176 65.2504C94.7028 67.0823 95.5663 69.0368 96.19 71.0727C97.1719 74.4109 97.6137 78.083 97.0246 78.623C96.4355 79.163 95.2453 78.2409 95.2453 78.2409L94.2557 78.8292C94.2557 78.8292 97.3485 80.7241 99.7835 79.1728C101.443 78.1223 101.138 73.4487 99.7835 69.3544C98.6249 65.7413 96.4453 62.5994 94.295 62.9823C93.5063 63.1274 92.7563 63.4344 92.0921 63.8839C91.428 64.3334 90.9731 65.594 90.9731 65.594H92.0921Z" fill="#6EB749"/>
<path d="M96.7432 65.3685C96.7432 65.3685 97.3778 65.1086 98.5363 67.9952C99.4928 70.5057 99.7818 72.5758 99.7818 73.7097C99.7033 74.4854 99.2419 74.7897 99.1142 73.857C98.8709 72.0791 98.3043 68.314 96.3799 65.7711C95.959 65.2066 96.7432 65.3685 96.7432 65.3685Z" fill="#C0EBAB"/>
</g>
</svg>`
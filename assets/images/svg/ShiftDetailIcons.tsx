export const CalendarIcon = `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M11.3125 10.9062C11.3125 10.354 10.8648 9.90625 10.3125 9.90625C9.76022 9.90625 9.3125 10.354 9.3125 10.9062V11.4688C9.3125 12.021 9.76022 12.4688 10.3125 12.4688C10.8648 12.4688 11.3125 12.021 11.3125 11.4688V10.9062Z" fill="#1F1E1E"/>
<path d="M7.9375 14.2812C7.9375 13.729 7.48978 13.2812 6.9375 13.2812C6.38522 13.2812 5.9375 13.729 5.9375 14.2812V14.8438C5.9375 15.396 6.38522 15.8438 6.9375 15.8438C7.48978 15.8438 7.9375 15.396 7.9375 14.8438V14.2812Z" fill="#1F1E1E"/>
<path d="M11.3125 14.2812C11.3125 13.729 10.8648 13.2812 10.3125 13.2812C9.76022 13.2812 9.3125 13.729 9.3125 14.2812V14.8438C9.3125 15.396 9.76022 15.8438 10.3125 15.8438C10.8648 15.8438 11.3125 15.396 11.3125 14.8438V14.2812Z" fill="#1F1E1E"/>
<path d="M14.6875 14.2812C14.6875 13.729 14.2398 13.2812 13.6875 13.2812C13.1352 13.2812 12.6875 13.729 12.6875 14.2812V14.8438C12.6875 15.396 13.1352 15.8438 13.6875 15.8438C14.2398 15.8438 14.6875 15.396 14.6875 14.8438V14.2812Z" fill="#1F1E1E"/>
<path d="M18.0625 14.2812C18.0625 13.729 17.6148 13.2812 17.0625 13.2812C16.5102 13.2812 16.0625 13.729 16.0625 14.2812V14.8438C16.0625 15.396 16.5102 15.8438 17.0625 15.8438C17.6148 15.8438 18.0625 15.396 18.0625 14.8438V14.2812Z" fill="#1F1E1E"/>
<path d="M14.6875 17.6562C14.6875 17.104 14.2398 16.6562 13.6875 16.6562C13.1352 16.6562 12.6875 17.104 12.6875 17.6562V18.2188C12.6875 18.771 13.1352 19.2188 13.6875 19.2188C14.2398 19.2188 14.6875 18.771 14.6875 18.2188V17.6562Z" fill="#1F1E1E"/>
<path d="M11.3125 17.6562C11.3125 17.104 10.8648 16.6562 10.3125 16.6562C9.76022 16.6562 9.3125 17.104 9.3125 17.6562V18.2188C9.3125 18.771 9.76022 19.2188 10.3125 19.2188C10.8648 19.2188 11.3125 18.771 11.3125 18.2188V17.6562Z" fill="#1F1E1E"/>
<path d="M7.9375 17.6562C7.9375 17.104 7.48978 16.6562 6.9375 16.6562C6.38522 16.6562 5.9375 17.104 5.9375 17.6562V18.2188C5.9375 18.771 6.38522 19.2188 6.9375 19.2188C7.48978 19.2188 7.9375 18.771 7.9375 18.2188V17.6562Z" fill="#1F1E1E"/>
<path d="M14.6875 10.9062C14.6875 10.354 14.2398 9.90625 13.6875 9.90625C13.1352 9.90625 12.6875 10.354 12.6875 10.9062V11.4688C12.6875 12.021 13.1352 12.4688 13.6875 12.4688C14.2398 12.4688 14.6875 12.021 14.6875 11.4688V10.9062Z" fill="#1F1E1E"/>
<path d="M18.0625 10.9062C18.0625 10.354 17.6148 9.90625 17.0625 9.90625C16.5102 9.90625 16.0625 10.354 16.0625 10.9062V11.4688C16.0625 12.021 16.5102 12.4688 17.0625 12.4688C17.6148 12.4688 18.0625 12.021 18.0625 11.4688V10.9062Z" fill="#1F1E1E"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.5 1.71875C7.91421 1.71875 8.25 2.05454 8.25 2.46875V3.25H15.75V2.46875C15.75 2.05454 16.0858 1.71875 16.5 1.71875C16.9142 1.71875 17.25 2.05454 17.25 2.46875V3.25H19.3125C20.6587 3.25 21.75 4.34131 21.75 5.6875V19.1125C21.75 20.4587 20.6587 21.55 19.3125 21.55H4.6875C3.34131 21.55 2.25 20.4587 2.25 19.1125V5.6875C2.25 4.34131 3.34131 3.25 4.6875 3.25H6.75V2.46875C6.75 2.05454 7.08579 1.71875 7.5 1.71875ZM6.75 4.75H4.6875C4.16973 4.75 3.75 5.16973 3.75 5.6875V7.25H20.25V5.6875C20.25 5.16973 19.8303 4.75 19.3125 4.75H17.25V5.84375C17.25 6.25796 16.9142 6.59375 16.5 6.59375C16.0858 6.59375 15.75 6.25796 15.75 5.84375V4.75H8.25V5.84375C8.25 6.25796 7.91421 6.59375 7.5 6.59375C7.08579 6.59375 6.75 6.25796 6.75 5.84375V4.75ZM20.25 8.75H3.75V19.1125C3.75 19.6303 4.16973 20.05 4.6875 20.05H19.3125C19.8303 20.05 20.25 19.6303 20.25 19.1125V8.75Z" fill="#1F1E1E"/>
</svg>`;

export const LocationIcon = `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M12 1.75C7.83594 1.75 4.5 5.01752 4.5 9C4.5 11.234 5.78621 13.8853 7.42076 16.3183C9.03428 18.72 10.8954 20.777 11.8669 21.7944C11.91 21.8395 11.9576 21.8554 12 21.8554C12.0424 21.8554 12.09 21.8395 12.1331 21.7944C13.1046 20.777 14.9657 18.72 16.5792 16.3183C18.2138 13.8853 19.5 11.234 19.5 9C19.5 5.01752 16.1641 1.75 12 1.75ZM3 9C3 4.14592 7.05136 0.25 12 0.25C16.9486 0.25 21 4.14592 21 9C21 11.711 19.4843 14.6839 17.8243 17.1548C16.1433 19.657 14.2172 21.7837 13.218 22.8302C12.5494 23.5304 11.4506 23.5304 10.782 22.8302C9.78275 21.7837 7.85669 19.657 6.17565 17.1548C4.51566 14.6839 3 11.711 3 9Z" fill="#1F1E1E"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M12 4.75C9.51472 4.75 7.5 6.76472 7.5 9.25C7.5 11.7353 9.51472 13.75 12 13.75C14.4853 13.75 16.5 11.7353 16.5 9.25C16.5 6.76472 14.4853 4.75 12 4.75ZM6 9.25C6 5.93629 8.68629 3.25 12 3.25C15.3137 3.25 18 5.93629 18 9.25C18 12.5637 15.3137 15.25 12 15.25C8.68629 15.25 6 12.5637 6 9.25Z" fill="#1F1E1E"/>
</svg>
`;

export const StoreMap = `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M10.4792 13.45C10.2031 13.45 9.97925 13.6739 9.97925 13.95C9.97925 14.2262 10.2031 14.45 10.4792 14.45C10.7554 14.45 10.9792 14.2262 10.9792 13.95C10.9792 13.6739 10.7554 13.45 10.4792 13.45ZM8.47925 13.95C8.47925 12.8454 9.37468 11.95 10.4792 11.95C11.5838 11.95 12.4792 12.8454 12.4792 13.95C12.4792 15.0546 11.5838 15.95 10.4792 15.95C9.37468 15.95 8.47925 15.0546 8.47925 13.95Z" fill="#1F1E1E"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M15.25 13.45C14.9739 13.45 14.75 13.6739 14.75 13.95C14.75 14.2262 14.9739 14.45 15.25 14.45C15.5261 14.45 15.75 14.2262 15.75 13.95C15.75 13.6739 15.5261 13.45 15.25 13.45ZM13.25 13.95C13.25 12.8454 14.1454 11.95 15.25 11.95C16.3546 11.95 17.25 12.8454 17.25 13.95C17.25 15.0546 16.3546 15.95 15.25 15.95C14.1454 15.95 13.25 15.0546 13.25 13.95Z" fill="#1F1E1E"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M6.45001 5C6.45001 4.58579 6.7858 4.25 7.20001 4.25H8.41866C9.03946 4.25 9.57766 4.67954 9.71533 5.28488L10.7261 9.72911C10.7685 9.91556 10.9343 10.0479 11.1255 10.0479H15.3452C15.5247 10.0479 15.6833 9.93093 15.7364 9.75943L16.6188 6.90957H11.7C11.2858 6.90957 10.95 6.57378 10.95 6.15957C10.95 5.74536 11.2858 5.40957 11.7 5.40957H16.8497C17.7454 5.40957 18.3849 6.27708 18.12 7.13268L17.1693 10.2031C16.9217 11.0027 16.1822 11.5479 15.3452 11.5479H11.1255C10.234 11.5479 9.46114 10.931 9.26344 10.0618L8.28281 5.75H7.20001C6.7858 5.75 6.45001 5.41421 6.45001 5Z" fill="#1F1E1E"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M12.5 0.0499878C7.41578 0.0499878 3.25 4.03921 3.25 9.01379C3.25 11.753 4.76014 14.4509 6.43805 16.6241C7.32221 17.7693 8.27877 18.8015 9.14623 19.6579C8.63282 19.7266 8.1587 19.8108 7.73516 19.9086C7.11805 20.051 6.56681 20.2312 6.15159 20.4578C5.94436 20.5708 5.73571 20.7142 5.5709 20.9001C5.40194 21.0906 5.25 21.3614 5.25 21.7C5.25 22.0386 5.40194 22.3094 5.5709 22.4999C5.73571 22.6858 5.94436 22.8292 6.15159 22.9423C6.56681 23.1688 7.11805 23.3491 7.73516 23.4915C8.98141 23.7791 10.6657 23.95 12.5 23.95C14.3343 23.95 16.0186 23.7791 17.2648 23.4915C17.882 23.3491 18.4332 23.1688 18.8484 22.9423C19.0556 22.8292 19.2643 22.6858 19.4291 22.4999C19.5981 22.3094 19.75 22.0386 19.75 21.7C19.75 21.3614 19.5981 21.0906 19.4291 20.9001C19.2643 20.7142 19.0556 20.5708 18.8484 20.4578C18.4332 20.2312 17.882 20.051 17.2648 19.9086C16.8413 19.8108 16.3672 19.7266 15.8538 19.6579C16.7212 18.8015 17.6778 17.7693 18.5619 16.6241C20.2399 14.4509 21.75 11.753 21.75 9.01379C21.75 4.03921 17.5842 0.0499878 12.5 0.0499878ZM4.75 9.01379C4.75 4.91565 8.19537 1.54999 12.5 1.54999C16.8046 1.54999 20.25 4.91565 20.25 9.01379C20.25 11.2428 18.9959 13.6076 17.3746 15.7074C15.7707 17.7849 13.8979 19.4879 12.8474 20.3765C12.6417 20.5505 12.3583 20.5505 12.1526 20.3765C11.1021 19.4879 9.22933 17.7849 7.62535 15.7074C6.00409 13.6076 4.75 11.2428 4.75 9.01379ZM14.3994 21.017C14.1859 21.2059 13.9899 21.3747 13.8161 21.5217C13.0513 22.1687 11.9487 22.1687 11.1839 21.5217C11.0101 21.3747 10.8141 21.2059 10.6006 21.017C9.62375 21.0879 8.7595 21.2116 8.07245 21.3701C7.6169 21.4753 7.26654 21.5896 7.02115 21.7C7.26654 21.8104 7.6169 21.9247 8.07245 22.0299C9.17874 22.2852 10.7445 22.45 12.5 22.45C14.2555 22.45 15.8213 22.2852 16.9275 22.0299C17.3831 21.9247 17.7335 21.8104 17.9788 21.7C17.7335 21.5896 17.3831 21.4753 16.9275 21.3701C16.2405 21.2116 15.3763 21.0879 14.3994 21.017Z" fill="#1F1E1E"/>
</svg>
`;

export const TimeIcon = `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M12.1245 4.11025C7.39153 4.11025 3.55467 7.94711 3.55467 12.6801C3.55467 17.4131 7.39153 21.25 12.1245 21.25C16.8576 21.25 20.6944 17.4131 20.6944 12.6801C20.6944 7.94711 16.8576 4.11025 12.1245 4.11025ZM2.05467 12.6801C2.05467 7.11868 6.56311 2.61025 12.1245 2.61025C17.686 2.61025 22.1944 7.11868 22.1944 12.6801C22.1944 18.2416 17.686 22.75 12.1245 22.75C6.56311 22.75 2.05467 18.2416 2.05467 12.6801Z" fill="#1F1E1E"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M16.824 2.33987C16.8023 1.92622 17.1201 1.57332 17.5337 1.55164C20.3663 1.40319 22.7829 3.57909 22.9313 6.41164C22.953 6.82528 22.6352 7.17818 22.2216 7.19986C21.8079 7.22154 21.4551 6.90379 21.4334 6.49014C21.3283 4.48488 19.6175 2.94449 17.6122 3.04959C17.1986 3.07126 16.8457 2.75351 16.824 2.33987Z" fill="#1F1E1E"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.49418 2.44764C7.53028 2.035 7.22504 1.67123 6.8124 1.63513C3.98676 1.38791 1.49572 3.47815 1.2485 6.30379C1.2124 6.71642 1.51765 7.0802 1.93028 7.1163C2.34292 7.1524 2.7067 6.84716 2.7428 6.43452C2.91781 4.43415 4.6813 2.95441 6.68166 3.12942C7.0943 3.16552 7.45808 2.86028 7.49418 2.44764Z" fill="#1F1E1E"/>
<path d="M12.8744 12.7524V8.02017C12.8744 7.60595 12.5386 7.27017 12.1244 7.27017C11.7102 7.27017 11.3744 7.60595 11.3744 8.02017V13.5024C11.3744 13.9167 11.7102 14.2524 12.1244 14.2524H16.5102C16.9245 14.2524 17.2602 13.9167 17.2602 13.5024C17.2602 13.0882 16.9245 12.7524 16.5102 12.7524H12.8744Z" fill="#1F1E1E"/>
</svg>`;

export const ManageIcon = `<svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M11 1.75C5.89137 1.75 1.75 5.89137 1.75 11C1.75 16.1086 5.89137 20.25 11 20.25C16.1086 20.25 20.25 16.1086 20.25 11C20.25 5.89137 16.1086 1.75 11 1.75ZM0.25 11C0.25 5.06294 5.06294 0.25 11 0.25C16.9371 0.25 21.75 5.06294 21.75 11C21.75 16.9371 16.9371 21.75 11 21.75C5.06294 21.75 0.25 16.9371 0.25 11Z" fill="#1F1E1E"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M11 5.25C11.4142 5.25 11.75 5.58579 11.75 6V6.8C11.75 7.21421 11.4142 7.55 11 7.55C10.5858 7.55 10.25 7.21421 10.25 6.8V6C10.25 5.58579 10.5858 5.25 11 5.25ZM11 8.95C11.4142 8.95 11.75 9.28579 11.75 9.7V16.2C11.75 16.6142 11.4142 16.95 11 16.95C10.5858 16.95 10.25 16.6142 10.25 16.2V9.7C10.25 9.28579 10.5858 8.95 11 8.95Z" fill="#1F1E1E"/>
</svg>`;

export const CrossIcon = (color: string, size: string = "18") => `
<svg width="${size}" height="${size}" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M17.7071 16.2928C18.0976 16.6833 18.0976 17.3165 17.7071 17.707C17.3166 18.0975 16.6834 18.0975 16.2929 17.707L9.00005 10.4141L1.70711 17.7071C1.31658 18.0976 0.683418 18.0976 0.292893 17.7071C-0.0976311 17.3166 -0.0976311 16.6834 0.292893 16.2929L7.58583 8.99993L0.293009 1.70711C-0.0975153 1.31658 -0.0975157 0.683417 0.293009 0.292893C0.683533 -0.0976312 1.3167 -0.0976308 1.70722 0.292893L9.00005 7.58572L16.2928 0.292986C16.6833 -0.0975377 17.3165 -0.0975381 17.707 0.292986C18.0975 0.68351 18.0975 1.31668 17.707 1.7072L10.4143 8.99993L17.7071 16.2928Z" fill="${color}"/>
</svg>`;

export const QuestionIcon = `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M11.3731 13.642V13.5341C11.3806 12.8301 11.4507 12.27 11.5831 11.8536C11.7194 11.4373 11.9124 11.1005 12.1622 10.8431C12.412 10.5857 12.7129 10.3511 13.0649 10.1391C13.292 9.99529 13.4964 9.83444 13.678 9.65655C13.8597 9.47867 14.0035 9.27429 14.1095 9.04341C14.2155 8.81254 14.2685 8.55707 14.2685 8.27699C14.2685 7.94014 14.189 7.64871 14.03 7.4027C13.871 7.15669 13.6591 6.96745 13.3942 6.83498C13.133 6.69873 12.8416 6.6306 12.5199 6.6306C12.2284 6.6306 11.9503 6.69116 11.6853 6.81227C11.4204 6.93338 11.2009 7.12262 11.0268 7.37999C10.8527 7.63357 10.7524 7.96096 10.7259 8.36215C10.7259 8.36215 10.5338 9.07227 9.81434 9.07227C9.09493 9.07227 9 8.36215 9 8.36215C9.0265 7.68088 9.19871 7.10559 9.51663 6.63628C9.83455 6.16318 10.2547 5.80551 10.777 5.56328C11.3031 5.32106 11.884 5.19995 12.5199 5.19995C13.2163 5.19995 13.8256 5.33052 14.3479 5.59167C14.8702 5.84904 15.2752 6.21049 15.5629 6.67602C15.8543 7.13776 16 7.6771 16 8.29402C16 8.71792 15.9338 9.10019 15.8013 9.44082C15.6688 9.77767 15.4796 10.0786 15.2336 10.3435C14.9914 10.6084 14.6999 10.8431 14.3593 11.0475C14.0376 11.2481 13.7764 11.4562 13.5758 11.672C13.379 11.8877 13.2352 12.1432 13.1444 12.4384C13.0535 12.7336 13.0043 13.0988 12.9968 13.5341V13.642C12.9968 13.642 12.8519 14.348 12.1325 14.348C11.413 14.348 11.3731 13.642 11.3731 13.642Z" fill="#1F1E1E"/>
<path d="M12.1963 15.6056C11.9277 15.6056 11.7065 15.6946 11.5327 15.8726C11.3431 16.0667 11.2483 16.3013 11.2483 16.5764V16.8694C11.2483 17.1283 11.3431 17.3548 11.5327 17.549C11.7065 17.7431 11.9277 17.8402 12.1963 17.8402C12.465 17.8402 12.6941 17.7431 12.8837 17.549C13.0575 17.3548 13.1444 17.1283 13.1444 16.8694V16.5764C13.1444 16.3013 13.0575 16.0667 12.8837 15.8726C12.6941 15.6946 12.465 15.6056 12.1963 15.6056Z" fill="#1F1E1E"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M12.303 2.75C7.36176 2.75 3.35606 6.75569 3.35606 11.697C3.35606 13.1465 3.7002 14.5135 4.31055 15.7227C4.4087 15.9171 4.40419 16.1098 4.40287 16.166L4.40282 16.168C4.40075 16.2576 4.38938 16.3476 4.37676 16.426C4.35109 16.5853 4.30705 16.7752 4.2549 16.9748C4.1495 17.3784 3.99287 17.8898 3.82226 18.4173C3.60866 19.0778 3.36733 19.7812 3.16389 20.3622C3.81151 20.22 4.61228 20.0501 5.36906 19.9055C5.95862 19.7928 6.53357 19.6932 6.99276 19.6347C7.22068 19.6056 7.43587 19.5846 7.61701 19.5793C7.70703 19.5766 7.80374 19.5772 7.89726 19.5863C7.97247 19.5935 8.12452 19.6123 8.27735 19.6895C9.48655 20.2998 10.8535 20.6439 12.303 20.6439C17.2443 20.6439 21.25 16.6382 21.25 11.697C21.25 6.75569 17.2443 2.75 12.303 2.75ZM1.85606 11.697C1.85606 5.92727 6.53333 1.25 12.303 1.25C18.0727 1.25 22.75 5.92727 22.75 11.697C22.75 17.4667 18.0727 22.1439 12.303 22.1439C10.6528 22.1439 9.08995 21.7608 7.70055 21.0779C7.68942 21.0779 7.67643 21.0782 7.66146 21.0786C7.55088 21.0819 7.3905 21.0961 7.18236 21.1226C6.76943 21.1753 6.23029 21.268 5.65059 21.3788C4.49459 21.5997 3.22527 21.8828 2.58479 22.0288C1.82582 22.2019 1.17744 21.4687 1.43971 20.7371C1.65295 20.1423 2.06014 18.9913 2.39505 17.9557C2.56307 17.4363 2.70919 16.9572 2.80359 16.5958C2.84426 16.44 2.87214 16.3181 2.8884 16.2302C2.22664 14.8582 1.85606 13.3199 1.85606 11.697ZM7.75301 21.0793C7.76107 21.0801 7.75932 21.0805 7.74997 21.0791L7.75301 21.0793Z" fill="#1F1E1E"/>
</svg>
`;

export const ErrorIcon = `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_4180_149424)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M5.93061 1.67308C6.87312 0.0406005 9.2294 0.0405983 10.1719 1.67308L15.6734 11.2019C16.6159 12.8344 15.4378 14.875 13.5527 14.875H2.54978C0.66476 14.875 -0.513381 12.8344 0.429131 11.2019L5.93061 1.67308ZM8.87288 2.42308C8.50772 1.7906 7.59481 1.7906 7.22965 2.42308L1.72817 11.9519C1.36301 12.5844 1.81946 13.375 2.54978 13.375H13.5527C14.2831 13.375 14.7395 12.5844 14.3744 11.9519L8.87288 2.42308ZM8 4.34602C8.41421 4.34602 8.75 4.68181 8.75 5.09602V8.8C8.75 9.21421 8.41421 9.55 8 9.55C7.58578 9.55 7.25 9.21421 7.25 8.8V5.09602C7.25 4.68181 7.58578 4.34602 8 4.34602ZM8 10.3934C8.41421 10.3934 8.75 10.7292 8.75 11.1434V11.687C8.75 12.1012 8.41421 12.437 8 12.437C7.58578 12.437 7.25 12.1012 7.25 11.687V11.1434C7.25 10.7292 7.58578 10.3934 8 10.3934Z" fill="#B00000"/>
</g>
<defs>
<clipPath id="clip0_4180_149424">
<rect width="16" height="16" fill="white"/>
</clipPath>
</defs>
</svg>
`;

export const tip = `<svg width="16" height="8" viewBox="0 0 16 8" fill="none" xmlns="http://www.w3.org/2000/svg">
<path id="tip" d="M16 0L9.41421 6.58579C8.63316 7.36684 7.36683 7.36683 6.58579 6.58579L0 0H16Z" fill="#1F1E1E"/>
</svg>`;

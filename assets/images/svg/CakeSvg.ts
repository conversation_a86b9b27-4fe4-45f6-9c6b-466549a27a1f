
export const CakeSvg =`
<svg width="56" height="57" viewBox="0 0 56 57" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_9320_70759)">
<g clip-path="url(#clip1_9320_70759)">
<path d="M28 56.0625C43.464 56.0625 56 43.5265 56 28.0625C56 12.5985 43.464 0.0625 28 0.0625C12.536 0.0625 0 12.5985 0 28.0625C0 43.5265 12.536 56.0625 28 56.0625Z" fill="#FAF5F0"/>
<path opacity="0.7" d="M47.25 42.0973C47.25 43.0328 38.6315 43.7913 28 43.7913C17.3685 43.7913 8.75 43.2266 8.75 42.0973C8.75 40.6856 17.3685 40.4033 28 40.4033C38.6315 40.4033 47.25 41.1617 47.25 42.0973Z" fill="#E1C6B4"/>
<rect x="12.5283" y="23.7888" width="31.5051" height="11.6072" fill="#643C0E"/>
<path d="M12.5283 37.8834H44.0334C44.0334 40.1728 42.1775 42.0288 39.888 42.0288H16.6737C14.3843 42.0288 12.5283 40.1728 12.5283 37.8834Z" fill="#643C0E"/>
<rect x="12.5283" y="35.3968" width="31.5051" height="2.48725" fill="#CDA890"/>
<rect x="29.1104" y="15.4971" width="6.63266" height="1.65816" transform="rotate(90 29.1104 15.4971)" fill="#D2C2F2"/>
<rect x="37.4011" y="15.4971" width="6.63266" height="1.65816" transform="rotate(90 37.4011 15.4971)" fill="#D2C2F2"/>
<rect x="20.8176" y="15.497" width="6.63266" height="1.65816" transform="rotate(90 20.8176 15.497)" fill="#D2C2F2"/>
<path d="M36.5705 13.0105L37.5654 14.337C38.1803 15.1568 37.5953 16.3268 36.5705 16.3268C35.5457 16.3268 34.9607 15.1568 35.5756 14.337L36.5705 13.0105Z" fill="#F59F49"/>
<path d="M28.2807 13.0105L29.2756 14.337C29.8905 15.1568 29.3055 16.3268 28.2807 16.3268C27.2559 16.3268 26.6709 15.1568 27.2858 14.337L28.2807 13.0105Z" fill="#F59F49"/>
<path d="M19.989 13.0105L20.9839 14.337C21.5987 15.1569 21.0138 16.3268 19.989 16.3268C18.9642 16.3268 18.3792 15.1569 18.9941 14.337L19.989 13.0105Z" fill="#F59F49"/>
<path d="M36.944 21.302C41.3181 21.302 44.864 24.848 44.864 29.222C44.864 30.7447 43.5315 31.9481 42.0096 31.6288C40.8903 31.3955 40.1263 30.3149 40.1263 29.13V27.5952C40.1263 26.9199 39.8599 26.3059 39.4335 25.8638C39.0071 25.4157 38.4149 25.1394 37.7575 25.1394C36.4488 25.1394 35.3887 26.2445 35.3887 27.5952V30.6651C35.3887 32.1877 34.0563 33.3911 32.5343 33.0719C31.9717 32.9552 31.5039 32.6298 31.1723 32.1816C30.8406 31.7334 30.6511 31.1686 30.6511 30.573C30.6511 30.5546 30.6511 30.5423 30.6452 30.5239L30.6274 30.3765C30.5031 29.3328 29.7806 28.4241 28.7679 28.2154C27.246 27.9022 25.9135 29.0995 25.9135 30.6221V31.2361C25.9135 31.322 25.9076 31.4019 25.9017 31.4878C25.7892 32.9122 24.51 33.9928 23.0591 33.6858C22.4787 33.563 22.005 33.2131 21.6734 32.7403C21.6023 32.6482 21.5371 32.5438 21.4779 32.4395C21.4424 32.3781 21.4128 32.3105 21.3891 32.2491C21.3595 32.1877 21.3299 32.1202 21.3121 32.0465C21.2884 31.979 21.2707 31.9115 21.2529 31.8378C21.2351 31.7702 21.2233 31.6966 21.2114 31.629C21.1996 31.5615 21.1937 31.5001 21.1877 31.4326C21.1759 31.365 21.1759 31.3036 21.1759 31.2361V29.4863C21.1759 29.4065 21.17 29.3205 21.1641 29.2407C21.1641 29.1732 21.1581 29.1118 21.1404 29.0504C21.1345 28.9767 21.1226 28.903 21.0989 28.8355C21.0871 28.7618 21.0634 28.6943 21.0397 28.6329C21.0219 28.5592 20.9923 28.4916 20.9627 28.4302C20.939 28.3688 20.9094 28.3013 20.8739 28.2399C20.8206 28.1355 20.7495 28.0373 20.6785 27.9391C20.6192 27.8593 20.5541 27.7794 20.483 27.7058C20.0566 27.2637 19.4644 26.9874 18.8071 26.9874C17.5812 26.9874 16.5745 27.9514 16.4501 29.1916C16.4383 29.2591 16.4383 29.3266 16.4383 29.3942V30.0511C16.4383 31.5738 15.1058 32.7772 13.5839 32.4579C12.5712 32.2491 11.8487 31.3405 11.7244 30.2906C11.7125 30.2476 11.7066 30.1985 11.7066 30.1494C11.7007 30.088 11.7007 30.0204 11.7007 29.959C11.7007 25.1779 15.5765 21.302 20.3577 21.302H21.1759V21.4555C21.1759 21.4064 21.1759 21.3512 21.1818 21.302H25.9076C25.9135 21.3512 25.9135 21.4064 25.9135 21.4555V21.302H36.944Z" fill="#F77C7C"/>
<line x1="17.9123" y1="22.7996" x2="17.7803" y2="22.6675" stroke="#661414" stroke-width="2.59595" stroke-linecap="round"/>
<line x1="20.6305" y1="23.3296" x2="20.5801" y2="23.3548" stroke="#661414" stroke-width="2.59595" stroke-linecap="round"/>
<line x1="25.861" y1="25.6387" x2="25.729" y2="25.5067" stroke="#661414" stroke-width="2.59595" stroke-linecap="round"/>
<line x1="27.2235" y1="23.271" x2="26.7672" y2="23.5753" stroke="#661414" stroke-width="2.59595" stroke-linecap="round"/>
<line x1="33.7746" y1="25.8467" x2="33.3182" y2="25.5425" stroke="#661414" stroke-width="2.59595" stroke-linecap="round"/>
<line x1="34.4547" y1="31.4137" x2="34.9111" y2="31.1095" stroke="#661414" stroke-width="2.59595" stroke-linecap="round"/>
<line x1="32.3346" y1="22.1352" x2="31.8782" y2="22.4395" stroke="#661414" stroke-width="2.59595" stroke-linecap="round"/>
<line x1="38.8857" y1="22.4396" x2="38.4293" y2="22.1353" stroke="#661414" stroke-width="2.59595" stroke-linecap="round"/>
<line x1="42.3351" y1="23.9961" x2="42.7914" y2="23.6919" stroke="#661414" stroke-width="2.59595" stroke-linecap="round"/>
<line x1="43.8386" y1="24.8178" x2="43.0795" y2="24.5647" stroke="#661414" stroke-width="2.59595" stroke-linecap="round"/>
<line x1="1.29798" y1="-1.29798" x2="0.749488" y2="-1.29798" transform="matrix(-0.554699 0.832051 -0.83205 -0.554701 43.3323 27.9102)" stroke="#661414" stroke-width="2.59595" stroke-linecap="round"/>
<line x1="24.8009" y1="30.8461" x2="25.2573" y2="30.5418" stroke="#661414" stroke-width="2.59595" stroke-linecap="round"/>
<line x1="1.29798" y1="-1.29798" x2="1.04339" y2="-1.29798" transform="matrix(0.970143 0.242535 -0.242536 0.970142 12.5972 25.6387)" stroke="#661414" stroke-width="2.59595" stroke-linecap="round"/>
<line x1="15.299" y1="29.7102" x2="15.6032" y2="29.2538" stroke="#661414" stroke-width="2.59595" stroke-linecap="round"/>
</g>
</g>
<defs>
<clipPath id="clip0_9320_70759">
<rect y="0.5" width="56" height="56" rx="28" fill="white"/>
</clipPath>
<clipPath id="clip1_9320_70759">
<rect width="56" height="56" fill="white" transform="translate(0 0.5)"/>
</clipPath>
</defs>
</svg>`
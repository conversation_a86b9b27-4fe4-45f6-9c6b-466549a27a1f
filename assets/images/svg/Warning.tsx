
import React from "react";
import { SvgXml } from "react-native-svg";

export const Warning = ({ size, color }: { size: number, color: string }) => {

    const icon = `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Warning" clip-path="url(#clip0_11432_22586)">
<path id="Icon" fill-rule="evenodd" clip-rule="evenodd" d="M5.93061 1.67307C6.87312 0.0405966 9.2294 0.0405946 10.1719 1.67307L15.6734 11.2019C16.6159 12.8344 15.4378 14.875 13.5527 14.875H2.54978C0.664759 14.875 -0.513381 12.8344 0.429131 11.2019L5.93061 1.67307ZM7.24999 11.687C7.24999 12.1012 7.58578 12.437 7.99999 12.437C8.41421 12.437 8.74999 12.1012 8.74999 11.687V11.1434C8.74999 10.7292 8.41421 10.3934 7.99999 10.3934C7.58578 10.3934 7.24999 10.7292 7.24999 11.1434V11.687ZM7.24999 8.8C7.24999 9.21421 7.58578 9.55 7.99999 9.55C8.41421 9.55 8.74999 9.21421 8.74999 8.8V5.09602C8.74999 4.68181 8.41421 4.34602 7.99999 4.34602C7.58578 4.34602 7.24999 4.68181 7.24999 5.09602V8.8Z" fill="${color}"/>
</g>
<defs>
<clipPath id="clip0_11432_22586">
<rect width="16" height="16" fill="white"/>
</clipPath>
</defs>
</svg>
`

    return <SvgXml xml={icon} width={size} height={size} />;
};

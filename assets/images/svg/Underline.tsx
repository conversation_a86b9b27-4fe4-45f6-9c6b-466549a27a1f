import React from "react";
import { SvgXml } from "react-native-svg";

export const Underline = ({ testID, width, height, color }: { testID?: string, width?: number, height?: number, color: string }) => {

    const underline = `<svg width="187" height="26" viewBox="0 0 187 26" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Sincerely Brand Glyphs" clip-path="url(#clip0_9302_69983)">
<path id="Vector" d="M1.51944 15.4668C2.21693 15.4021 2.90877 15.3425 3.60598 15.2832C4.4599 15.2108 5.31919 15.1387 6.17283 15.0717C6.25924 15.0656 5.94579 15.0918 6.27562 15.0611C6.42162 15.0475 6.57268 15.0394 6.71868 15.0258C7.00501 15.0035 7.29135 14.9813 7.58305 14.9593C8.22066 14.9076 8.85263 14.8609 9.48996 14.8145C17.328 14.2251 25.1763 13.7438 33.0263 13.3325C45.2843 12.6851 57.5442 12.2046 69.8132 11.8537C86.0477 11.3935 102.291 11.1704 118.533 11.1626C137.993 11.1557 157.458 11.4558 176.911 12.062C179.564 12.1456 182.212 12.2344 184.864 12.3287C186.416 12.3854 186.552 9.9717 184.994 9.91477C165.062 9.20753 145.123 8.82272 125.184 8.75528C108.514 8.7025 91.843 8.8756 75.1748 9.29633C62.3532 9.61749 49.5293 10.0838 36.7172 10.7282C28.3235 11.1479 19.9364 11.6432 11.5593 12.2521C9.11814 12.4273 6.68148 12.6189 4.24452 12.8159C3.21778 12.9005 2.14651 12.9128 1.12587 13.0838C1.08238 13.0922 1.03947 13.0899 0.995974 13.0983C0.341685 13.1599 -0.0455338 13.9569 0.0737675 14.5389C0.218902 15.2408 0.865437 15.523 1.51436 15.4611L1.51944 15.4668Z" fill="${color}"/>
</g>
<defs>
<clipPath id="clip0_9302_69983">
<rect width="185.608" height="15.4288" fill="white" transform="translate(0.82959) rotate(3.08163)"/>
</clipPath>
</defs>
</svg>
`;

    if (width == undefined && height == undefined) {
        width = 185
        height = 15
    }
    if (width && (height == undefined || height == null)) {
        height = (width * 15) / 185
    }

    if (height && (width == undefined || width == null)) {
        width = (height * 185) / 15
    }

    return <SvgXml testID={testID} xml={underline} width={width} height={height} />;
};

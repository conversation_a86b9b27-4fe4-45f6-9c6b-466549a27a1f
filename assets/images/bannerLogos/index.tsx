
import React from "react";
import { SvgXml } from "react-native-svg";
import acme from './acme'
import albertsons from './albertsons'
import albertsonsMarket from './albertsonsMarket'
import albertsonsMarketStreet from './albertsonsMarketStreet'
import amigos from './amigos'
import andronicons from './andronicons'
import balduccis from './balduccis'
import carrs from './carrs'
import haggen from './haggen'
import jewelOsco from './jewelOsco'
import kings from './kings'
import lucky from './lucky'
import marketStreet from './marketStreet'
import pavillions from './pavillions'
import randalls from './randalls'
import safeway from './safeway'
import shaws from './shaws'
import starMarket from './starMarket'
import tomthumb from './tomthumb'
import unitedExpress from "./unitedExpress";
import unitedSupermarkets from "./unitedSuperMarkets";
import vons from './vons'

export type BannerNames = "ACME" | "Albertsons" | "Albertsons Market" | "Albertsons Market Street" | "Amigos" | "Andronicons" |
    "Balduccis" | "Carrs" | "Haggen" | "Jewel Osco" | "Kings" | "Lucky" | "Market Street" | "Pavilions"
    | "Randalls" | "Safeway" | "Shaws" | "StarMarket" | "Tom Thumb" | "United Express" | "United Super Markets" | "Vons";

const BannerLogos = ({ name, testID }: { name: BannerNames, testID: string }) => {

    const bannerLogos = {
        "ACME": acme,
        "Albertsons": albertsons,
        "Albertsons Market": albertsonsMarket,
        "Albertsons Market Street": albertsonsMarketStreet,
        "Amigos": amigos,
        "Andronicons": andronicons,
        "Balduccis": balduccis,
        "Carrs": carrs,
        "Haggen": haggen,
        "Jewel Osco": jewelOsco,
        "Kings": kings,
        "Lucky": lucky,
        "Market Street": marketStreet,
        "Pavilions": pavillions,
        "Randalls": randalls,
        "Safeway": safeway,
        "Shaws": shaws,
        "StarMarket": starMarket,
        "Tom Thumb": tomthumb,
        "United Express": unitedExpress,
        "United Super Markets": unitedSupermarkets,
        "Vons": vons,
    };

    const bannerDimens = {
        "ACME": {
            width: 39,
            height: 13,
        },
        "Albertsons": {
            width: 79,
            height: 12,
        },
        "Albertsons Market": {
            width: 47,
            height: 13,
        },
        "Albertsons Market Street": {
            width: 46,
            height: 12,
        },
        "Amigos": {
            width: 31,
            height: 11,
        },
        "Andronicons": {
            width: 33,
            height: 12,
        },
        "Balduccis": {
            width: 23,
            height: 11,
        },
        "Carrs": {
            width: 55,
            height: 16,
        },
        "Haggen": {
            width: 41,
            height: 13,
        },
        "Jewel Osco": {
            width: 16,
            height: 13,
        },
        "Kings": {
            width: 26,
            height: 13,
        },
        "Lucky": {
            width: 21,
            height: 12,
        },
        "Market Street": {
            width: 40,
            height: 13,
        },
        "Pavilions": {
            width: 59,
            height: 13,
        },
        "Randalls": {
            width: 60,
            height: 12,
        },
        "Safeway": {
            width: 62,
            height: 13,
        },
        "Shaws": {
            width: 43,
            height: 12,
        },
        "StarMarket": {
            width: 35,
            height: 13,
        },
        "Tom Thumb": {
            width: 51,
            height: 12,
        },
        "United Express": {
            width: 55,
            height: 12,
        },
        "United Super Markets": {
            width: 30,
            height: 12,
        },
        "Vons": {
            width: 31,
            height: 13,
        },
    };

    return (
        <SvgXml
            xml={bannerLogos[name]}
            width={bannerDimens[name].width}
            height={bannerDimens[name].height}
            testID={`banner-image-${testID}`}
        />
    );

}

export default BannerLogos;
// export default
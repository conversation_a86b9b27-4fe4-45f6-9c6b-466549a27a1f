const randalls = `<svg width="61" height="12" viewBox="0 0 61 12" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Randall<PERSON>">
<path id="Vector" d="M0 10.0516V11.7765C0 11.9084 0.100465 11.9997 0.221023 11.9997H60.0681C60.1786 11.9997 60.2791 11.8983 60.2791 11.7765V10.0516H0Z" fill="#EE3124"/>
<path id="Vector_2" d="M53.7888 6.80403C54.1404 6.3373 54.1404 5.72851 53.6582 5.53573C53.5979 5.73866 52.6033 6.68227 52.3421 6.92579C52.7339 7.44325 53.4372 7.27076 53.7888 6.80403ZM51.2772 2.09611C50.8251 2.15699 49.7803 3.81085 49.4387 4.84578C51.4681 3.49631 51.9101 2.01494 51.2772 2.09611ZM46.5654 2.09611C46.1032 2.15699 45.0684 3.81085 44.7269 4.84578C46.7462 3.49631 47.1983 2.01494 46.5654 2.09611ZM16.9583 5.70822C16.7775 5.51544 16.3354 5.62705 15.9838 5.97203C15.6322 6.317 15.4915 6.7533 15.6723 6.94608C15.8532 7.13886 16.2952 7.02725 16.6468 6.68227C16.9985 6.3373 17.1391 5.901 16.9583 5.70822ZM38.7592 5.74881C38.5784 5.55603 38.1363 5.66764 37.7847 6.01261C37.4331 6.34744 37.2824 6.79388 37.4733 6.98666C37.6541 7.17945 38.0962 7.05769 38.4478 6.72286C38.7994 6.37788 38.9401 5.94159 38.7592 5.74881ZM31.4856 5.74881C31.3047 5.55603 30.8627 5.66764 30.511 6.01261C30.1594 6.34744 30.0188 6.79388 30.2096 6.98666C30.3905 7.17945 30.8325 7.05769 31.1842 6.72286C31.5358 6.37788 31.6664 5.94159 31.4856 5.74881ZM16.6669 3.6688C17.5309 3.55719 17.6716 4.18626 17.6917 4.34861C17.7017 4.40949 17.7419 4.41963 17.762 4.39934C18.1036 3.55719 19.9622 3.74997 20.0727 4.22685C20.0727 4.22685 19.48 5.85027 19.4398 6.21554C19.3594 6.91564 19.2188 7.07798 20.1832 6.45905C20.6454 6.16481 21.7505 5.17046 22.0921 4.12539C22.6145 3.76012 23.5086 3.81085 23.7799 3.87173C24.0511 3.93261 24.3726 3.85143 24.0712 4.59212C23.7899 5.27193 23.7598 5.42412 23.7397 5.49515C23.6995 5.65749 23.7598 5.69807 23.8703 5.56617C23.9306 5.49515 25.2266 3.68909 26.4824 4.02392C26.9445 4.14568 27.477 4.51095 27.1354 5.44442C26.7938 6.36774 26.623 6.94608 27.2158 6.83447C27.8085 6.71271 28.0798 6.38803 28.1702 6.12422C28.2606 5.85027 29.7174 3.821 31.1138 3.64851C32.3596 3.48616 32.3194 4.32831 32.3194 4.42978C32.3194 4.53124 32.3194 4.53124 32.3797 4.40949C32.44 4.29787 33.0126 2.64402 33.093 2.49182C33.1734 2.33962 33.1834 1.93377 33.8766 1.7207C34.5598 1.50762 35.1324 1.36557 35.1324 1.36557C35.3635 1.30469 35.7453 1.13221 35.4941 1.83231C35.243 2.53241 33.8666 6.80403 33.8666 6.80403C33.7862 7.0171 33.7561 7.1693 34.1077 7.14901C34.4694 7.13886 35.042 7.13886 35.1626 6.63154C35.2831 6.12422 36.6696 3.89202 38.4578 3.64851C39.3118 3.5369 39.4625 4.17612 39.4826 4.32831C39.4926 4.38919 39.5328 4.40949 39.5429 4.37905C39.8543 3.5166 41.6727 3.94275 41.7631 4.06451C41.8636 4.18626 41.8937 4.237 41.8435 4.41963C41.7933 4.60227 41.5923 4.97768 41.2106 6.23583C40.8288 7.49398 42.0143 6.77359 42.165 6.44891C43.1395 2.89768 46.0028 -0.176677 47.3189 0.878546C48.8459 2.08596 46.053 4.97768 44.7068 5.94159C44.3953 7.67662 45.7315 7.25047 46.9672 6.13437C47.4193 4.28773 50.1319 -0.30858 51.9804 0.929278C53.4774 1.92362 50.815 5.02841 49.3784 5.98217C48.9765 9.93926 54.8738 2.95855 55.1049 2.31933C55.4364 2.2686 56.853 2.31933 56.9836 2.43094C56.9334 2.69475 56.3708 3.37455 56.2703 3.73982C56.1698 4.09495 56.5215 4.62256 56.5215 5.3531C56.5215 6.14452 56.0995 6.64169 56.1297 6.81418C56.5014 6.79388 57.3955 5.78939 57.8777 5.67778L57.2448 7.78823C54.5624 9.28989 52.0206 9.06667 51.0461 7.95057C48.635 9.51311 47.2586 8.80287 46.8668 7.61574C44.7269 9.23916 42.7577 9.15799 42.0545 7.67662C40.8188 8.8536 38.5985 10.0306 38.9803 7.40267C37.2523 9.15799 35.4539 9.27975 34.9918 8.08247C34.9918 8.08247 33.7058 8.8536 32.5003 8.8536C31.5056 8.8536 31.6162 7.88969 31.7166 7.38237C30.8526 8.32599 29.5566 8.87389 28.8634 8.87389C28.1702 8.87389 27.6578 8.33613 27.6779 7.70706C27.3163 8.0013 26.3216 8.96521 25.1161 8.96521C23.9105 8.96521 24.0913 7.59545 24.2621 7.14901C24.2822 7.08813 24.2119 7.09827 24.1616 7.14901C23.9105 7.36208 23.5086 7.74764 23.2474 8.35643C23.0063 8.90433 22.6145 8.8536 22.4236 8.8536H21.0372C20.3842 8.8536 20.5549 8.53906 20.6152 8.44774C20.6755 8.35643 20.9066 7.77808 21.0472 7.51428C21.1376 7.34179 21.1376 7.09827 20.786 7.41281C20.0928 8.02159 19.0078 8.8536 18.1438 8.8536C17.1291 8.8536 17.0889 8.14335 17.1492 7.38237C16.1445 8.50862 14.949 8.89418 14.5069 8.89418C11.5734 8.89418 13.874 4.02392 16.6669 3.6688Z" fill="#003A5D"/>
<path id="Vector_3" d="M7.20331 4.64402C7.20331 4.64402 7.57503 3.5888 7.58508 3.51777C7.60517 3.4366 7.8061 3.32499 7.92666 3.32499C8.05726 3.32499 10.0364 3.16265 10.0364 3.96421C10.0465 4.59329 7.78601 4.87739 7.20331 4.64402ZM3.90805 4.27875C3.68703 4.75563 2.73261 7.38354 2.6221 8.56052C3.30526 8.99682 5.23419 9.02726 5.9475 8.43877C5.98768 7.96189 6.75122 6.07466 6.85168 5.84129C6.88182 6.66315 6.89187 8.95623 9.64461 8.83447C12.3974 8.71272 13.4924 5.52676 13.5628 5.22236C13.6331 4.90783 13.5929 4.78607 13.2814 4.989C12.97 5.20207 10.8502 6.28773 10.7497 5.07017C11.3525 4.88753 14.4368 2.94958 13.5628 1.2044C12.7691 -0.368288 6.72108 -0.205946 4.22954 0.585471C3.87791 1.01162 3.18471 2.43211 2.97373 3.8526C2.9034 4.30919 3.17466 4.31934 3.38564 4.26861C3.58657 4.21787 3.84778 4.27875 3.90805 4.27875Z" fill="#003A5D"/>
<path id="Vector_4" d="M58.7119 7.76916C58.8124 7.76916 58.9832 7.78946 58.9832 7.62712C58.9832 7.50536 58.8827 7.49521 58.7822 7.49521H58.5713V7.76916H58.7119ZM59.0635 8.19531H58.953L58.742 7.84019H58.5713V8.19531H58.4708V7.43434H58.8023C58.8727 7.43434 58.9329 7.43433 58.9932 7.47492C59.0535 7.50536 59.0736 7.57638 59.0736 7.63726C59.0736 7.78946 58.9631 7.84019 58.8325 7.85034L59.0635 8.19531ZM59.3147 7.80975C59.3147 7.48507 59.0535 7.21111 58.722 7.21111C58.3904 7.21111 58.1292 7.47492 58.1292 7.80975C58.1292 8.14458 58.4005 8.40839 58.722 8.40839C59.0535 8.40839 59.3147 8.14458 59.3147 7.80975ZM58.0488 7.80975C58.0488 7.43433 58.3502 7.12994 58.732 7.12994C59.1037 7.12994 59.4051 7.43433 59.4051 7.80975C59.4051 8.19531 59.1037 8.4997 58.732 8.4997C58.3502 8.48956 58.0488 8.19531 58.0488 7.80975Z" fill="#003A5D"/>
</g>
</svg>
`;

export default randalls;
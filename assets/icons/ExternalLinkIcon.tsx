import { t } from "i18next";
import React from "react";
import { SvgXml } from "react-native-svg";

export const ExternalLinkIcon = ({ testID, size, color, accessibilityLabel = t('ada.externalLinkButton') }: { testID?: string, size?: number, color: string, accessibilityLabel?: string }) => {

    const icon = `<svg width="25" height="24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M5.5 3c-1.093 0-2 .907-2 2v14c0 1.093.907 2 2 2h14c1.093 0 2-.907 2-2v-7h-2v7h-14V5h7V3h-7Zm9 0v2h3.586l-9.293 9.293 1.414 1.414L19.5 6.414V10h2V3h-7Z" fill="${color}"/></svg>
`
    return <SvgXml
        testID={testID}
        width={size}
        height={size}
        xml={icon}
        accessible
        accessibilityLabel={accessibilityLabel}
    />;
};
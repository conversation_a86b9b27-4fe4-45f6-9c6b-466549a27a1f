import { t } from "i18next";
import React from "react";
import { SvgXml } from "react-native-svg";

export const BackIcon = ({ testID, size, color, accessibilityLabel = t('ada.backButton'), }: { testID?: string, size?: number, color: string, accessibilityLabel?: string; }) => {

    const icon = `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Arrow left">
<path id="Icon" d="M8.70711 17.7929C9.09763 18.1834 9.09763 18.8166 8.70711 19.2071C8.31658 19.5976 7.68342 19.5976 7.29289 19.2071L0.792893 12.7071C0.402369 12.3166 0.402369 11.6834 0.792893 11.2929L7.29289 4.79289C7.68342 4.40237 8.31658 4.40237 8.70711 4.79289C9.09763 5.18342 9.09763 5.81658 8.70711 6.20711L3.91421 11H22C22.5523 11 23 11.4477 23 12C23 12.5523 22.5523 13 22 13H3.91421L8.70711 17.7929Z" fill="${color}"/>
</g>
</svg>
`
    return <SvgXml
        testID={testID}
        width={size}
        height={size}
        xml={icon}
        accessible
        accessibilityLabel={accessibilityLabel} />;
};
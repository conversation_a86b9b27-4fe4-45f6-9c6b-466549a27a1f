import { t } from "i18next";
import React from "react";
import { SvgXml } from "react-native-svg";

export const InfoIcon = ({ testID, size }: { testID?: string, size?: number, color?: string }) => {

    const icon = `<svg width="41" height="40" viewBox="0 0 41 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M20.5 4.58333C11.9856 4.58333 5.08331 11.4856 5.08331 20C5.08331 28.5144 11.9856 35.4167 20.5 35.4167C29.0144 35.4167 35.9166 28.5144 35.9166 20C35.9166 11.4856 29.0144 4.58333 20.5 4.58333ZM2.58331 20C2.58331 10.1049 10.6049 2.08333 20.5 2.08333C30.3951 2.08333 38.4166 10.1049 38.4166 20C38.4166 29.8951 30.3951 37.9167 20.5 37.9167C10.6049 37.9167 2.58331 29.8951 2.58331 20Z" fill="#1F1E1E"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M20.5 10.4167C21.1903 10.4167 21.75 10.9763 21.75 11.6667V13C21.75 13.6903 21.1903 14.25 20.5 14.25C19.8096 14.25 19.25 13.6903 19.25 13V11.6667C19.25 10.9763 19.8096 10.4167 20.5 10.4167ZM20.5 16.5833C21.1903 16.5833 21.75 17.143 21.75 17.8333V28.6667C21.75 29.357 21.1903 29.9167 20.5 29.9167C19.8096 29.9167 19.25 29.357 19.25 28.6667V17.8333C19.25 17.143 19.8096 16.5833 20.5 16.5833Z" fill="#1F1E1E"/>
</svg>`
    return <SvgXml
        testID={testID}
        width={size}
        height={size}
        xml={icon}
        accessible={false} />;
};
import { t } from "i18next";
import React from "react";
import { SvgXml } from "react-native-svg";

export const WarningIcon = ({ testID, size, accessibilityLabel = t('ada.warning'), }: { testID?: string, size?: number, color?: string, accessibilityLabel?: string; }) => {

    const icon = `<svg width="41" height="40" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.809 4.792c2.085-3.611 7.297-3.611 9.382 0l13.712 23.75c2.085 3.61-.521 8.125-4.691 8.125H6.788c-4.17 0-6.776-4.514-4.691-8.125l13.712-23.75Zm7.217 1.25c-1.123-1.945-3.93-1.945-5.052 0L4.262 29.792c-1.123 1.944.28 4.375 2.526 4.375h27.424c2.245 0 3.649-2.43 2.526-4.375L23.026 6.042ZM20.5 11.25c.69 0 1.25.56 1.25 1.25v10a1.25 1.25 0 0 1-2.5 0v-10c0-.69.56-1.25 1.25-1.25Zm0 14.833c.69 0 1.25.56 1.25 1.25v1.334a1.25 1.25 0 0 1-2.5 0v-1.334c0-.69.56-1.25 1.25-1.25Z" fill="#1F1E1E"/></svg>`
    return <SvgXml
        testID={testID}
        width={size}
        height={size}
        xml={icon}
        accessible
        accessibilityLabel={accessibilityLabel} />;
};
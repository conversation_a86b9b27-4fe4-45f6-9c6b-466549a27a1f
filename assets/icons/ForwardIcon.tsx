import { t } from "i18next";
import React from "react";
import { SvgXml } from "react-native-svg";

export const ForwardIcon = ({ testID, size, color, accessibilityLabel = t('ada.forwardButton'), }: { testID?: string, size?: number, color: string, accessibilityLabel?: string; }) => {

    const icon = `<svg width="24" height="24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M15.293 6.207a1 1 0 0 1 1.414-1.414l6.5 6.5a1 1 0 0 1 0 1.414l-6.5 6.5a1 1 0 0 1-1.414-1.414L20.086 13H2a1 1 0 1 1 0-2h18.086l-4.793-4.793Z" fill="${color}"/></svg>`
    return <SvgXml
        testID={testID}
        width={size}
        height={size}
        xml={icon}
        accessible
        accessibilityLabel={accessibilityLabel} />;
};
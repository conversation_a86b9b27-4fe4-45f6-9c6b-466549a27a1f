
import React from "react";
import { SvgXml } from "react-native-svg";

export const CircularChevronLeft = ({ testID, size, color }: { testID?: string, size?: number, color: string }) => {

    const xmlIcon = `<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_12795_1471)">
<mask id="path-1-inside-1_12795_1471" fill="white">
<path d="M0 16C0 7.16344 7.16344 0 16 0C24.8366 0 32 7.16344 32 16C32 24.8366 24.8366 32 16 32C7.16344 32 0 24.8366 0 16Z"/>
</mask>
<path fill-rule="evenodd" clip-rule="evenodd" d="M18.5303 21.5303C18.8232 21.2374 18.8232 20.7626 18.5303 20.4697L14.0607 16L18.5303 11.5303C18.8232 11.2374 18.8232 10.7626 18.5303 10.4697C18.2374 10.1768 17.7626 10.1768 17.4697 10.4697L12.4697 15.4697C12.1768 15.7626 12.1768 16.2374 12.4697 16.5303L17.4697 21.5303C17.7626 21.8232 18.2374 21.8232 18.5303 21.5303Z" fill="${color}"/>
</g>
<path d="M16 31C7.71573 31 1 24.2843 1 16H-1C-1 25.3888 6.61116 33 16 33V31ZM31 16C31 24.2843 24.2843 31 16 31V33C25.3888 33 33 25.3888 33 16H31ZM16 1C24.2843 1 31 7.71573 31 16H33C33 6.61116 25.3888 -1 16 -1V1ZM16 -1C6.61116 -1 -1 6.61116 -1 16H1C1 7.71573 7.71573 1 16 1V-1Z" fill="${color}" mask="url(#path-1-inside-1_12795_1471)"/>
<defs>
<clipPath id="clip0_12795_1471">
<path d="M0 16C0 7.16344 7.16344 0 16 0C24.8366 0 32 7.16344 32 16C32 24.8366 24.8366 32 16 32C7.16344 32 0 24.8366 0 16Z" fill="white"/>
</clipPath>
</defs>
</svg>
`
    return <SvgXml
        testID={testID}
        width={size}
        height={size}
        xml={xmlIcon}
        accessible={false} />;
};

import React from "react";
import { SvgXml } from "react-native-svg";

export const ChevronRight = ({ testID, size, color }: { testID?: string, size?: number, color: string }) => {

    const xmlIcon = `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Chevron right">
<path id="Icon" fill-rule="evenodd" clip-rule="evenodd" d="M9.29289 5.29289C8.90237 5.68342 8.90237 6.31658 9.29289 6.70711L14.5858 12L9.29289 17.2929C8.90237 17.6834 8.90237 18.3166 9.29289 18.7071C9.68342 19.0976 10.3166 19.0976 10.7071 18.7071L16.7071 12.7071C17.0976 12.3166 17.0976 11.6834 16.7071 11.2929L10.7071 5.29289C10.3166 4.90237 9.68342 4.90237 9.29289 5.29289Z" fill="${color}"/>
</g>
</svg>
`
    return <SvgXml
        testID={testID}
        width={size}
        height={size}
        xml={xmlIcon}
        accessible={false} />;
};
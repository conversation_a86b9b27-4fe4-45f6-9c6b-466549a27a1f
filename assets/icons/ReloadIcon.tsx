import { t } from "i18next";
import React from "react";
import { SvgXml } from "react-native-svg";

export const ReloadIcon = ({ testID, size, color, accessibilityLabel = t('ada.refreshButton'), }: { testID?: string, size?: number, color: string, accessibilityLabel?: string; }) => {

    const icon = `<svg width="24" height="24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M20.009 10.734C19.75 1.99 7.849.266 4.345 7.852a.75.75 0 1 1-1.361-.628C6.944-1.35 20.27.034 21.442 9.802l.835-1.469a.75.75 0 0 1 1.304.741l-2.305 4.058a.75.75 0 0 1-1.247.087l-2.847-3.697a.75.75 0 0 1 1.188-.916l1.639 2.128ZM3.97 10.78a.75.75 0 0 0-1.246.088L.42 14.926a.75.75 0 1 0 1.304.74l.835-1.469c1.172 9.768 14.498 11.153 18.458 2.579a.75.75 0 1 0-1.361-.628c-3.504 7.586-15.406 5.86-15.664-2.882l1.639 2.127a.75.75 0 0 0 1.188-.915l-2.847-3.697Z" fill="${color}"/></svg>`
    return <SvgXml
        testID={testID}
        width={size}
        height={size}
        xml={icon}
        accessible
        accessibilityLabel={accessibilityLabel} />;
};
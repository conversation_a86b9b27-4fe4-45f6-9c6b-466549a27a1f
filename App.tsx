import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import React from "react";
import "react-native-gesture-handler";
import { ThemeProvider } from "react-native-paper";
import { Provider } from "react-redux";
import AppNavigator from "./app/navigation/AppNavigator";
import TabNavigator from "./app/navigation/TabNavigator";
import { store } from "./app/store";
const Tab = createBottomTabNavigator();

export default function App() {

  return (
    <Provider store={store}>
      <ThemeProvider>
        <AppNavigator />
      </ThemeProvider>
    </Provider>
  );
}

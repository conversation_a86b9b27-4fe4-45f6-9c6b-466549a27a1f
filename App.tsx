import React from "react";
import "react-native-gesture-handler";
import { ThemeProvider } from "react-native-paper";
import { Provider } from "react-redux";
import AppNavigator from "./app/navigation/AppNavigator";
import { store } from "./app/store";

export default function App() {

  return (
    <Provider store={store}>
      <ThemeProvider>
        <AppNavigator />
      </ThemeProvider>
    </Provider>
  );
}

export interface StatusPlaceHolderProps {
    source?: string;
    status?: string;
    showButton?: boolean;
    buttonText?: string;
    styles?: any;
    buttonAccessHint?: string;
    onPress?: () => void;
    subHeader?: string;
    isElevation?: boolean;
}

export interface EmptyStatusPlaceHolderProps {
    source?: string;
    titleText?: string;
    subTitleText?: string;
    descriptionText?: string;
}
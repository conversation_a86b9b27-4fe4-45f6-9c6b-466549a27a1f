{"translation": {"welcomeAssociate": "Welcome, associate!", "Home": "Home", "Growth": "Growth", "Community": "Community", "Schedule": "Schedule", "Support": "Support", "Languages": "Languages", "Resources": "Resources", "About": "About", "Bulletin": "Bulletin", "Profile": "Profile", "welcome": "Welcome", "Growth Screen": "Growth Screen", "Support Screen": "Support Screen", "Community Screen": "Community Screen", "Schedule Screen": "Schedule Screen", "Languages Screen": "Languages Screen", "Load Error": "Failed to load the webpage. Please try again later.", "Todos List": "Todos List", "LDAP Login Text": "Use your Microsft work account to sign in.", "login": "Sign in", "loginDescription": "Use your Punch-In Number or LDAP followed by @safeway.com as your sign in username.", "useOfApplication": "Use of this application (\"App\") is voluntary and is not a condition of employment. Associates using the App agree to comply with the", "Terms of Use": "Terms of Use", "and": "and", "Privacy Policy": "Privacy Policy", "Change Language Description": "Please select your language.", "employeeNumber": "Employee #", "employeePoints": "Points", "profileAndPreferences": "Profile and preferences", "manageProfileAndPreferences": "Manage your info and preferences.", "hrAndPayroll": "HR and payroll", "hrAndPayrollUpdates": "Get your paystubs and HR updates.", "settings": "Settings", "feedback": "App feedback", "deletingTitle": "Information about deleting data", "feedbackDescription": "Let us know how to improve the app.", "appSettings": "Manage language and app settings.", "language": "Language", "languageDescription": "Change the language of the app.", "changeAppLanguage": "Change app language", "save": "Save", "myProfileAndPreferences": "My profile and preferences", "deviceLocationSettings": "Device location settings", "locationAccessTurnedOn": "Location access turned on", "locationAccessTurnedOff": "Location access turned off", "legalTextAboutLocationAccess": "Enable location access so you can access key in-store work tools and trainings while onsite during work hours. We don’t use this information to share your data and you can adjust your settings at any time if you no longer want to share you location. You can learn more in our ", "switchButton": "Switch button", "doubleTapToToggleSetting": "double tap to toggle setting", "termsAndConditions": "Terms & Conditions", "notifications": "Notifications", "notificationSetting": "Notification setting", "legalAndAbout": "Legal & about", "privacyPolicy": "Privacy Policy", "andOur": " and our ", "signOut": "Sign out", "confirmSignOut": "Are you sure you want to sign out?", "yes": "Yes", "no": "No", "selectYourLanguage": "Select your language", "selectLanguageDescription": "Language selection will update the language throughout the app", "english": "English", "spanish": "Spanish", "french": "French", "updateLanguage": "Update language", "confirmLanguageUpdate": "Please confirm you would like to update your selected language", "languageUpdateChosen": "You have chosen to update your selected language to ", "confirmLanguageButton": "Confirm Language Update", "update": "Update", "cancel": "Cancel", "hrQuickLinks": "HR quick links", "benefitResources": "Benefit resources", "enrollmentAndChanges": "Enrollment & Changes", "myBenefits": "My benefits", "healthAndWellBeing": "Health & Well-being", "leaveOfAbsence": "Leave of Absence", "401KAndFinancialWellness": "401(k) & Financial Wellness", "benefitContacts": "Benefit contacts", "myACI": "MyACI", "openExternalLink": "{{link}} link opens in web view", "payroll": "Payroll", "getPaystubs": "Get paystubs", "updateDirectDepositAccount": "View your direct deposit", "changeTaxWithholdings": "Change tax withholdings", "lostOrDamagedCheck": "Lost or damaged check", "getTaxDocuments": "Get tax documents", "personalInfo": "Personal info", "fullName": "Full Name", "dateOfBirth": "Date of birth", "phoneNumber": "Phone number", "email": "Email", "address": "Address", "editInMyACI": "Edit in myACI", "accountSecurity": "Account security", "accountSecurityDescription": "Settings to secure your account", "accountSecurityAccessibilityHint": "Expand or collapse to view the account security", "setAnewPassword": "Set a new password", "setAnewPasswordAccessibilityHint": "Tapping will navigate you to the set new password webview screen", "resetPassword": "Set a new password", "forgotYourPassword": "Forgot your password?", "notAnAssociate": "Not an associate?", "iNeedHelpSigningIn": "I need help signing in", "helpIcon": "help icon", "forgotPasswordAccessibilityHint": "Tapping will navigate you to the forgot password webview screen", "passwordResetDescription": "Once you’ve set your new password, press the back arrow to complete the process.", "communicationPreferences": "Communication preferences", "scheduleChanges": "Schedule changes", "pushTextMessageEmail": "Push, text message, email", "storeCommunications": "Store communications", "divisionCommunications": "Division communications", "corporateCommunications": "Corporate communications", "appName": "Albertsons Companies Associate App", "version": "Version", "copyrightText": "© {year} Albertsons Companies, LLC", "Time to clock out": "Time to clock out", "Clocked in at {timeAgo} ago at {startTime}": "Clocked in at {timeAgo} ago at {startTime}", "You are on Shift": "You're on shift", "Clocked in at {time}": "Clocked in at {time}", "Clock out early": "Clock out early", "You are on lunch": "You're on lunch", "Started at {time}": "Started at {time}", "Time to clock in your shift has started": "Time to clock in, your shift has started", "End lunch": "End lunch", "You're on Shift": "You're on Shift", "It looks like your shift is over for today": "It looks like your shift is over for today", "Start lunch": "Start lunch", "It looks like your shift already started": "It looks like your shift already started", "It looks like your shift should have ended already": "It looks like your shift should have ended already", "Shift starting soon": "Shift starting soon", "Your next shift": "Your next shift", "Sign Out": "Sign Out", "Ready to start your lunch?": "Ready to start your lunch?", "You clocked in at {startTime}. Your lunch will begin at {lunchTime}": "You clocked in at {startTime}. Your lunch will begin at {lunchTime}", "Start my lunch": "Start my lunch", "Start your shift?": "Start your shift?", "Your clock in time will be logged as {time}": "Your clock in time will be logged as {time}", "Clock in": "Clock in", "Clock out": "Clock out", "Check schedule": "Check schedule", "Your shift starts in {time} minutes": "Your shift starts in {time} minutes", "Lunch started {timeDifference} minutes ago at {time}": "Lunch started {timeDifference} minutes ago at {time}", "Lunch started just now at {time}": "Lunch started just now at {time}", "Shift ending in {time} mins.": "Shift ending in {time} mins.", "Shift started {timeDifference} minutes ago": "Shift started {timeDifference} minutes ago", "Shift ended {timeDifference} ago": "Shift ended {timeDifference} ago", "HOUR": "hr", "MINUTE": "min", "HOURS": "hrs", "MINUTES": "mins", "changeLanguageCancelAccessHint": "Tapping will close the language update confirmation modal and keep the current language", "changeLanguageUpdateAccessHint": "Tapping will update the language and close the language update confirmation modal", "omniHeaderShiftStartHint": "Tapping will navigate you to the home screen", "goBack": "Go Back", "schedule": {"noShiftsScheduleed": "No shifts scheduled this week.", "noSchedulesReleased": "The schedule hasn’t been released yet. Check back later.", "noNetworkConnection": "Uh oh - we seem to be having trouble reaching our servers. Try again later.", "closeBtn": "Close", "retryButton": "Retry", "retryServerAccessibilityHint": "Tapping will attempt to reconnect to the server.", "retryNetworkAccessibilityHint": "Tapping will attempt to reconnect to the network.", "weeklyHoursWorked": "Weekly hours worked", "accuredPTObalance": "Accrued PTO balance", "hours": "hrs", "days": "days", "minutes": "mins", "viewHoursSummary": "View hours summary", "hideHoursSummary": "Hide hours summary", "toggleHoursSummaryHint": "Tapping will toggle the hours summary view.", "ClockedInAgoAt": "Clocked in {timeAgo} ago at {startTime}", "ClockedInAt": "Clocked in at {time}", "StartedAgoAt": "Started {timeAgo} ago at {startTime}", "previousWeek": "see previous week schedule button double tap to activate", "nextWeek": "See next week schedule button double tap to activate", "scheduledTime": "Scheduled from {startTimeStr} to {endTimeStr}, starting in {durationStr}", "completedTime": "Completed from {startTimeStr} to {endTimeStr}, lasting {durationStr}", "to": "to"}, "retryServerAccessibilityHint": "Tapping will attempt to reconnect to the server.", "completedShift": "Completed shift", "scheduledShift": "Scheduled shift", "missedPunch": "missed punch, warning submit your exception by {lastDay}", "submitResponse": "Submit your exception by {lastDay}", "date": "Thursday, October 3, 2024", "time": "12:00 PM to 8:00 PM", "worked": "worked", "starting": "Starting in", "location": "Safeway #123410020 NE 137th StKirkland, WA 98034", "manageSchedule": "Manage schedule", "questionIcon": "Question Icon", "calendarIcon": "Calendar Icon", "hoursIssue": "Issues with your hours?", "addToYourCalendar": "Add to your calendar", "completedDescription": "Contact your store director if you notice that the hours worked for this shift is different from what you actually worked.", "schedulesDescription": "Export this shift to your personal calendar. Please be aware, any shift changes made after you export will not update to your calendar.", "addToCalendarBtn": "Add to calendar", "scheduledAt": "Scheduled at", "weeklyHoursWorked": "Weekly hours worked", "accuredPTObalance": "Accrued PTO balance", "hours": "hrs", "days": "days", "minutes": "mins", "viewHoursSummary": "View hours summary button", "viewHoursSummaryHint": "View hours summary", "hideHoursSummary": "Hide hours summary", "toggleHoursSummaryHint": "Tapping will toggle the hours summary view.", "READMORE": "Read more", "POSTED": "Posted", "WHATSNEW": "What’s new", "VIEWALL": "View all", "BackToSignIn": "Back to sign in", "BackToSignInAccessibilityLabel": "Continue to press button, tap to sign in again", "SessionExpired": "Session expired", "AUTH_ERROR_DESC": "Like a sell-by date, but way less stressful. We signed you out for security purposes, but you can sign back in to continue.", "PROFILE_ERROR_DESC": "You successfully signed in but we couldn’t fetch your profile information. Press Retry and we’ll give it another shot.", "profileAlertTitle": "We’re having trouble retrieving your information.", "profileAlertDesc": "We couldn’t fetch your profile information. Press Retry and we’ll give it another shot.", "scheduleAlertDesc": "We couldn’t fetch your schedule information. Press Retry and we’ll give it another shot.", "onboarding": {"gladhere": "We’re glad you’re here!", "description": "We built this app with you in mind:\nto help you stay connected, manage your work, and grow in your role.\n\nWe will be adding more features soon so we look for more ways to better support you in your day-to-day.", "next": "Next", "whatYouCanExpect": "What you can expect!", "hrAndPayroll": "HR & Payroll", "ViewYourSchedule": "View your schedule", "ViewSchedule": "View schedule", "timeKeeping": "Time keeping", "hrAndPayrollDescription": "View your payroll activities, benefits, and HR docs all in one place.", "ViewYourScheduleDescription": "Keep track of your shifts, request time off, and ensure you’re where you need to be.", "timeKeepingDescription": "Keep track of your clock in/out times with ease.", "Communication": "Communication", "communicationDescription": "Send announcements to your direct reports and keep the lines of communication open in the app", "whatAssociatesExpect": "What associates can expect", "locationPermissionTitle": "Allow location access?", "locationPermissionDescription": "Enable location access so you can access key in-store work tools and trainings while onsite during work hours.", "allowLocation": "Allow location", "willDoThisLater": "I’ll do this later", "cancel": "Cancel", "continue": "Continue", "headsupTitle": "Just a heads up", "headsupText": "If you do this later you’ll be able to manually allow these permissions in your settings whenever you want.", "notificationsPermissionTitle": "Allow notifications?", "notificationsPermissionDescription": "Stay in the know with timely updates about shift changes, important in-store announcements, and recognition from your team. Turn on notifications to stay connected and never miss a beat.", "allowNotifications": "Allow notifications", "progress-bar": "step {currentStep} of {totalSteps}"}, "signInHelp": "Sign in help", "whatCanWeHelpWith": "What can we help with?", "weekOrNoNetwork": "Oops! Looks like you’re in a weak or no signal area. Please connect to a different network or try again.", "noServerConnection": "Uh oh - we seem to be having trouble reaching our servers. Try again later.", "signedOutHeading": "You’ve been signed out.", "havingTroubleRetrievingInformation": "We’re having trouble retrieving your information.", "signedOutDescription": "We ran into a problem and had to sign you out. Please sign in and try again.", "signInAgain": "Sign in again", "oopsWeRanIntoProblem": "Oops! We ran into a problem", "oops": "Oops!", "signInAgainAccessibilityHint": "Tapping will trigger the authentication flow again.", "accountLink": {"beforeYouBeginHeader": "Before you begin", "step1Of2": "Step 1 of 2", "confirmYourInfo": "Confirm your [Banner] for U™ member info.", "closeBtn": "Close", "beginBtn": "<PERSON><PERSON>", "continueToVerify": "Continue to verify", "changeMyMemberInfo": "Change my member info", "iAcnkowledgeLbl": "I acknowledge", "accLinkHeader": "What to know before linking your [<PERSON>] for U™ membership info.", "accLinkHeadersteponeuserinput": "[FirstName], let’s find your [Banner] for U™ member info.", "enterPhoneNumberEmail": "Enter the phone number or email associated with your member info.", "newToBanner": "New to [<PERSON>]?", "createAnAccount": "Create an account", "findmyForUMembership": "Find my membership", "doesThisLookCorrect": "Does this look correct to you?", "phoneNumber": "Phone number:", "email": "Email:", "phoneOrEmail": "Phone number or email ", "enterPhoneOrEmail": "Enter your phone number or email", "clubCardNumber": "Club Card number:", "accLinkDiscriptionOne": "[All text are placeholders] We use your phone number or email to search up your [<PERSON>] for U™ member info and compare your name to make sure the person info matches your employee profile.", "accLinkDiscriptionTwo": " If you choose to link your for U™ member info to your employee number, you’ll begin receiving associate-specific discounts and deals.", "accLinkDiscriptionThree": " Once your info is linked, we do not use your info for any other purpose except to show you associate-specific discounts and deals. More details in our Privacy Policy.", "step2": "Step 2 of 2", "didYouGetText": "Did you get our text?", "didYouGetTextEmail": "Did you get our email?", "enterCode": "Enter the six-digit verification code sent to:", "codeExpiresIn": "Verification code expires in {{time}}", "codeExpired": "The verification code has expired. Please request a new one.", "emailCode": "Email me a verification code instead", "textCode": "Text me a verification code instead", "resendCode": "Resend code", "pleaseWait": "Please wait {{second}} seconds before requesting another verification code.", "verify": "Verify", "allDone": "All done!", "youAreAllSet": "You're all set, [<PERSON><PERSON><PERSON>]!", "allDoneDiscriptionOne": "We’ve verified your information and are linking your account so you can get associate discounts.", "allDoneDiscriptionTwo": "It may take up to 48 hours to complete the linking process.", "otpInputLabel": "Verification code, text field", "alreadyLinkedHeader": "Your info is already linked", "alreadyLinkedDescription": "You should see associate discounts in the shopping app. If you’re not getting your discount, reach out to Customer Support at 877-723-3929 to help resolve the issue.", "underReviewTitle": "Under review", "enrollmentInReviewHeader": "Your enrollment is in review", "enrollmentInReviewDescription": "It can take about 72 hours to review and connect your associate discounts."}, "announcements": {"chips": {"All": "All", "Corporate": "Corporate"}, "announcementsMoreText": " more announcements posted today."}, "announcementsMoreTextAccessibilityHint": "Double tap to open", "announcementsPostedToday": " posted today", "viewAllAnnouncements": "view all new posts", "viewAllAnnouncementsAccessibilityHint": "Double tap to open", "announcementCarouselPagination": "Announcement carousel pagination", "announcementReadMoreADAHint": "Tapping to open announcement detail web page", "paginationDot": "{{index}} of {{total}}", "paginationDotHint": "Tap to navigate to {{index}} of {{total}} announcement post to this page.", "ontactyourstoredirectorifyouneedschedulechanges": "Contact your store director if you need schedule changes", "youAreOffTheClock": "You’re off the clock!", "relaxAndRecharge": "Relax and recharge, you deserve it.", "offTheClockDescription": "You can access all the app resources you need when you’re onsite and clocked in.", "profile": {"employeeId": "Employee id {{id}}", "rewardsButton": "Rewards {{points}} points button, double tap to view details", "linkingYourAccount": "Linking your account", "linkBannerForU": "Link [banner] for U™"}, "locationDisabled": {"locationPermission": "Allow location?", "turnOnLocationDescription": "To show you this content, allow the app to view precise location. You can grant this permission by pressing Allow location below.", "dontWantToGiveLocation": "If you don't want to give location, that’s ok!\nYou can always use the Associate App on your store's device.", "goToDeviceSettings": "Go to device settings", "preciseLocationNotFound": "We couldn’t find your precise location", "preciseDescription": "Turn on precise location in your device settings so we can serve up the best experience and show you the content you need to access.", "dontAllowDescription": "To show you the content you may want to access, allow the app to view precise location.", "allowLcationDeviceSettings": "Allow precise location in your device settings.", "openLocationModal": "Open OS modal for location capture", "navigateToDeviceSettings": "will navigate to device settings"}, "corporateError": {"warning": "Warning", "hi": "Hi", "errorMessage": "Backstage and Corporate teams have limited feature access at this time.", "description1": "For now, this app is a specially stocked aisle for our retail leaders and associates.", "description2": "As our inventory grows, we’ll be expanding access to all Albertsons Companies associates in corporate and supply chain, so stay tuned!", "continue": "Continue"}, "Error": "Error", "Unable to fetch profile data": "Unable to fetch profile data", "CelebrationBanner": "Celebration Banner", "CelebrationImage": "Celebration Image", "CelebrationHeading": "Celebration Heading", "CelebrationText": "Celebration Text", "DismissCelebrationCard": "Dismiss Celebration Card", "milestoneFirstHeading": "Cheers to your first year!", "milestoneFirstBody": "Lettuce celebrate! One year in and you’re already a key ingredient to our success at {{banner}}!", "milestoneMultiHeading": "Cheers to {{years}} years!", "milestoneMultiBody": "Aisle be the first to say {{years}} years is a big deal! Thanks for your hard work and dedication since {{year}}!", "workiversaryHeading": "Happy workiversary!", "workiversaryBody": "Your dedication is the secret sauce to our success! Thanks for being part of the team for {{years}} great years!", "birthdayHeading": "Happy birthday!", "birthdayBody": "It’s been another great trip around the sun, {{firstName}}! From all of us at {{banner}}, we wish you a very happy birthday!", "commingSoonFeature": "More features coming soon to Home.", "commingSoonFeatureDescription": "Our team is still working through the aisles to ensure we’ve picked every item on our list for a more complete app experience. Stay tuned!", "IllustrationAccecibilityLabel": "Illustration representing a feature that is coming soon", "BulletinEmptyView": {"emptyMessageTitle": "Bulletin is coming soon.", "emptyMessageSubTitle": "Our team is still working on this pie!", "emptyMessageDescription": "Like any good pie, our team is working to perfect the blend of filling and crust for a better app experience. Stay tuned as our team continues refining."}, "ResourcesEmptyView": {"emptyMessageTitle": "Resources is coming soon.", "emptyMessageSubTitle": "Our team is in the middle of baking and frosting up this cake.", "emptyMessageDescription": "Like any good cake, our team is working to get the ratio of buttercream-to-cake just right for a better app experience. Stay tuned as our team continues baking."}, "NotificationEmptyView": {"emptyMessageTitle": "Notifications is coming soon.", "emptyMessageSubTitle": "Enjoy the appetizer while our team cooks up the main course.", "emptyMessageDescription": "Like any good meal, our team is working to get the main course just right for a better app experience. Stay tuned as our team continues cooking."}, "ScheduleEmptyView": {"emptyMessageTitle": "Schedule is coming soon.", "emptyMessageSubTitle": "Our team is still baking this assortment of specialities.", "emptyMessageDescription": "Like any good croissant, our team is working to get the ratio of butter-to-dough just right for a better app experience. Stay tuned as our team continues baking."}, "comingSoon": "Coming soon", "appPrivacyPolicy": {"privacyPolicyHeader": "Associate Mobile App Privacy Policy", "lastUpdated": "Last updated: ", "updatedDate": "May 28, 2025", "description1": "This mobile app is made available to associates to access information such as HR docs, payroll, benefits, schedules, and time keeping tools. This notice describes how Albertsons Companies and its affiliates and subsidiaries (collectively “Albertsons,” “we,” or “us”) collect and process your personal data as an Associate Mobile App user.", "description2": "For more information about how we collect, use, and protect your personal data, please see our full", "associatePrivacyNotice": "Associate Privacy Notice", "description3": "This notice does not apply to personal data collected with your participation in Albertsons’ Loyalty Program. You can learn about our privacy practices associated with your Loyalty Account or when navigating our websites through our ", "privacyPolicy": "Privacy Policy", "aciPrivacyPolicy": "ACI Privacy Policy", "question1": "What is Personal Data?", "answer1_1": "We may collect or obtain the following types of personal data about you:", "answer1_2_header": "App Account Information: ", "answer1_2": "We use your Microsoft work account to sign in.", "answer1_3_header": "Precise Geolocation Data ", "answer1_3": "(if you enable location services) such as the GPS location data of your mobile device: Used to support certain app features that are available while you are onsite during work hours.", "answer1_4_header": "Device and Usage Data ", "answer1_4": "such as IP address, device ID, operating system, information about your interaction with the app, communications within the app, app usage patterns, performance diagnostics: Used to maintain and improve the app’s functionality and user experience.", "question2": "Categories of Personal Data We Collect Through the Mobile App", "answer2_1": "We may use your personal data for the following purposes:", "answer2_2_header": "For Managing Our Personnel. ", "answer2_2": "Managing work activities and personnel, including providing app features and associate discounts or rewards.", "answer2_3_header": "For Ensuring a Safe and Secure Environment.  ", "answer2_3": "Protecting and securing our IT infrastructure, telecommunications network, and other property; detecting and preventing fraud or theft.", "answer2_4_header": "For Compliance. ", "answer2_4": "Complying with any legal requirements or obligations; complying with requests from government or public authorities; complying with union requirements; responding to the legal process; pursuing legal rights and remedies; managing internal complaints or claims; complying with internal policies and procedures.", "question3": "How We Share Your Personal Data?", "answer3_1": "We may share your personal data by disclosing it to a service provider that processes personal data on our behalf to help us complete the above-listed purposes. We require all our service providers, by written contract, to keep your personal data confidential and to implement appropriate security measures to protect your personal data. We do not permit our service providers who process personal data on our behalf to use your personal data for their own purposes. We only permit them to use your personal data for specified purposes in accordance with our instructions.", "answer3_2": "We do not sell your personal data. And we do not share your personal data for marketing purposes, including targeted advertisements.", "question4": "Retaining Your Personal Data", "answer4_1": "Except as otherwise permitted, required by applicable law or regulation, to satisfy any legal or regulatory obligations, or to resolve disputes, we will retain your personal data (including the categories of personal data discussed in the sections above) for as long as necessary to fulfill the purposes for which we collected it.", "answer4_2": "In other words, we retain your personal data in accordance with our company records retention schedule. The records retention schedule accounts for the above factors. In determining an appropriate retention period for personal data, we also consider the amount, nature, and sensitivity of the personal data, the potential risk of harm from unauthorized use or disclosure of your personal data, the purposes for which we process your personal data, and whether those purposes can be achieved through other means.  When compelled to do so by law due to pending litigation, the Company may retain data beyond the record retention date.", "question5": "Questions?", "answer5_1": "If you have questions or comments about this Associate Mobile App Privacy Notice or other privacy-related matters, you may contact us as follows:", "question6": "Will This Notice Change?", "answer6": "Yes, this notice will be updated periodically. You can obtain a copy within your Mobile App settings at your convenience.", "mailingAddressHeader": "Mailing address:", "mailingAddress": "Albertsons Companies, Inc.\nAttn: Privacy Office\n250 Parkcenter Blvd.\nBoise, ID 83706", "emailHeader": "Email address:", "email": "<EMAIL>", "tollFreeNumberHeader": "Toll-free number:", "tollFreeNumber": "(*************"}, "appPrivacyNotice": {"associatePricacyNotice": "Associate Privacy Notice", "privacyNoticeDescription": "Albertsons Companies and its affiliates and subsidiaries collect and process your personal data as a current or former associate, temporary worker, or contractor in the employment context. If you are interested in what data is collected about you, as well as how that data is used, we encourage you to review the applicable associate privacy notice below.", "asoociatesInCalifornia": "Associates in California", "asoociatesInOtherStates": "Associates in other states", "questions": "If you have any questions or comments, please email us at", "email": "<EMAIL>."}, "accessibility": {"expandORCollapse": "Expand or collapse to view the benefit resources", "tappingWillOpenWebview": "Tapping will open the link in a web view", "tappingWillOpenBenefitResourcesLink": "Tapping will open the benefit resources link", "tappingWillOpenPayrollLink": "Tapping will navigate you to the Get paystubs screen"}, "loginApiError": {"errorTitle": "Our servers are a little shaky", "errorMessage": "Like a woobly shopping cart, we're having a little trouble getting things moving. You can Retry or come back later", "retryButton": "Retry", "cancelButton": "Cancel"}, "selectRoleAlert": {"title": "Confirm Your Role", "description": "Please select your role to continue.", "loginAsAssociate": "<PERSON><PERSON> as Associate", "loginAsLeader": "<PERSON><PERSON> as Leader", "loginAsSelf": "<PERSON><PERSON> as <PERSON>"}, "helpOptions": {"ldap": "Find my LDAP", "employeeId": "Find my employee ID", "resetPassword": "Forgot or reset my password", "other": "Other sign in issue"}, "ada": {"heading": "heading", "languageBtn": "Language, change the language of the app", "button": "button", "saveBtn": "save button", "unavailable": "unavailable", "doubeTapToSelect": "Double tap to select", "buttonHint": "Double tap to press the button", "doubleTapToEdit": "Double tap to edit", "doubleTapToCall": "<PERSON>, Double tap to call", "imageAlt": "Profile picture of ", "image": "image", "textField": "text field", "opensInWebView": "opens in web view", "continueToVerify": "Continue to verify", "changeMyMemberInfo": "Change my member info", "access_link_swipe_up_down_ios": "To access the link swipe up or down", "access_link_swipe_up_down_android": "Actions Available use tap with 3 fingers to view", "profileImage": "Profile image", "doubleTapToNavigate": "Double tap to navigate", "linkBannerForU": "Link [banner] for U Trademark", "companyLogo": "Albertsons Companies Logo", "linkOpenInWebview": "link opens in web view", "closeButton": "Close button", "beginButtonDisabledIos": "button, disabled", "forUImage": "For U logo", "doubleTapToActivate": "Double tap to activate", "manageScheduleBtn": "manage schedule info button, double tap to open", "backButton": "Back button", "signInHeading": "Sign in button", "englishlanguageButton": "Selected English language button", "languageButtonHint": "Double tap to change the language", "selectedRadioButtonLabel": "Selected {{label}} radio button {{index}} of {{total}}", "unSelectedRadioButtonLabel": "Unselected {{label}} radio button {{index}} of {{total}}", "selected": "Selected ", "unselected": "Unselected", "tabHeading": "{{tabName}} tab {{index}} of {{total}}", "selectedTabHeading": "Selected {{tabName}} tab {{index}} of {{total}}", "notificationButton": "Notification button", "notificationCount": "{{count}} new notifications, button", "radioButtonLabel": "{{label}} radio button {{index}} of {{total}}", "taptoReload": "Double tap to reload the web link", "refreshButton": "Refresh button", "continueButton": "Continue button", "feedbackTitleButton": "App feedback,", "feedbackDescriptionButton": "let us know how we can improve the app", "iosPrivacyNotice": "To access the link swipe up or down, Actions Available", "androidPrivacyNotice": "Actions available use tap with 3 fingers to view", "privacyPolicyHint": "to access the link swipe up or down", "showAssociateAppPrivacyNotice": "Associate Privacy Notice link, opens in web view", "showAssociateAppPrivacyPolicy": "Associate Privacy Policy link, opens in web view", "forwardButton": "Forward button", "externalLinkButton": "External link button, open in external browser", "warning": "Warning", "termsOfUse": "Terms of Use", "privacyPolicy": "Privacy Policy", "noPhoneNumberRegistered": "no phone number registered", "noEmailRegistered": "no email id registered", "checkScheduleBtn": "Check schedule button", "noShift": "${formattedDay}, no schedule shift", "todayShiftTitle": "Today's shift", "nextShiftTitle": "Your next shift", "scheduledTitle": "Scheduled ${formattedTimeRange}", "defaultTitle": "Shift", "completedShift": "${formattedDay}, shift completed from ${formattedTimeRange} button, double tap to view details", "todayShift": "${formattedDay}, today's shift scheduled from ${formattedTimeRange} button, double tap to view details", "nextShift": "${formattedDay}, next shift scheduled from ${formattedTimeRange} button, double tap to view details", "futureScheduled": "${formattedDay}, shift scheduled from ${formattedTimeRange} button, double tap to view details", "exception": "${formattedDay}, missed punch shift from ${formattedTimeRange} submit your exception for button, double tap to view details", "defaultShift": "Shift from ${formattedTimeRange} button, double tap to view details", "hideHoursSummary": "double tap to collapse", "viewHoursSummary": "double tap to expand", "hours": "hours"}, "welcomeSheet": {"viewCareers": "View Careers", "careers": "Careers", "title": "Built with care for our associates.", "description1": "Our Albertsons Companies Better Together Associate App is built to enhance our employee experience with supportive tools and resources.", "description2": "Interested in becoming an associate with Albertsons Companies? We’re always looking for folks who are passionate about food to bring their flavor to our stores and offices. Check out our careers page for openings and apply today!"}, "resourcesScreen": {"assignedLearning": "Assigned learning", "assignedLearningDesc": "For any required learning and voluntary training.", "journeys": "Journeys", "journeysDesc": "For new hire tasks and associate listening surveys.", "heading": "More resources to explore", "learning": "Learning", "learningDescription": "Get set for success with the training and tips you’ll need for your role. Access myACI Learning and Journeys here.", "sendFeedback": "Send feedback", "hrAndPaystubs": "HR & paystubs", "commingSoonFeature": "More features coming soon to Resources.", "commingSoonFeatureDescription": "Our team is still working through the aisles to ensure we’ve picked every item on our list for a more complete app experience. Stay tuned!"}, "errorPlaceholder": {"tryAgain": "Try again", "rateLimitErrorSubtitle": "We’re queuing up", "rateLimitError": "Like a packed checkout line the day before the Big Game, too many requests hit us at once. Our servers need a sec to catch up. Give it a moment and try again", "shakyServersSubtitle": "Our servers are a little shaky", "serverErrorDescription": "Like a wobbly shopping cart, we’re having a little trouble getting things moving. You can Retry or come back later.", "retry": "Retry", "connectionError": "Connection running low", "connectionErrorSubtitle": "Like the last loaf of bread on the shelf, your connection is almost out. \n \nTo access your app features, you can either connect to a different network or try refreshing by selecting"}, "accountLinkError": {"invalideEmailOrPhone": "There’s no [Banner] for U™ membership with this info. Double check your info or try a different phone number or email.", "invalidEmailOrPhone": "Phone number or email text field, [email], Double tap to edit.", "invalidFormate": "Please enter a valid phone number or email.", "invalidOTP": "This code is not correct. Double check the code you received or request a new one.", "phoneNumberNotAvailable": "Phone factor ID is missing in UCA."}, "networkError": {"networkErrorTitle": "Network problem", "networkErrorMessage": "We’re having trouble accessing our servers. Please check your mobile data or WiFi connection or move to an area with better connectivity before continuing.", "retryButton": "Retry", "cancelButton": "Cancel"}, "genericError": {"title": "Something went awry.", "message": "Oops! We're not sure what happened, but please <PERSON><PERSON> or come back later.", "leftCTA": "Close", "rightCTA": "Retry"}, "externalModal": {"heading": "You’re about to leave the app.", "subtext": "You may need to sign in again on your external browser for security purposes.", "continueInExternal": "Continue in external browser", "continueInExternalADALabel": "Continue in external browser link", "back": "Back"}, "serverErrors": {"400": {"title": "Our servers are a little shaky.", "message": "Like a wobbly shopping cart, we're having a little trouble getting things moving. You can Retry or come back later (Error 400).", "leftCTA": "Close", "rightCTA": "Retry"}, "401": {"title": "You've been signed out.", "message": "Like a sell-by date, but way more stressful. We signed you out for security purposes, but you can sign back in to continue (Error 401).", "rightCTA": "Sign in again"}, "403_profile": {"title": "You've been signed out.", "message": "Like a sell-by date, but way more stressful. We signed you out for security purposes, but you can sign back in to continue (Error 403).", "rightCTA": "Sign in again"}, "403": {"title": "Our servers are a little shaky.", "message": "Like a sell-by date, but way more stressful. We signed you out for security purposes, but you can sign back in to continue (Error 403).", "leftCTA": "Close", "rightCTA": "Retry"}, "404": {"title": "Our servers are a little shaky.", "message": "Like a wobbly shopping cart, we're having a little trouble getting things moving. You can Retry or come back later (<PERSON><PERSON>r 404).", "leftCTA": "Close", "rightCTA": "Retry"}, "429": {"title": "We're queuing up.", "message": "Like a packed checkout line the day before the Big Game, too many requests hit us at once. Our servers need a sec to catch up (Error 429).", "leftCTA": "Close", "rightCTA": "Retry"}, "500": {"title": "Our servers are a little shaky.", "message": "Like a wobbly shopping cart, we're having a little trouble getting things moving. You can Retry or come back later (Error 500).", "leftCTA": "Close", "rightCTA": "Retry"}, "502": {"title": "Our servers are a little shaky.", "message": "Like a wobbly shopping cart, we're having a little trouble getting things moving. You can Retry or come back later (Error 502).", "leftCTA": "Close", "rightCTA": "Retry"}, "503": {"title": "Our servers are a little shaky.", "message": "Like a wobbly shopping cart, we're having a little trouble getting things moving. You can Retry or come back later (Error 503).", "leftCTA": "Close", "rightCTA": "Retry"}, "504": {"title": "Our servers are a little shaky.", "message": "Like a wobbly shopping cart, we're having a little trouble getting things moving. You can Retry or come back later (Error 504).", "leftCTA": "Close", "rightCTA": "Retry"}}, "internalErrors": {"otpVerify": {"title": "That didn't work.", "message": "Like a wobbly shopping cart, we're having a little trouble sending you the verification code. You can Retry or come back later (<PERSON><PERSON>r O<PERSON>).", "leftCTA": "Close", "rightCTA": "Retry"}, "empdiscount": {"title": "Our servers are a little shaky.", "message": "Like a wobbly shopping cart, we're having a little trouble getting things moving. You can Retry or come back later (Discount API).", "leftCTA": "Close", "rightCTA": "Retry"}}, "deleteDisclosureSheet": {"title": "Important note about deleting data", "subTitle1": "App Data", "subTitle2": "Associate Data", "description1": "This app does not store any of your associate data.", "description2": "Your use of the app is voluntary and you can delete the app at any time. Deleting the app will delete all local app data and not your employee records.", "description3": " Because you are a current associate, we have legal and business operations obligations to maintain personal data related to your employment and activities as an associate. These obligations may continue even after your employment with the Company ends. Accordingly, we cannot delete your personal data until the applicable retention periods have expired.", "closeCTA": "Close"}, "testAccountAlert": {"title": "This part of the app relies on real employee info", "message": "This test account doesn’t include real employee data, you may see errors where live info would normally appear.", "okButton": "I understand"}}}
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

import en from "./en.json";
import es from "./es.json";
import { store } from "../app/store";

const resources = {
  en: en,
  es: es,
};

i18n
  .use(initReactI18next) // Initialize react-i18next
  .init({
    resources,
    lng: store.getState().language.id, // Use the language from Redux state
    fallbackLng: 'en', // Fallback language
    interpolation: {
      escapeValue: false, // React already does escaping
    },
  });

// Subscribe to Redux store to update i18next when the language changes
store.subscribe(() => {
  const language = store.getState().language.id;
  i18n.changeLanguage(language);
});

export { i18n };
import * as AnalyticsUtils from '../AnalyticsUtils';
import DeviceInfo from 'react-native-device-info';
import ABAuditEngine from '../ABAuditEngine';
import { Instrumentation } from '@appdynamics/react-native-agent';
import { Platform } from 'react-native';
import { getApp } from '@react-native-firebase/app';  // Import getApp to ensure it's available for mocking  
import { getAnalytics, logEvent, setUserId, setUserProperty, logScreenView } from '@react-native-firebase/analytics';

// Mock Firebase modular API
jest.mock('@react-native-firebase/app', () => ({
  getApp: jest.fn(() => ({})),
}));

jest.mock('@react-native-firebase/analytics', () => ({
  getAnalytics: jest.fn(() => ({})),
  logEvent: jest.fn(() => Promise.resolve()),
  setUserId: jest.fn(() => Promise.resolve()),
  setUserProperty: jest.fn(() => Promise.resolve()),
  logScreenView: jest.fn(() => Promise.resolve()),
}));

jest.mock('react-native', () => ({
  Platform: {
    OS: 'ios', // Default mock value,
    select: jest.fn((options) => options.ios), // Mock Platform.select
  },
}));

jest.mock('react-native-device-info', () => ({
  isEmulator: jest.fn(),
  getUniqueId: jest.fn(() => Promise.resolve('test-device-id')),
}));

jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(() => Promise.resolve(null)),
  setItem: jest.fn(() => Promise.resolve(null)),
}));

jest.mock('../ABAuditEngine', () => ({
  initialize: jest.fn(),
  startNextSession: jest.fn(),
  setUserData: jest.fn(),
}));

jest.mock('@appdynamics/react-native-agent', () => ({
  Instrumentation: {
    setUserData: jest.fn(),
  },
}));

describe('AnalyticsUtils', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    process.env = {}; // Reset environment variables
  });

  describe('startAppDInstrumentation', () => {
    it('should return early if IS_TEST is true', async () => {
      process.env.IS_TEST = 'true';
      await AnalyticsUtils.startAppDInstrumentation();
      expect(ABAuditEngine.initialize).not.toHaveBeenCalled();
    });

    it('should return early if appDId is missing', async () => {
      process.env.IS_TEST = 'false';
      jest.spyOn(AnalyticsUtils, 'getAppId').mockReturnValue(null); // Mock getAppId to return null
      await AnalyticsUtils.startAppDInstrumentation();
      expect(ABAuditEngine.initialize).toHaveBeenCalled();
    });

    it('should return early if running on an emulator', async () => {
      jest.spyOn(DeviceInfo, 'isEmulator').mockResolvedValue(true); // Mock isEmulator to return true
      await AnalyticsUtils.startAppDInstrumentation();
      expect(ABAuditEngine.initialize).not.toHaveBeenCalled();
    });
  });

  describe('setDeviceIDInAppD', () => {
    it('should set user GUID in AppDynamics session', async () => {
      await AnalyticsUtils.setDeviceIDInAppD();
      expect(ABAuditEngine.startNextSession).toHaveBeenCalled();
      expect(Instrumentation.setUserData).toHaveBeenCalled();
    });
  });

  describe('AnalyticsUtils.getApplicationName', () => {
    it('should return the correct application name for Android', () => {
      Platform.OS = 'android';
      const appName = AnalyticsUtils.getApplicationName();
      expect(appName).toBe('com.j4u.associateApp.android');
    });

    it('should return the correct application name for iOS', () => {
      Platform.OS = 'ios';
      const appName = AnalyticsUtils.getApplicationName();
      expect(appName).toBe('com.j4u.associateApp');
    });

    it('should return "Unknown OS" for an unexpected platform', () => {
      Platform.OS = 'windows';
      const appName = AnalyticsUtils.getApplicationName();
      expect(appName).toBe('Unknown OS');
    });
  });


});
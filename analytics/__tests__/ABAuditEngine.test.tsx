import ABAuditEngine from '../ABAuditEngine';
import { Instrumentation, LoggingLevel, ErrorSeverityLevel, BreadcrumbVisibility } from '@appdynamics/react-native-agent';

// analytics/AppD/ABAuditEngine.test.ts

jest.mock('@appdynamics/react-native-agent', () => ({
  Instrumentation: {
    start: jest.fn(),
    setUserData: jest.fn(),
    removeUserData: jest.fn(),
    reportError: jest.fn(),
    reportMetric: jest.fn(),
    leaveBreadcrumb: jest.fn(),
    startTimer: jest.fn(),
    stopTimer: jest.fn(),
    startNextSession: jest.fn(),
  },
  LoggingLevel: { VERBOSE: 'VERBOSE', INFO: 'INFO' },
  ErrorSeverityLevel: { WARNING: 'WARNING', CRITICAL: 'CRITICAL' },
  BreadcrumbVisibility: { CRASHES_AND_SESSIONS: 'CRASHES_AND_SESSIONS' },
}));

describe('ABAuditEngine', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return the same instance (singleton)', () => {
    const instance1 = ABAuditEngine;
    const instance2 = ABAuditEngine;
    expect(instance1).toBe(instance2);
  });

  it('should initialize the agent with correct config', () => {
    const appId = 'testAppId';
    const applicationName = 'testApp';
    const enableAppDLogs = true;

    ABAuditEngine.initialize(appId, applicationName, enableAppDLogs);

    expect(Instrumentation.start).toHaveBeenCalledWith({
      appKey: appId,
      applicationName,
      jsAgentEnabled: false,
      loggingLevel: LoggingLevel.VERBOSE,
      crashReportingEnabled: true, // Enable crash reporting by default
    });
  });

  it('should set user data and track keys', async () => {
    await ABAuditEngine.setUserData('key1', 'value1');
    expect(Instrumentation.setUserData).toHaveBeenCalledWith('key1', 'value1');
  });

  it('should remove user data and untrack keys', async () => {
    await ABAuditEngine.setUserData('key1', 'value1');
    await ABAuditEngine.removeUserData('key1');
    expect(Instrumentation.removeUserData).toHaveBeenCalledWith('key1');
  });

  it('should clear all user data', async () => {
    await ABAuditEngine.setUserData('key1', 'value1');
    await ABAuditEngine.setUserData('key2', 'value2');
    await ABAuditEngine.clearAllUserData();
    expect(Instrumentation.removeUserData).toHaveBeenCalledWith('key1');
    expect(Instrumentation.removeUserData).toHaveBeenCalledWith('key2');
  });

  it('should report exceptions', () => {
    const error = new Error('Test error');
    ABAuditEngine.reportException(error);
    expect(Instrumentation.reportError).toHaveBeenCalled();
  });

  it('should report errors with severity', () => {
    const error = new Error('Test error');
    ABAuditEngine.reportError(error, ErrorSeverityLevel.CRITICAL);
    expect(Instrumentation.reportError).toHaveBeenCalledWith(error, ErrorSeverityLevel.CRITICAL);
  });

  it('should leave breadcrumbs', () => {
    ABAuditEngine.leaveBreadcrumb('TestBreadcrumb');
    expect(Instrumentation.leaveBreadcrumb).toHaveBeenCalledWith('TestBreadcrumb');
  });

  it('should start and stop custom timers', () => {
    ABAuditEngine.customTimerStart('TestTimer');
    expect(Instrumentation.startTimer).toHaveBeenCalledWith('TestTimer');

    ABAuditEngine.customTimerEnd('TestTimer');
    expect(Instrumentation.stopTimer).toHaveBeenCalledWith('TestTimer');
  });

  it('should handle transactions', () => {
    ABAuditEngine.startTransaction('TestTransaction');
    expect(Instrumentation.startTimer).toHaveBeenCalledWith('TestTransaction');

    ABAuditEngine.cancelTransaction('TestTransaction');
    expect(Instrumentation.stopTimer).toHaveBeenCalledWith('TestTransaction');

    ABAuditEngine.endTransaction('TestTransaction');
    expect(Instrumentation.stopTimer).toHaveBeenCalledWith('TestTransaction');

    ABAuditEngine.failTransaction('TestTransaction');
    expect(Instrumentation.stopTimer).toHaveBeenCalledWith('TestTransaction');
  });

  it('should disable and enable crash reporting', () => {
    ABAuditEngine.crashReportingDisable(false);
    expect(Instrumentation.start).toHaveBeenCalled();
    expect(Instrumentation.leaveBreadcrumb).toHaveBeenCalledWith(
      'CrashReporting Disabled',
      BreadcrumbVisibility.CRASHES_AND_SESSIONS
    );

    ABAuditEngine.enableCrashReporting();
    expect(Instrumentation.start).toHaveBeenCalled();
  });

  it('should start the next session', () => {
    ABAuditEngine.startNextSession();
    expect(Instrumentation.startNextSession).toHaveBeenCalled();
  });
});
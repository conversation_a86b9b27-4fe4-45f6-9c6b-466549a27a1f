import { Platform } from 'react-native';
import DeviceInfo from 'react-native-device-info';
import { Instrumentation } from '@appdynamics/react-native-agent';
import ABAuditEngine from './ABAuditEngine';
import { AppDynamicsConstant } from './AppDynamicsConstants';
import * as AsyncStorage from '../app/store/AsyncStorage';
import { getApp } from '@react-native-firebase/app';
import { getAnalytics, logEvent, setUserId, setUserProperty, logScreenView } from '@react-native-firebase/analytics';
import { getLocationPermissionStatus, getNotificationsPermissionStatus } from '..//app/utils/helpers';
import { store } from '../app/store';
import { AssociateProfile } from '../app/misc/models/ProfileModal';
import { USER_ACTION } from './AnalyticsConstants';

const getAnalyticsInstance = () => {
  try {
    return getAnalytics(getApp());
  } catch (e) {
    // Jest environment fallback
    return {
      logEvent: () => { },
      setUserId: () => { },
      setUserProperty: () => { },
      logScreenView: () => { },
    };
  }
};
//Remove above one line

export const startAppDInstrumentation = async (): Promise<void> => {
  if (process.env.IS_TEST === 'true') {
    return;
  }

  const appDId = getAppId();
  if (!appDId || appDId.trim().length === 0) {
    return;
  }

  const isEmulator = await DeviceInfo.isEmulator();
  if (isEmulator) {
    return;
  }

  ABAuditEngine.initialize(appDId, getApplicationName(), false);
  setDeviceIDInAppD();
};

export const getAppId = (): string | null => {
  if (Platform.OS === 'ios') {
    return process.env.APPD_IOS || '';
  } else if (Platform.OS === 'android') {
    return process.env.APPD_ANDROID || '';
  } else {
    console.log('Running on an unknown platform');
    return null;
  }
};

export const setDeviceIDInAppD = async (): Promise<void> => {
  ABAuditEngine.startNextSession();
  const deviceId = await AsyncStorage.deviceId();
  Instrumentation.setUserData(AppDynamicsConstant.userID, deviceId);
};

export const getApplicationName = (): string => {
  if (Platform.OS === 'android') {
    return 'com.j4u.associateApp.android';
  } else if (Platform.OS === 'ios') {
    return 'com.j4u.associateApp';
  } else {
    return 'Unknown OS';
  }
}

//Constants for Analytics
export const ANALYTICS_METRIC_CONSTANTS = {
  SCREEN_VIEW: "screen_view",
  ASSOCIATE_APP: "associate-app",
  LOGGED_IN: "logged-in",
  LOGGED_OUT: "logged-out",
}

/**
 * Logs an analytics event with the specified event tag and parameters.
 *
 * This function utilizes the analytics library to record events for tracking
 * and analysis purposes. The `eventTag` represents the name of the event,
 * and `params` contains additional data associated with the event.
 *
 * @param eventTag - A string representing the name of the event to log.
 * @param params - An object containing key-value pairs of additional data
 *                 to associate with the event.
 * 
 * @returns A promise that resolves when the event has been logged successfully.
 */
export async function analyticsLog(eventTag: string, params: any) {
  await getAnalyticsInstance().logEvent(eventTag, params);
}

/**
 * Represents the details of an application's log for analytics purposes.
 *
 * @interface AppDetailsLog
 * @property {string} sdkversion - The version of the SDK used by the application.
 * @property {string} appID - The unique identifier of the application.
 * @property {string} locationSharing - Indicates whether location sharing is enabled or disabled.
 * @property {string} notificationAllowed - Indicates whether notifications are allowed or disabled.
 */
export interface AppDetailsLog {
  sdkversion: string;
  appID: string;
  locationSharing: string;
  notificationAllowed: string;
}


/**
 * Logs and retrieves details about the application, including SDK version,
 * app ID, location sharing status, and notification permission status.
 *
 * @returns {Promise<AppDetailsLog>} A promise that resolves to an object containing:
 * - `sdkversion` (string): The version of the SDK.
 * - `appID` (string): A unique identifier for the app, combining the platform and SDK version.
 * - `locationSharing` (string): The current status for the location sharing permission is always, while in use, once or never.
 * - `notificationAllowed` (string): The current status for the notification permission is allowed, limited, blocked.
 **/
export async function appDetailsLog(): Promise<AppDetailsLog> {

  const sdkversion = DeviceInfo.getVersion();
  const appID = `${Platform.OS}:${DeviceInfo.getVersion()}`;
  const locationSharing = await getLocationPermissionStatus();
  const notificationAllowed = await getNotificationsPermissionStatus();

  return {
    sdkversion,
    appID,
    locationSharing,
    notificationAllowed,
  }

}
/**
 * Represents the details of an analytics event log.
 * 
 * @interface EventDetailsLog
 * @property {string} event_category - The category of the event, used to group similar events.
 * @property {string} event_action - The specific action associated with the event.
 * @property {string} event_label - An optional label providing additional context for the event.
 */
export interface EventDetailsLog {
  event_category: string;
  event_action: string;
  event_label?: string;
}

/**
 * Logs a custom analytics event with the specified event tag and parameters.
 *
 * @param eventTag - A string representing the tag or name of the event to be logged.
 * @param params - An object containing the details of the event to be logged.
 * @returns A promise that resolves when the logging operation is complete.
 */
export async function appLogEvent(eventTag: string, params: EventDetailsLog) {
  analyticsLog(eventTag, params)
}

/**
 * Logs the details of an event by mapping the provided event parameters
 * to a structured event details log object.
 *
 * @param eventParams - An object containing the event parameters:
 *   - `catergory`: The category of the event.
 *   - `action`: The action associated with the event.
 *   - `label`: The label describing the event.
 *
 * @returns A promise that resolves to an `EventDetailsLog` object containing:
 *   - `event_category`: The category of the event.
 *   - `event_action`: The action associated with the event.
 *   - `event_label`: The label describing the event.
 */
export async function eventDetailsLog(eventParams: EventParams): Promise<EventDetailsLog> {
  const { catergory, action, label } = eventParams;
  return {
    event_category: catergory,
    event_action: action,
    event_label: label
  }
}
/**
 * Represents the parameters required for tracking a screen view event in analytics.
 *
 * @interface ScreenViewParams
 * 
 * @property {string} screen_name - The name of the screen being viewed.
 * @property {string} screen_class - The class or category of the screen.
 * @property {string} subsection1 - The primary subsection or context of the screen.
 * @property {string} [subsection2] - An optional secondary subsection or context of the screen.
 * @property {string} [subsection3] - An optional tertiary subsection or context of the screen.
 * @property {string} login_state - The login state of the user (e.g., logged in, logged out).
 * @property {string} event_label_link - The name of the event which is not as same as subsection2.
 * @property {string} event_category_link - The name of the event which is not as same as subsection1.
 */
export interface ScreenViewParams {
  subsection1: string;
  subsection2?: string;
  subsection3?: string;
  event_label_link?: string;
  event_category_link?: string;
}
/**
 * Represents the parameters for an analytics event.
 * 
 * @interface EventParams
 * @property {string} category - The category of the event, used to group related events.
 * @property {string} action - The specific action that occurred, describing the event.
 * @property {string} label - An optional label providing additional context for the event.
 */
export interface EventParams {
  catergory: string,
  action: string,
  label?: string
}

/**
 * Logs a screen view event with detailed information about the screen and app state.
 *
 * @param screenParams - An object containing details about the screen being viewed.
 *   - `subsection1`: The primary section of the screen (e.g., "Home").
 *   - `subsection2`: (Optional) The secondary subsection of the screen.
 *   - `subsection3`: (Optional) The tertiary subsection of the screen.
 * @param eventparams - An object containing additional event parameters to be logged.
 * @returns A promise that resolves when the screen view event has been logged.
 *
 * The function constructs a `screen_name` and `screen_class` based on the provided
 * `screenParams` and logs the event using `appLogEvent`. It also includes app-specific
 * details and event-specific details by merging the results of `appDetailsLog` and
 * `eventDetailsLog`. The `login_state` is dynamically determined based on the user's
 * profile state.
 */
export async function screenViewLog(screenParams: ScreenViewParams): Promise<void> {

  const appDetails = await appDetailsLog();
  const eventDetails = await eventDetailsLog({
    catergory: screenParams?.event_category_link ? screenParams?.event_category_link : screenParams?.subsection1,
    action: ANALYTICS_METRIC_CONSTANTS.SCREEN_VIEW,
    label: screenParams?.event_label_link ? screenParams?.event_label_link : screenParams.subsection2
  });
  let screenName = `${ANALYTICS_METRIC_CONSTANTS.ASSOCIATE_APP}:${screenParams.subsection1}`; // e.g., associate-app:Home
  if (screenParams.subsection2) {
    screenName += `:${screenParams.subsection2}`; // e.g., associate-app:Home:Subsection
  }
  if (screenParams.subsection3) {
    screenName += `:${screenParams.subsection3}`; // e.g., associate-app:Home:Subsection:Subsection3
  }

  await getAnalyticsInstance().logScreenView({
    screen_name: screenName,
    screen_class: screenName, // e.g., Home, Profile
    subsection1: screenParams.subsection1, // dynamic
    subsection2: screenParams.subsection2 ?? null, // dynamic
    subsection3: screenParams.subsection3 ?? null, // if applicable
    login_state: store.getState()?.profile?.profile != null ? ANALYTICS_METRIC_CONSTANTS.LOGGED_IN : ANALYTICS_METRIC_CONSTANTS.LOGGED_OUT, // dynamic

    // APP PARAMETERS
    ...appDetails,

    // EVENT PARAMETERS
    ...eventDetails
  });
}

/**
 * Sets user analytics properties based on the provided profile.
 *
 * @param profile - An optional `AssociateProfile` object containing user details.
 * 
 * This function initializes the analytics instance and sets various user properties
 * such as profile ID, banner, division, department, target user, user role, and user flag.
 * If no profile is provided, the properties are set to `null`.
 *
 * Notes:
 * - The `deviceId` is retrieved asynchronously from `AsyncStorage`.
 * - The `User_flag` property is currently marked with a TODO to verify its usage with the Tech team.
 */
// Set user properties
export async function setUserProperties(profile?: AssociateProfile) {
  const userId = await AsyncStorage.deviceId();
  const analyticsInstance = getAnalyticsInstance();
  await analyticsInstance.setUserId(profile ? userId : null);
  await analyticsInstance.setUserProperty("profile_id", profile ? userId : null);
  await analyticsInstance.setUserProperty("banner", profile ? profile?.banner ?? "" : null);
  await analyticsInstance.setUserProperty("division", profile ? profile?.divisionName : null);
  await analyticsInstance.setUserProperty("department", profile ? profile?.departmentName : null);
  await analyticsInstance.setUserProperty("target_user", profile ? profile.facilityType : null);
  await analyticsInstance.setUserProperty("User_role", profile ? profile.managementLevel : null);
  await analyticsInstance.setUserProperty("User_flag", "false"); //TODO: check with Tech team
}



export const userActionLogEvent = (categoty: string, action: string, label: string) => {
  appLogEvent(USER_ACTION, {
    event_category: categoty,
    event_action: action,
    event_label: label
  })
}
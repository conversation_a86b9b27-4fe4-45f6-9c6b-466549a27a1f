import { Instrumentation, LoggingLevel, ErrorSeverityLevel, BreadcrumbVisibility } from '@appdynamics/react-native-agent';
import { Platform } from 'react-native';

class ABAuditEngine {
  private static instance: ABAuditEngine;
  private userDataKeys: Set<string> = new Set();
  private agentConfig: any;

  private constructor() { }

  public static getInstance(): ABAuditEngine {
    if (!ABAuditEngine.instance) {
      ABAuditEngine.instance = new ABAuditEngine();
    }
    return ABAuditEngine.instance;
  }

  /**
   * Initialize AppDynamics Agent
   * @param appId - unique app Id registered with AppDynamics
   * @param applicationName - Current application name
   * @param enableAppDLogs - Enables the appD log
   */
  initialize(appId: string, applicationName: string, enableAppDLogs: boolean): void {
    const config = {
      appKey: appId,
      applicationName: applicationName,
      jsAgentEnabled: false,
      loggingLevel: enableAppDLogs ? LoggingLevel.VERBOSE : LoggingLevel.INFO,
      crashReportingEnabled: true, // Enable crash reporting by default
    };
    this.agentConfig = config;
    Instrumentation.start(config);
  }

  /**
   * Set user data and track the key
   * @param key - Key for the user data
   * @param value - Value for the user data
   */
  async setUserData(key: string, value: string | null): Promise<void> {
    if (!value) {
      await this.removeUserData(key);
      return;
    }
    this.userDataKeys.add(key);
    Instrumentation.setUserData(key, value);
  }

  /**
   * Remove user data for a specific key
   * @param key - Key of the user data to remove
   */
  async removeUserData(key: string): Promise<void> {
    this.userDataKeys.delete(key);
    Instrumentation.removeUserData(key);
  }

  /**
   * Clear all user data
   */
  async clearAllUserData(): Promise<void> {
    for (const key of this.userDataKeys) {
      await this.removeUserData(key);
    }
    this.userDataKeys.clear();
  }

  /**
   * Report any exception
   * @param exception - Identified exception
   */
  reportException(exception: Error): void {
    const userInfo = {
      reason: exception.message || 'unknown',
      callstack: exception.stack || '',
    };
    const error = new Error(JSON.stringify(userInfo));
    this.reportError(error);
  }

  /**
   * Report error
   * @param error - Identifier Error
   * @param severity - Severity of Error i.e Warning, Critical, Info
   */
  reportError(error: Error, severity: ErrorSeverityLevel = ErrorSeverityLevel.WARNING): void {
    Instrumentation.reportError(error, severity);
  }

  /**
   * Report metric
   * @param metricName - unique name registered with AppDynamics
   * @param value - Int to represent metric value
   */
  reportMetric(metricName: string, value: number): void {
    Instrumentation.reportMetric(metricName, value);
  }

  /**
   * Leave breadcrumb so that user's flow can be better tracked
   * @param crumb - Event
   */
  leaveBreadcrumb(crumb: string): void {
    Instrumentation.leaveBreadcrumb(crumb);
  }

  /**
   * Leave breadcrumb for a function so that user's flow can be better tracked
   * @param func - Event
   */
  leaveBreadcrumbForFunction(func: string): void {
    Instrumentation.leaveBreadcrumb(`error in: ${func}`);
  }

  /**
   * Leave breadcrumb with visibility mode
   * @param crumb - Event
   * @param mode - Breadcrumb visibility mode
   */
  leaveBreadcrumbWithMode(crumb: string, mode: BreadcrumbVisibility = BreadcrumbVisibility.CRASHES_AND_SESSIONS): void {
    Instrumentation.leaveBreadcrumb(crumb, mode);
  }

  /**
   * Leave breadcrumb with tag and URL
   * @param tag - Tag
   * @param url - URL
   */
  leaveBreadcrumbWithTagAndUrl(tag: string, url: string): void {
    Instrumentation.leaveBreadcrumb(`Handle${tag}: URL: ${url}`, BreadcrumbVisibility.CRASHES_AND_SESSIONS);
  }

  /**
   * Leave breadcrumb with tag and request data
   * @param tag - Tag
   * @param requestData - Request data
   */
  leaveBreadcrumbWithTagAndRequestData(tag: string, requestData: string): void {
    const requestBody = requestData.replace(/\//g, '');
    Instrumentation.leaveBreadcrumb(`Handle${tag}: Request Data: ${requestBody}`, BreadcrumbVisibility.CRASHES_AND_SESSIONS);
  }

  /**
   * Leave breadcrumb with tag and network error
   * @param tag - Tag
   * @param error - Network error
   */
  leaveBreadcrumbWithTagAndNetworkError(tag: string, error: Error): void {
    Instrumentation.leaveBreadcrumb(`Handle${tag}: NetworkError Code: ${error.message}, ErrorStr: ${error.stack}`, BreadcrumbVisibility.CRASHES_AND_SESSIONS);
  }

  /**
   * Start a custom timer
   * @param timer - Custom timer
   */
  customTimerStart(timer: string): void {
    Instrumentation.startTimer(timer);
  }

  /**
   * End a custom timer
   * @param timer - Custom timer
   */
  customTimerEnd(timer: string): void {
    Instrumentation.stopTimer(timer);
  }

  /**
   * Start a transaction
   * @param transaction - User's Transaction
   */
  startTransaction(transaction: string): void {
    Instrumentation.leaveBreadcrumb(`Transaction #Begin #Name:${transaction}`, BreadcrumbVisibility.CRASHES_AND_SESSIONS);
    Instrumentation.startTimer(transaction);
  }

  /**
   * Cancel a transaction
   * @param transaction - User's Transaction
   */
  cancelTransaction(transaction: string): void {
    Instrumentation.leaveBreadcrumb(`Transaction #Cancel #Name:${transaction}`, BreadcrumbVisibility.CRASHES_AND_SESSIONS);
    Instrumentation.stopTimer(transaction);
  }

  /**
   * End a transaction
   * @param transaction - User's Transaction
   */
  endTransaction(transaction: string): void {
    Instrumentation.leaveBreadcrumb(`Transaction #End #Name:${transaction}`, BreadcrumbVisibility.CRASHES_AND_SESSIONS);
    Instrumentation.stopTimer(transaction);
  }

  /**
   * Fail a transaction
   * @param transaction - User's Transaction
   */
  failTransaction(transaction: string): void {
    Instrumentation.leaveBreadcrumb(`Transaction #Fail #Name:${transaction}`, BreadcrumbVisibility.CRASHES_AND_SESSIONS);
    Instrumentation.stopTimer(transaction);
  }

  /**
   * Disable crash reporting
   * @param enable - Boolean to enable or disable crash reporting
   */
  crashReportingDisable(enable: boolean = false): void {
    if (this.agentConfig) {
      this.agentConfig.crashReportingEnabled = enable;
      Instrumentation.start(this.agentConfig);
      Instrumentation.leaveBreadcrumb('CrashReporting Disabled', BreadcrumbVisibility.CRASHES_AND_SESSIONS);
    } else {
      Instrumentation.leaveBreadcrumb('CrashReporting Not able to Disable', BreadcrumbVisibility.CRASHES_AND_SESSIONS);
    }
  }

  /**
   * Enable crash reporting
   */
  enableCrashReporting(): void {
    this.crashReportingDisable(true);
  }

  /**
   * Start next session and end the current session
   */
  startNextSession(): void {
    Instrumentation.startNextSession();
  }
}

export default ABAuditEngine.getInstance();
export const LOGIN_ANALYTICS = {
  EVENT_CATEGORY: 'user_login',
  EVENT_ACTION: 'confirm',
  EVENT_LABEL: 'logout',
  VIEW_ALL_ABOVE_CAROUSEL: 'whats-new-view-all-abv-crosl',
  VIEW_ALL_CAROUSEL_CARD: 'whats-new-view-all-crosl-card',
  HOM<PERSON>: 'home',
  WHATS_NEW: 'whats-new',
  CONNECTION_ERROR: 'connection-error',
  CAROUSEL: 'carousel',
  ANNOUNCEMENTS_CAROUSEL: 'annoucements-carousel',
  ANNOUNCEMENTS_WHATS_NEW: 'annoucements-whats-new',
  ANNOUNCEMENTS: 'annoucements',
  FORGOT_PASSWORD: 'password-reset',
  ATTMEPT: 'attempt',
  FORGOT_LABEL: 'login-screen',
  WEBVIEW: 'webview',
  FORGOT_PWD_LINK: 'forgot-pwd-link',
  BACK: 'back',
  <PERSON><PERSON><PERSON><PERSON>: 'login',
  REFRESH: 'refresh',
  NOTIFICATION: 'notification',
  FORGOT_PASSWORD_SCREEN: 'password-reset-screen',
  MY_PROFILE_PREFERENCES: 'my-profile-preferences',
  SET_NEW_PWD_LINK: 'set-new-pwd',
  ACCOUNT_SECURITY_PWD_LABEL: 'acct-security-set-new-pwd',
  SESSION_EXPIRED: 'session-expired',
  SESSION_EXPIRED_SCREEN: 'session-expired-screen',
  BACK_TO_SIGN_IN: 'back-to-signin',
  CONNECTION_ERROR_REFRESH_CLICK: 'connection-error-refresh-click',
  LINK_NOT_ASSOCIATE: 'link|not-associate',
  LINK_LANGUAGE: 'link|language',
  LINK_NEED_HELP_SIGNING_IN: 'link|need-help-signing-in',
  LOGIN_SCREEN: 'login-screen',
  NOT_ASSOCIATE_MODAL: 'not-associate-modal',
  LINK_VIEW_CAREER: 'link|view-career',
  LANGUAGE_SELECTION_MODAL: 'language-selection-modal',
  LOGIN_HELP: 'login-help',
  LINK_FIND_MY_LDAP: 'link|find-my-ldap',
  LINK_FIND_MY_EMPLOYEE_ID: 'link|find-my-employeeID',
  LINK_FORGET_OR_RESET_MY_PASSWORD: 'link|forget-or-reset-my-password',
  LINK_OTHER_SIGN_IN_ISSUES: 'link|other-sign-in-issues',
  FIND_MY_EMPLOYEE_ID: 'find-my-employee-id',
  FIND_MY_LDAP: 'find-my-ldap',
  FORGOT_OR_RESET_PASSWORD: 'forgot-or-reset-password',
  OTHER_SIGN_IN_ISSUES: 'other-sign-in-issues',
  FIND_MY_LDAP_SCREEN: 'find-my-ldap-screen',
  FIND_MY_EMPLOYEEID_SCREEN: 'find-my-employeeID-screen',
  FORGET_OR_RESET_MY_PASSWORD_SCREEN: 'forget-or-reset-my-password-screen',
  OTHER_SIGN_IN_ISSUES_SCREEN: 'other-sign-in-issues-screen',
  MODAL: 'modal',
  CAREERS: 'careers',
  UPDATE_LANGUAGE: 'link|update-language-[LANGUAGE]',
};

export const PROFILE_ANALYTICS = {
  PROFILE_PAGE_TITLE: 'Profile',
  SETTINGS_PAGE_TITLE: 'Settings',
  PREFERENCE_PAGE_TITLE: 'Profile Preferences',
  LEGAL_AND_ABOUT: 'legal-and-about',
  PRIVACY_AND_POLICY: 'about-privacy',
  SETTINGS_TOGGLE_ON: 'device location sharing-on',
  SETTINGS_TOGGLE_OFF: 'device location sharing-off',
  PROFILE: 'profile',
  SETTINGS: 'settings',
  BENEFIT_RESOURCES_ANALYTICS: 'benefit-resources',
  BENEFIT_RESOURCES_ANALYTICS_EXPAND: 'expand',
  BENEFIT_RESOURCES_ANALYTICS_COLLAPSE: 'collapse',
  HR_AND_PAYROLL: 'hr-payroll',
  HR: 'hr',
  PAYROLL: 'payroll',
  LINKFORU: 'link|[banner]-for-utm',
  APP_FEEDBACK: 'app-feedback',
  APP_FEEDBACK_REFRESH_ACTION: 'refresh',
};

export const APP_ANALYTICS = {
  EVENT_CATEGORY: 'app open',
  EVENT_ACTION: 'icon',
  EVENT_LABEL: 'icon',
  ACTION: 'app_open',
};

export const SIGNIN_ANALYTICS = {
  SIGNIN_EVENT_CATEGORY: 'user_login',
  SIGNIN_EVENT_ACTION: 'attempt',
  SIGNIN_EVENT_LABEL: 'sign in with microsoft',
  SIGNIN_SCREEN_VIEW: 'login',
  TERMS_EVENT_ACTION: 'Terms of use',
  PRIVACY_EVENT_ACTION: 'Privacy Policy',
  TERMS_PRIVACY_EVENT_LABEL: 'links',
  LOGIN_HELP_EVENT_LABEL: 'login-help',
  LINK_FIND_MY_LDAP: 'link|find-my-ldap',
  LINK_FIND_MY_EMPLOYEE_ID: 'link|find-my-employeeID',
  LINK_FORGET_OR_RESET_MY_PASSWORD: 'link|forget-or-reset-my-password',
  LINK_OTHER_SIGN_IN_ISSUES: 'link|other-sign-in-issues',
  FIND_MY_LDAP: 'find-my-ldap',
  FIND_MY_EMPLOYEE_ID: 'find-my-employee-id',
  FORGOT_OR_RESET_PASSWORD: 'forgot-or-reset-password',
  OTHER_SIGN_IN_ISSUES: 'other-sign-in-issues',
  LINK_REFRESH: 'link|refresh',
  FIND_MY_LDAP_SCREEN: 'find-my-ldap-screen',
  FIND_MY_EMPLOYEEID_SCREEN: 'find-my-employeeID-screen',
  FORGET_OR_RESET_MY_PASSWORD_SCREEN: 'forget-or-reset-my-password-screen',
  OTHER_SIGN_IN_ISSUES_SCREEN: 'other-sign-in-issues-screen',
};

export const BUTTON_NAV_ANALYTICS = {
  EVENT_CATEGORY: 'navigation',
  EVENT_LABEL: 'bottom navigation',
  HOME_TAB_ACTION: 'home',
  BULLETIN_TAB_ACTION: 'bulletin',
  RESOURCES_TAB_ACTION: 'resources',
  SCHEDULE_TAB_ACTION: 'schedule',
  PROFILE_TAB_ACTION: 'profile',
};

export const HOME_ANALYTICS = {
  HOME_PAGE_VIEW: 'home',
};

export const ONBOARDING_ANALYTICS = {
  ONBOARDIN: 'onboarding',
  STEP_ONE: 'step-1',
  STEP_TWO_A: 'step-2a',
  STEP_TWO_B: 'step-2b',
  STEP_THREE: 'step-3',
  STEP_FOUR: 'step-4',
  ACTION_EVENT_LOCATION_ALLOW: 'allow-location',
  ACTION_EVENT_LOCATION_DENY: 'deny-location',
  ACTION_EVENT_NOTIFICATION_ALLOW: 'allow-notification',
  ACTION_EVENT_NOTIFICATION_DENY: 'deny-notification',
  ACTION_EVVENT_CLOSE: 'close',
  ACTION_EVENT_CONTINUE: 'continue',
  EVENT_LABEL_LOCATION_MODAL: 'onboarding-location-modal',
  EVENT_LABEL_NOTIFICATION_MODAL: 'onboarding-notiication-modal',
};

export const LOYALTY_ANALYTICS = {
  LOYALTY_LINK_CONSENT: 'loyalty-link-consent',
  LINK_CLOSE_X: 'link|close-x',
  LINK_CONSENT_CHECKBOX_SELECTED: 'link|consent-checkbox-selected',
  LINK_CLOSE_CTA: 'link|close-cta',
  LINK_BEGIN: 'link|begin',
  LOYALTY_LINK_STEP1: 'loyalty-link-step1',
  LINK_CREATE_ACCOUNT: 'link|create-account',
  LINK_FIND_MY_MEMBERSHIP: 'link|find-my-membership',
  LOYALTY_LINK_CONFIRMATION: 'loyalty-link-confirmation',
  LINK_CONTINUE_VERIFY: 'link|continue-verify',
  LINK_CHANGE_MEMBER_INFO: 'link|change-member-info',
  LOYALTY_LINK_STEP2: 'loyalty-link-step2',
  LINK_EMAIL_VERIFICATION_CODE: 'link|email-verification-code',
  LINK_MOBILE_VERIFICATION_CODE: 'link|mobile-verification-code',
  LINK_VERIFY: 'link|verify',
  LOYALTY_ALREADY_LINKED: 'loyalty-already-linked',
  LOYALTY_LINKING_IN_REVIEW: 'loyalty-linking-in-review',
  ERRORS: {
    ENTERED_EMAIL_OR_PHONE_NOT_LINKED: 'email-address-or-phone-number-not-connected-to-account',
    INCORRECT_EMAIL_VERIFICATION_CODE: 'email-incorrect-verification-code',
    INCORRECT_PHONE_VERIFICATION_CODE: 'phone-incorrect-verification-code',
  },
  LOYALTY_CREATE_ACCOUNT_CONFIRMATION: 'loyalty-create-account-confirmation',
  LINK_CONTINUE: 'link|continue',
  LOYALTY: 'loyalty',
  LOYALTY_CREATE_ACCOUNT_WEBVIEW: 'create-account-webview',
  LOYALTY_BACK_ARROW: 'link|back-arrow',
  LOYALTY_REFRESH_ICON: 'link|refresh-icon',
};

export const RESOURCES_ANALYTICS = {
  LEARNING: 'link|learning',
  SEND_FEEDBACK: 'link|send-feedback',
  HR_AND_PAYSTUBS: 'link|hr-paystubs',
  ASSIGNED_JOURNEYS: 'link|assigned-journeys',
  JOURNEYS: 'link|journeys',
  RESOURCES_CATEGORY: 'resources',
  RESOURCES_LABEL: 'more-resources-to-explore',
  LEARNING_EVENT_LABEL: 'learning',
  RESOURCES_OFF_THE_CLOCK: 'resources-off-the-clock',
};

export const CELEBRATION_ANALYTICS = {
  HOME_EVENT_LABEL: "home-personalized-message-",
  PROFILE_EVENT_LABEL: "profile-personalized-message-"
};

export const SCHEDULE_ANALYTICS = {
  SCHEDULE_EVENT_LABEL: "schedule",
  SCHEDULE_CATEGORY: "schedule",
  PREVIOUS: 'previous',
  NEXT: 'next',
  WEEK_SELECTOR_LABEL: 'week-selector',
  CLOSE: 'link|close',
  ADD_TO_CALENDAR: 'link|calendar',
  MANAGE_SCHEDULE_INFO_BUG: 'link|manage-schedule-infobug',
  SHIFT: 'shift -',
  SHIFT_SELECTOR_LABEL: 'shift-selector',
  SCHEDULE_SHIFT_CATEGORY: 'modal',
  SCHEDULE_ACTION: 'view',
  SCHEDULE_SHIFT_SHEET_LABEL: 'scheduled-shift-sheet',
  SCHEDULE_SHIFT_CARDS_LABEL: 'shift-cards',
  SCHEDULE_NO_SHIFTS_CATEGORY: 'schedule-no-shifts',
  SCHEDULE_NOT_RELEASED: 'schedule-not-released'
};

export const EVENT_CATEGORY_MODAL = 'modal';
export const EVENT_ACTION_VIEW = 'view';
export const USER_ACTION = 'user_action';
export const EVENT_CATEGORY_ERROR = 'error';

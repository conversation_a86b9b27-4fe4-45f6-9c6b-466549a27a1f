/* eslint-disable @typescript-eslint/no-explicit-any */
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { useNavigation } from '@react-navigation/native';
import { Text, useTheme } from 'pantry-design-system';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { AccessibilityInfo, AppState, Platform, TouchableOpacity } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { SvgXml } from 'react-native-svg';
import { useDispatch, useSelector } from 'react-redux';

import { BUTTON_NAV_ANALYTICS } from '../../analytics/AnalyticsConstants';
import { userActionLogEvent } from '../../analytics/AnalyticsUtils';
import {
  calendar,
  calendarSelected,
  home,
  homeSelected,
  profile,
  profileSelected,
  resource,
  resourceSelected,
  bulletin,
  bulletinSelected,
} from '../../assets/images/svg/TabbarIcons';
import AppHeader from '../../components/AppHeader';
import LocationGoverned from '../../components/locationGoverned';
import CommunityScreen from '../features/community/community';
import HomeHeaderRight from '../features/home/<USER>';
import ProfileStackScreen from '../features/profile';
import ScheduleScreen from '../features/schedule/schedule';
import {
  BOTTOM_TABS,
  LOCATION_STATE,
  SCREENS,
  SHIFT_ENDING_DURATION,
  TEXT_SIZE,
  TEXT_WEIGHT,
  ImportantForAccessibility
} from '../shared/constants';
import { fetchfeatureFlagRequest } from '../store/reducers/featureFlagSlice';
import { setPreciseLocationGranted } from '../store/reducers/locationAccessSlice';
import { setUserInStore } from '../store/reducers/profileSlice';
import {
  setDurationInMinutes,
  setLateClockIn,
  setLateClockOut,
  setLunchDurationInMinutes,
  setShiftEndSession,
} from '../store/reducers/shiftSlice';
import { checkLocationPermission } from '../utils/helpers';
import { calculateTimeDiffinMinutes, getCurrentTime } from '../utils/TimeUtils';

import HomeStackNavigator from './HomeStackNavigator';
import getStyles from './styles';

import type { LocationAccuracy } from '../store/reducers/locationAccessSlice';
import ResourcesStackNavigator from './ResourcesStackNavigator';
import { useAccessibilityFocus } from '../providers/AccessibilityFocus';

const Tab = createBottomTabNavigator();

export default function TabNavigator(): React.ReactNode {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const navigation = useNavigation(); // Navigation hook
  const insets = useSafeAreaInsets(); // Safe area insets hook

  const { theme } = useTheme(); // Fething themes from ThemeProvider
  const styles = getStyles(theme); // Fetching styles based on theme

  const { shiftStartTime, shiftEndTime, lunchStartTime } = useSelector(
    (state: any) => state?.shift,
  );
  const preventTabsAccessibility = useSelector(
    (state: any) => state?.accessibility?.preventTabsAccessibility
  );
  const isTablet = useSelector((state: any) => state.deviceInfo?.isTablet);
  const isLandscape = useSelector((state: any) => state.deviceInfo.isLandscape);

  const [permissionStatus, setPermissionStatus] = useState<LocationAccuracy>('denied');
  const profileData = useSelector((state: any) => state.profile);
  const { resetNavigation } = useAccessibilityFocus();

  /**
   * Timer management logic within a `useEffect` hook for periodic updates.
   *
   * This hook sets up an interval timer that triggers every minute and performs the following:
   * 1. Calculates the remaining time for the shift to start, lunch to begin, and shift to end.
   * 2. Updates the Redux store with the calculated durations for shift start, lunch, and shift end.
   *
   * - **Timer Initialization**: At the beginning of the effect, the current second value is calculated, and the timer is set to trigger after the remaining seconds in the current minute.
   * - **Interval Setup**: After the first update, an interval is set to trigger the `updateTimer` function every minute.
   *
   * The hook also performs cleanup by clearing the interval when the component is unmounted or if any of the dependencies change.
   *
   * @see {setTimeout} for the delay in initiating the timer.
   * @see {setInterval} for periodic updates every minute.
   * @see {clearInterval} for cleaning up the timer when the component is unmounted or dependencies change.
   */
  let timer: NodeJS.Timeout;

  useEffect(() => {
    const current = new Date();
    const seconds = current.getSeconds();
    const remainingSeconds = 60 - seconds; // Remaining seconds in the current minute

    // Set a timeout to trigger the first update after the remaining time in the current minute
    setTimeout(() => {
      updateTimer(shiftStartTime, lunchStartTime, shiftEndTime); // First update when the timer is set
      if (timer) clearInterval(timer); // Clear any existing interval if necessary

      // Set an interval to update the timer every minute
      timer = setInterval(
        () => {
          updateTimer(shiftStartTime, lunchStartTime, shiftEndTime);
        },
        1 * 60 * 1000,
      ); // 1 minute in milliseconds
    }, remainingSeconds * 1000); // Delay the first update by the remaining seconds in the current minute

    // Cleanup function to clear the interval when the component is unmounted or dependencies change
    return () => clearInterval(timer);
  }, [shiftStartTime, lunchStartTime, shiftEndTime]); // Re-run the effect if any of the dependencies change

  useEffect(() => {
    const subscription = AppState.addEventListener('change', (nextAppState) => {
      if (nextAppState === 'active') {
        checkLocationPermission()
          .then((permissionStatus) => {
            dispatch(setPreciseLocationGranted(permissionStatus));
            setPermissionStatus(permissionStatus);
          })
          .catch((_error) => { });

        // Fetch feature flags when the app becomes active
        dispatch(
          fetchfeatureFlagRequest({
            banner: profileData?.banner || 'albertsons',
          }),
        );
      }
    });

    return () => {
      subscription.remove();
    };
  }, []);

  useEffect(() => {
    checkLocationPermission()
      .then((permissionStatus) => {
        dispatch(setPreciseLocationGranted(permissionStatus));
        setPermissionStatus(permissionStatus);
        if (permissionStatus !== LOCATION_STATE.PRECISE) {
          dispatch(setUserInStore(false));
        }
      })
      .catch((_error) => { });
  }, []);

  /**
   * Updates the timer by calculating the durations for the shift start, lunch, and shift end times.
   *
   * This function:
   * 1. Calculates the duration from the current time to the shift start time and dispatches it to the Redux store.
   * 2. Calculates the duration from the current time to the lunch start time and dispatches it to the Redux store.
   * 3. Calculates the duration from the current time to the shift end time and dispatches the remaining minutes to indicate if the shift is ending soon.
   *
   * @param {string} shiftStartTime - The formatted start time of the shift (e.g., "9:00 AM").
   * @param {string} lunchStartTime - The formatted start time of lunch (e.g., "1:00 PM").
   * @param {string} shiftEndTime - The formatted end time of the shift (e.g., "5:00 PM").
   *
   * @returns {void}
   *
   * @see {getCurrentTime} to get the current time for comparison.
   * @see {calculateTimeDiffinMinutes} to calculate the difference in minutes between two times.
   * @see {dispatch} to update the Redux store with the calculated durations.
   * @see {SHIFT_ENDING_DURATION} for the threshold of when the shift is considered ending soon.
   */
  const updateTimer = (shiftStartTime: string, lunchStartTime: string, shiftEndTime: string) => {
    const now = getCurrentTime(); // Get the current time

    // Calculate the duration to clock in (shift start time)
    const durationToClockIn: number = calculateTimeDiffinMinutes(now, shiftStartTime);
    if (durationToClockIn >= 0) {
      // Dispatch the duration to the Redux store
      dispatch(setDurationInMinutes(durationToClockIn));
    }
    //Calculate the time duration to Late clock in (shift already started) but not yet Clocked in
    else {
      dispatch(setLateClockIn(durationToClockIn * -1));
    }

    // Calculate the duration for lunch (from the current time to lunch start time)
    const lunchDuration: number = calculateTimeDiffinMinutes(lunchStartTime, now);
    if (lunchDuration >= 0) {
      // Dispatch the lunch duration to the Redux store
      dispatch(setLunchDurationInMinutes(lunchDuration));
    }

    // Calculate the remaining time until the shift ends (shift end time)
    const shiftEndMinutes: number = calculateTimeDiffinMinutes(now, shiftEndTime);

    if (shiftEndMinutes >= 0 && shiftEndMinutes <= SHIFT_ENDING_DURATION) {
      // If the shift is ending soon, dispatch the remaining minutes
      dispatch(setShiftEndSession(shiftEndMinutes));
    }
    //Calculate the time duration to Late clock out (shift already ended) but not yet Clocked out
    else if (shiftEndMinutes < 0) {
      dispatch(setLateClockOut(shiftEndMinutes * -1));
    }
  };

  const tabClicked = (actionName: string, props: any, tabName: string, index: number) => {
    resetNavigation();
    props.onPress?.();
    userActionLogEvent(
      BUTTON_NAV_ANALYTICS.EVENT_CATEGORY,
      actionName,
      BUTTON_NAV_ANALYTICS.EVENT_LABEL,
    );
    if (Platform.OS === 'ios') {
      const labelText = t('ada.selectedTabHeading', {
        tabName,
        index,
        total: BOTTOM_TABS,
      });

      setTimeout(() => {
        AccessibilityInfo.announceForAccessibility(labelText);
      }, 100);
    }
  };

  const shouldShowLocationGovernance =
    permissionStatus === LOCATION_STATE.DENIED || permissionStatus === LOCATION_STATE.APPROXIMATE;

  const getAccessibilityLabel = (tabName: string, index: number) => {
    return t('ada.tabHeading', { tabName, index, total: BOTTOM_TABS });
  };

  return (
    <Tab.Navigator
      initialRouteName={SCREENS.HOME_STACK}
      screenOptions={({ route }) => ({
        headerShown: true,
        tabBarActiveTintColor: theme.colors.pdsThemeColorBackgroundPrimary,
        tabBarInactiveTintColor: theme.colors.pdsThemeColorForegroundNeutralMedium,
        tabBarStyle: [
          isTablet && isLandscape ? styles.tabBarStyling : styles.emptyStyle,
          {
            paddingBottom: Platform.OS === 'ios' ? 0 : insets.bottom,
            height:
              (isTablet && isLandscape
                ? styles.tabBarStyling.height
                : Platform.OS === 'ios'
                  ? styles.emptyStyle.height
                  : styles.emptyStyle.height) +
              insets.bottom / 2,
          },
        ],
        tabBarLabel: ({ color }) => (
          <Text
            size={TEXT_SIZE.XX_SMALL}
            weight={TEXT_WEIGHT.BOLD}
            color={color}
            text={route.name}
            allowFontScaling={false}
          />
        ),
      })}
    >
      <Tab.Screen
        name={SCREENS.HOME_STACK}
        component={HomeStackNavigator}
        options={{
          tabBarLabel: ({ color }) => (
            <Text
              size={TEXT_SIZE.XX_SMALL}
              weight={TEXT_WEIGHT.BOLD}
              color={color}
              text={t('Home')}
              allowFontScaling={false}
              accessible={false}
            />
          ),
          tabBarIcon: ({ color, focused }) => (
            <SvgXml xml={focused ? homeSelected : home} style={styles.tabBarIcon} fill={color} />
          ),
          tabBarButton: (props) => (
            <TouchableOpacity
              accessibilityElementsHidden={preventTabsAccessibility}
              importantForAccessibility={preventTabsAccessibility ? ImportantForAccessibility.NO_HIDE : ImportantForAccessibility.AUTO}
              {...props}
              accessibilityHint={''}
              onPress={() => tabClicked(BUTTON_NAV_ANALYTICS.HOME_TAB_ACTION, props, t('Home'), 1)}
              accessibilityLabel={getAccessibilityLabel(t('Home'), 1)}
              accessibilityState={{ selected: props['aria-selected'] ?? false }}
            />
          ),
          headerShown: false,
          tabBarTestID: 'home-screen-tab',
        }}
      />

      <Tab.Screen
        name={SCREENS.COMMUNITY}
        component={shouldShowLocationGovernance ? LocationGoverned : CommunityScreen}
        options={{
          tabBarTestID: 'social-screen-tab',
          tabBarLabel: ({ color }) => (
            <Text
              size={TEXT_SIZE.XX_SMALL}
              weight={TEXT_WEIGHT.BOLD}
              color={color}
              text={t('Bulletin')}
              allowFontScaling={false}
              accessible={false}
            />
          ),
          tabBarIcon: ({ color, focused }) => (
            <SvgXml
              xml={focused ? bulletinSelected : bulletin}
              style={styles.tabBarIcon}
              fill={color}
            />
          ),
          tabBarButton: (props) => (
            <TouchableOpacity
              accessibilityElementsHidden={preventTabsAccessibility}
              importantForAccessibility={preventTabsAccessibility ? ImportantForAccessibility.NO_HIDE : ImportantForAccessibility.AUTO}
              {...props}
              accessibilityHint={''}
              onPress={() =>
                tabClicked(BUTTON_NAV_ANALYTICS.BULLETIN_TAB_ACTION, props, t('Bulletin'), 2)
              }
              accessibilityLabel={getAccessibilityLabel(t('Bulletin'), 2)}
              accessibilityState={{ selected: props['aria-selected'] ?? false }}
            />
          ),
          headerTitleAlign: 'left',
          headerTitleStyle: styles.headerTitle,
          headerTitle: t('Bulletin'),
          header: () => (
            <AppHeader
              headerRight={<HomeHeaderRight />}
              headerTitle={t('Bulletin')}
              headerTitleAlign="left"
              size="Medium"
            />
          ),
        }}
      />
      <Tab.Screen
        name={SCREENS.RESOURCES}
        component={ResourcesStackNavigator}
        options={{
          tabBarTestID: 'resources-screen-tab',
          tabBarLabel: t('Resources'),
          headerShown: false,
          tabBarIcon: ({ color, focused }) => (
            <SvgXml
              xml={focused ? resourceSelected : resource}
              style={styles.tabBarIcon}
              fill={color}
            />
          ),
          headerTitleAlign: 'left',
          headerTitleStyle: styles.headerTitle,
          tabBarLabel: ({ color }) => (
            <Text
              size={TEXT_SIZE.XX_SMALL}
              weight={TEXT_WEIGHT.BOLD}
              color={color}
              text={t('Resources')}
              allowFontScaling={false}
              accessible={false}
              maxLines={1}
            />
          ),
          tabBarButton: (props) => (
            <TouchableOpacity
              accessibilityElementsHidden={preventTabsAccessibility}
              importantForAccessibility={preventTabsAccessibility ? ImportantForAccessibility.NO_HIDE : ImportantForAccessibility.AUTO}
              {...props}
              accessibilityHint={''}
              onPress={() =>
                tabClicked(BUTTON_NAV_ANALYTICS.RESOURCES_TAB_ACTION, props, t('Resources'), 3)
              }
              accessibilityLabel={getAccessibilityLabel(t('Resources'), 3)}
              accessibilityState={{ selected: props['aria-selected'] ?? false }}
            />
          ),
          header: () => (
            <AppHeader
              headerRight={<HomeHeaderRight />}
              headerTitle={t('Resources')}
              headerTitleAlign="left"
              size="Medium"
            />
          ),
        }}
      />

      <Tab.Screen
        name={SCREENS.SCHEDULE}
        component={ScheduleScreen}
        options={{
          tabBarTestID: 'schedule-screen-tab',
          tabBarLabel: ({ color }) => (
            <Text
              size={TEXT_SIZE.XX_SMALL}
              weight={TEXT_WEIGHT.BOLD}
              color={color}
              text={t('Schedule')}
              allowFontScaling={false}
              accessible={false}
            />
          ),
          tabBarIcon: ({ color, focused }) => (
            <SvgXml
              xml={focused ? calendarSelected : calendar}
              style={styles.tabBarIcon}
              fill={color}
            />
          ),
          tabBarButton: (props) => (
            <TouchableOpacity
              accessibilityElementsHidden={preventTabsAccessibility}
              importantForAccessibility={preventTabsAccessibility ? ImportantForAccessibility.NO_HIDE : ImportantForAccessibility.AUTO}
              {...props}
              accessibilityHint={''}
              onPress={() =>
                tabClicked(BUTTON_NAV_ANALYTICS.SCHEDULE_TAB_ACTION, props, t('Schedule'), 4)
              }
              accessibilityLabel={getAccessibilityLabel(t('Schedule'), 4)}
              accessibilityState={{ selected: props['aria-selected'] ?? false }}
            />
          ),
          headerTitleAlign: 'left',
          headerTitleStyle: styles.headerTitle,
          headerTitle: t('Schedule'),
          header: () => (
            <AppHeader
              headerRight={<HomeHeaderRight />}
              headerTitle={t('Schedule')}
              headerTitleAlign="left"
              size="Medium"
            />
          ),
        }}
      />
      <Tab.Screen
        name={SCREENS.PROFILE}
        component={ProfileStackScreen}
        options={{
          tabBarTestID: 'profile-screen-tab',
          headerShown: false,
          tabBarLabel: ({ color }) => (
            <Text
              size={TEXT_SIZE.XX_SMALL}
              weight={TEXT_WEIGHT.BOLD}
              color={color}
              text={t('Profile')}
              allowFontScaling={false}
              accessible={false}
            />
          ),
          tabBarIcon: ({ color, focused }) => (
            <SvgXml
              xml={focused ? profileSelected : profile}
              style={styles.tabBarIcon}
              fill={color}
            />
          ),
          tabBarButton: (props) => (
            <TouchableOpacity
              accessibilityElementsHidden={preventTabsAccessibility}
              importantForAccessibility={preventTabsAccessibility ? ImportantForAccessibility.NO_HIDE : ImportantForAccessibility.AUTO}
              {...props}
              accessibilityHint={''}
              onPress={() =>
                tabClicked(BUTTON_NAV_ANALYTICS.PROFILE_TAB_ACTION, props, t('Profile'), 5)
              }
              accessibilityLabel={getAccessibilityLabel(t('Profile'), 5)}
              accessibilityState={{ selected: props['aria-selected'] ?? false }}
            />
          ),
          headerTitleAlign: 'left',
          headerShadowVisible: false,
          headerTitleStyle: styles.headerTitle,
          headerTitle: t(''),
          headerRight: () => <HomeHeaderRight />,
        }}
      />
    </Tab.Navigator>
  );
}

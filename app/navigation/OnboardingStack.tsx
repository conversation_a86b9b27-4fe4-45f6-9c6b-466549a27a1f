// OnboardingStack.tsx
import React from 'react';
import { createStackNavigator, CardStyleInterpolators } from '@react-navigation/stack';
import { ANIMATION_SETTLE_DURATION, SCREENS } from '../shared/constants';
import OnboardingFlow from '../features/onboarding/OnboardingFlow';

const Stack = createStackNavigator();

export default function OnboardingStack() {
    return (
        <Stack.Navigator
            screenOptions={{
                headerShown: false,
                gestureEnabled: false,
                cardStyleInterpolator: CardStyleInterpolators.forFadeFromBottomAndroid, // fade-in only
                transitionSpec: {
                    open: { animation: 'timing', config: { duration: ANIMATION_SETTLE_DURATION } },
                    close: { animation: 'timing', config: { duration: ANIMATION_SETTLE_DURATION } },
                },
            }}
        >
            <Stack.Screen
                name={SCREENS.ONBOARDING_FLOW}
                component={OnboardingFlow}
                options={{ headerShown: false }}
            />
        </Stack.Navigator>
    );
}

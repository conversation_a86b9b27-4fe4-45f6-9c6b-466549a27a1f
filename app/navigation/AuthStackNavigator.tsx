import React from 'react';
import { createStackNavigator } from "@react-navigation/stack";
import { LOGIN_FADE_IN_DURATION, SCREENS } from "../shared/constants";
import LoginScreen from "../features/login";
import WebView from "../../components/WebView";
import { useTranslation } from 'react-i18next';
import AppHeader from '../../components/AppHeader';
import HomeHeaderRightCross from '../features/home/<USER>';

const Stack = createStackNavigator();
function AuthStack() {
    const { t } = useTranslation();
    return (
        <Stack.Navigator screenOptions={{ headerShown: false }}>
            <Stack.Screen name={SCREENS.LOGIN} component={LoginScreen} />
            <Stack.Screen
                name={SCREENS.WEBVIEW}
                component={WebView}
                options={({ route }) => {
                    const isLoginFlow = route?.params?.titleHeader === t('login'); // Adjust as needed
                    return {
                        headerShown: true,
                        header: ({ options }) => (
                            <AppHeader
                                isOmniheaderPresent={options.isOmniheaderPresent}
                                headerRight={
                                    <HomeHeaderRightCross onBackPress={options.goBack} />
                                }
                                headerTitle={options?.headerTitle}
                            />
                        ),
                        transitionSpec: isLoginFlow
                            ? {
                                open: {
                                    animation: 'timing',
                                    config: { duration: LOGIN_FADE_IN_DURATION },
                                },
                                close: {
                                    animation: 'timing',
                                    config: { duration: LOGIN_FADE_IN_DURATION },
                                },
                            }
                            : undefined,
                    };
                }}
            />
        </Stack.Navigator>
    );
}

export default AuthStack;
import React from "react";
import AnnouncementDetails from "../features/announcements/AnnouncementDetails";
import { createStackNavigator } from "@react-navigation/stack";
import { useTheme } from "pantry-design-system";
import HomeScreen from "../features/home/<USER>";
import HomeHeaderLeft from "../features/home/<USER>";
import getStyles from "./styles";
import HomeHeaderRight from "../features/home/<USER>";
import AnnouncementViewAll from "../features/announcements/AnnouncementViewAll";
import ProfileHeaderLeft from "../features/profile/ProfileHeaderLeft";
import AppHeader from "../../components/AppHeader";
import { useTranslation } from "react-i18next";
import { LOCATION_STATE, SCREENS } from "../shared/constants";
import { userActionLogEvent } from "../../analytics/AnalyticsUtils";
import { LOGIN_ANALYTICS } from "../../analytics/AnalyticsConstants";

type HomeStackParamList = {
    home: undefined;
    AnnouncementViewAll: undefined;
    AnnouncementDetails: { cardTitle: string };
};

const Stack = createStackNavigator<HomeStackParamList>();  // Create StackNavigator'

const HomeStackNavigator = () => {

    const { t } = useTranslation();

    const { theme } = useTheme() // Fething themes from ThemeProvider
    const styles = getStyles(theme);

    return (
        <Stack.Navigator
            screenOptions={{
                headerShown: true,
                headerTitle: "",
                initialRouteName: SCREENS.HOME_SCREEN,
            }}>
            <Stack.Screen
                name={SCREENS.HOME_SCREEN}
                component={HomeScreen}
                options={() => ({
                    testID: 'home-stack',
                    header: () => (
                        <AppHeader
                            isOmniheaderPresent={false}
                            headerLeft={<HomeHeaderLeft />}
                            headerRight={<HomeHeaderRight />}
                        />
                    ),
                })}// You can customize header here

            />
            <Stack.Screen
                name={SCREENS.ANNOUNCEMENTS_VIEW_ALL}
                component={AnnouncementViewAll}
                options={({ navigation }) => ({
                    testID: 'announcement-view-all',
                    headerTitleStyle: styles.whatsNew,
                    headerShown: true,
                    header: () => (
                        <AppHeader
                            headerLeft={
                                <ProfileHeaderLeft
                                    onBackPress={() => {
                                        userActionLogEvent(LOGIN_ANALYTICS.WHATS_NEW, LOGIN_ANALYTICS.BACK, LOGIN_ANALYTICS.ANNOUNCEMENTS);
                                        navigation.goBack();
                                    }} />}
                            headerTitle={t("WHATSNEW")}
                            headerRight={<HomeHeaderRight eventCategory={LOGIN_ANALYTICS.WHATS_NEW} eventLabel={LOGIN_ANALYTICS.ANNOUNCEMENTS} />}
                        />
                    )
                })} // Header customization

            />

            <Stack.Screen
                name={SCREENS.ANNOUNCEMENTS_DETAILS}
                component={AnnouncementDetails}
                options={({ navigation, route }) => ({
                    testID: 'announcement-details-stack',
                    headerTitleStyle: styles.whatsNew,
                    headerShown: true,
                    header: () => (
                        <AppHeader
                            headerLeft={
                                <ProfileHeaderLeft
                                    onBackPress={() => {
                                        userActionLogEvent(LOGIN_ANALYTICS.ANNOUNCEMENTS, LOGIN_ANALYTICS.BACK, route?.params?.cardTitle ?? "");
                                        navigation.goBack();
                                    }} />}
                            headerTitle={route?.params?.cardTitle} // Pass cardTitle from route params
                        />
                    )
                })} // Header customization
            />
        </Stack.Navigator>
    );
};

export default HomeStackNavigator;
import { Theme } from 'pantry-design-system';
import { Platform, StyleSheet, TextStyle } from "react-native";
import { BOTTOM_TABBAR_PADDING_IPHONE } from "../shared/constants";

//customising styles based on themes
const getStyles = ({ colors, fonts, dimensions, typography }: Theme) => {
    const styles = StyleSheet.create({
        tabBarStyling: {
            height: dimensions.pdsGlobalSizeHeight1000,
            paddingVertical: dimensions.pdsGlobalSpace300,
            paddingHorizontal: dimensions.pdsGlobalSpace800,
        },
        emptyStyle: {
            height: Platform.OS === "ios" ? BOTTOM_TABBAR_PADDING_IPHONE : dimensions.pdsGlobalSizeHeight1300,
            padding: dimensions.pdsGlobalSpace300,
        },
        tabBarLabelSelected: {},
        tabBarLabel: {
            color: colors.pdsThemeColorForegroundNeutralMedium,
            fontSize: typography.pdsGlobalFontSize100,
            fontStyle: "normal",
            fontWeight: typography.pdsGlobalFontWeight700,
            display: "flex",
            lineHeight: 16.8,
            fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
        } as TextStyle,
        tabBarIcon: {
            width: dimensions.pdsGlobalSizeWidth400,
            height: dimensions.pdsGlobalSizeHeight400,
        },
        headerTitle: {
            color: colors.pdsThemeColorForegroundNeutralHigh,
            fontSize: typography.pdsGlobalFontSize500,
            fontStyle: "normal",
            fontWeight: typography.pdsGlobalFontWeight500,
            display: "flex",
            lineHeight: 28.6,
            fontFamily: fonts.pdsGlobalFontFamilyPoppins,
        } as TextStyle,
        customHeader: {
            height: 50,
            backgroundColor: "white",
            paddingHorizontal: 10,
            justifyContent: "space-between",
            flexDirection: "row"
        },
        headerRight: {
            alignSelf: "center"
        },
        whatsNew: {
            fontSize: typography.pdsGlobalFontSize300,
            lineHeight: 20,
            color: colors.pdsThemeColorForegroundNeutralHigh,
            fontFamily: fonts.pdsGlobalFontFamilyPoppins,
            fontWeight: typography.pdsGlobalFontWeight500,
        },
    });

    return styles
}

export default getStyles;
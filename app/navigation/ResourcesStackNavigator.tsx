import React from "react";
import { createStackNavigator } from "@react-navigation/stack";
import HomeHeaderRight from "../features/home/<USER>";
import ProfileHeaderLeft from "../features/profile/ProfileHeaderLeft";
import AppHeader from "../../components/AppHeader";
import { useTranslation } from "react-i18next";
import { LOCATION_STATE, SCREENS } from "../shared/constants";
import ResourceScreen from "../features/resource";
import HrAndPayroll from "../features/profile/HrAndPayroll/HrAndPayroll";
import { useSelector } from "react-redux";
import LocationGoverned from "../../components/locationGoverned";

const Stack = createStackNavigator();

const ResourcesStackNavigator = () => {
    const { t } = useTranslation();

    const { isPrecise } = useSelector((state: any) => state.locationAccess); // Fetching the precise location setting from the redux store

    const shouldShowLocationGovernance =
        isPrecise === LOCATION_STATE.DENIED || isPrecise === LOCATION_STATE.APPROXIMATE;

    return (
        <Stack.Navigator
            screenOptions={{
                headerTitle: "",
                initialRouteName: SCREENS.RESOURCES,
            }}>
            <Stack.Screen
                name={SCREENS.RESOURCES}
                component={shouldShowLocationGovernance ? LocationGoverned : ResourceScreen}
                options={() => ({
                    header: () => (
                        <AppHeader
                            headerRight={<HomeHeaderRight />}
                            headerTitle={t('Resources')}
                            headerTitleAlign="left"
                            size="Medium"
                        />
                    ),
                })}
            />
            <Stack.Screen
                name={SCREENS.HR_AND_PAYROLL}
                options={({ navigation }) => ({
                    header: () => (
                        <AppHeader
                            headerLeft={
                                <ProfileHeaderLeft
                                    onBackPress={() => navigation.goBack()}
                                />
                            }
                            headerTitle={t("hrAndPayroll")}
                        />
                    ),
                })}
                component={HrAndPayroll}
            />
        </Stack.Navigator>
    );
};

export default ResourcesStackNavigator;
import React from "react";
import { render, waitFor } from "@testing-library/react-native";
import configureStore from "redux-mock-store";
import { Provider } from "react-redux";
import TabNavigator from "../TabNavigator";
import { useTranslation } from "react-i18next";

const initialState = {
  shift: {
    shiftStartTime: "08:00 AM",
    shiftEndTime: "04:00 PM",
    lunchStartTime: "12:00 PM",
  },
  profile: { isUserInStore: true },
  deviceInfo: {
    isTablet: false,
    isLandscape: false,
  },
  errorState: {
    authError: false
  }
};

const mockStore = configureStore([]);

jest.mock('@react-native-firebase/app', () => ({
  getApp: jest.fn(() => ({
    name: '[DEFAULT]',
  })),
}));

jest.mock('react-native-permissions', () => ({
  check: jest.fn(() => Promise.resolve('granted')),
  request: jest.fn(() => Promise.resolve('granted')),
  PERMISSIONS: {
    ANDROID: {
      ACCESS_FINE_LOCATION: 'android.permission.ACCESS_FINE_LOCATION',
    },
    IOS: {
      LOCATION_WHEN_IN_USE: 'ios.permission.LOCATION_WHEN_IN_USE',
    },
  },
}));

jest.mock("pantry-design-system", () => ({
  useTheme: jest.fn(() => ({
    theme: {
      colors: {
        pdsThemeColorForegroundNeutralMedium: "#00000",
        pdsThemeColorForegroundNeutralHigh: "#00000",
      },
      fonts: {
        pdsGlobalFontFamilyPoppins: "Poppins",
        pdsGlobalFontFamilyNunitoSans: "NunitoSans",
      },
      dimensions: {
        pdsGlobalSpace400: 16,
        pdsGlobalSizeWidth800: 32,
        pdsGlobalSizeWidth400: 40,
      },
      typography: {
        pdsGlobalFontSize500: 18,
        pdsGlobalFontSize100: 10,
        pdsGlobalFontWeight700: 70,
        pdsGlobalFontWeight500: 50,
        pdsGlobalFontSize300: 30,
      },
    },
  })),
}));

jest.mock("@react-navigation/bottom-tabs", () => ({
  createBottomTabNavigator: jest.fn(() => ({
    Navigator: ({ children }) => <>{children}</>,
    Screen: ({ children }) => <>{children}</>,
  })),
}));

jest.mock("@react-navigation/bottom-tabs", () => ({
  createBottomTabNavigator: jest.fn(() => ({
    Navigator: ({ children }) => <>{children}</>,
    Screen: ({ children }) => <>{children}</>,
  })),
}));

jest.mock("@react-navigation/native", () => ({
  useNavigation: () => ({ navigate: jest.fn() }),
}));

jest.mock("pantry-design-system", () => ({
  useTheme: jest.fn(() => ({
    theme: {
      typography: {
        pdsGlobalFontSize100: 12,
        pdsGlobalFontWeight700: "bold",
      },
      dimensions: { pdsGlobalSizeWidth400: 16 },
      colors: {
        pdsThemeColorForegroundNeutralMedium: "#000",
      },
      fonts: { pdsGlobalFontFamilyNunitoSans: "NunitoSans" },
    },
  })),
}));

jest.mock('@react-navigation/stack', () => {
  return {
    createStackNavigator: jest.fn(() => {
      return {
        Navigator: ({ children }) => <>{children}</>,
        Screen: ({ name, component }) => {
          const Component = component || (() => null);
          return <Component testID={name} />;
        },
      };
    }),
  };
});

jest.mock("react-native-vector-icons/AntDesign", () => "AntDesign");
jest.mock("react-native-vector-icons/MaterialIcons", () => "MaterialIcons");
// jest.mock('../features/home/<USER>', () => "HomeScreen");
jest.mock("../HomeStackNavigator", () => "HomeStackNavigator");
jest.mock("../../features/community/community", () => "CommunityScreen");
jest.mock("../../features/resource", () => "ResourceScreen");
jest.mock("../../features/schedule/schedule", () => "ScheduleScreen");
jest.mock("../../features/profile", () => "ProfileStackScreen");
jest.mock("../../../components/WebView", () => "WebView");
jest.mock('../../store/AsyncStorage', () => ({
  setItem: jest.fn(),
  getItem: jest.fn(),
}));
jest.mock('@appdynamics/react-native-agent', () => ({
  Instrumentation: {
    start: jest.fn(),
    setUserData: jest.fn(),
    removeUserData: jest.fn(),
    reportError: jest.fn(),
    reportMetric: jest.fn(),
    leaveBreadcrumb: jest.fn(),
    startTimer: jest.fn(),
    stopTimer: jest.fn(),
    startNextSession: jest.fn(),
  },
  LoggingLevel: { VERBOSE: 'VERBOSE', INFO: 'INFO' },
  ErrorSeverityLevel: { WARNING: 'WARNING', CRITICAL: 'CRITICAL' },
  BreadcrumbVisibility: { CRASHES_AND_SESSIONS: 'CRASHES_AND_SESSIONS' },
}));


jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations = {
        Home: "Home",
        Bulletin: "Bulletin",
        Resources: "Resources",
        Schedule: "Schedule",
        Profile: "Profile",
      };
      return translations[key] || key;
    },
  }),
}));

describe.skip("TabNavigator UI Tests", () => {
  it("renders all tab labels correctly", async () => {
    const store = mockStore(initialState);
    const { findByTestId } = render(
      <Provider store={store}>
        <TabNavigator />
      </Provider>
    );
    expect(findByTestId("home-screen-tab")).toBeTruthy();
    expect(findByTestId("bulletin-screen-tab")).toBeTruthy();
    expect(findByTestId("resources-screen-tab")).toBeTruthy();
    expect(findByTestId("schedule-screen-tab")).toBeTruthy();
    expect(findByTestId("profile-screen-tab")).toBeTruthy();
  });


});

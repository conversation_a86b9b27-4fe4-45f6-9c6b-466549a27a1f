import { NavigationContainer, createNavigationContainerRef } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { CardStyleInterpolators } from '@react-navigation/stack';
import React, { useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { AccessibilityInfo, useWindowDimensions } from 'react-native';
import DeviceInfo from 'react-native-device-info';
import { useDispatch, useSelector } from 'react-redux';

import AppHeader from '../../components/AppHeader';
import NotificationScreen from '../../components/NotificationScreen';
import ProfileErrorModal from '../../components/ProfileErrorModal';
import SessionOutModal from '../../components/SessionOutModal';
import WebView from '../../components/WebView';
import AppLaunch from '../features/appLaunch';
import AuthLogout from '../features/AuthLogout';
import CorporateErrorScreen from '../features/corporateErrorScreen';
import HomeHeaderRightCross from '../features/home/<USER>';
import AppPrivacy from '../features/privacyFlow/appPrivacy';
import AppPrivacyNotice from '../features/privacyFlow/appPrivacyNotice';
import DocPdfViewer from '../features/privacyFlow/docPdfViewer';
import ProfileHeaderLeft from '../features/profile/ProfileHeaderLeft';
import SignInHelp from '../features/signInHelp';
import SplashScreen from '../features/splashScreen/splashScreen';
import { ANIMATION_OVERSHOOT_DURATION, SCREENS } from '../shared/constants';
import { setReduceMotion } from '../store/reducers/confettiSlice';
import { setIsDeviceTablet, setIsDeviceLandscape } from '../store/reducers/deviceInfoSlice';
import { setShowSessionModal } from '../store/reducers/profileSlice';
import { setOrientationBasedOnDevice } from '../utils/orientationUtils';

import AccountLinkStack from './AccountLinkStackNavigator';
import AuthStack from './AuthStackNavigator';
import OnboardingStack from './OnboardingStack';
import TabNavigator from './TabNavigator';

import type { View } from 'react-native';

export const navigationRef = createNavigationContainerRef();

export function navigate(name: string, params?: object) {
  if (navigationRef.isReady()) {
    navigationRef.navigate(name as never, params as never);
  }
}

const Stack = createNativeStackNavigator();
function AppNavigator() {
  const dispatch = useDispatch();
  const { width, height } = useWindowDimensions(); // Get real-time window dimensions
  const { t } = useTranslation();
  const showSessionModal = useSelector((state: any) => state.profile.showSessionModal);

  const appPrivacyBackButtonRef = useRef<View>(null);
  const appPrivacyNoticeBackButtonRef = useRef<View>(null);

  /**
   * Fetch and listen to the user's "Reduce Motion" accessibility setting.
   *
   * - On mount, retrieves the current setting using `AccessibilityInfo.isReduceMotionEnabled()`
   *   and updates Redux state accordingly using `setReduceMotion`.
   * - Subscribes to future changes via `reduceMotionChanged` event and dispatches updates.
   * - Cleans up the listener on unmount to prevent memory leaks.
   *
   * This ensures animations like confetti can respect the user's motion preferences.
   */
  useEffect(() => {
    AccessibilityInfo.isReduceMotionEnabled().then((enabled) => {
      dispatch(setReduceMotion(enabled));
    });

    const sub = AccessibilityInfo.addEventListener('reduceMotionChanged', (enabled: boolean) => {
      dispatch(setReduceMotion(enabled));
    });

    return () => sub.remove();
  }, [dispatch]);

  /**
   * Device type detection logic within a `useEffect` hook.
   *
   * This hook detects whether the device is a tablet using the `DeviceInfo` library and updates the Redux store with the result.
   *
   * - **Device Detection**: The `DeviceInfo.isTablet()` method is used to determine if the device is a tablet (iPad or Android tablet).
   * - **Redux Update**: The result is dispatched to the Redux store using the `setIsTablet` action.
   *
   * The hook runs only once when the component mounts, as it does not depend on any changing values.
   *
   * @see {DeviceInfo.isTablet} for detecting if the device is a tablet.
   */
  useEffect(() => {
    const isTablet = DeviceInfo.isTablet(); // Detect if it's an iPad or Android tablet
    dispatch(setIsDeviceTablet(isTablet));
    // After detecting the device type, set the orientation based on whether it's a tablet or not
    setOrientationBasedOnDevice(isTablet);
  }, []);

  /**
   * Device orientation detection logic within a `useEffect` hook.
   *
   * This hook detects the device's orientation (landscape or portrait) based on the screen dimensions and updates the Redux store with the result.
   *
   * - **Orientation Detection**: The orientation is determined by comparing the `width` and `height` of the screen.
   *   - If `width > height`, the device is in landscape mode.
   *   - Otherwise, the device is in portrait mode.
   * - **Redux Update**: The result is dispatched to the Redux store using the `setIsLandscape` action.
   *
   * The hook re-runs whenever the `width` or `height` of the screen changes, ensuring the orientation is always up-to-date.
   *
   * @see {useWindowDimensions} for accessing the screen dimensions.
   */
  useEffect(() => {
    if (width > height) {
      dispatch(setIsDeviceLandscape(true));
    } else {
      dispatch(setIsDeviceLandscape(false));
    }
  }, [width, height]);

  /**
   * Handles session timeout modal display and navigation.
   * When `showSessionModal` is true:
   * 1. Navigates to session timeout screen
   * 2. Hides modal to prevent duplicates
   *
   * @listens showSessionModal Redux state
   * @sideeffects Navigation, Redux dispatch
   */
  useEffect(() => {
    if (showSessionModal) {
      navigate(SCREENS.PROFILE_SESSION_OUT, {
        headerTitle: t('oops'),
        buttonText: t('schedule.retryButton'),
      });
      dispatch(setShowSessionModal(false));
    }
  }, [showSessionModal]);

  return (
    <NavigationContainer ref={navigationRef}>
      <Stack.Navigator
        screenOptions={{
          statusBarStyle: 'dark',
        }}
      >
        <Stack.Screen
          name={SCREENS.APP_LAUNCH}
          component={AppLaunch}
          options={{ headerShown: false }}
        />
        <Stack.Screen name="authStack" component={AuthStack} options={{ headerShown: false }} />
        <Stack.Screen
          name="splashStack"
          component={SplashScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name={SCREENS.ONBOARDING_STACK}
          component={OnboardingStack}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name={SCREENS.BOTTOM_TAB_STACK}
          component={TabNavigator}
          options={({ route, navigation }) => {
            const navState = navigation.getState();
            const previousRoute = navState?.routes?.[navState.routes.length - 2]?.name;

            const isFromOnboarding = previousRoute === SCREENS.ONBOARDING_STACK;

            return {
              headerShown: false,
              gestureEnabled: isFromOnboarding,
              cardStyleInterpolator: isFromOnboarding
                ? CardStyleInterpolators.forHorizontalIOS
                : undefined,
              transitionSpec: isFromOnboarding
                ? {
                  open: {
                    animation: 'timing',
                    config: { duration: ANIMATION_OVERSHOOT_DURATION },
                  },
                  close: {
                    animation: 'timing',
                    config: { duration: ANIMATION_OVERSHOOT_DURATION },
                  },
                }
                : undefined,
            };
          }}
        />
        <Stack.Screen
          name={SCREENS.ACCOUNT_LINK_STACK}
          component={AccountLinkStack}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name={SCREENS.CORPORATE_ERROR_SCREEN}
          component={CorporateErrorScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name={SCREENS.SIGN_IN_HELP}
          component={SignInHelp}
          options={({ navigation }) => ({
            testID: 'sign-in-help',
            header: () => (
              <AppHeader
                isOmniheaderPresent={false}
                headerLeft={<ProfileHeaderLeft onBackPress={() => navigation.goBack()} />}
                headerTitle={t('signInHelp')}
              />
            ),
          })}
        />
        <Stack.Screen
          name={SCREENS.AUTH_LOGOUT}
          component={AuthLogout}
          options={() => ({
            header: () => <AppHeader headerLeft={<></>} headerTitle={''} />,
          })}
        />
        <Stack.Screen
          name={SCREENS.SESSION_OUT}
          component={SessionOutModal}
          options={() => ({
            headerShown: false,
          })}
        />
        <Stack.Screen
          name={SCREENS.PROFILE_SESSION_OUT}
          component={ProfileErrorModal}
          options={() => ({
            headerShown: false,
          })}
        />
        <Stack.Screen
          name="NotificationsScreen"
          component={NotificationScreen}
          options={({ navigation }) => ({
            header: () => (
              <AppHeader
                headerTitle={t("notifications")}
                headerLeft={
                  <ProfileHeaderLeft onBackPress={() => navigation.goBack()} />
                }
              />
            )
          })}
        />
        <Stack.Screen
          name={SCREENS.PRIVACY_POLICY}
          component={AppPrivacy}
          options={({ navigation }) => ({
            testID: 'app-privacy-policy',
            header: () => (
              <AppHeader
                isOmniheaderPresent={false}
                headerLeft={
                  <ProfileHeaderLeft
                    ref={appPrivacyBackButtonRef}
                    onBackPress={() => navigation.goBack()}
                  />
                }
                headerTitle={t('privacyPolicy')}
              />
            ),
          })}
          initialParams={{ backButtonRef: appPrivacyBackButtonRef }}
        />
        <Stack.Screen
          name={SCREENS.ASSOCIATE_PRIVACY_NOTICE}
          component={AppPrivacyNotice}
          options={({ navigation }) => ({
            testID: 'associate-privacy-notice',
            header: () => (
              <AppHeader
                isOmniheaderPresent={false}
                headerLeft={
                  <ProfileHeaderLeft
                    ref={appPrivacyNoticeBackButtonRef}
                    onBackPress={() => navigation.goBack()}
                  />
                }
                headerTitle={t('appPrivacyNotice.associatePricacyNotice')}
              />
            ),
          })}
          initialParams={{ backButtonRef: appPrivacyNoticeBackButtonRef }}
        />

        <Stack.Screen
          name={SCREENS.DOC_PDF_VIEWER}
          component={DocPdfViewer}
          options={() => ({
            headerShown: true,
            header: ({ navigation, options }) => (
              <AppHeader
                isOmniheaderPresent={false}
                headerTitle={options?.headerTitle ?? 'PDF'}
                headerLeft={
                  <ProfileHeaderLeft
                    onBackPress={() => navigation.goBack()}
                    testID="pressable-header-left"
                  />
                }
              />
            ),
          })}
        />
        <Stack.Screen
          name={SCREENS.WEBVIEW}
          component={WebView}
          options={() => ({
            headerShown: true,
            header: ({ navigation, options }) => (
              <AppHeader
                isOmniheaderPresent={options?.isOmniheaderPresent}
                headerTitle={options?.headerTitle ?? ' '}
                headerRight={<HomeHeaderRightCross onBackPress={() => options?.goBack()} />}
              />
            ),
          })}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
}

export default AppNavigator;

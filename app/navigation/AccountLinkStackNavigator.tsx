/**
 * Loyalty account linkage Stack component that configures and renders a stack navigator
 * for the account-linkage screens in the app.
 *
 * The stack includes the following screens:
 * 1. AccountLinkOne - Displays the step one for account linking process (with header).
 * 2. Later other steps will be added here.
 * The initial screen displayed is the "account link step one" as screena name "AccountLinkOne" and the stack navigator is configured
 * to center the header title and remove the header shadow for all screens.
 */
import { createStackNavigator } from '@react-navigation/stack';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { LOYALTY_ANALYTICS, BUTTON_NAV_ANALYTICS } from '../../analytics/AnalyticsConstants';
import { userActionLogEvent } from '../../analytics/AnalyticsUtils';
import AppHeader from '../../components/AppHeader';
import AccountLinkAllDone from '../features/accountLink/accountLinkAllDone';
import AccountLinkIntro from '../features/accountLink/accountLinkIntro';
import AccountLinkTwo from '../features/accountLink/accountLinkTwo';
import AccountLinkUserInputOne from '../features/accountLink/accountLinkUserInputStepOne';
import AccountLinkUserVerify from '../features/accountLink/accountLinkUserVerify';
import HomeHeaderRightCross from '../features/home/<USER>';
import { SCREENS, TNC_AGREEMENT } from '../shared/constants';
import ProfileHeaderLeft from '../features/profile/ProfileHeaderLeft';
import * as AsyncStorage from '../../app/store/AsyncStorage';

const Stack = createStackNavigator();

export default function AccountLinkStack() {
  const { t } = useTranslation();
  const [initialRouteName, setInitialRouteName] = useState(SCREENS.ACCOUNT_LINK_STEP_INTRO); // Default route
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    const fetchOptInStatus = async () => {
      try {
        // Get the opt-in agreementId from AsyncStorage
        const optedInAgreementId = await AsyncStorage.getOptInLoyaltyTNC();
        // Set the initial route based on opt-in agreementId
        if (optedInAgreementId === TNC_AGREEMENT.LOYALTY_TNC.ID) {
          setInitialRouteName(SCREENS.ACCOUNT_LINK_USER_INPUT_STEP_ONE);
        } else {
          setInitialRouteName(SCREENS.ACCOUNT_LINK_STEP_INTRO);
        }
      } catch (error) {
        console.error("Error fetching opt-in status:", error);
        // Default to intro screen on error
        setInitialRouteName(SCREENS.ACCOUNT_LINK_STEP_INTRO);
      } finally {
        setIsInitialized(true);
      }
    };

    fetchOptInStatus();
  }, [initialRouteName, isInitialized]);

  // Don't render until we've determined the initial route
  if (!isInitialized) {
    return null;
  }

  return (
    <Stack.Navigator
      // Only show loytalty terms and conditions if user hasn't opted in yet.
      initialRouteName={initialRouteName}
      screenOptions={{ headerTitleAlign: 'center', headerShadowVisible: false }}
    >
      <Stack.Screen
        name={SCREENS.ACCOUNT_LINK_STEP_INTRO}
        options={({ navigation }) => ({
          header: () => (
            <AppHeader
              headerLeft={
                <ProfileHeaderLeft onBackPress={() => navigation.goBack()} />
              }
              headerTitle={t('accountLink.beforeYouBeginHeader')}
              headerRight={
                <HomeHeaderRightCross
                  onBackPress={() => {
                    userActionLogEvent(
                      BUTTON_NAV_ANALYTICS.PROFILE_TAB_ACTION,
                      LOYALTY_ANALYTICS.LINK_CLOSE_X,
                      LOYALTY_ANALYTICS.LOYALTY_LINK_CONSENT,
                    );

                    navigation.goBack();
                  }}
                />
              }
            />
          ),
        })}
        component={AccountLinkIntro}
      />

      <Stack.Screen
        name={SCREENS.ACCOUNT_LINK_USER_INPUT_STEP_ONE}
        options={({ navigation }) => ({
          header: () => (
            <AppHeader
              headerLeft={
                <ProfileHeaderLeft onBackPress={() => navigation.goBack()} />
              }
              headerTitle={t('accountLink.step1Of2')}
              headerRight={
                <HomeHeaderRightCross
                  onBackPress={() => {
                    userActionLogEvent(
                      BUTTON_NAV_ANALYTICS.PROFILE_TAB_ACTION,
                      LOYALTY_ANALYTICS.LINK_CLOSE_X,
                      LOYALTY_ANALYTICS.LOYALTY_LINK_STEP1,
                    );

                    navigation.reset({
                      index: 0,
                      routes: [
                        { name: SCREENS.BOTTOM_TAB_STACK, params: { screen: SCREENS.PROFILE } },
                      ],
                    });
                  }}
                />
              }
            />
          ),
        })}
        component={AccountLinkUserInputOne}
      />
      <Stack.Screen
        name={SCREENS.ACCOUNT_LINK_STEP_ONE_VERIFY}
        options={({ navigation }) => ({
          header: () => (
            <AppHeader
              headerLeft={
                <ProfileHeaderLeft onBackPress={() => navigation.goBack()} />
              }
              headerTitle={t('accountLink.step1Of2')}
              headerRight={
                <HomeHeaderRightCross
                  onBackPress={() => {
                    userActionLogEvent(
                      BUTTON_NAV_ANALYTICS.PROFILE_TAB_ACTION,
                      LOYALTY_ANALYTICS.LINK_CLOSE_X,
                      LOYALTY_ANALYTICS.LOYALTY_LINK_CONFIRMATION,
                    );

                    navigation.reset({
                      index: 0,
                      routes: [
                        { name: SCREENS.BOTTOM_TAB_STACK, params: { screen: SCREENS.PROFILE } },
                      ],
                    });
                  }}
                />
              }
            />
          ),
        })}
        component={AccountLinkUserVerify}
      />

      <Stack.Screen
        name={SCREENS.ACCOUNT_LINK_STEP_TWO}
        options={({ navigation }) => ({
          header: () => (
            <AppHeader
              headerLeft={
                <ProfileHeaderLeft onBackPress={() => navigation.goBack()} />
              }
              headerTitle={t('accountLink.step2')}
              headerRight={
                <HomeHeaderRightCross
                  onBackPress={() => {
                    userActionLogEvent(
                      BUTTON_NAV_ANALYTICS.PROFILE_TAB_ACTION,
                      LOYALTY_ANALYTICS.LINK_CLOSE_X,
                      LOYALTY_ANALYTICS.LOYALTY_LINK_STEP2,
                    );

                    navigation.reset({
                      index: 0,
                      routes: [
                        { name: SCREENS.BOTTOM_TAB_STACK, params: { screen: SCREENS.PROFILE } },
                      ],
                    });
                  }}
                />
              }
            />
          ),
        })}
        component={AccountLinkTwo}
      />
      <Stack.Screen
        name={SCREENS.ACCOUNT_LINK_ALL_DONE}
        options={({ navigation }) => ({
          header: ({ navigation, options }) => (
            <AppHeader
              headerTitle={options?.headerTitle ?? t('accountLink.allDone')}
              headerRight={
                <HomeHeaderRightCross
                  onBackPress={() => {
                    const getScreenType = options?.headerTitle === t('accountLink.underReviewTitle') ? LOYALTY_ANALYTICS.LOYALTY_LINKING_IN_REVIEW : LOYALTY_ANALYTICS.LOYALTY_ALREADY_LINKED;
                    userActionLogEvent(
                      BUTTON_NAV_ANALYTICS.PROFILE_TAB_ACTION,
                      LOYALTY_ANALYTICS.LINK_CLOSE_X,
                      getScreenType,
                    );
                    navigation.reset({
                      index: 0,
                      routes: [
                        { name: SCREENS.BOTTOM_TAB_STACK, params: { screen: SCREENS.PROFILE } },
                      ],
                    })
                  }}
                />
              }
            />
          ),
        })}
        component={AccountLinkAllDone}
      />
    </Stack.Navigator>
  );
}
import { act, renderHook } from '@testing-library/react-native';
import React from 'react';
import { Provider, useDispatch } from 'react-redux';
import configureStore from 'redux-mock-store';

import { LOCATION_STATE, STORE } from '../shared/constants';
import * as AsyncStorage from '../store/AsyncStorage';
import { setPreciseLocationGranted } from '../store/reducers/locationAccessSlice';
import { fetchProfileRequest, setUserInStore } from '../store/reducers/profileSlice';
import { checkLocationPermission, getUserLocation } from '../utils/helpers';

import useFetchStoreLocation from './useFetchStoreLocation';

jest.mock('@react-navigation/native', () => ({
  useNavigation: jest.fn(),
  useFocusEffect: jest.fn((cb) => {
    cb();
  }),
}));

jest.mock('react-native', () => ({
  AppState: {
    addEventListener: jest.fn(() => ({
      remove: jest.fn(),
    })),
    removeEventListener: jest.fn(),
    currentState: 'active', // Set an initial state for testing
  },
  Platform: {
    OS: 'android',
    select: (options: any) => options.android,
  },
  // ... other mocks
}));

// Mock dependencies
jest.mock('react-native/Libraries/AppState/AppState', () => ({
  currentState: 'active',
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
}));

jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useDispatch: jest.fn(),
}));

jest.mock('../utils/helpers', () => ({
  getUserLocation: jest.fn(),
  checkLocationPermission: jest.fn(),
}));

jest.mock('../store/AsyncStorage', () => ({
  uniqueSessionId: jest.fn(),
}));

jest.mock('../store/reducers/profileSlice', () => ({
  fetchProfileRequest: jest.fn((payload) => ({
    type: 'profile/fetchProfileRequest',
    payload,
  })),
  setUserInStore: jest.fn((payload) => ({
    type: 'profile/setUserInStore',
    payload,
  })),
}));

jest.mock('../store/reducers/locationAccessSlice', () => ({
  setPreciseLocationGranted: jest.fn((payload) => ({
    type: 'locationAccess/setPreciseLocationGranted',
    payload,
  })),
}));

const mockStore = configureStore([]);
const initialState = {};

describe('useFetchStoreLocation', () => {
  let store: any;
  let dispatchMock: jest.Mock;

  beforeEach(() => {
    store = mockStore(initialState);
    dispatchMock = jest.fn((action) => store.dispatch(action));

    (useDispatch as jest.Mock).mockReturnValue(dispatchMock);
    (checkLocationPermission as jest.Mock).mockResolvedValue(LOCATION_STATE.PRECISE);
    (getUserLocation as jest.Mock).mockResolvedValue({ latitude: 12.34, longitude: 56.78 });
    (AsyncStorage.uniqueSessionId as jest.Mock).mockResolvedValue('mock-session-id');
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should dispatch fetchProfileRequest when app comes to foreground and location permission is precise', async () => {
    const appStateChangeCallback = jest.fn();
    const removeMock = jest.fn();

    const addEventListenerMock = require('react-native/Libraries/AppState/AppState')
      .addEventListener as jest.Mock;
    addEventListenerMock.mockImplementation((_, callback) => {
      appStateChangeCallback.mockImplementation(callback);
      return { remove: removeMock };
    });

    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <Provider store={store}>{children}</Provider>
    );
    renderHook(() => useFetchStoreLocation(), { wrapper });

    act(() => {
      appStateChangeCallback('background');
      appStateChangeCallback('active');
    });

    await act(async () => {
      await Promise.resolve();
    });

    expect(checkLocationPermission).toHaveBeenCalled();
    expect(setPreciseLocationGranted).toHaveBeenCalledWith(LOCATION_STATE.PRECISE);
    expect(fetchProfileRequest).toHaveBeenCalledWith({
      requestId: 'mock-session-id',
      fl: STORE,
      location: { latitude: 12.34, longitude: 56.78 },
      isSilent: true,
    });
  });

  it('should set userInStore to false when location permission is not precise', async () => {
    (checkLocationPermission as jest.Mock).mockResolvedValue(LOCATION_STATE.DENIED);

    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <Provider store={store}>{children}</Provider>
    );
    renderHook(() => useFetchStoreLocation(), { wrapper });

    await act(async () => {
      await Promise.resolve();
    });

    expect(checkLocationPermission).toHaveBeenCalled();
    expect(setPreciseLocationGranted).toHaveBeenCalledWith(LOCATION_STATE.DENIED);
    expect(setUserInStore).toHaveBeenCalledWith(false);
    expect(fetchProfileRequest).not.toHaveBeenCalled();
  });

  it('should not dispatch fetchProfileRequest if app state does not change to active', async () => {
    const appStateChangeCallback = jest.fn();
    const removeMock = jest.fn();

    const addEventListenerMock = require('react-native/Libraries/AppState/AppState')
      .addEventListener as jest.Mock;
    addEventListenerMock.mockImplementation((_, callback) => {
      appStateChangeCallback.mockImplementation(callback);
      return { remove: removeMock };
    });

    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <Provider store={store}>{children}</Provider>
    );
    renderHook(() => useFetchStoreLocation(), { wrapper });

    act(() => {
      appStateChangeCallback('background');
      appStateChangeCallback('background');
    });

    await Promise.resolve();

    expect(fetchProfileRequest).not.toHaveBeenCalled();
  });

  it('should clean up AppState event listener on unmount', () => {
    const removeMock = jest.fn();
    const addEventListenerMock = require('react-native/Libraries/AppState/AppState')
      .addEventListener as jest.Mock;

    // Mock addEventListener to return subscription with remove method
    addEventListenerMock.mockImplementation((_: string, _callback: (state: string) => void) => {
      // Don't need to call callback in this test, just return the subscription
      return { remove: removeMock };
    });

    // Mock useFocusEffect to properly simulate cleanup
    const mockUseFocusEffect = require('@react-navigation/native').useFocusEffect;
    let cleanupFn: (() => void) | undefined;

    mockUseFocusEffect.mockImplementation((cb: () => (() => void) | undefined) => {
      // Call the callback and store the cleanup function it returns
      cleanupFn = cb();
    });

    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <Provider store={store}>{children}</Provider>
    );
    const { unmount } = renderHook(() => useFetchStoreLocation(), { wrapper });

    // Explicitly call the cleanup function before unmounting
    act(() => {
      if (cleanupFn) {
        cleanupFn();
      }
    });

    unmount();

    // expect(removeMock).toHaveBeenCalled();
  });
});

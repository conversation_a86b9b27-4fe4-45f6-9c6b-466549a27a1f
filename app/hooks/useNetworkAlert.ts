import { Alert } from 'react-native';
import NetInfo from '@react-native-community/netinfo';
import { useTranslation } from 'react-i18next';
import DeviceInfo from 'react-native-device-info';

export const useNetworkAlert = () => {
    const { t } = useTranslation();

    // Checks if the device is connected to the internet
    const checkInternetConnection = async (): Promise<boolean> => {
        const state = await NetInfo.fetch();
        const isEmulator = await DeviceInfo.isEmulator();
        if (isEmulator) return !!state.isConnected;
        return !!state.isConnected && state.isInternetReachable !== false;
    };

    // Shows an alert if there is no internet connection and returns false, otherwise returns true
    const showAlertWithRetry = async (
        onRetry: () => void,
        title?: string,
        message?: string,
        cancel?: string,
        retry?: string
    ) => {
        const alertTitle = title ?? t('networkError.networkErrorTitle');
        const alertMessage = message ?? t('networkError.networkErrorMessage');
        const alertCancel = cancel ?? t('networkError.cancelButton');
        const alertRetry = retry ?? t('networkError.retryButton');
        Alert.alert(
            alertTitle,
            alertMessage,
            [
                { text: alertCancel },
                { text: alertRetry, onPress: onRetry },
            ],
            { cancelable: false }
        );
    };

    return {
        showAlertWithRetry,
        checkInternetConnection,
    };
};

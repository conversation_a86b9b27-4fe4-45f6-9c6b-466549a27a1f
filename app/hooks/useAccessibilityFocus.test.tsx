import { renderHook, act } from '@testing-library/react-native';
import { useNavigation } from '@react-navigation/native';
import { AccessibilityInfo, findNodeHandle } from 'react-native';
import useAccessibilityFocus from './useAccessibilityFocus';

jest.mock('@react-navigation/native', () => ({
  useNavigation: jest.fn(),
}));

jest.mock('react-native', () => ({
  AccessibilityInfo: {
    setAccessibilityFocus: jest.fn(),
    announceForAccessibility: jest.fn(),
  },
  findNodeHandle: jest.fn(() => 1),
}));

describe('useAccessibilityFocus', () => {
  let navigationMock;

  beforeEach(() => {
    navigationMock = {
      addListener: jest.fn((event, callback) => {
        if (event === 'focus') {
          setTimeout(callback, 0);
        }
        return jest.fn();
      }),
    };
    (useNavigation as jest.Mock).mockReturnValue(navigationMock);
  });


  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should return buttonRefs, setLastFocused, and setRef', () => {
    const { result } = renderHook(() => useAccessibilityFocus());
    expect(result.current).toHaveProperty('setLastFocused');
    expect(result.current).toHaveProperty('restoreFocus');
    expect(result.current).toHaveProperty('setRef');
  });

  it('should store and restore the last focused element', async () => {
    const { result } = renderHook(() => useAccessibilityFocus());

    const mockElement = {}; // dummy object
    (findNodeHandle as jest.Mock).mockReturnValue(1); // simulate native tag

    act(() => {
      result.current.setRef('button1')(mockElement as any);
      result.current.setLastFocused('button1');
    });

    await act(async () => {
      result.current.restoreFocus();
      await new Promise((resolve) => setTimeout(resolve, 150)); // allow time for focus logic
    });

    expect(findNodeHandle).toHaveBeenCalledWith(mockElement);
    expect(AccessibilityInfo.setAccessibilityFocus).toHaveBeenCalledWith(1);
  });

  it('should restore focus on navigation focus', () => {
    jest.useFakeTimers();

    const mockElement = {};
    (findNodeHandle as jest.Mock).mockReturnValue(1);

    const navigationFocusCallbacks: Function[] = [];

    const mockNavigation = {
      addListener: jest.fn((event, cb) => {
        if (event === 'focus') navigationFocusCallbacks.push(cb);
        return jest.fn(); // unsubscribe
      }),
    };

    (useNavigation as jest.Mock).mockReturnValue(mockNavigation);

    const { result } = renderHook(() => useAccessibilityFocus());

    // Setup element
    act(() => {
      result.current.setRef('btn')(mockElement as any);
      result.current.setLastFocused('btn');
    });

    // Simulate navigation focus event
    act(() => {
      navigationFocusCallbacks.forEach((cb) => cb());
    });

    // Run the timers to simulate setTimeout inside restoreFocus
    jest.runAllTimers();

    expect(findNodeHandle).toHaveBeenCalledWith(mockElement);
    expect(AccessibilityInfo.setAccessibilityFocus).toHaveBeenCalledWith(1);
    expect(AccessibilityInfo.announceForAccessibility).toHaveBeenCalledWith('');

    jest.useRealTimers();
  });

});

import { useEffect, useRef, useCallback } from "react";
import {
  AccessibilityInfo,
  findNodeHandle,
  View
} from "react-native";
import { useNavigation } from "@react-navigation/native";

// Store the last focused element globally (across re-renders)
const lastFocusedRef = { current: null as null | View };

const useAccessibilityFocus = () => {
  const navigation = useNavigation();
  const buttonRefs = useRef<Record<string, View | null>>({});

  // Set a ref to track last focused element
  const setLastFocused = useCallback((key: string) => {
    if (buttonRefs.current[key]) {
      lastFocusedRef.current = buttonRefs.current[key];
    }
  }, []);

  // Restore focus programmatically (used after modal closes or screen gains focus)
  const restoreFocus = useCallback(() => {
    if (lastFocusedRef.current) {
      const reactTag = findNodeHandle(lastFocusedRef.current);
      if (reactTag) {
        setTimeout(() => {
          AccessibilityInfo.setAccessibilityFocus(reactTag);
          setTimeout(() => {
            AccessibilityInfo.announceForAccessibility("");
          }, 500);
        }, 100); // ensures layout is ready
      }
    }
  }, []);

  // Register the view ref for a given key
  const setRef = (key: string) => (el: View | null) => {
    if (el) {
      buttonRefs.current[key] = el;
    }
  };

  // Automatically restore focus on screen transition
  useEffect(() => {
    const unsubscribe = navigation.addListener("focus", () => {
      restoreFocus();
    });
    return unsubscribe;
  }, [navigation, restoreFocus]);

  return {
    setLastFocused,
    restoreFocus,
    setRef,
  };
};

export default useAccessibilityFocus;

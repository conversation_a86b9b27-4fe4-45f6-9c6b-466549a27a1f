import { useEffect, useRef, useState } from 'react';
import { Platform, useWindowDimensions } from 'react-native';

export const useFontScaleRerender = () => {
  const { fontScale } = useWindowDimensions();
  const prevFontScale = useRef(fontScale);
  const [key, setKey] = useState(0);

  useEffect(() => {
    if (prevFontScale.current !== fontScale) {
      prevFontScale.current = fontScale;
      setKey((prev) => prev + 1);
    }
  }, [fontScale]);

  return Platform.OS == 'android' && key;
};

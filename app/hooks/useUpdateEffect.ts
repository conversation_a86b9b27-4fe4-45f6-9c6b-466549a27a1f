/**
 * useUpdateEffect
 * ------------------------------
 * A custom React hook that behaves like `useEffect`, 
 * but **skips execution on the initial mount**.
 * 
 * This is useful when you want to run side-effects 
 * only in response to dependency changes — not on first render.
 * 
 * ✅ Common use cases:
 * - Triggering API calls **only** after a value changes
 * - Avoiding unnecessary effects on component load
 * - Handling controlled state updates
 * 
 * @param effect - The effect callback function (like in useEffect)
 * @param deps - Dependency array that triggers the effect on change
 * 
 * 🔁 Runs: Only when dependencies change after initial render
 * 🚫 Skips: First render
 * 
 * Example:
 * ```ts
 * useUpdateEffect(() => {
 *   console.log('Runs only when "count" changes, not on mount');
 * }, [count]);
 * ```
 */
import { useEffect, useRef, EffectCallback, DependencyList } from 'react';

export function useUpdateEffect(effect: EffectCallback, deps: DependencyList) {
    const isFirstRender = useRef(true);

    useEffect(() => {
        if (isFirstRender.current) {
            isFirstRender.current = false;
            return;
        }
        return effect();
    }, deps);
}
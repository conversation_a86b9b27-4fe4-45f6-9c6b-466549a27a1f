import { useEffect, useState } from "react";
import dayjs from "dayjs";
import es from "dayjs/locale/es";
import en from "dayjs/locale/en";
import weekOfYear from "dayjs/plugin/weekOfYear";
import { ShiftState } from "../shared/constants";
import { useSelector } from "react-redux";
dayjs.extend(weekOfYear);

export interface WeekItem {
  dateStr: string;
  isToday: boolean;
  isSelected: boolean;
  hasShift: boolean;
  accessibilityLabel: string;
  shiftStatus: ShiftState;
  shift?: { workDate: string;[key: string]: any };
  pastWorkedHoursStartTime?: string;
  pastWorkedHoursEndTime?: string;
  workedHours?: any;
}

/**
 * Custom hook for managing week-based calendar navigation and date calculations
 * 
 * Provides functionality for:
 * - Week navigation (previous/next)
 * - Current week state management
 * - Date formatting and calculations
 * - Week number and year extraction
 * 
 * The hook initializes with the current week (starting from Sunday) and allows
 * navigation to previous or next weeks while maintaining all relevant date information.
 * 
 * @returns {object} An object containing navigation function and week data
 */
export const useWeekCalendar = (): {
  navigateWeek: (direction: "prev" | "next") => void;
  weekStart: string;
  weekEnd: string;
  weekNumber: number;
  year: number;
  weekStartDate: string;
  weekEndDate: string;
  currentWeek: any,
  weekEndDay: string;
  weekStartDay: string;
} => {
  const [currentWeek, setCurrentWeek] = useState(dayjs().startOf("week"));

  const language = useSelector((state: any) => state.language);

  useEffect(() => {
    if (language?.id === "es")
      dayjs.locale({
        ...es,
      });
    else
      dayjs.locale({
        ...en,
      });
    setCurrentWeek(dayjs().startOf("week"));
  }, [language]);

  /**
   * Navigates to the previous or next week.
   *
   * This function updates the current week state based on the direction provided.
   * It calculates the new week by adding or subtracting 7 days from the current week.
   *
   * @param {"prev" | "next"} direction - The direction to navigate:
   *   - "prev": Navigate to the previous week (subtract 7 days)
   *   - "next": Navigate to the next week (add 7 days)
   */
  const navigateWeek = (direction: "prev" | "next") => {
    const newWeek =
      direction === "prev"
        ? currentWeek.subtract(7, "day")
        : currentWeek.add(7, "day");
    setCurrentWeek(newWeek);
  };

  const weekStart = currentWeek.format("MMMM D");
  const weekEnd = currentWeek.add(6, "day").format("MMMM D");

  const weekStartDay = currentWeek.format("MMM D");
  const weekEndDay = currentWeek.clone().add(6, "day").format("MMM D");

  const weekStartDate = currentWeek.format("YYYY-MM-DD");
  const weekEndDate = currentWeek.add(6, "day").format("YYYY-MM-DD");
  const weekNumber = currentWeek.week();
  const year = currentWeek.year();

  return {
    navigateWeek,
    weekStart,
    weekEnd,
    weekNumber,
    year,
    weekStartDate,
    weekEndDate,
    currentWeek,
    weekStartDay,
    weekEndDay
  };
};

import { useFocusEffect } from '@react-navigation/native';
import { useCallback, useRef } from 'react';
import { AppState } from 'react-native';
import { useDispatch } from 'react-redux';

import { LOCATION_STATE, STORE } from '../shared/constants';
import * as AsyncStorage from '../store/AsyncStorage';
import { setPreciseLocationGranted } from '../store/reducers/locationAccessSlice';
import { fetchProfileRequest, setUserInStore } from '../store/reducers/profileSlice';
import { checkLocationPermission, getUserLocation } from '../utils/helpers';

import type { AppStateStatus } from 'react-native';

/**
 * Hook to fetch store-specific location data when:
 * - App resumes from background
 * - User grants precise location
 * - Screen comes to focus
 */
const useFetchStoreLocation = () => {
  const dispatch = useDispatch();

  // Track the current app state (active, background, inactive)
  const appState = useRef<AppStateStatus>(AppState.currentState);
  /**
   * Attempts to fetch store-specific API data based on the user's location.
   * - Only proceeds if precise location permission is granted.
   * - Dispatches `fetchProfileRequest` with user location and a session ID.
   */
  const fetchStoreApi = async (location: { latitude: number; longitude: number }) => {
    try {
      const requestId = await AsyncStorage.uniqueSessionId();
      dispatch(
        fetchProfileRequest({
          requestId,
          fl: STORE,
          location,
          isSilent: true,
        }),
      );
    } catch (err) {
      throw err;
    }
  };
  /**
   * Checks location permission and fetches data if permission is "precise"
   */
  const checkAndFetchIfPrecise = useCallback(async () => {
    const permission = await checkLocationPermission();
    dispatch(setPreciseLocationGranted(permission));
    if (permission === LOCATION_STATE.PRECISE) {
      const location = await getUserLocation();
      const { latitude, longitude } = location;
      await fetchStoreApi({ latitude, longitude });
    } else {
      dispatch(setUserInStore(false));
    }
  }, []);

  useFocusEffect(
    useCallback(() => {
      let subscription: any;

      checkAndFetchIfPrecise(); // on screen focus

      const handleAppStateChange = (nextAppState: AppStateStatus) => {
        const wasInactiveOrBackground = appState.current.match(/inactive|background/);
        appState.current = nextAppState;

        if (wasInactiveOrBackground && nextAppState === 'active') {
          checkAndFetchIfPrecise(); // on app foreground while screen is focused
        }
      };

      subscription = AppState.addEventListener('change', handleAppStateChange);

      return () => {
        subscription.remove();
      };
    }, [checkAndFetchIfPrecise]),
  );

  return { checkAndFetchIfPrecise };
};

export default useFetchStoreLocation;

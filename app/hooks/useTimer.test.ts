import { useTimer } from "./useTimer";
import { act, waitFor, renderHook } from "@testing-library/react-native";

describe("useTimer", () => {
  jest.useFakeTimers();

  it("should initialize with default values", () => {
    const { result } = renderHook(() => useTimer());
    expect(result.current.timeLeft).toBe(300);
    expect(result.current.isExpired).toBe(false);
  });

  it("should count down every second", async () => {
    const { result } = renderHook(() => useTimer({ initialTime: 5 }));
    act(() => {
      jest.advanceTimersByTime(1000);
    });
    await waitFor(() => expect(result.current.timeLeft).toBe(4));
  });

  it("should call onExpire when time runs out", () => {
    const onExpire = jest.fn();
    const { result } = renderHook(() => useTimer({ initialTime: 1, onExpire }));
    act(() => {
      jest.advanceTimersByTime(1000);
    });
    expect(result.current.isExpired).toBe(true);
    expect(onExpire).toHaveBeenCalled();
  });

  it("should reset the timer", () => {
    const { result } = renderHook(() => useTimer({ initialTime: 5 }));
    act(() => {
      jest.advanceTimersByTime(3000);
      result.current.reset();
    });
    expect(result.current.timeLeft).toBe(5);
    expect(result.current.isExpired).toBe(false);
  });

  it("should format time correctly", () => {
    const { result } = renderHook(() => useTimer());
    expect(result.current.formatTime(65)).toBe("1m 05s");
  });

  it("should not auto run if autoRun is false", () => {
    const { result } = renderHook(() =>
      useTimer({ initialTime: 5, autoRun: false })
    );
    act(() => {
      jest.advanceTimersByTime(1000);
    });
    expect(result.current.timeLeft).toBe(5);
  });
});

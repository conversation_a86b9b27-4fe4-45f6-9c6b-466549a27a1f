import { useState, useEffect, useCallback } from "react";

interface UseTimerReturn {
  timeLeft: number;
  isRunning: boolean;
  isExpired: boolean;
  reset: () => void;
  formatTime: (seconds: number) => string;
}

interface UseTimerProps {
  initialTime?: number;
  autoRun?: boolean;
  onExpire?: () => void;
}

export const useTimer = ({
  initialTime = 300,
  autoRun = true,
  onExpire,
}: UseTimerProps = {}): UseTimerReturn => {
  const [timeLeft, setTimeLeft] = useState<number>(initialTime);
  const [isExpired, setIsExpired] = useState<boolean>(false);
  const [isRunning, setIsRunning] = useState<boolean>(false);

  useEffect(() => {
    if (timeLeft === 0) {
      setIsExpired(true);
      setIsRunning(false);
      if (onExpire) {
        onExpire();
      }
      return;
    }

    if (autoRun || timeLeft < initialTime) {
      const timer = setInterval(() => {
        setTimeLeft((prevTime) => {
          if (prevTime > 0) return prevTime - 1;
          clearInterval(timer);
          return 0;
        });
      }, 1000);

      setIsRunning(true);

      return () => {
        clearInterval(timer);
        setIsRunning(false);
      };
    }
  }, [timeLeft, autoRun, initialTime, onExpire]);

  const reset = useCallback(() => {
    setTimeLeft(initialTime);
    setIsExpired(false);
    setIsRunning(false);
  }, [initialTime]);

  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${String(minutes).padStart(1, "0")}m ${String(secs).padStart(
      2,
      "0"
    )}s`;
  };

  return {
    timeLeft,
    isRunning,
    isExpired,
    reset,
    formatTime,
  };
};

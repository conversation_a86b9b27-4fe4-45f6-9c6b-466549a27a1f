import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface userState {
    preventHeaderAccessibility: boolean;
    preventTabsAccessibility: boolean;
}

const initialState: userState = {
    preventHeaderAccessibility: false,
    preventTabsAccessibility: false,
};

const accessibilitySlice = createSlice({
    name: 'accessibility',
    initialState,
    reducers: {
        setPreventHeaderAccessibility: (state, action: PayloadAction<boolean>) => {
            state.preventHeaderAccessibility = action.payload;
        },
        setPreventTabsAccessibility: (state, action: PayloadAction<boolean>) => {
            state.preventTabsAccessibility = action.payload;
        },
    },
});

export const {
    setPreventHeaderAccessibility,
    setPreventTabsAccessibility
} = accessibilitySlice.actions;

export default accessibilitySlice.reducer;
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface userState {
    isTablet: boolean;
    isLandscape: boolean;
}

const initialState: userState = {
    isTablet: false,
    isLandscape: false,
};

const deviceInfoSlice = createSlice({
    name: 'deviceInfo',
    initialState,
    reducers: {
        setIsDeviceTablet: (state, action: PayloadAction<boolean>) => {
            state.isTablet = action.payload;
        },
        setIsDeviceLandscape: (state, action: PayloadAction<boolean>) => {
            state.isLandscape = action.payload;
        },
    },
});

export const {
    setIsDeviceTablet,
    setIsDeviceLandscape
} = deviceInfoSlice.actions;

export default deviceInfoSlice.reducer;
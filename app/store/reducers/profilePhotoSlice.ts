import { ProfilePhotoResponse } from '../../misc/models/ProfileModal';
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface ProfileState {
    loading: boolean;
    error: string | null;
    photoUrl: string | null;
}

const initialState: ProfileState = {
    loading: false,
    error: null,
    photoUrl: null,
};

interface ProfilePhotoActionPayload {
    requestId: string;
    banner: string;
    empId?: string;
}


const profilePhotoSlice = createSlice({
    name: 'profilePhoto',
    initialState,
    reducers: {
        fetchProfilePhotoRequest: (state, action: PayloadAction<ProfilePhotoActionPayload>) => {
            state.loading = true;
            state.error = null;
        },
        fetchProfilePhotoSuccess: (state, action: PayloadAction<string | null>) => {
            state.loading = false;
            state.photoUrl = action.payload;
        },
        fetchProfilePhotoFailure: (state, action: PayloadAction<any>) => {
            state.loading = false;
            state.error = action.payload;
        },
        clearProfilePhoto: (state) => {
            state = initialState
        },
    },
});

// Export actions
export const {
    fetchProfilePhotoRequest,
    fetchProfilePhotoSuccess,
    fetchProfilePhotoFailure,
    clearProfilePhoto,
} = profilePhotoSlice.actions;

// Export reducer
export default profilePhotoSlice.reducer;
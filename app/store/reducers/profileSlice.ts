import { createSlice } from '@reduxjs/toolkit';

import type { CelebrationCardProps } from '../../../components/CelebrationCard';
import type { FL_TYPES, StoreLocation } from '../../config/payloadTypes';
import type {
  AssociateProfile,
  ProfileResponse,
  UcaProfile,
  Store,
} from '../../misc/models/ProfileModal';
import type { UserType } from '../../shared/constants';
import type { PayloadAction } from '@reduxjs/toolkit';

export interface ProfileState {
  response: ProfileResponse | null;
  profile?: AssociateProfile | null;
  ucaProfile?: UcaProfile | null;
  loading: boolean;
  error: string | null;
  slocData?: Store | null;
  banner: string; // * Banner is used for switching theme
  bannerName: string; // * Banner Name is legal name of company from Profile
  isUserInStore?: boolean; // * User is in store or not
  homeCelebrationCards?: CelebrationCardProps[];
  profileCelebrationCards?: CelebrationCardProps[];
  ucaProfileLoaded?: boolean;
  showSessionModal?: boolean;
  loggedIn?: boolean;
  userType?: UserType.NON_RESTRICTED | UserType.RESTRICTED;
  isTestAccount?: boolean;
}

const initialState: ProfileState = {
  loading: false,
  error: null,
  response: null,
  banner: '',
  loggedIn: false,
  bannerName: '',
  isUserInStore: false,
  homeCelebrationCards: [],
  profileCelebrationCards: [],
  ucaProfileLoaded: false,
  userType: undefined,
  showSessionModal: false,
  isTestAccount: false,
};

interface ProfileActionPayload {
  requestId: string;
  fl: FL_TYPES | FL_TYPES[];
  contactNbr?: string;
  email?: string;
  location?: StoreLocation;
  empId?: string;
  isSilent?: boolean; // * indicate this is silent call
}

interface BannerPayLoad {
  banner: string; // * Banner is used for switching theme
  bannerName: string; // * Banner Name is legal name of company from Profile
}

// Slice
const profileSlice = createSlice({
  name: 'profile',
  initialState,
  reducers: {
    fetchProfileRequest: (state, action: PayloadAction<ProfileActionPayload>) => {
      state.loading = true;
      state.error = null;
      if (state.response != null) state.response.errors = null;
      else state.response = null;
    },
    setProfileError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    fetchProfileSuccess: (state, action: PayloadAction<ProfileResponse>) => {
      state.loading = false;
      state.response = action.payload;
    },
    storeProfileData: (state, action: PayloadAction<AssociateProfile>) => {
      state.loading = false;
      state.profile = action.payload;
    },
    storeUcaProfileData: (state, action: PayloadAction<UcaProfile>) => {
      state.loading = false;
      state.ucaProfile = action.payload;
    },
    fetchStoreLocation: (state, action: PayloadAction<Store>) => {
      state.loading = false;
      state.slocData = action.payload;
    },
    fetchProfileFailure: (state, action: PayloadAction<string>) => {
      state.loading = false;
      state.error = action.payload;
    },
    setBanner: (state, action: PayloadAction<BannerPayLoad>) => {
      state.banner = action.payload.banner;
      state.bannerName = action.payload.bannerName;
    },
    setUserInStore: (state, action: PayloadAction<boolean>) => {
      state.isUserInStore = action.payload;
    },
    setProfileAsTestAccount: (state, action: PayloadAction<boolean>) => {
      state.isTestAccount = action.payload;
    },
    clearProfile: (state) => {
      state.loading = false;
      state.error = null;
      state.response = null;
      state.profile = null;
      state.ucaProfile = null;
      state.banner = '';
      state.bannerName = '';
      state.isUserInStore = false;
      state.isTestAccount = false;
    },
    setHomeCelebrationCards: (state, action) => {
      state.homeCelebrationCards = action.payload;
    },
    setProfileCelebrationCards: (state, action) => {
      state.profileCelebrationCards = action.payload;
    },
    setUserRestrictedStatus: (state, action) => {
      state.userType = action.payload;
    },
    setShowSessionModal: (state, action: PayloadAction<boolean>) => {
      state.showSessionModal = action.payload;
    },
    setLoggedIn: (state, action: PayloadAction<boolean>) => {
      state.loggedIn = action.payload;
    },
  },
});

// Export actions
export const {
  fetchProfileRequest,
  fetchProfileSuccess,
  fetchProfileFailure,
  storeProfileData,
  storeUcaProfileData,
  fetchStoreLocation,
  setBanner,
  setUserInStore,
  clearProfile,
  setHomeCelebrationCards,
  setProfileCelebrationCards,
  setUserRestrictedStatus,
  setShowSessionModal,
  setProfileError,
  setLoggedIn,
  setProfileAsTestAccount,
} = profileSlice.actions;

// Export reducer
export default profileSlice.reducer;

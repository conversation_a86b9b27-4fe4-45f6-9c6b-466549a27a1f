import { createSlice } from '@reduxjs/toolkit';

const confettiSlice = createSlice({
  name: 'confetti',
  initialState: {
    visible: false,
    triggerId: null,
    reduceMotionEnabled: null,
    screen: null,
  },
  reducers: {
    showConfetti: (state, { payload }) => {
      state.visible = true;
      state.triggerId = payload?.cardId;
      state.screen = payload?.currentScreen;
    },
    hideConfetti: (state) => {
      state.visible = false;
      state.screen = null;
    },
    setReduceMotion: (state, { payload }) => {
      state.reduceMotionEnabled = payload;
    },
  },
});

export const { showConfetti, hideConfetti, setReduceMotion } = confettiSlice.actions;
export default confettiSlice.reducer;

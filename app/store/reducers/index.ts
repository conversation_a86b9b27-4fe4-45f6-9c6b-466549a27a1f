// store/rootReducer.js
import { combineReducers } from "redux";
import authorableContentReducer from "./authorableContentSlice";
import { selectLanguageReducer } from "./selectLanguageSlice";
import { shiftReducer } from "./shiftSlice";
import scorecardSlice from "./scorecardSlice";
import deviceInfoReducer from "./deviceInfoSlice";
import profileReducer from "./profileSlice"
import announcementSlice from "./announcementSlice";
import { locationAccessReducer } from "./locationAccessSlice";
import profilePhotoReducer from "./profilePhotoSlice";
import errorReducer from "./errorSlice";
import featureFlagReducer from "./featureFlagSlice";
import confettiSlice from "./confettiSlice";
import otpVerifyReducer from "./otpVerifySlice";
import discountReducer from "./discountSlice";
import employeeScheduleReducer from "./employeeScheduleSlice";
import clockInStatusReducer from "./clockInStatusSlice";
import tncAgreementReducer from "./tncAgreementSlice";
import accessibilityReducer from "./accessibilitySlice";

const rootReducer = combineReducers({
  authorableContent: authorableContentReducer,
  language: selectLanguageReducer,
  shift: shiftReducer,
  rewardsScorecard: scorecardSlice,
  deviceInfo: deviceInfoReducer,
  profile: profileReducer,
  announcements: announcementSlice,
  locationAccess: locationAccessReducer,
  profilePhoto: profilePhotoReducer,
  errorState: errorReducer,
  featureFlags: featureFlagReducer,
  confetti: confettiSlice,
  otpVerify: otpVerifyReducer,
  discount: discountReducer,
  empSchedule: employeeScheduleReducer,
  clockInStatus: clockInStatusReducer,
  tncAgreement: tncAgreementReducer,
  accessibility: accessibilityReducer,
});

export default rootReducer;

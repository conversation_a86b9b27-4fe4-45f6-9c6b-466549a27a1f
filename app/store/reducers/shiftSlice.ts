import { SHIFT_ENDING_DURATION } from '../../shared/constants';
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Schedule } from '../../config/responseTypes';

export interface ShiftState {
    shiftStarted: boolean;
    lunchStarted: boolean;
    lunchEnded: boolean;
    clockedIn: boolean;
    clockedOut: boolean;
    lateShift: boolean;
    lateClockIn: boolean,
    lateClockInTime: string,
    lateClockInTimeDuration: number,
    timeAgo: string;
    shiftStartTime: string;
    shiftEndTime: string;
    shiftStartDate: string;
    shiftEndDate: string;
    clockInTime: string | undefined;
    clockOutTime: string;
    lateClockedOut: boolean;
    lateClockOutTimeDuration: number;
    lunchStartTime: string;
    lunchEndTime: string;
    shiftDuration?: string | undefined;
    durationInMinutes: number;
    lunchDurationInMinutes: number;
    sessionEnding: number;
    shiftEnded: boolean;
    todaysShift?: Schedule; 
    scheduleShift?: Schedule; 
}

const initialState: ShiftState = {
    shiftStartTime: "",
    shiftEndTime: "",
    shiftStartDate: "",
    shiftEndDate: "",
    shiftStarted: false,
    shiftDuration: undefined,
    clockedIn: false,
    clockInTime: "",
    lunchStarted: false,
    lunchStartTime: "",
    lunchEnded: false,
    lunchEndTime: "",
    lateShift: false,
    lateClockIn: false,
    lateClockInTime: "",
    lateClockInTimeDuration: 0,
    timeAgo: "",
    clockedOut: false,
    clockOutTime: "",
    lateClockedOut: false,
    lateClockOutTimeDuration: 0,
    durationInMinutes: 0,
    lunchDurationInMinutes: 0,
    sessionEnding: SHIFT_ENDING_DURATION + 1, //added buffer time
    shiftEnded: false,
    todaysShift: undefined, // store today's shift details
    scheduleShift: undefined, // store next shift details
};

export interface ShiftTiming {
    shiftStartEndTime?: string
    startTime?: string
    endTime?: string
    startDate?: string
    endDate?: string
    todaysShift?: Schedule // optional, to store today's shift details
    scheduleShift?: Schedule // optional, to store next shift details
}

const shiftSlice = createSlice({
    name: 'shift',
    initialState,
    reducers: {
        setShiftTime(state, action: PayloadAction<ShiftTiming>) {
            const { shiftStartEndTime, startTime, endTime, startDate, endDate } = action.payload;
            state.shiftDuration = shiftStartEndTime;
            state.shiftStartTime = startTime ?? "";
            state.shiftEndTime = endTime ?? "";
            state.shiftStartDate = startDate ?? "";
            state.shiftEndDate = endDate ?? "";
            state.todaysShift = action.payload.todaysShift; 
            state.scheduleShift = action.payload.scheduleShift; 
            state.clockedIn = false;
            state.clockInTime = "";
            state.lunchStarted = false;
            state.lateClockIn = false;
            state.lateClockInTimeDuration = 0;
            state.lunchStartTime = "";
            state.lunchEnded = false;
            state.lunchEndTime = "";
            state.clockedOut = false;
            state.lateClockedOut = false;
            state.lateClockOutTimeDuration = 0;
            state.clockOutTime = "";
            state.lunchDurationInMinutes = 0;
            state.sessionEnding = SHIFT_ENDING_DURATION + 1
        },
        //Defining Shift started or not as per Shift Timing
        setShiftStarted(state) {
            state.shiftStarted = true;
        },
        //Defining Shift Ended or not as per Shift Timing
        setShiftEnd(state, action: PayloadAction<Date>) {
            state.shiftEnded = true;
        },

        setLateShift(state, action: PayloadAction<boolean>) { // need to check whether is require or not
            state.lateShift = action.payload;
        },
        //stating Clocking Time after clock in
        setClockedIn(state, action: PayloadAction<string>) {
            state.clockInTime = action.payload;
            state.clockedIn = true;
            state.clockedOut = false;
            state.lunchStarted = false;
            state.lunchEnded = false;
            //Updating late clock in state, only when it needed
            if(state.lateClockIn){
                state.lateClockIn = false;
                state.lateClockInTimeDuration = 0;
            }
        },
        // This is used to reset the clocked in state when there is an error fetching the clock in status
        // This is used in clockInStatusSaga.ts
        resetClockedIn(state) {
            state.clockedIn = false;
            state.clockInTime = "";
            state.lateClockIn = false;
            state.lateClockInTimeDuration = 0;
            state.durationInMinutes = 0; // Resetting duration in minutes when clocked in state is reset
        },
        //Stating for Late clock in(shift already started)
        setLateClockIn(state, action: PayloadAction<number>) {
            state.lateClockIn = true;
            state.lateClockInTimeDuration = action.payload;
        },
        //Stating Lunch Started after Clock-In
        setLunchStarted(state, action: PayloadAction<string>) {
            state.lunchStarted = true;
            state.lunchDurationInMinutes = 0;
            state.lunchStartTime = action.payload;
        },
        //Stating Lunch End after Lunch Started
        setLunchEnded(state, action: PayloadAction<boolean>) {
            state.lunchStarted = true;
            state.lunchEnded = action.payload;
        },
        //Stating after Clock-Out
        setClockedOut(state, action: PayloadAction<string>) {
            state.clockOutTime = action.payload;
            // state.clockedIn = false;
            state.clockedOut = true;
            if(state.lateClockedOut){ //Updating late clock out state, only it needed
                state.lateClockedOut = false;
                state.lateClockOutTimeDuration = 0;
            }
        },
        //Stating for Late clock out(shift already ended)
        setLateClockOut(state, action: PayloadAction<number>) {
            state.lateClockedOut = true;
            state.lateClockOutTimeDuration = action.payload;
        },
        setTimeAgo(state, action: PayloadAction<string>) {
            state.timeAgo = action.payload;
        },
        resetShiftState(_state) {
            return initialState;
        },
        setDurationInMinutes: (state, action) => {
            state.durationInMinutes = action.payload;
        },
        //Storing lunch break in minutes
        setLunchDurationInMinutes: (state, action) => {
            state.lunchDurationInMinutes = action.payload;
        },
        setShiftEndSession: (state, action: PayloadAction<number>) => {
            state.sessionEnding = action.payload;
        },
    }
});

// Export the actions
export const {
    setShiftTime,
    setShiftStarted,
    setShiftEnd,
    setLateShift,
    setClockedIn,
    resetClockedIn,
    setLateClockIn,
    setLunchStarted,
    setLunchEnded,
    setClockedOut,
    setLateClockOut,
    setTimeAgo,
    resetShiftState,
    setDurationInMinutes,
    setLunchDurationInMinutes,
    setShiftEndSession,
} = shiftSlice.actions;

// Export the reducer
export const shiftReducer = shiftSlice.reducer;

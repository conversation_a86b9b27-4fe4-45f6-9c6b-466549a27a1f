import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { OtpVerifyResponse, OtpVerifyActionPayload } from '../../misc/models/ProfileModal';


interface OtpError {
    status?: number;
    message?: string;
}
interface OtpVerifyState {
    loading: boolean;
    error: OtpError | string | null;
    response: OtpVerifyResponse | null;
}

const initialState: OtpVerifyState = {
    loading: false,
    error: null,
    response: null,
};


const otpVerifySlice = createSlice({
    name: 'otpVerify',
    initialState,
    reducers: {
        fetchOtpVerifyRequest: (state, action: PayloadAction<OtpVerifyActionPayload>) => {
            state.loading = true;
            state.error = null;
        },
        fetchOtpVerifySuccess: (state, action: PayloadAction<OtpVerifyResponse>) => {
            state.loading = false;
            state.response = action.payload;
        },
        fetchOtpVerifyFailure: (state, action: PayloadAction<any>) => {
            state.loading = false;
            state.error = action.payload;
        },
        clearOtpVerify: (state) => {
            state = initialState
        },
    },
});

// Export actions
export const {
    fetchOtpVerifyRequest,
    fetchOtpVerifySuccess,
    fetchOtpVerifyFailure,
    clearOtpVerify,
} = otpVerifySlice.actions;

// Export reducer
export default otpVerifySlice.reducer;
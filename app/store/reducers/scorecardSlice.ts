import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { RewardScorecard } from "../../config/payloadTypes";
import { RewardsScorecardResponse } from "../../config/responseTypes";

// Define the shape of the action payload
interface RewardScorecardPayload {
  body: RewardScorecard;
}

const initialState = {
  status: "idle", // 'idle', 'loading', 'succeeded', 'failed'
  data: {},
  error: null,
};

const scorecardSlice = createSlice({
  name: "rewards",
  initialState: initialState,
  reducers: {
    fetchRewardsScorecardRequest: (
      state,
      action: PayloadAction<RewardScorecardPayload>
    ) => {
      state.status = "loading";
    },
    fetchRewardsScorecardSuccess: (
      state,
      action: PayloadAction<RewardsScorecardResponse>
    ) => {
      state.status = "success";
      state.data = action.payload;
      state.error = null;
    },
    fetchRewardsScorecardFailure: (state, action) => {
      state.status = "failed";
      state.data = {};
      state.error = action.payload;
    },
  },
});

// Export actions
export const {
  fetchRewardsScorecardRequest,
  fetchRewardsScorecardSuccess,
  fetchRewardsScorecardFailure,
} = scorecardSlice.actions;

export default scorecardSlice.reducer;

import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { TncAgreementResponse, TncAgreementActionPayload } from '../../misc/models/ProfileModal';

interface TncAgreementError {
    code?: number;
    message?: string;
    statusCode: number;
}
interface TncAgreementState {
    loading: boolean;
    errors: TncAgreementError | string | null;
    result: TncAgreementResponse | null;
}

const initialState: TncAgreementState = {
    loading: false,
    errors: null,
    result: null,
};


const tncAgreementSlice = createSlice({
    name: 'tncAgreement',
    initialState,
    reducers: {
        fetchTncAgreementRequest: (state, action: PayloadAction<TncAgreementActionPayload>) => {
            state.loading = true;
            state.errors = null;
        },
        fetchTncAgreementSuccess: (state, action: PayloadAction<TncAgreementResponse>) => {
            state.loading = false;
            state.result = action.payload;
        },
        fetchTncAgreementFailure: (state, action: PayloadAction<any>) => {
            state.loading = false;
            state.errors = action.payload;
        },
        clearTncAgreement: (state) => {
            state = initialState
        },
    },
});

// Export actions
export const {
    fetchTncAgreementRequest,
    fetchTncAgreementSuccess,
    fetchTncAgreementFailure,
    clearTncAgreement,
} = tncAgreementSlice.actions;

// Export reducer
export default tncAgreementSlice.reducer;
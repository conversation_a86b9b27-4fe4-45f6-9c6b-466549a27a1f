import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { EmployeeScheduleParams } from '../../config/payloadTypes';
import { EmployeeScheduleResponse } from '../../config/responseTypes';

/**
 * State interface for employee schedule.
 */
interface EmployeeScheduleState {
    data: EmployeeScheduleResponse | {};
    error: string | null;
    loading: boolean;
}

/**
 * Payload interface for fetching employee schedule.
 */
interface EmployeeScheduleUrlPayload {
    params: EmployeeScheduleParams;
}

/**
 * Initial state for the employee schedule slice.
 */
const initialState: EmployeeScheduleState = {
    data: {},
    error: null,
    loading: false,
};

/**
 * Redux slice for managing employee schedule state.
 * Handles fetching, success, failure, and clearing of employee schedule data.
 */
const employeeScheduleSlice = createSlice({
    name: 'empSchedule',
    initialState,
    reducers: {
        /**
         * Triggered when a fetch employee schedule request is made.
         * Sets loading to true and clears any previous error.
         */
        fetchEmployeeScheduleRequest: (state, action: PayloadAction<EmployeeScheduleUrlPayload>) => {
            state.data = {};
            state.loading = true;
            state.error = null;
        },
        /**
        * Triggered when employee schedule data is successfully fetched.
        * Sets loading to false and updates the data.
        */
        fetchEmployeeScheduleSuccess: (state, action: PayloadAction<EmployeeScheduleResponse>) => {
            state.loading = false;
            state.data = action.payload;
        },
        /**
         * Triggered when fetching employee schedule fails.
         * Sets loading to false and updates the error message.
         */
        fetchEmployeeScheduleFailure: (state, action: PayloadAction<any>) => {
            state.loading = false;
            state.error = action.payload;
        },
        /**
        * Clears the employee schedule state, resetting to initial values.
        */
        clearEmployeeSchedule: (state) => {
            state = initialState
            state.data = {}; // Resetting data to an empty object
            state.error = null;
            state.loading = false;
        },
    },
});

// Export actions
export const {
    fetchEmployeeScheduleRequest,
    fetchEmployeeScheduleSuccess,
    fetchEmployeeScheduleFailure,
    clearEmployeeSchedule,
} = employeeScheduleSlice.actions;

// Export reducer
export default employeeScheduleSlice.reducer;
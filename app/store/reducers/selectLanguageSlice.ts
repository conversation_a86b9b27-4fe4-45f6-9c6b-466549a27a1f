import { languages } from "../../shared/constants";
import { createSlice } from "@reduxjs/toolkit";

export interface languagetypes {
  id: string;
  image: string;
  label: string;
  value: string;
}

const initialState = languages[0];

const selectLanguage = createSlice({
  name: "language",
  initialState,
  reducers: {
    updateLanguage: (_state, action) => {
      return action.payload;
    },
  },
});

export const { updateLanguage } = selectLanguage.actions;

export const selectLanguageReducer = selectLanguage.reducer;
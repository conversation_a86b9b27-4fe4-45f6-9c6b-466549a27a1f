import { AuthorableContent } from '../../misc/models/authorableModels';
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface AuthorableContentState {
    authorableContent: AuthorableContent;
    loading: boolean;
    error: string | null;
}

const initialState: AuthorableContentState = {
  authorableContent: {
    loginPage: {
      title: '',
      subTitle: '',
      primaryCTA: '',
      bgImage: '',
      titleES: '',
      subTitleES: '',
      primaryCTAES: '',
      bgImageTabletLandscape: '',
      bgImageTabletPortrait: '',
    },
    accountLinkage: {
      title: '',
      content: [],
    },
    onboardingInfo: {
      image: '',
      primaryText: '',
      secondaryText: '',
      cta: '',
    },
    onboardingAssociateInfo: {
      v1: [],
      v2: [],
    },
    onboardingManagerInfo: {
      v1: [],
      v2: [],
    },
    locationOnboarding: {
      v1: [],
    },
    storeInfo: {
      radius: 0,
    },
    manager: undefined,
    associate: undefined,
  },
  loading: false,
  error: '',
};

// Slice
const authorableContentSlice = createSlice({
    name: 'authorableContent',
    initialState,
    reducers: {
        fetchAuthorableContentRequest: (state) => {
            state.loading = true;
            state.error = ""
        },
        fetchAuthorableContentSuccess: (state, action: PayloadAction<AuthorableContent>) => {
            state.loading = false;
            state.error = null;
            state.authorableContent = action.payload;
        },
        fetchAuthorableContentFailure: (state, action: PayloadAction<string>) => {
            state.loading = false;
            state.error = action.payload;
        },
    },
});

// Export actions
export const {
    fetchAuthorableContentRequest,
    fetchAuthorableContentSuccess,
    fetchAuthorableContentFailure,
} = authorableContentSlice.actions;

// Export reducer
export default authorableContentSlice.reducer;
import { createSlice, PayloadAction } from "@reduxjs/toolkit"

export type LocationAccuracy = "precise" | "approximate" | "denied";

export interface LocationState {
    isSkipped: boolean,
    granted: boolean,
    isPrecise: LocationAccuracy,
    isPrefered: boolean,
}

const initialState: LocationState = {
    isSkipped: false,
    granted: false,
    isPrecise: "denied",
    isPrefered: false,
}

const locationSlice = createSlice({
    name: 'location',
    initialState,
    reducers: {
        setLocationSkipped(state, action: PayloadAction<boolean>) {
            state.isSkipped = action.payload;
        },
        setPreciseLocationGranted(state, action: PayloadAction<LocationAccuracy>) {
            state.isPrecise = action.payload;
        },

    }
})

// Export the actions
export const {
    setLocationSkipped,
    setPreciseLocationGranted,
} = locationSlice.actions;

export const locationAccessReducer = locationSlice.reducer;

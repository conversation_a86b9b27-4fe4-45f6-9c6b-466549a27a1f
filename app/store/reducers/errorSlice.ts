import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface errorState {
    authError: boolean;
    announcemntNetworkError: boolean;
    announcementServerError: boolean;
}

const initialState: errorState = {
    authError: false,
    announcemntNetworkError: false,
    announcementServerError: false,
};

const errorSlice = createSlice({
    name: 'errorState',
    initialState,
    reducers: {
        setAuthError: (state, action: PayloadAction<boolean>) => {
            state.authError = action.payload;
        },
        setAnnouncemntNetworkError: (state, action: PayloadAction<boolean>) => {
            state.announcemntNetworkError = action.payload;
        },
        setAnnouncementServerError: (state, action: PayloadAction<boolean>) => {
            state.announcementServerError = action.payload;
        },
    },
});

export const {
    setAuthError,
    setAnnouncemntNetworkError,
    setAnnouncementServerError
} = errorSlice.actions;

export default errorSlice.reducer;
import { FeatureFlagResponse } from '../../misc/models/ProfileModal';
import { createSlice, PayloadAction } from '@reduxjs/toolkit';


type FeatureFlag = FeatureFlagResponse;

interface FeatureFlagsState {
    flags: FeatureFlag[];
    loading: boolean,
    error: string | null;
    response: FeatureFlag[] | null;
}

const initialState: FeatureFlagsState = {
    flags: [],
    loading: false,
    error: null,
    response: null,
};

interface FeatureFlagRequestPayload {
    requestId?: string;
    banner: string;
    empId?: string;
}

const featureFlagSlice = createSlice({
    name: 'featureFlag',
    initialState,
    reducers: {
        fetchfeatureFlagRequest: (state, action: PayloadAction<FeatureFlagRequestPayload>) => {
            state.loading = true;
            state.error = null;
        },
        fetchfeatureFlagSuccess: (state, action: PayloadAction<FeatureFlag[]>) => {
            state.loading = false;
            state.response = action.payload;
            state.flags = action.payload;
        },
        fetchfeatureFlagFailure: (state, action: PayloadAction<any>) => {
            state.loading = false;
            state.error = action.payload;
        },
        setFeatureFlags(state, action) {
            state.flags = action.payload;
        },
        clearfeatureFlag: (state) => {
            state = initialState
        },
    },
});

// Export actions
export const {
    fetchfeatureFlagRequest,
    fetchfeatureFlagSuccess,
    fetchfeatureFlagFailure,
    clearfeatureFlag,
    setFeatureFlags
} = featureFlagSlice.actions;

// Export reducer
export default featureFlagSlice.reducer;
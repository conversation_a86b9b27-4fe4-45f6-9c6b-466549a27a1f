import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ClockInStatusActionPayload, ClockInStatusResponse,  Error } from '../../misc/models/ScheduleModal';


interface ClockInStatusState {
    loading: boolean;
    error: Error | string | null;
    response: ClockInStatusResponse | null;
}

const initialState: ClockInStatusState = {
    loading: false,
    error: null,
    response: null,
};


const clockInStatusSlice = createSlice({
    name: 'clockInStatus',
    initialState,
    reducers: {
        fetchClockInStatusRequest: (state, action: PayloadAction<ClockInStatusActionPayload>) => {
            state.loading = true;
            state.error = null;
        },
        fetchClockInStatusSuccess: (state, action: PayloadAction<ClockInStatusResponse>) => {
            state.loading = false;
            state.response = action.payload;
        },
        fetchClockInStatusFailure: (state, action: PayloadAction<any>) => {
            state.loading = false;
            state.error = action.payload;
        },
        clearClockInStatus: (state) => {
            state.loading= false
            state.error= null
            state.response= null          
        },
    },
});

// Export actions
export const {
    fetchClockInStatusRequest,
    fetchClockInStatusSuccess,
    fetchClockInStatusFailure,
    clearClockInStatus,
} = clockInStatusSlice.actions;

// Export reducer
export default clockInStatusSlice.reducer;
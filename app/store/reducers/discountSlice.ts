import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { DiscountResponse, DiscountActionPayload } from '../../misc/models/ProfileModal';

interface DiscountError {
    status?: number;
    message?: string;
}
interface DiscountState {
    loading: boolean;
    error: DiscountError | string | null;
    response: DiscountResponse | null;
}

const initialState: DiscountState = {
    loading: false,
    error: null,
    response: null,
};


const discountSlice = createSlice({
    name: 'discount',
    initialState,
    reducers: {
        fetchDiscountRequest: (state, action: PayloadAction<DiscountActionPayload>) => {
            state.loading = true;
            state.error = null;
        },
        fetchDiscountSuccess: (state, action: PayloadAction<DiscountResponse>) => {
            state.loading = false;
            state.response = action.payload;
        },
        fetchDiscountFailure: (state, action: PayloadAction<any>) => {
            state.loading = false;
            state.error = action.payload;
        },
        clearDiscount: (state) => {
            state = initialState
        },
    },
});

// Export actions
export const {
    fetchDiscountRequest,
    fetchDiscountSuccess,
    fetchDiscountFailure,
    clearDiscount,
} = discountSlice.actions;

// Export reducer
export default discountSlice.reducer;
import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { AnnouncementsParams, AnnouncementUrlParams } from "../../config/payloadTypes";
import { AnnouncementsResponse, AnnouncementUrlResponse } from "../../config/responseTypes";

// Define the shape of the action payload
interface AnnouncementsPayload {
  params: AnnouncementsParams;
}
interface AnnouncementUrlPayload {
  params: AnnouncementUrlParams;
}

interface AnnouncementState {
  data: AnnouncementsResponse | {};
  urldetails?: AnnouncementUrlResponse | {};
  error: string | null;
  loading: boolean;
  urlDetailsLoading: boolean;
  urlDetailsError: string | null;
  cacheImgUrls?: string[];
  allAnnouncemnts?: AnnouncementsResponse | {};
  allAnnouncemntsError: string | null;
  allAnnouncemntsLoading: boolean;
  allCacheImgUrls?: string[];
}

const initialState: AnnouncementState = {
  data: {},
  urldetails: {},
  error: null,
  loading: false,
  urlDetailsLoading: false,
  urlDetailsError: null,
  cacheImgUrls: [],
  allAnnouncemnts: {},
  allAnnouncemntsError: null,
  allAnnouncemntsLoading: false,
  allCacheImgUrls: [],
};

const announcementSlice = createSlice({
  name: "announcements",
  initialState,
  reducers: {
    fetchAnnouncementsRequest: (
      state,
      action: PayloadAction<AnnouncementsPayload>
    ) => {
      state.loading = true;
      state.error = null;
    },
    fetchAnnouncementsSuccess: (
      state,
      action: PayloadAction<AnnouncementsResponse>
    ) => {
      state.data = action.payload;
      state.loading = false;
      state.error = null;
    },
    fetchAnnouncementsFailure: (
      state,
      action: PayloadAction<string>
    ) => {
      state.data = {};
      state.error = action.payload;
      state.loading = false;
    },
    fetchAllAnnouncementsRequest: (
      state,
      action: PayloadAction<AnnouncementsPayload>
    ) => {
      state.allAnnouncemntsLoading = true;
      state.allAnnouncemntsError = null;
    },
    fetchAllAnnouncementsSuccess: (
      state,
      action: PayloadAction<AnnouncementsResponse>
    ) => {
      state.allAnnouncemnts = action.payload;
      state.allAnnouncemntsLoading = false;
      state.allAnnouncemntsError = null;
    },
    fetchAllAnnouncementsFailure: (
      state,
      action: PayloadAction<string>
    ) => {
      state.allAnnouncemnts = {};
      state.allAnnouncemntsError = action.payload;
      state.allAnnouncemntsLoading = false;
    },
    fetchAnnouncementUrlRequest: (
      state,
      action: PayloadAction<AnnouncementUrlPayload>
    ) => {
      state.urlDetailsLoading = true;
      state.urlDetailsError = null;
    },
    fetchAnnouncementUrlSuccess: (
      state,
      action: PayloadAction<AnnouncementUrlResponse>
    ) => {
      state.urldetails = action.payload;
      state.urlDetailsLoading = false;
      state.urlDetailsError = null;
    },
    fetchAnnouncementUrlFailure: (
      state,
      action: PayloadAction<string>
    ) => {
      state.urldetails = {};
      state.urlDetailsError = action.payload;
      state.urlDetailsLoading = false;
    },
    setcacheImgUrls: (state, action) => {
      state.cacheImgUrls = action.payload;
    },
    setAllcacheImgUrls: (state, action) => {
      state.allCacheImgUrls = action.payload;
    },
    clearAnnouncements: (
      state
    ) => {
      state.data = {};
      state.error = null;
      state.loading = false;
      state.allAnnouncemnts = {};
      state.allAnnouncemntsError = null;
      state.allAnnouncemntsLoading = false;
      state.urldetails = {};
      state.urlDetailsError = null;
      state.urlDetailsLoading = false;
    },
  },
});

// Export actions
export const {
  fetchAnnouncementsRequest,
  fetchAnnouncementsSuccess,
  fetchAnnouncementsFailure,
  fetchAllAnnouncementsFailure,
  fetchAllAnnouncementsRequest,
  fetchAllAnnouncementsSuccess,
  fetchAnnouncementUrlFailure,
  fetchAnnouncementUrlRequest,
  fetchAnnouncementUrlSuccess,
  clearAnnouncements,
  setcacheImgUrls,
  setAllcacheImgUrls
} = announcementSlice.actions;

export default announcementSlice.reducer;

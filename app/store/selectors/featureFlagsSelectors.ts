import { RootState } from '../index';

// Constants for feature flag names
const profileEnabledFlag: string = "profileEnabled";
const profileHeaderEnabledFlag: string = "profileHeaderEnabled";
const profilePhotoEnabledFlag: string = "profilePhotoEnabled";
const sharePointEnabledFlag: string = "diaaSharepoint";
const scheduleTabEnabledFlag: string = "diaaScheduleTab";
const socialTabEnabledFlag: string = "diaaSocialTab";
const resourceTabEnabledFlag: string = "diaaResourcesTab";
const roleSelectionEnabledFlag: string = "diaaLoginAs";
const roleGovernanceEnabledFlag: string = "diaaGovernance";
const learningEnabledFlag: string = "diaaLearningModule";
const notificationsEnabledFlag: string = "diaaNotifications";
const bulletinEnabledFlag: string = "diaaBulletin";

/**
 * Generic helper to get the value of a feature flag.
 * 
 * @param state - The root state of the application.
 * @param flagName - The name of the feature flag to retrieve.
 * @returns The value of the feature flag (true/false). Defaults to false if the flag is not found.
 */
export const getFeatureFlagValue = (state: RootState, flagName: string): boolean => {
    const flag = state.featureFlags?.flags?.find((f) => f.featureFlagName === flagName);
    return flag ? flag.featureFlagValue : false; // default to false if not found
};

/**
 * Selector to check if the "Profile" feature is enabled.
 * 
 * @param state - The root state of the application.
 * @returns True if the "Profile" feature is enabled, otherwise false.
 */
export const isProfileEnabled = (state: RootState): boolean => {
    return getFeatureFlagValue(state, profileEnabledFlag);
};

/**
 * Selector to check if the "Profile Header" feature is enabled.
 * 
 * @param state - The root state of the application.
 * @returns True if the "Profile header" feature is enabled, otherwise false.
 */
export const profileHeaderEnabled = (state: RootState): boolean => {
    return getFeatureFlagValue(state, profileHeaderEnabledFlag);
};

/**
 * Selector to check if the "Profile photo" feature is enabled.
 * 
 * @param state - The root state of the application.
 * @returns True if the "Profile photo" feature is enabled, otherwise false.
 */
export const profilePhotoEnabled = (state: RootState): boolean => {
    return getFeatureFlagValue(state, profilePhotoEnabledFlag);
};

/**
 * Selector to check if the "Profile photo" feature is enabled.
 * 
 * @param state - The root state of the application.
 * @returns True if the "Announcement Carousel" feature is enabled, otherwise false.
 */
export const sharePointEnabled = (state: RootState): boolean => {
    return getFeatureFlagValue(state, sharePointEnabledFlag);
};

/**
* Selector to check if the "scheduleTabEnabled" feature is enabled.
*
* @param state - The root state of the application.
* @returns True if the "scheduleTabEnabled" feature is enabled, otherwise false.
*/
export const scheduleTabEnabled = (state: RootState): boolean => {
    return getFeatureFlagValue(state, scheduleTabEnabledFlag);
};


/**
* Selector to check if the "socialTabEnabled" feature is enabled.
*
* @param state - The root state of the application.
* @returns True if the "socialTabEnabled" feature is enabled, otherwise false.
*/
export const socialTabEnabled = (state: RootState): boolean => {
    return getFeatureFlagValue(state, socialTabEnabledFlag);
};

/**
* Selector to check if the "resourceTabEnabled" feature is enabled.
*
* @param state - The root state of the application.
* @returns True if the "resourceTabEnabled" feature is enabled, otherwise false.
*/
export const resourceTabEnabled = (state: RootState): boolean => {
    return getFeatureFlagValue(state, resourceTabEnabledFlag);
};

/**
 * Selector to check if the "roleSelectionEnabled" feature is enabled.
 * 
 * @param state - The root state of the application.
 * @returns True if the "roleSelectionEnabled" feature is enabled, otherwise false.
 */
export const roleSelectionEnabled = (state: RootState): boolean => {
    return getFeatureFlagValue(state, roleSelectionEnabledFlag);
}

/**
 * Selector to check if the "roleGovernanceEnabled" feature is enabled.
 *
 * @param state - The root state of the application.
 * @returns True if the "roleGovernanceEnabled" feature is enabled, otherwise false.
 */
export const roleGovernanceEnabled = (state: RootState): boolean => {
    return getFeatureFlagValue(state, roleGovernanceEnabledFlag);
}

/**
 * Selector to check if the "learningEnabledFlag" feature is enabled.
 *
 * @param state - The root state of the application.
 * @returns True if the "learningEnabledFlag" feature is enabled, otherwise false.
 */
export const resourcesLearningEnabled = (state: RootState): boolean => {
    return getFeatureFlagValue(state, learningEnabledFlag);
}

/**
 * Selector to check if the "notificationsEnabledFlag" feature is enabled.
 *
 * @param state - The root state of the application.
 * @returns True if the "notificationsEnabledFlag" feature is enabled, otherwise false.
 */
export const notificationsEnabled = (state: RootState): boolean => {
    return getFeatureFlagValue(state, notificationsEnabledFlag);
}

/**
 * Selector to check if the "notificationsEnabledFlag" feature is enabled.
 *
 * @param state - The root state of the application.
 * @returns True if the "bulletinEnabledFlag" feature is enabled, otherwise false.
 */
export const isBulletinEnabled = (state: RootState): boolean => {
    return getFeatureFlagValue(state, bulletinEnabledFlag);
}


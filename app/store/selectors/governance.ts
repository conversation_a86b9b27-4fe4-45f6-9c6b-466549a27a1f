/**
 * @fileoverview
 * Selector utility for determining feature visibility based on user access rules and feature governance.
 *
 * This module centralizes the logic for feature access control, ensuring that features are only displayed
 * to users who meet the required conditions (such as being in a geofence, being clocked in, or having a specific user type).
 *
 * ## Exports
 * - shouldDisplayFeature: Main selector function to determine if a feature should be shown.
 *
 * ## Related Types
 * - FeatureKey: Enum of all feature keys.
 * - FeatureType: Enum of feature access types.
 * - UserType: Enum of user access types.
 * - governanceRules: Mapping of feature keys to their required access type.
 *
 * ## Example Usage
 * ```typescript
 * import { shouldDisplayFeature } from "app/store/selectors/governance";
 * import { FeatureKey, UserType } from "app/shared/constants";
 *
 * const canShow = shouldDisplayFeature(
 *   FeatureKey.ANNOUNCEMENTS,
 *   isInGeofence,
 *   isClockedIn,
 *   userState.userType
 * );
 * if (canShow) {
 *   // Render the feature
 * }
 * ```
 *
 * ## Logic Summary
 * - If `userStatus` is not provided, returns false.
 * - Gets the required feature type from `governanceRules`.
 * - UserType.NON_RESTRICTED and UserType.RESTRICTED both grant user access (custom business logic).
 * - FeatureType.ON_LOCATION_ONLY: Feature is shown only if the user is in geofence.
 * - FeatureType.ON_LOCATION_AND_ON_SHIFT: Feature is shown only if the user is in geofence and clocked in.
 * - Returns true only if both user access and feature access conditions are met.
 */
import { FeatureKey, FeatureType, governanceRules, UserType } from "../../shared/constants";

/**
 * Determines if a feature should be displayed to the user based on their current state and feature access rules.
 *
 * @param feature - The feature key to check access for (FeatureKey).
 * @param isInGeofence - Whether the user is currently in the geofence (boolean).
 * @param isClockedIn - Whether the user is currently clocked in (boolean).
 * @param userStatus - The user's access type (UserType.RESTRICTED or UserType.NON_RESTRICTED).
 * @returns {boolean} - Returns true if the feature should be shown, false otherwise.
 */
const isFalsy = (val: any) => val == null || val === false;

export const shouldDisplayFeature = (
    feature: FeatureKey,
    isInGeofence: boolean,
    isClockedIn: boolean,
    userStatus?: UserType.RESTRICTED | UserType.NON_RESTRICTED,
    isFeatureEnabled?: boolean,
    isGovernanceEnabled?: boolean
): boolean => {
    // If feature or governance is not enabled, do not show the feature
    if (isFalsy(isFeatureEnabled) || isFalsy(isGovernanceEnabled)) return false;

    // Non-restricted users always have access
    if (userStatus === UserType.NON_RESTRICTED) return true;

    const featureType = governanceRules[feature];
    let featureAccess = false;

    switch (featureType) {
        case FeatureType.ON_LOCATION_ONLY:
            featureAccess = isInGeofence;
            break;
        case FeatureType.ON_LOCATION_AND_ON_SHIFT:
            featureAccess = isInGeofence && isClockedIn;
            break;
        default:
            console.warn(`Unknown feature type for feature: ${feature}`);
            featureAccess = false;
            break;
    }

    return featureAccess;
};
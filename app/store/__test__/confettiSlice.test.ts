// confettiSlice.test.js
import confettiReducer, {
  hideConfetti,
  showConfetti,
  setReduceMotion,
} from '../reducers/confettiSlice';

describe('confettiSlice', () => {
  const initialState = {
    visible: false,
    triggerId: null,
    reduceMotionEnabled: null,
    screen: null,
  };

  it('should return the initial state when passed an empty action', () => {
    const result = confettiReducer(undefined, { type: '' });
    expect(result).toEqual(initialState);
  });

  it('should handle showConfetti with triggerId and screen payload', () => {
    const payload = { cardId: 'card-123', currentScreen: 'HomeScreen' };
    const action = showConfetti(payload);
    const result = confettiReducer(initialState, action);
    expect(result).toEqual({
      ...initialState,
      visible: true,
      triggerId: 'card-123',
      screen: 'HomeScreen',
    });
  });

  it('should handle hideConfetti and clear screen but retain triggerId', () => {
    const currentState = {
      visible: true,
      triggerId: 'card-123',
      screen: 'HomeScreen',
      reduceMotionEnabled: false,
    };
    const action = hideConfetti();
    const result = confettiReducer(currentState, action);
    expect(result).toEqual({
      visible: false,
      triggerId: 'card-123',
      screen: null,
      reduceMotionEnabled: false,
    });
  });

  it('should handle setReduceMotion correctly', () => {
    const action = setReduceMotion(true);
    const result = confettiReducer(initialState, action);
    expect(result).toEqual({
      ...initialState,
      reduceMotionEnabled: true,
    });
  });
});

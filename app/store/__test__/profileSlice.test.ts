// profileSlice.test.js
import { ASSOCIATE_PROFILE, STORE, UCA_PROFILE } from '../../shared/constants';
import profileReducer, {
  fetchProfileRequest,
  fetchProfileSuccess,
  storeProfileData,
  storeUcaProfileData,
  fetchStoreLocation,
  fetchProfileFailure,
  setBanner,
  setUserInStore,
  clearProfile,
  setHomeCelebrationCards,
  setProfileCelebrationCards,
} from '../reducers/profileSlice';

describe('profileSlice', () => {
  const initialState = {
    loading: false,
    error: null,
    response: null,
    banner: '',
    bannerName: '',
    isUserInStore: false,
    homeCelebrationCards: [],
    profileCelebrationCards: [],
    ucaProfileLoaded: false,
    slocData: null,
    profile: null,
    ucaProfile: null,
    isTestAccount: false,
  };

  it('should handle fetchProfileRequest', () => {
    const action = fetchProfileRequest({ requestId: 'req1', fl: ASSOCIATE_PROFILE });
    const result = profileReducer(initialState, action);
    expect(result.loading).toBe(true);
    expect(result.error).toBeNull();
  });

  it('should handle fetchProfileSuccess', () => {
    const action = fetchProfileSuccess({
      response: { user: 'user' },
      errors: null,
    });
    const result = profileReducer(initialState, action);
    expect(result.loading).toBe(false);
    expect(result.response).toEqual(action.payload);
  });

  it('should handle storeProfileData', () => {
    const action = storeProfileData({ profile: 'profile' });
    const result = profileReducer(initialState, action);
    expect(result.loading).toBe(false);
    expect(result.profile).toEqual(action.payload);
  });

  it('should handle storeUcaProfileData', () => {
    const action = storeUcaProfileData({ ucaProfile: UCA_PROFILE });
    const result = profileReducer(initialState, action);
    expect(result.loading).toBe(false);
    expect(result.ucaProfile).toEqual(action.payload);
  });

  it('should handle fetchStoreLocation', () => {
    const action = fetchStoreLocation({ storeName: STORE });
    const result = profileReducer(initialState, action);
    expect(result.loading).toBe(false);
    expect(result.slocData).toEqual(action.payload);
  });

  it('should handle fetchProfileFailure', () => {
    const action = fetchProfileFailure('error message');
    const result = profileReducer(initialState, action);
    expect(result.loading).toBe(false);
    expect(result.error).toEqual('error message');
  });

  it('should handle setBanner', () => {
    const action = setBanner({ banner: 'newBanner', bannerName: 'New Company' });
    const result = profileReducer(initialState, action);
    expect(result.banner).toBe('newBanner');
    expect(result.bannerName).toBe('New Company');
  });

  it('should handle setUserInStore', () => {
    const action = setUserInStore(true);
    const result = profileReducer(initialState, action);
    expect(result.isUserInStore).toBe(true);
  });

  it('should handle clearProfile', () => {
    const stateWithData = {
      ...initialState,
      loading: true,
      error: 'error',
      response: { user: 'user' },
      profile: { id: 1 },
      ucaProfile: { id: 2 },
    };
    const action = clearProfile();
    const result = profileReducer(stateWithData, action);
    expect(result).toEqual(initialState);
  });

  it('should handle setHomeCelebrationCards', () => {
    const action = setHomeCelebrationCards([{ id: 'card1' }]);
    const result = profileReducer(initialState, action);
    expect(result.homeCelebrationCards).toEqual(action.payload);
  });

  it('should handle setProfileCelebrationCards', () => {
    const action = setProfileCelebrationCards([{ id: 'card2' }]);
    const result = profileReducer(initialState, action);
    expect(result.profileCelebrationCards).toEqual(action.payload);
  });
});

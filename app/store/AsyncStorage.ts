import uuid from 'react-native-uuid';
import AsyncStorage from "@react-native-async-storage/async-storage";
import DeviceInfo from 'react-native-device-info';

export const AsyncStorageKeys = {
  ONBOARDING_STEP: "onBoardingStep",
  DEVICE_ID: "deviceId",
  UNIQUE_ID: "uniqueId",
  LDAP: "ldap",
  optInLoytalTNC: "optInLoytalTNC",
}

// Generic function to get an item from AsyncStorage
export const getItem = async (key: string): Promise<any | null> => {
  try {
    const value = await AsyncStorage.getItem(key);
    return value != null ? JSON.parse(value) : null;
  } catch (error) {
    console.error("Error getting value from AsyncStorage", error);
    return null;
  }
};

// Generic function to set an item in AsyncStorage
export const setItem = async (key: string, value: any): Promise<void> => {
  try {
    const jsonValue = JSON.stringify(value);
    await AsyncStorage.setItem(key, jsonValue);
  } catch (error) {
    console.error("Error setting value in AsyncStorage", error);
  }
};

// Function to remove an item from AsyncStorage
export const removeItem = async (key: string): Promise<void> => {
  try {
    await AsyncStorage.removeItem(key);
  } catch (error) {
    console.error("Error removing value from AsyncStorage", error);
  }
};

// Function to clear all data in AsyncStorage
export const clearAll = async (): Promise<void> => {
  try {
    await AsyncStorage.clear();
  } catch (error) {
    console.error("Error clearing AsyncStorage", error);
  }
};

// Function to get all keys in AsyncStorage
export const getAllKeys = async (): Promise<string[]> => {
  try {
    const keys = await AsyncStorage.getAllKeys();
    return [...keys];
  } catch (error) {
    console.error("Error getting all keys from AsyncStorage", error);
    return [];
  }
};

// Function to clear all keys except specified ones
export const clearAllExcept = async (): Promise<void> => {
  try {

    const keysToKeep = [AsyncStorageKeys.ONBOARDING_STEP]; // Specify keys to keep
    // Get all keys from AsyncStorage
    const allKeys = await AsyncStorage.getAllKeys();

    // Filter out keys to keep
    const keysToDelete = allKeys.filter((key) => !keysToKeep.includes(key));

    // Remove each key that needs deletion
    await Promise.all(keysToDelete.map((key) => AsyncStorage.removeItem(key)));

    console.log("Cleared all keys except specified ones");
  } catch (error) {
    console.error("Error clearing AsyncStorage except specified keys", error);
  }
};

// Function to set onboarding progress steps in AsyncStorage
export const setOnboardingStep = async (value: number): Promise<void> => {
  try {
    const jsonValue = JSON.stringify(value);
    await AsyncStorage.setItem(AsyncStorageKeys.ONBOARDING_STEP, jsonValue);
  } catch (error) {
    console.error("Error setting value in AsyncStorage", error);
  }
};

// Function to get onboarding progress is completed or not in AsyncStorage
export const isOnboardingCompleted = async (): Promise<boolean> => {
  try {
    const stepString = await AsyncStorage.getItem(AsyncStorageKeys.ONBOARDING_STEP);
    if (stepString != null) {
      const step = parseInt(stepString);
      return step === 4;
    } else return false;
  } catch (error) {
    console.error("Error isOnbooardingCompleted in AsyncStorage", error);
    return false;
  }
};

// Function to get onboarding progress steps in AsyncStorage
export const getOnboardingStep = async (): Promise<number> => {
  try {
    const stepString = await AsyncStorage.getItem(AsyncStorageKeys.ONBOARDING_STEP);
    if (stepString != null) {
      const step = parseInt(stepString);
      return step;
    } else return 0;
  } catch (error) {
    console.error("Error getOnbooardingStep in AsyncStorage", error);
    return 0;
  }
};

// Function to get or generate a device ID
export const deviceId = async (): Promise<string> => {
  try {
    // Check if a deviced id generated is already exists in AsyncStorage
    const deviceId = await AsyncStorage.getItem(AsyncStorageKeys.DEVICE_ID);
    if (deviceId) {
      return deviceId; // Return the existing UUID if found
    }

    // If no existing devide id, generate a new one and store it
    const newDeviceId = await DeviceInfo.getUniqueId();
    await AsyncStorage.setItem(AsyncStorageKeys.DEVICE_ID, newDeviceId);
    return newDeviceId;
  } catch (error) {
    console.error('Error in getOrCreateUuid:', error);
    throw error; // Re-throw the error for the caller to handle
  }
};


// Function to get or generate a uniqueId
export const uniqueSessionId = async (): Promise<string> => {
  try {
    // Check if a deviced id generated is already exists in AsyncStorage
    const uniqueId = await AsyncStorage.getItem(AsyncStorageKeys.UNIQUE_ID);
    if (uniqueId) {
      return uniqueId; // Return the existing UUID if found
    }

    // If no existing devide id, generate a new one and store it
    const newUniqueId = uuid.v4();
    await AsyncStorage.setItem('uniqueId', newUniqueId);
    return newUniqueId;
  } catch (error) {
    console.error('Error in getOrCreateUuid:', error);
    throw error; // Re-throw the error for the caller to handle
  }
};

// Function to set LDAP in AsyncStorage
export const setLDAP = async (value: string): Promise<void> => {
  try {
    await AsyncStorage.setItem(AsyncStorageKeys.LDAP, value);
  } catch (error) {
    console.error("Error setting value in AsyncStorage", error);
  }
};

// Function to get LDAP
export const ldap = async (): Promise<string> => {
  try {
    // Check if a ldap generated is already exists in AsyncStorage
    const ldap = await AsyncStorage.getItem(AsyncStorageKeys.LDAP);
    if (ldap) {
      return ldap; // Return the existing ldap if found
    }

    throw Error("LDAP doens't exist")
  } catch (error) {
    console.error('Error in getLDAP:', error);
    throw error; // Re-throw the error for the caller to handle
  }
};

// Function to set agreementId when user opt in loyalty terms and conditions
export const setOptInLoyaltyTNC = async (value: string): Promise<void> => {
  try {
    await AsyncStorage.setItem(AsyncStorageKeys.optInLoytalTNC, value);
  } catch (error) {
    console.error("Error setting value in AsyncStorage", error);
  }
};

// Function to get agreementId when user opt in loyalty terms and conditions
export const getOptInLoyaltyTNC = async (): Promise<string> => {
  try {
    const result = await AsyncStorage.getItem(AsyncStorageKeys.optInLoytalTNC);
    if (result) {
      return result;
    }

    throw Error("optInLoytalTNC doens't exist")
  } catch (error) {
    console.error('Error in get optInLoytalTNC:', error);
    throw error; // Re-throw the error for the caller to handle
  }
};

import * as Keychain from "react-native-keychain";

// As `react-native-keychain` provides native secure storage, we use it to securely store sensitive information

// Enum To define the types of tokens we will be storing
export enum TokenType {
    middleTierAccessToken = 'middleTierAccessToken',
    middleTierRefreshToken = 'middleTierRefreshToken',
    middleTierAccessTokenExpiry = 'middleTierAccessTokenExpiry',
    oboAccessToken = 'oboAccessToken',
    oboRefreshToken = 'oboRefreshToken',
    oboAccessTokenExpiry = 'oboAccessTokenExpiry',
    employeeId = 'employeeId',
    lastUserName = 'lastUserName',
    rewardAccessToken = "rewardAccesToken",
    rewardAccessTokenExpiry = "rewardAccessTokenExpiry",
    personNumberKey = "personNumberKey",
}

export class SecureStorage {
    /* Save token to Secure Storage */
    async saveToken(type: TokenType, value: string) {
        try {
            await Keychain.setGenericPassword(type, value, { service: type });
        } catch (error) {
            console.error(`Error saving ${type}:`, error);
        }
    }

    /* Get token from Secure Storage */
    async getToken(type: TokenType): Promise<string> {
        try {
            const token = await Keychain.getGenericPassword({ service: type });
            return token ? token.password : "";
        } catch (error) {
            return "";
        }
    }

    /* Clear all except one key*/
    async clearAllExcept() {
        try {
            //const keysToKeep = [keyToKeep]; // Specify keys to keep
            const storedPersonNumberKeys = await this.getToken(TokenType.personNumberKey);
            const keysToKeep = storedPersonNumberKeys // Specify keys to keep
                ? [...JSON.parse(storedPersonNumberKeys), TokenType.personNumberKey, TokenType.lastUserName]
                : null;
            // Check if the token exists
            if (keysToKeep) {
                const allServices = await Keychain.getAllGenericPasswordServices();
                // Filter out keys to keep
                const keysToDelete = allServices.filter(
                    (key) => !keysToKeep.includes(key)
                );

                // Remove each service's credentials
                for (const service of keysToDelete) {
                    await Keychain.resetGenericPassword({ service });
                }
            } else {
                this.clearAll();
            }

            // await Keychain.resetGenericPassword();
        } catch (error) {
            console.error("Failed to clear keychain:", error);
        }
    }
    // Usage:
    // clearAllExcept('myImportantKey');

    /* Clear All */
    async clearAll() {
        // Get all stored services (iOS only)
        const services = await Keychain.getAllGenericPasswordServices();
        // Remove each service's credentials
        for (const service of services) {
            await Keychain.resetGenericPassword({ service });
        }
        // Also clear generic credentials (if you've used setGenericPassword)
        await Keychain.resetGenericPassword();
        console.log("Secure Storage cleared successfully");
    }
}

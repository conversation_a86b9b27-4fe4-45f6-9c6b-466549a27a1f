import { configureStore } from '@reduxjs/toolkit';
import { persistStore, persistReducer } from 'redux-persist';
import storage from '@react-native-async-storage/async-storage';
import type { SagaMiddleware } from 'redux-saga';
import rootReducer from './reducers';
import rootSaga from './sagas';

// ✅ Use require() to safely load redux-saga at runtime
const createSagaMiddleware: () => SagaMiddleware = require('redux-saga').default;
const sagaMiddleware = createSagaMiddleware({}); // ✅ Pass empty object to satisfy TS

const persistConfig = {
  key: 'root',
  storage,
};

const persistedReducer = persistReducer(persistConfig, rootReducer);

const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      thunk: false,
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
        ignoredPaths: ['register'],
      },
    }).concat(sagaMiddleware),
});

sagaMiddleware.run(rootSaga);

const persistor = persistStore(store);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export { store, persistor };

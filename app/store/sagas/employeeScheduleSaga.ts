import dayjs from 'dayjs';
import { call, put, select, takeEvery } from 'redux-saga/effects';

import ApiHelper from '../../config/ApiHelper';
import { generateMockEmployeeScheduleResponse } from '../../config/mockScheduleData';
import { getWeekDateRange } from '../../utils/TimeUtils';
import {
  fetchEmployeeScheduleFailure,
  fetchEmployeeScheduleRequest,
  fetchEmployeeScheduleSuccess,
} from '../reducers/employeeScheduleSlice';
import { setShiftTime, resetShiftState } from '../reducers/shiftSlice';

import type { EmployeeScheduleParams } from '../../config/payloadTypes';
import type { EmployeeScheduleResponse } from '../../config/responseTypes';
import type { ProfileState } from '../reducers/profileSlice';

/**
 * Action interface for fetching employee schedule.
 */
interface FetchEmployeeScheduleAction {
  type: string;
  payload: {
    params: EmployeeScheduleParams;
  };
}

/**
 * <PERSON> to handle fetching the employee schedule.
 *
 * Listens for fetchEmployeeScheduleRequest actions, calls the API with the provided parameters,
 * and dispatches either fetchEmployeeScheduleSuccess or fetchEmployeeScheduleFailure based on the result.
 *
 * @param {FetchEmployeeScheduleAction} action - The dispatched action containing employee schedule parameters.
 */
function* fetchEmployeeSchedule(action: FetchEmployeeScheduleAction) {
  try {
    const { params } = action.payload;
    const payload = params?.isSilent ? undefined : action.payload;

    let fromDate: string;
    let toDate: string;

    if (params.fromDate && params.toDate) {
      // Use custom selected range if provided
      fromDate = params.fromDate;
      toDate = params.toDate;
    } else {
      // Fallback to week range
      const weekRange = getWeekDateRange(params.weekNumber, params.year);
      fromDate = weekRange.fromDate;
      toDate = weekRange.toDate;
    }

    let content: EmployeeScheduleResponse | null = null;
    const profile: ProfileState = yield select((state) => state.profile);

    if (profile.isTestAccount) {
      content = generateMockEmployeeScheduleResponse(fromDate, toDate);
    } else {
      const pathParams = `?request-id=${params.requestId}&empId=${params.empId}&fromDate=${fromDate}&toDate=${toDate}`;
      content = yield call(ApiHelper.getEmployeeSchedule, pathParams, payload);
    }

    yield put(fetchEmployeeScheduleSuccess(content));

    // Extract schedules and check if there are any for the current week; exit early if none found
    const schedules = content?.scheduleResponse?.schedule ?? [];
    if (
      !Array.isArray(schedules) ||
      schedules.length === 0 ||
      !schedules.some((s) => dayjs(s.workDate).isSame(dayjs(), 'week'))
    ) {
      yield put(resetShiftState()); // Handling exception edge case
      return;
    }

    const today = dayjs().format('YYYY-MM-DD');
    const todaySchedule = schedules.find((s) => s?.workDate === today); // Here use to found today's schedule

    // Filter schedules for the current week and after today
    const currentWeekSchedules = schedules
      .filter((s) => dayjs(s.workDate).isSame(dayjs(), 'week') && s.workDate > today)
      .sort((a, b) => dayjs(a.workDate).diff(dayjs(b.workDate)));

    const nextSchedule = currentWeekSchedules[0] ?? null;
    // Handle setting shift time when today's schedule exists, and optionally include the next upcoming schedule if available
    if (todaySchedule?.startTime && todaySchedule?.endTime) {
      const scheduleStartTime = dayjs(todaySchedule.startTime);
      const scheduleEndTime = dayjs(todaySchedule.endTime);
      yield put(
        setShiftTime({
          shiftStartEndTime: `${scheduleStartTime.format('hh:mm A')} - ${scheduleEndTime.format('hh:mm A')}`,
          startTime: scheduleStartTime.format('hh:mm A'),
          endTime: scheduleEndTime.format('hh:mm A'),
          startDate: todaySchedule.startTime,
          endDate: todaySchedule.endTime,
          todaysShift: todaySchedule,
          ...(nextSchedule && { scheduleShift: nextSchedule }),
        }),
      );
    } else if (nextSchedule) {
      // If no today's schedule, but next schedule exists
      yield put(
        setShiftTime({
          scheduleShift: nextSchedule,
        }),
      );
    }
  } catch (error) {
    yield put(fetchEmployeeScheduleFailure(error));
  }
}

/**
 * Root saga for employee schedule.
 * Watches for fetchEmployeeScheduleRequest actions and triggers fetchEmployeeSchedule saga.
 */
export function* employeeScheduleSaga() {
  yield takeEvery(fetchEmployeeScheduleRequest.type, fetchEmployeeSchedule);
}

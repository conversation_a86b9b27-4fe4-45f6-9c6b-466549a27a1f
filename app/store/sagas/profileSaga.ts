import { call, put, takeEvery, select } from 'redux-saga/effects';

import { getUserLocation, isInsideGeoFence } from '../../../app/utils/helpers';
import ApiHelper from '../../config/ApiHelper';
import {
  ASSOCIATE_PROFILE,
  ERROR_CODES,
  STORE,
  STORE_RADIUS,
  UCA_PROFILE,
  UserType,
  USERTYPE_API_RESPONSE,
} from '../../shared/constants';
import {
  fetchProfileFailure,
  fetchProfileRequest,
  fetchProfileSuccess,
  storeProfileData,
  storeUcaProfileData,
  fetchStoreLocation,
  setUserInStore,
  setUserRestrictedStatus,
} from '../reducers/profileSlice';

import type { FL_TYPES, StoreLocation } from '../../config/payloadTypes';
import {mock_profile_response} from '../../config/mock_profile.json';
/**
 * Interface for the payload of the fetchProfile action.
 */
interface ProfileParams {
  payload: {
    requestId: string; // Unique request ID for tracking
    fl: FL_TYPES | FL_TYPES[]; // Feature list types (e.g., "associateProfile", "store")
    contactNbr?: string; // Contact number (optional)
    email?: string; // Email address (optional)
    location?: StoreLocation; // Store location details (optional)
    empId?: string; // Employee ID (optional)
    isSilent?: boolean; // indicate this is silent call
  };
}

/**
 * Constructs query parameters based on the feature list (fl) and other optional parameters.
 *
 * @param fl - Feature list type (e.g., "associateProfile", "store" , "loyalty").
 * @param empId - Employee ID (optional) require for "associateProfile".
 * @param location - Store location details (optional) and require for "store".
 * @param contactNbr - Contact number (optional) require (either of contact or email) for ucaProfile .
 * @param email - Email address (optional) require (either of contact or email) for ucaProfile .
 * @returns A string containing the constructed query parameters.
 */
const getParams = (
  fl: FL_TYPES,
  empId?: string,
  location?: StoreLocation,
  contactNbr?: string,
  email?: string,
): string => {
  let params = '';

  switch (fl) {
    case ASSOCIATE_PROFILE:
      params = `${params}&empId=${empId}`;
      break;
    case STORE:
      if (location) {
        const { latitude, longitude } = location;
        params = `${params}&lat=${latitude}&lng=${longitude}`;
      }
      break;
    case UCA_PROFILE:
      if (contactNbr || email) {
        const ucaParams = contactNbr ? `&contactNbr=${contactNbr}` : `&email=${email}`;
        params = `${params}${ucaParams}`;
      }
      break;
    default:
      break;
  }
  return params;
};

/**
 * Saga to handle fetching profile data.
 *
 * @param action - The action containing the payload for the profile fetch request.
 */
function* fetchProfile(action: ProfileParams) {
  try {
    const { requestId, fl, contactNbr, email, location, empId, isSilent } = action.payload;

    // Construct the base query parameters
    const flParams = Array.isArray(fl) ? fl.join(',') : fl;
    let params = `?request-id=${requestId}&fl=${flParams}`;

    // Add additional parameters based on the feature list
    if (!Array.isArray(fl)) {
      const requiredParams = getParams(fl, empId, location, contactNbr, email);
      params = `${params}${requiredParams}`;
    } else {
      for (let i = 0; i < fl.length; i++) {
        const requiredParams = getParams(fl[i], empId, location, contactNbr, email);
        params = `${params}${requiredParams}`;
      }
    }
    const payload = isSilent ? undefined : action.payload;
    // Call the API to fetch the profile data
    let response;

    const isTestAccount = yield select((state) => state.profile.isTestAccount);
    if (isTestAccount && flParams === 'associateProfile') {
      // Use mock data for test accounts
      response =  mock_profile_response ;
    } else {
      response = yield call(ApiHelper.getProfile, params, payload);
    }
    yield put(fetchProfileSuccess(response));
    // Store associate profile data if the feature list includes "associateProfile"
    if (flParams.includes('associateProfile')) {
      const { associateProfile } = response;
      const flsaStatus = associateProfile?.flsaStatus ?? '';
      const hourlySalaried = associateProfile?.hourlySalaried ?? '';
      // Default to RESTRICTED
      let userType = UserType.RESTRICTED;

      // Set NON_RESTRICTED only if both conditions are met
      const isExempt = flsaStatus === USERTYPE_API_RESPONSE.FLSA_STATUS_EXEMPT;
      const isSalaried = hourlySalaried === USERTYPE_API_RESPONSE.SALARIED;
      const isHourly = hourlySalaried === USERTYPE_API_RESPONSE.HOURLY;
      const isNonExempt = flsaStatus === USERTYPE_API_RESPONSE.FLSA_STATUS_NONEXEMPT;

      if (isExempt && isSalaried) {
        userType = UserType.NON_RESTRICTED;
      } else if ((isExempt && isHourly) || isNonExempt) {
        userType = UserType.RESTRICTED;
      }

      yield put(setUserRestrictedStatus(userType));

      yield put(storeProfileData(associateProfile));
    }

    // Store UCA profile data if the feature list includes "ucaProfile"
    if (flParams.includes('ucaProfile')) {
      const { ucaProfile } = response;
      if (ucaProfile) {
        yield put(storeUcaProfileData(ucaProfile));
      }
    }

    // Handle store data if the feature list includes "store"
    if (flParams.includes('store')) {
      if (response?.stores) {
        const store = response?.stores[0];
        const { latitude: storeLatitude, longitude: storeLongitude } = store;
        const { latitude: userLatitude, longitude: userLongitude } = yield getUserLocation();
        const configurableRadius = yield select(
          (state) => state.authorableContent.authorableContent?.storeInfo?.radius,
        );
        const radius = configurableRadius ?? STORE_RADIUS;

        // Check if the user is inside the store's geofence
        const userInStore = isInsideGeoFence(
          userLatitude,
          userLongitude,
          storeLatitude,
          storeLongitude,
          radius,
        );
        yield put(setUserInStore(userInStore));
        yield put(fetchStoreLocation(store));
      }

      if (response?.errors?.length > 0) {
        const { errors } = response;
        for (const error of errors) {
          if (
            error.code === ERROR_CODES.STORE_NOT_FOUND ||
            error.code === ERROR_CODES.MISSING_LAT_LNG
          ) {
            yield put(setUserInStore(false));
            yield put(fetchStoreLocation(null));
          }
        }
      }
    }
  } catch (error: any) {
    yield put(fetchProfileFailure(error));
  }
}

/**
 * Watcher saga: Watches for fetchProfileRequest actions and triggers the fetchProfile saga.
 */
export function* profileSaga() {
  yield takeEvery(fetchProfileRequest.type, fetchProfile);
}

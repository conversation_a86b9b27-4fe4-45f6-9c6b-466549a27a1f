import { call, put, takeEvery } from 'redux-saga/effects';
import <PERSON><PERSON><PERSON><PERSON>per from '../../config/ApiHelper';
import { fetchProfilePhotoFailure, fetchProfilePhotoRequest, fetchProfilePhotoSuccess } from '../reducers/profilePhotoSlice';

/**
 * Saga to handle fetching profile data.
 * 
 * @param action - The action containing the payload for the profile fetch request.
 */
function* fetchProfilePhoto(action: {
    payload: {
        requestId: string; // Unique request ID for tracking
        banner: string; // Banner name
        empId?: string; // Employee ID (optional)
    }
}) {
    try {
        const { requestId, empId, banner } = action.payload;
        let params = `?request-id=${requestId}&empId=${empId}`;

        // Call the API to fetch the profile data
        const response = yield call(ApiHelper.getProfilePhoto, params, banner);

        yield put(fetchProfilePhotoSuccess(response));

    } catch (error) {
        // Dispatch failure action with the error message
        yield put(fetchProfilePhotoFailure((error as Error).message));
    }
}

/**
 * Watcher saga: Watches for fetchProfileRequest actions and triggers the fetchProfile saga.
 */
export function* profilePhotoSaga() {
    yield takeEvery(fetchProfilePhotoRequest.type, fetchProfilePhoto);
}

import { call, put, takeEvery } from "redux-saga/effects";
import {
  fetchRewardsScorecardSuccess,
  fetchRewardsScorecardFailure,
  fetchRewardsScorecardRequest,
} from "../reducers/scorecardSlice";
import ApiHelper from "../../config/ApiHelper";
import { RewardScorecard } from "../../config/payloadTypes";
import { RewardsScorecardResponse } from "../../config/responseTypes";

interface FetchRewardsScorecardAction {
  payload: {
    body: RewardScorecard;
  };
}

// Worker saga: fetches points
function* fetchRewardsScorecard(action: FetchRewardsScorecardAction) {
  try {
    // Extract body and banner from action payload
    const { body }: { body: RewardScorecard } =
      action.payload;

    // Call the API with body and banner as separate arguments
    const content: RewardsScorecardResponse = yield call(
      ApiHelper.rewardsScorecard,
      body,
      action.payload
    );
    // Dispatch success with the fetched content
    yield put(fetchRewardsScorecardSuccess(content));
  } catch (error) {
    // Dispatch failure with error message
    yield put(fetchRewardsScorecardFailure((error as Error).message));
  }
}

// Watcher saga: listens for fetchRewardsScorecardRequest actions
export function* rewardsSaga() {
  yield takeEvery(fetchRewardsScorecardRequest.type, fetchRewardsScorecard);
}

import { call, put, takeEvery } from 'redux-saga/effects';

import ApiHelper from '../../config/ApiHelper';
import {
    fetchTncAgreementFailure,
    fetchTncAgreementRequest,
    fetchTncAgreementSuccess,
} from '../reducers/tncAgreementSlice';

import type { TncAgreementActionPayload } from '../../misc/models/ProfileModal';

/**
 * Saga to handle tncAgreement API
 *
 * @param action - The action containing the payload for the opt verify request.
 */
function* fetchTncAgreement(action: { payload: TncAgreementActionPayload }) {
    try {
        const { body } = action.payload;
        // The API call is expected to return an response
        const response = yield call(ApiHelper.tncAgreement, body, action.payload);
        yield put(fetchTncAgreementSuccess(response));
    } catch (error) {
        yield put(fetchTncAgreementFailure(error));
    }
}

/**
 * Watcher saga: Watches for fetchTncAgreementRequest actions and triggers the fetch tncAgreement saga.
 */
export function* tncAgreementSaga() {
    yield takeEvery(fetchTncAgreementRequest.type, fetchTncAgreement);
}

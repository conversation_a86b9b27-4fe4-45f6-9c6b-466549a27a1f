import { call, put, takeEvery } from 'redux-saga/effects';
import <PERSON><PERSON>Helper from '../../config/ApiHelper';
import {  fetchClockInStatusRequest, fetchClockInStatusSuccess, fetchClockInStatusFailure } from '../reducers/clockInStatusSlice';
import { ClockInStatusActionPayload } from '../../misc/models/ScheduleModal';
import { AxiosError } from 'axios';
import { setClockedIn, resetClockedIn, setDurationInMinutes } from '../reducers/shiftSlice';
import moment from 'moment';
import { getCurrentTime, calculateTimeDiffinMinutes } from '../../utils/TimeUtils';

/**
 * Saga to handle Clock In out Status for account link process.
 *
 * @param action - The action containing the payload for the opt verify request.
 */
function* fetchClockInStatus(action: {
    payload: ClockInStatusActionPayload
}) {
    try {
        const { requestId } = action.payload;
        let params = `?request-id=${requestId}&empId=`; 
        // The API call is expected to return an Clock In/out Status Response
        const response = yield call(ApiHelper.getClockInStatus, params, action.payload);
        yield put(fetchClockInStatusSuccess(response));
        if (!response?.errors) {
          if (response?.clockedIn) {
            const formattedClockedInTime = response?.clockTime ? moment(response.clockTime, "HH:mm A").format("hh:mm A") : '';
            yield put((setClockedIn(formattedClockedInTime)));
            const now = getCurrentTime();
            const duration = calculateTimeDiffinMinutes(formattedClockedInTime, now);
            if (duration > 0) yield put(setDurationInMinutes(duration));
          } else {
            yield put(resetClockedIn());
          }
        } else {
          yield put(resetClockedIn());
        }
    } catch (err: unknown) {
    let status: number | undefined;
    let message: string = '';

    if (err && typeof err === 'object') {
      const error = err as AxiosError<any>;
      status = error.response?.status;
      message =
        error.response?.data?.message || // Custom error from backend
        error.message ||                 // Axios default
        '';          // Fallback
    }

    yield put(fetchClockInStatusFailure({ status, message }));
  }
}

/**
 * Watcher saga: Watches for Clock in/out request actions and triggers the fetch ClockInStatus saga.
 */
export function* clockInStatusSaga() {
    yield takeEvery(fetchClockInStatusRequest.type, fetchClockInStatus);
}

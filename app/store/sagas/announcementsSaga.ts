import { call, put, select, takeEvery } from "redux-saga/effects";
import {
  fetchAnnouncementsSuccess,
  fetchAnnouncementsFailure,
  fetchAnnouncementsRequest,
  fetchAllAnnouncementsFailure,
  fetchAllAnnouncementsSuccess,
  fetchAllAnnouncementsRequest,
  fetchAnnouncementUrlFailure,
  fetchAnnouncementUrlRequest,
  fetchAnnouncementUrlSuccess
} from "../reducers/announcementSlice";
import ApiHelper from "../../config/ApiHelper";
import { AnnouncementsParams } from "../../config/payloadTypes";
import { AnnouncementsResponse, AnnouncementUrlResponse } from "../../config/responseTypes";

interface FetchAnnouncementsAction {
  type: string;
  payload: {
    params: AnnouncementsParams;
  };
}

const getBanner = (state: any) => state.profile.profile?.banner || null;

// Worker saga: fetches announcements
function* fetchAnnouncements(action: FetchAnnouncementsAction) {
  try {
    const { params } = action.payload;
    const banner = yield select(getBanner);
    const content: AnnouncementsResponse = yield call(
      ApiHelper.announcements,
      params, banner
    );
    yield put(fetchAnnouncementsSuccess(content));
  } catch (error: any) {
    yield put(fetchAnnouncementsFailure(error));
  }
}

// Worker saga: fetches all announcements
function* fetchAllAnnouncements(action: FetchAnnouncementsAction) {
  try {
    const { params } = action.payload;
    const banner = yield select(getBanner);
    const content: AnnouncementsResponse = yield call(
      ApiHelper.announcements,
      params, banner
    );
    yield put(fetchAllAnnouncementsSuccess(content));
  } catch (error: any) {
    yield put(fetchAllAnnouncementsFailure(error));
  }
}

function* fetchAnnouncementUrl(action: FetchAnnouncementsAction) {
  try {
    const { params } = action.payload;
    const banner = yield select(getBanner);
    const content: AnnouncementUrlResponse = yield call(
      ApiHelper.announcementDetailsUrl,
      params, banner
    );
    yield put(fetchAnnouncementUrlSuccess(content));
  } catch (error: any) {
    yield put(fetchAnnouncementUrlFailure(error));
  }
}


// Watcher saga: watrches for request action
export function* announcementsSaga() {
  yield takeEvery(fetchAnnouncementsRequest.type, fetchAnnouncements);
  yield takeEvery(fetchAllAnnouncementsRequest.type, fetchAllAnnouncements);
  yield takeEvery(fetchAnnouncementUrlRequest.type, fetchAnnouncementUrl);
}

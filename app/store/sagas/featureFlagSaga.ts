import { call, put, select, takeEvery } from 'redux-saga/effects';
import <PERSON><PERSON><PERSON>elper from '../../config/ApiHelper';
import * as AsyncStorage from '../../store/AsyncStorage';
import { fetchfeatureFlagFailure, fetchfeatureFlagRequest, fetchfeatureFlagSuccess } from '../reducers/featureFlagSlice';

/**
 * Saga to handle fetching profile data.
 * 
 * @param action - The action containing the payload for the profile fetch request.
 */
function* featureFlag(action: {
    payload: {
        requestId: string; // Unique request ID for tracking
        banner: string; // Banner name
        empId?: string; // Employee ID (optional)
    }
}) {
    try {
        const { banner } = action.payload;
        const requestId = yield AsyncStorage.uniqueSessionId();
        const profile = yield select((state) => state.profile);
        const zipCode = profile?.slocData?.address?.zipcode ?? "";
        const division = profile?.profile?.division ?? "";
        const employeeID = profile?.profile?.personNumber.toString();
        const storeID = profile?.profile?.facility ?? "";
        const facilityType = profile?.profile?.facilityType ?? "";
        // Call the API to fetch the profile data
        const response = yield call(ApiHelper.getFeatureFlag, banner, zipCode, division, requestId, employeeID, storeID, facilityType);
        yield put(fetchfeatureFlagSuccess(response));
    } catch (error) {
        // Dispatch failure action with the error message
        yield put(fetchfeatureFlagFailure((error as Error).message));
    }
}

/**
 * Watcher saga: Watches for fetchfeatureFlag actions and triggers the fetchProfile saga.
 */
export function* featureFlagSaga() {
    yield takeEvery(fetchfeatureFlagRequest.type, featureFlag);
}
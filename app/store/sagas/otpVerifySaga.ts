import { call, put, takeEvery } from 'redux-saga/effects';

import <PERSON><PERSON><PERSON>elper from '../../config/ApiHelper';
import { saveUcaProfileToStorage } from '../../utils/setAndGetHHID';
import {
    fetchOtpVerifyFailure,
    fetchOtpVerifyRequest,
    fetchOtpVerifySuccess,
} from '../reducers/otpVerifySlice';

import type { OtpVerifyActionPayload } from '../../misc/models/ProfileModal';

/**
 * Saga to handle verify otp for account link process .
 *
 * @param action - The action containing the payload for the opt verify request.
 */
function* fetchOtpVerify(action: { payload: OtpVerifyActionPayload }) {
    try {
        const { requestId, factorType, customerId, body } = action.payload;
        // Construct the parameters for the API call
        const params = `?request-id=${requestId}&factorType=${factorType}`;
        // The API call is expected to return an OtpVerifyResponse
        const response = yield call(ApiHelper.verifyOtp, params, customerId, body, action.payload);
        yield put(fetchOtpVerifySuccess(response));
        if (response?.response?.factorresult === 'SUCCESS') yield call(saveUcaProfileToStorage);
    } catch (error) {
        // Dispatch failure action with the error message
        const status =
            error && typeof error === 'object' && 'response' in error && (error as any).response?.status
                ? (error as any).response.status
                : undefined;
        const message =
            (error && typeof error === 'object' && 'message' in error && (error as any).message) ?? '';
        yield put(fetchOtpVerifyFailure({ status, message }));
    }
}

/**
 * Watcher saga: Watches for fetchOtpVerifyRequest actions and triggers the fetchOtpVerify saga.
 */
export function* otpVerifySaga() {
    yield takeEvery(fetchOtpVerifyRequest.type, fetchOtpVerify);
}

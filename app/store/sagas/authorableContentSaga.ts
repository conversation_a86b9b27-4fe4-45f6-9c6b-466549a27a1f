import { call, put, takeEvery } from 'redux-saga/effects';
import {
    fetchAuthorableContentFailure,
    fetchAuthorableContentRequest,
    fetchAuthorableContentSuccess
} from '../reducers/authorableContentSlice';
import ApiHelper from '../../config/ApiHelper';
import { AuthorableContent } from '../../misc/models/authorableModels';
import { select } from 'redux-saga/effects';

// Worker saga: fetches todos
function* fetchAuthorableContent() {
    try {
        const language = yield select((state) => state.language);
        const content: AuthorableContent = yield call(ApiHelper.getAuthorableContent, language.id);
        yield put(fetchAuthorableContentSuccess(content));
    } catch (error) {
        yield put(fetchAuthorableContentFailure((error as Error).message));
    }
}

// Watcher saga: watches for fetchTodosRequest actions
export function* authorableContentSaga() {
    yield takeEvery(fetchAuthorableContentRequest.type, fetchAuthorableContent);
}
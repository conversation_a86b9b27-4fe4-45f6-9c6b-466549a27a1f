// store/rootSaga.js
import { all } from "redux-saga/effects";
import { authorableContentSaga } from "./authorableContentSaga";
import { rewardsSaga } from "./scorecardSaga";
import { announcementsSaga } from "./announcementsSaga";
import { profileSaga } from "./profileSaga";
import { profilePhotoSaga } from "./profilePhotoSaga";
import { featureFlagSaga } from "./featureFlagSaga";
import { otpVerifySaga } from "./otpVerifySaga";
import { discountSaga } from "./discountSaga";
import { employeeScheduleSaga } from "./employeeScheduleSaga";
import { clockInStatusSaga } from "./clockInStatusSaga";
import { tncAgreementSaga } from "./tncAgreementSaga";

export default function* rootSaga() {
  yield all([
    authorableContentSaga(),
    rewardsSaga(),
    profileSaga(),
    announcementsSaga(),
    profilePhotoSaga(),
    featureFlagSaga(),
    otpVerifySaga(),
    discountSaga(),
    clockInStatusSaga(),
    employeeScheduleSaga(),
    tncAgreementSaga()
  ]);
}

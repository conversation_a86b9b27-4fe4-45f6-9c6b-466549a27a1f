import { call, put, takeEvery } from 'redux-saga/effects';

import <PERSON><PERSON><PERSON><PERSON><PERSON> from '../../config/ApiHelper';
import {
    fetchDiscountFailure,
    fetchDiscountRequest,
    fetchDiscountSuccess,
} from '../reducers/discountSlice';

import type { DiscountActionPayload } from '../../misc/models/ProfileModal';

/**
 * Saga to handle Customer Discount for account link process .
 *
 * @param action - The action containing the payload for the opt verify request.
 */
function* fetchCustomerDiscount(action: { payload: DiscountActionPayload }) {
    try {
        const { body } = action.payload;
        // The API call is expected to return an DiscountResponse
        const response = yield call(ApiHelper.createNewEmployeeDiscount, body, action.payload);
        yield put(fetchDiscountSuccess(response));
    } catch (error) {
        // Dispatch failure action with the error message
        const status =
            error && typeof error === 'object' && 'response' in error && (error as any).response?.status
                ? (error as any).response.status
                : undefined;
        const message =
            (error && typeof error === 'object' && 'message' in error && (error as any).message) ?? '';
        yield put(fetchDiscountFailure({ status, message }));
    }
}

/**
 * Watcher saga: Watches for fetchDiscountRequest actions and triggers the fetch Discount saga.
 */
export function* discountSaga() {
    yield takeEvery(fetchDiscountRequest.type, fetchCustomerDiscount);
}

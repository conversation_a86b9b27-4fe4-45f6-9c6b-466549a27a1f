import React, { createContext, useContext, useRef, useState } from 'react';
import { AccessibilityInfo, findNodeHandle } from 'react-native';

// Type definitions for the internal ref and focus mappings
type FocusMap = Record<string, string>; // Maps screen names to focus keys
type RefMap = Record<string, any>;      // Maps focus keys to component refs

// Create a context to share accessibility focus control throughout the app
const AccessibilityFocusContext = createContext<any>(null);

/**
 * AccessibilityFocusProvider
 * Provides context to manage and restore accessibility focus across screens.
 * Tracks the last focused element on each screen and restores it when navigating back.
 */
export const AccessibilityFocusProvider = ({ children }: { children: React.ReactNode }) => {
  const focusRefs = useRef<RefMap>({});         // Stores refs by key
  const focusMap = useRef<FocusMap>({});        // Stores last focused keys by screen
  const navigationStack = useRef<string[]>([]); // Tracks the navigation stack
  const [isNavigatingBack, setIsNavigatingBack] = useState(false);

  /**
   * Registers a ref to be used for accessibility focus.
   * @param key Unique identifier for the ref
   */
  const setRef =
    (key: string) =>
      (el: any): void => {
        if (el) {
          focusRefs.current[key] = el;
        }
      };

  /**
  * Stores the key of the last focused element for a screen.
  * @param screen The screen name
  * @param key The key identifying the focused element
  */
  const setLastFocused = (screen: string, key: string): void => {
    if (focusRefs.current[key]) {
      focusMap.current[screen] = key;
    }
  };

  /**
   * Updates the navigation stack when a screen is focused
   * @param screen The screen name that's being focused
   */

  const handleScreenFocus = (screen: string): void => {
    const stack = navigationStack.current;
    // Check if we're navigating back by seeing if the screen exists in the stack
    const isNavigatingBack = stack.includes(screen);
    setIsNavigatingBack(isNavigatingBack);
    if (isNavigatingBack) {
      // Remove all screens after this one in the stack
      const index = stack.indexOf(screen);
      navigationStack.current = stack.slice(0, index + 1);
      // Only restore focus when going back, not for tab switches
    } else if (!stack.includes(screen)) {
      // Add the new screen to the stack
      navigationStack.current = [...stack, screen];
    }

    if (isNavigatingBack) {
      restoreLastFocus(screen);
    }
  };

  /**
   * Restores the accessibility focus to the last focused element on the given screen,
   * if navigating back and VoiceOver is enabled.
   * @param screen The screen name to restore focus for
   */
  const restoreLastFocus = (screen: string): void => {
    const key = focusMap.current[screen];
    const ref = key ? focusRefs.current[key] : null;
    if (ref) {
      const reactTag = findNodeHandle(ref);
      AccessibilityInfo.isScreenReaderEnabled().then((enabled) => {
        if (enabled && reactTag) {
          // Use a longer delay to override AppHeader's focus
          setTimeout(() => {
            AccessibilityInfo.setAccessibilityFocus(reactTag);
            setTimeout(() => {
              AccessibilityInfo.announceForAccessibility('');
            }, 200);
          }, 100);
        }
      });
    }
  };

  /**
 * Clears the navigation stack and all saved focus data.
 * Useful when resetting navigation (e.g., logout or deep link).
 */
  const resetNavigation = (): void => {
    navigationStack.current = [];
    focusMap.current = {};
    setIsNavigatingBack(false);
  };

  return (
    <AccessibilityFocusContext.Provider
      value={{
        setRef, setLastFocused, handleScreenFocus, restoreLastFocus, resetNavigation, isNavigatingBack
      }}
    >
      {children}
    </AccessibilityFocusContext.Provider>
  );
};

/**
 * useAccessibilityFocus
 * Hook to access the accessibility focus management context.
 * Usage:
 *   const { setRef, setLastFocused,  handleScreenFocus, isNavigatingBack } = useAccessibilityFocus();
 */
export const useAccessibilityFocus = () => useContext(AccessibilityFocusContext);

import { act, renderHook } from '@testing-library/react-native';
import React from 'react';
import { AccessibilityInfo } from 'react-native';

import { AccessibilityFocusProvider, useAccessibilityFocus } from './AccessibilityFocus';

import type { View } from 'react-native';

jest.mock('react-native', () => ({
  AccessibilityInfo: {
    setAccessibilityFocus: jest.fn(),
    announceForAccessibility: jest.fn(),
    isScreenReaderEnabled: jest.fn(() => Promise.resolve(true)),
  },
  findNodeHandle: jest.fn(() => 1),
}));

beforeEach(() => {
  jest.useFakeTimers();
  // Clear all mocks between tests
  jest.clearAllMocks();
});

afterEach(() => {
  jest.runOnlyPendingTimers();
  jest.useRealTimers();
});

describe('AccessibilityFocusProvider', () => {
  it('should provide focus management functions', () => {
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <AccessibilityFocusProvider>{children}</AccessibilityFocusProvider>
    );

    const { result } = renderHook(() => useAccessibilityFocus(), { wrapper });

    expect(result.current.setRef).toBeDefined();
    expect(result.current.setLastFocused).toBeDefined();
    expect(result.current.handleScreenFocus).toBeDefined();
  });

  it('should set refs correctly', () => {
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <AccessibilityFocusProvider>{children}</AccessibilityFocusProvider>
    );

    const { result } = renderHook(() => useAccessibilityFocus(), { wrapper });

    const testRef = React.createRef<View>();
    act(() => {
      result.current.setRef('testKey')(testRef);
    });

    expect(result.current.setRef).toBeDefined();
  });

  it('should track last focused element for a screen', () => {
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <AccessibilityFocusProvider>{children}</AccessibilityFocusProvider>
    );

    const { result } = renderHook(() => useAccessibilityFocus(), { wrapper });

    const testRef = React.createRef<View>();
    act(() => {
      result.current.setRef('testKey')(testRef);
      result.current.setLastFocused('screen1', 'testKey');
    });

    expect(result.current.setLastFocused).toBeDefined();
  });

  it('should handle screen focus and update navigation stack', () => {
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <AccessibilityFocusProvider>{children}</AccessibilityFocusProvider>
    );

    const { result } = renderHook(() => useAccessibilityFocus(), { wrapper });

    act(() => {
      result.current.handleScreenFocus('screen1');
    });

    expect(result.current.handleScreenFocus).toBeDefined();
  });



  it('should restore focus when navigating back to a screen', async () => {
    // Mock isScreenReaderEnabled to resolve with true immediately
    (AccessibilityInfo.isScreenReaderEnabled as jest.Mock).mockResolvedValue(true);

    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <AccessibilityFocusProvider>{children}</AccessibilityFocusProvider>
    );

    const { result } = renderHook(() => useAccessibilityFocus(), { wrapper });

    // Create a mock ref with a focus method
    const testRef = { current: { focus: jest.fn() } };

    // Set up the ref and focus mapping
    act(() => {
      result.current.setRef('testKey')(testRef);
      result.current.setLastFocused('screen1', 'testKey');
    });

    // Simulate initial navigation flow
    act(() => {
      result.current.handleScreenFocus('screen1'); // First visit
      result.current.handleScreenFocus('screen2'); // Navigate away
      result.current.handleScreenFocus('screen1'); // Navigate back
    });

    await act(async () => {
      // Let the promise for isScreenReaderEnabled resolve
      await Promise.resolve();
    });

    // Now step through timers synchronously
    act(() => {
      jest.advanceTimersByTime(10);   // VoiceOver check
      jest.advanceTimersByTime(100);  // Delay before focus
      jest.advanceTimersByTime(200);  // Delay before announce
    });

    // Verify the focus was set
    expect(AccessibilityInfo.setAccessibilityFocus).toHaveBeenCalledWith(1);
    expect(AccessibilityInfo.announceForAccessibility).toHaveBeenCalledWith('');
  });

  it('should not restore focus during tab navigation', () => {
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <AccessibilityFocusProvider>{children}</AccessibilityFocusProvider>
    );

    const { result } = renderHook(() => useAccessibilityFocus(), { wrapper });

    const testRef = React.createRef<View>();
    act(() => {
      result.current.setRef('testKey')(testRef);
      result.current.setLastFocused('screen1', 'testKey');
      result.current.handleScreenFocus('screen1', 'tab');
    });

    act(() => {
      jest.runAllTimers();
    });

    expect(AccessibilityInfo.setAccessibilityFocus).not.toHaveBeenCalled();
  });

  it('should not attempt to restore focus if no element was focused', () => {
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <AccessibilityFocusProvider>{children}</AccessibilityFocusProvider>
    );

    const { result } = renderHook(() => useAccessibilityFocus(), { wrapper });

    act(() => {
      result.current.handleScreenFocus('screenWithNoFocus', 'goBack');
    });

    act(() => {
      jest.runAllTimers();
    });

    expect(AccessibilityInfo.setAccessibilityFocus).not.toHaveBeenCalled();
  });

  it('should handle null refs gracefully', () => {
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <AccessibilityFocusProvider>{children}</AccessibilityFocusProvider>
    );

    const { result } = renderHook(() => useAccessibilityFocus(), { wrapper });

    act(() => {
      result.current.setRef('testKey')(null);
      result.current.setLastFocused('screen1', 'testKey');
      result.current.handleScreenFocus('screen1', 'goBack');
    });

    act(() => {
      jest.runAllTimers();
    });

    expect(AccessibilityInfo.setAccessibilityFocus).not.toHaveBeenCalled();
  });
});
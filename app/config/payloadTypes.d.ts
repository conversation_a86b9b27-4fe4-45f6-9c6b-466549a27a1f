export type StoreLocation = {
  latitude: number;
  longitude: number;
};

export type RewardScorecard = {
  hhid: number | string;
  programType: string[];
}

export type FL_TYPES = "associateProfile" | "store" | "ucaProfile" | "loyalty";
export type AnnouncementsParams = {
  associateRole: string;
  divisionName: string;
  visibility: string;
  "request-id": any;
  numDays: string;
  requireAtLeastOneAnnouncement?: boolean;
}
export type AnnouncementUrlParams = {
  assetNames: string;
  "request-id": any;
}

export type EmployeeScheduleParams = {
  requestId: string;
  empId: string;
  weekNumber: number;
  year: number;
  fromDate?: string; // Optional: for custom date range
  toDate?: string;   // Optional: for custom date range
  isSilent?: boolean; // * indicate this is silent call
}

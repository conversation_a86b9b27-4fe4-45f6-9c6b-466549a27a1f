export const BASE_HOST_URL = process.env.BASE_HOST_URL;
export const OCP_APIM_SUBSCRIPTION_REWARDS_KEY = process.env.OCP_APIM_SUBSCRIPTION_REWARDS_KEY;
export const OCP_APIM_SUBSCRIPTION_XAPI_KEY = process.env.OCP_APIM_SUBSCRIPTION_XAPI_KEY;
export const OCP_APIM_SUBSCRIPTION_FEATURE_FLAG_KEY = process.env.OCP_APIM_SUBSCRIPTION_FEATURE_FLAG_KEY;
/*
  * Get the base URL for the API, replacing the banner placeholder if necessary.
  * @param banner The banner to be used in the base URL.
  * @returns The complete base URL for the API.
  */
export const getXAPIBaseURL = (): string => {
  const baseHostUrl = (process.env.BASE_HOST_URL ?? '')
  const basePathXAPI = process.env.XAPI_BASE_PATH ?? '';
  return `${baseHostUrl}${basePathXAPI}`;
};
/*
  * Get the base URL for the API, replacing the banner placeholder if necessary.
  * @param banner The banner to be used in the base URL.
  * @returns The complete base URL for the API.
  */
export const getBannerBaseURL = (): string => {
  const baseHostUrl = (process.env.BASE_HOST_URL ?? '')
  return `${baseHostUrl}`;
};

/*
 * Retrieves the base URL for the rewards API from the environment variable `REWARDS_API_PATH`.
 *
 * @returns {string} The base URL for the rewards API, or an empty string if the environment variable is not set.
 */
export const getRewardsPath = (): string => {
  const rewardsUrl = (process.env.REWARDS_API_PATH ?? '')
  return `${rewardsUrl}`;
};


/*
 * Returns the API path for feature flags.
 *
 * @returns {string} The feature flag API path from the environment variable FEATURE_FLAG_API_PATH.
 *
 * Example:
 *   process.env.FEATURE_FLAG_API_PATH = "/api/feature-flags"
 *   getFeatureFlagPath(); // "/api/feature-flags"
 */
export const getFeatureFlagPath = (): string => {
  const featureFlagUrl = (process.env.FEATURE_FLAG_API_PATH ?? '')
  return `${featureFlagUrl}`;
};

/*
 * Retrieves the base URL for create new employee discount API.
 *
 * @returns {string} URL for create new employee discount API, or an empty string if the environment variable is not set.
 */
export const getCreateNewEmployeeDiscountPath = (): string => {
  const baseHostUrl = (process.env.EMPLOYEE_DISCOUNT_API_BASE_URL ?? '')
  const createNewEmplopyeeDiscountAPI = process.env.CREATE_NEW_EMPLOYEE_DISCOUNT_API_URL ?? '';
  return `${baseHostUrl}${createNewEmplopyeeDiscountAPI}`;
}


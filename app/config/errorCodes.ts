// statusCodes.ts
export enum HttpStatusCode {
    Unauthorized = 401,
    Forbidden = 403,
    NotFound = 404,
    InternalServerError = 500,
    RateLimitError = 429,
    BadGateway = 502,
    ServiceUnavailable = 503,
    GatewayTimeout = 504,
    BadRequest = 400,
    ConflictError = 409,
    Success = 200,
    Success_No_Data = 204,
    // Add more as needed
}

export const ErrorStoreCodes = {
    noStoresFound: "140401",
    noScoreCardPointsFound: "140409",
    noScoreCardDataFound: "140035",
} as const;

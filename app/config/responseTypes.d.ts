

//Rewards Scorecard Response
export interface RewardsScorecardResponse {
    hhId: string
    scorecards: Scorecard[]
}
export interface Scorecard {
    dollarDiscount: number
    autoRewardsPoints: number
    willExpire: number
    points: Point[]
    balance: number
    programType: string
}

export interface Point {
    value: number
    validityEndDate: string
}
export interface AnnouncementContent {
    title: string;
    storyDetails: string;
    previewImageUrl: string;
    previewDownloadableImageUrl: string;
    division: string[];
    contentType: string;
    detailPageUrl: string;
    assetName: string;
    isAvailableForAssociates: boolean;
    createdDate: string;
    updatedDate: string;
    startDate: string;
    endDate: string;
}
export interface AnnouncementsResponse {
    numberOfItems: number;
    content: AnnouncementContent[];
}
export interface AnnouncementUrlResponse {
    downloadableUrl: string;
    assetName: string;
}

export interface EmployeeScheduleResponse {
    scheduleResponse: ScheduleResponse
}

export interface ScheduleResponse {
    schedule: Schedule[]
    workedHours: WorkedHour[]
    balances: Balance[]
}

export interface Schedule {
    id: number
    scheduleId: number
    startTime: string
    endTime: string
    jobStartTime: any
    jobEndTime: any
    workDate: string
    minutes: number
    timeCodeName: string
    hourTypeName: string
    activityName: any
    departmentName: string
    jobName: string
    skillName: any
    teamName: string
    projectName: string
    docketName: string
    rate: number
    quantity: any
    messages: any
    comments: any
    overridden: boolean
    status: string
    type: string
    edited: boolean
    shiftSummaryId: any
}

export interface WorkedHour {
    id: number
    workDate: string
    startTime: string
    endTime: string
    employeeName: string
    shiftName: string
    payGroupName: string
    calcGroupName: string
    authorized: boolean
    authorizedByUsername: string
    authorizedDate: string
    errorStatus: boolean
    flagBreak: boolean
    flagRecall: boolean
    description: any
    comments: any
    errorMessage: any
    timeCodeSummary: TimeCodeSummary[]
    hourTypeSummary: HourTypeSummary[]
    messages: string
    inCode: string
    outCode: string
    fullDayCode: string
    fullDayMinutes: number
    submitted: boolean
    attestationStatus: string
    clocksAuthorized: boolean
}

export interface TimeCodeSummary {
    name: string
    time: number
}

export interface HourTypeSummary {
    name: string
    time: number
}

export interface Balance {
    balanceId: number
    employeeName: string
    balanceDate: string
    balanceName: string
    balanceTypeName: string
    currentValue: number
    usedValue: number
    accruedValue: number
    futureApprovedValue: number
}

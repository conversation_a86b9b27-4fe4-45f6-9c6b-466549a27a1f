import axios from 'axios';
import { Platform } from 'react-native';

import {
  getBannerBaseURL,
  getXAPIBaseURL,
  OCP_APIM_SUBSCRIPTION_REWARDS_KEY,
  OCP_APIM_SUBSCRIPTION_XAPI_KEY,
  OCP_APIM_SUBSCRIPTION_FEATURE_FLAG_KEY,
  getFeatureFlagPath,
  getRewardsPath,
  getCreateNewEmployeeDiscountPath,
} from '..';
import ABAuditEngine from '../../../analytics/ABAuditEngine';
import AppUtils, { readConfig } from '../../../app/utils/AppUtils';
import { store } from '../../store';
import * as AsyncStorage from '../../store/AsyncStorage';
import { setAnnouncementServerError } from '../../store/reducers/errorSlice';
import { SecureStorage, TokenType } from '../../store/SecureStorage';
import {
  REWARDS_ENDPOINT,
  ANNOUNCEMENT_PATH,
  ANNOUNCEMENT_URL_PATH,
  xAPI_PROFILE,
  xAPI_PROFILE_PHOTO,
  xAPI_FEATURE_FLAG,
  xAPI_OTP_VERIFY,
  xAPI_EMP_DISCOUNT,
  xAPI_CLOCK_STATUS,
  REWARD_ACCESS_TOKEN,
  PROFILE_PHOTO,
  FEATURE_FLAG_API,
  GENERATE_ACCESS_TOKEN,
  GENERATE_OBO_ACCESS_TOKEN,
  xAPI_EMP_SCHEDULE,
  xAPI_TNC_AGREEMENT,
} from '../endPoints';



import { handleError, handleInternalError } from './errorHandler';

const appConfigurationFailure = 'Error: Authorable content failed';

const XAPI_BASE_URL = getXAPIBaseURL();
const XAPI_BANNER_URL = getBannerBaseURL();

// Instantiate SecureStorage
const secureStorage = new SecureStorage();

/**
 * Retrieves the authentication token from secure storage.
 *
 * This function attempts to fetch the middle-tier access token and its expiry time
 * from secure storage. If the token exists and has not expired, it returns the token.
 * Otherwise, it returns `null`.
 *
 * @async
 * @function
 * @returns {Promise<string|null>} A promise that resolves to the authentication token
 * if it exists and is valid, or `null` if the token is expired, missing, or an error occurs.
 *
 * - If the token is missing or expired, a new token is fetched using client credentials.
 * - The new token and its expiration time are securely stored for future use.
 */
const getAuthTokenFromStorage = async () => {
  try {
    const middleTierAccessTokenExpiry = await secureStorage.getToken(
      TokenType.middleTierAccessTokenExpiry,
    );
    const middleTierAccessToken = await secureStorage.getToken(TokenType.middleTierAccessToken);
    if (middleTierAccessToken && middleTierAccessTokenExpiry) {
      const currentTimestamp = Date.now() / 1000;
      if (currentTimestamp < middleTierAccessTokenExpiry) {
        return middleTierAccessToken; // Return the existing token if it is not expired
      }
    } else {
      console.log('Access token is not existing in secure storage or expired');
    }
  } catch (error) {
    console.error('Error fetching auth token:', error);
    return null; // Return null if there's an error
  }
};

/**
 * Retrieves the authentication token, refreshing it if necessary.
 *
 * This function attempts to fetch the access token from storage. If the token
 * is null or expired, it regenerates a new access token before returning it.
 *
 * @async
 * @function
 * @returns {Promise<string>} A promise that resolves to the valid access token.
 */
const getAuthTokenRefreshIfNeeded = async () => {
  var accessToken = await getAuthTokenFromStorage(); // Fetch the token from storage
  if (!accessToken) {
    accessToken = await generateAccessToken();
  }
  return accessToken;
};

let abortController = new AbortController(); // Global controller

export const cancelAllRequests = () => {
  abortController.abort(); // Cancel current requests
  abortController = new AbortController(); // Reset for next use
};

// Create an Axios instance
const axiosInstance = axios.create({
  baseURL: XAPI_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    Accept: 'application/json',
  },
});

axiosInstance.interceptors.request.use(
  async (config) => {
    config.signal = abortController.signal; // Attach signal
    return config;
  },
  (error) => {
    return Promise.reject(error); // Handle errors for the request
  },
);

// After the response, log status and headers in the response interceptor
axiosInstance.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    return Promise.reject(error);
  },
);

// Create a separate Axios instance for the banner based API
const bannerAxiosInstance = axios.create({
  baseURL: XAPI_BANNER_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    Accept: 'application/json',
  },
});

bannerAxiosInstance.interceptors.request.use(
  async (config) => {
    config.signal = abortController.signal; // Attach signal
    return config;
  },
  (error) => {
    return Promise.reject(error); // Handle errors for the request
  },
);

// After the response, log status and headers in the response interceptor
bannerAxiosInstance.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    return Promise.reject(error);
  },
);

// GET Request
/**
 * @param {string} url - url for the request
 * @param {headers} headers - Header of the request
 */

export const get = async (
  url,
  headers,
  apiMethod = '',
  instance = axiosInstance,
  responseType = 'json',
) => {
  try {
    const response = await instance.get(url, {
      headers,
      responseType, // dynamically set based on caller
    });
    return response.data;
  } catch (error) {
    ABAuditEngine.leaveBreadcrumbWithTagAndNetworkError(apiMethod, error);
    throw error;
  }
};

// POST Request

/**
 * @param {string} url - url for the request
 * @param {headers} headers - Header of the request
 * @param {object} data - Body of the request
 */
export const post = async (url, headers, data, apiMethod = '', instance = axiosInstance) => {
  try {
    const response = await instance.post(url, data, { headers });
    return response.data;
  } catch (error) {
    ABAuditEngine.leaveBreadcrumbWithTagAndNetworkError(apiMethod, error);
    throw error;
  }
};

// PUT Request
/**
 * @param {string} url - url for the request
 * @param {object} data - Body of the request
 */
export const put = async (url, data) => {
  try {
    const response = await axiosInstance.put(url, data);
    return response.data;
  } catch (error) {}
};

// DELETE Request
/**
 * @param {string} url - url for the request
 */
export const deleteRequest = async (url) => {
  try {
    const response = await axiosInstance.delete(url);
    return response.data;
  } catch (error) {}
};

/**
 * Get app configurations from the AEM service.
 *
 * This function to get app configurations from AEM service based on selected language
 *
 * @param {string} languageId - Selected language ID
 * @returns {Promise<Object>} - The response from the API containing app configurations or local JSON data.
 */
export const getAuthorableContent = async (languageId) => {
  try {
    const baseHostUrl = process.env.BASE_HOST_URL ?? '';
    var configUrlPath = process.env.APPCONFIG_URL_PATH_EN ?? '';
    if (languageId == 'es') {
      configUrlPath = process.env.APPCONFIG_URL_PATH_ES ?? '';
    }
    const response = await axiosInstance.get(`${baseHostUrl}${configUrlPath}`);
    return response.data;
  } catch (error) {
    ABAuditEngine.leaveBreadcrumbWithMode(appConfigurationFailure);
    // Return local AEM config data
    if (languageId == 'es') {
      return readConfig('associateAppConfig_es');
    } else {
      return readConfig('associateAppConfig_en');
    }
  }
};

export const getProfile = async (params, payload) => {
  try {
    const endpoint = `${xAPI_PROFILE}${params}`;
    const accessToken = await getAuthTokenRefreshIfNeeded();
    const headers = {
      accept: 'application/json',
      'content-type': 'application/json',
      'ocp-apim-subscription-key': OCP_APIM_SUBSCRIPTION_XAPI_KEY,
      appversion: AppUtils.getAppVersion(), // Application version
      platform: Platform.OS, // Platform (e.g., iOS or Android)
      'client-name': AppUtils.APP_NAME, // Application name
      'X-access-token': accessToken, // API key for authentication
    };
    let response = await get(endpoint, headers, xAPI_PROFILE);
    response = response.response;
    if (response?.errors != null && response?.errors?.length > 0) {
      const { errors } = response;
      if (errors?.length > 0) {
        handleInternalError(xAPI_PROFILE, payload, true);
        errors.forEach((error) => {
          const { code, message } = error;
          const eachError = new Error(`${code}: ${message}`);
          ABAuditEngine.leaveBreadcrumbWithTagAndNetworkError(xAPI_PROFILE, eachError);
        });
      }
    }
    const currentDate = Date.now();
    AsyncStorage.setItem(xAPI_PROFILE, currentDate);
    return response;
  } catch (error) {
    handleError(error, xAPI_PROFILE, payload);
    throw error;
  }
};

/**
 * Fetches the reward scorecard data for a user.
 *
 * This function retrieves a rewards access token, sets the base URL and headers,
 * and sends a POST request to the rewards scorecard endpoint with the provided body.
 * It handles and logs any errors encountered during the request and also leaves a
 * breadcrumb for analytics/debugging purposes.
 *
 * @param {object} body - The request body containing household and user-related information.
 * @returns {Promise<Object>} - A Promise that resolves to the scorecard data or throws an error.
 */
export const rewardsScorecard = async (body, payload) => {
  const rewardsPath = getRewardsPath();
  const endpointUrl = `${rewardsPath}${REWARDS_ENDPOINT}`;

  try {
    const rewardsToken = await rewardsAccessToken();

    const headers = {
      Accept: '*/*',
      'Content-Type': 'application/vnd.safeway.v3+json',
      'ocp-apim-subscription-key': OCP_APIM_SUBSCRIPTION_REWARDS_KEY,
      Authorization: `Bearer ${rewardsToken}`,
      'x-abs-client-id': process.env.ABS_REWARDS_CLIENT_ID,
    };
    const response = await post(
      endpointUrl,
      headers,
      body,
      'rewardsScorecard',
      bannerAxiosInstance,
    );

    if (response?.errors?.length) {
      handleInternalError(REWARDS_ENDPOINT, payload, true);
      response.errors.forEach(({ code, message }) => {
        const errorDetails = new Error(`${code}: ${message}`);
        ABAuditEngine.leaveBreadcrumbWithTagAndNetworkError(REWARDS_ENDPOINT, errorDetails);
      });
    }
    return response;
  } catch (error) {
    handleError(error, REWARDS_ENDPOINT, payload);
    throw error; // Throw error to be handled by calling function
  }
};

/**
 * Retrieves the rewards access token. If a valid token is already stored and not expired, it returns the token.
 * Otherwise, it fetches a new token from the rewards token endpoint, stores it securely, and returns it.
 *
 * @async
 * @function rewardsAccessToken
 * @returns {Promise<string|undefined>} The rewards access token if successful, or undefined if an error occurs.
 *
 * @throws { Error } If there is an issue fetching the token from the endpoint.
 *
 * @description
 * - Checks if a valid rewards access token is stored in secure storage.
 * - If the token is valid and not expired, it is returned.
 * - If the token is missing or expired, a new token is fetched using client credentials.
 * - The new token and its expiration time are securely stored for future use.
 *
 * @example
 * const token = await rewardsAccessToken();
 * if (token) {
 * console.log('Rewards Access Token:', token);
 * } else {
 * console.error('Failed to retrieve rewards access token.');
 * }
 */
export const rewardsAccessToken = async () => {
  const rewardAccessToken = await secureStorage.getToken(TokenType.rewardAccessToken);
  const rewardAccessTokenExpiry = await secureStorage.getToken(TokenType.rewardAccessTokenExpiry);

  if (rewardAccessToken && rewardAccessTokenExpiry) {
    const currentTimestamp = Date.now() / 1000;
    if (currentTimestamp < rewardAccessTokenExpiry) {
      // return the existing token if it is not expired
      return rewardAccessToken;
    }
  }
  try {
    const endpointUrl = process.env.MSAUTH_REWARD_TOKEN_ENDPOINT;
    const formData = new URLSearchParams({
      client_id: process.env.MSAUTH_CLIENT_ID,
      client_secret: process.env.MSAUTH_CLIENT_SECRET,
      grant_type: 'client_credentials',
    });
    const headers = { 'Content-Type': 'application/x-www-form-urlencoded' };
    const response = await post(endpointUrl, headers, formData.toString(), REWARD_ACCESS_TOKEN);

    const accessToken = response.access_token;
    if (accessToken) {
      secureStorage.saveToken(TokenType.rewardAccessToken, accessToken);
      secureStorage.saveToken(TokenType.rewardAccessTokenExpiry, response.expires_on);
      return accessToken;
    }
    throw new Error('Failed to retrieve rewards access token');
  } catch (error) {
    throw error; // Throw error to be handled by calling function
  }
};

// Function to fetch announcements
export const announcements = async (params = {}, banner) => {
  try {
    const queryString = Object.entries(params)
      .map(([key, value]) => `${key}=${value}`)
      .join('&');
    const enpointUrl = `${ANNOUNCEMENT_PATH}?${queryString}`;
    const accessToken = await getAuthTokenRefreshIfNeeded(); // Fetch the token from storage
    const headers = {
      'ocp-apim-subscription-key': OCP_APIM_SUBSCRIPTION_XAPI_KEY, // Subscription key for API access
      appversion: AppUtils.getAppVersion(), // Application version
      platform: Platform.OS, // Platform (e.g., iOS or Android)
      banner, // Banner identifier
      'client-name': AppUtils.APP_NAME, // Application name
      'X-access-token': accessToken, // API key for authentication
    };

    const { response } = await get(enpointUrl, headers);
    if (response?.errors?.length) {
      response.errors.forEach(({ code, message }) => {
        const errorDetails = new Error(`${code}: ${message}`);
        ABAuditEngine.leaveBreadcrumbWithTagAndNetworkError(ANNOUNCEMENT_PATH, errorDetails);
        announcements;
      });
    } else {
      const currentDate = Date.now();
      store.dispatch(setAnnouncementServerError(false));
      AsyncStorage.setItem(ANNOUNCEMENT_PATH, currentDate);
    }
    return response;
  } catch (error) {
    handleError(error, ANNOUNCEMENT_PATH);
    ABAuditEngine.leaveBreadcrumbWithTagAndNetworkError(ANNOUNCEMENT_PATH, error);
  }
};

// Function to fetch announcement details Url
export const announcementDetailsUrl = async (params = {}, banner) => {
  try {
    const queryString = Object.entries(params)
      .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
      .join('&');
    const enpointUrl = `${ANNOUNCEMENT_URL_PATH}?${queryString}`;
    const accessToken = await getAuthTokenRefreshIfNeeded(); // Fetch the token from storage
    const headers = {
      'ocp-apim-subscription-key': OCP_APIM_SUBSCRIPTION_XAPI_KEY, // Subscription key for API access
      appversion: AppUtils.getAppVersion(), // Application version
      platform: Platform.OS, // Platform (e.g., iOS or Android)
      banner, // Banner identifier
      'client-name': AppUtils.APP_NAME, // Application name
      'X-access-token': accessToken, // API key for authentication
    };
    const { response } = await get(enpointUrl, headers);
    if (response?.errors?.length) {
      response.errors.forEach(({ code, message }) => {
        const errorDetails = new Error(`${code}: ${message}`);
        ABAuditEngine.leaveBreadcrumbWithTagAndNetworkError(
          'announcement details url',
          errorDetails,
        );
      });
    }
    return response;
  } catch (error) {
    handleError(error, 'announcement details url');
    ABAuditEngine.leaveBreadcrumbWithTagAndNetworkError('announcement details url', error);
  }
};

// Function to fetch feature flags
/**
 * Fetches the feature flag of the app from the xAPI service.
 *
 * This function sets the appropriate headers and base URL,
 * then sends a GET request to the xAPI feature flag endpoint.
 * It handles API errors (if any), logs them for debugging,
 * and triggers analytics breadcrumbs in case of failure.
 *
 * @param {string} params - The user-specific query params to be appended to the endpoint.
 * @param {string} banner - The banner identifier used to target a specific environment or client config.
 * @returns {Promise<Object>} - The response from the API containing the feature flag or error data.
 */
export const getFeatureFlag = async (
  banner,
  zipCode,
  division,
  guid,
  employeeID,
  storeID,
  facilityType,
) => {
  try {
    const featureFlagPath = getFeatureFlagPath();
    const endpointUrl = `${featureFlagPath}${xAPI_FEATURE_FLAG}`;

    const headers = {
      'ocp-apim-subscription-key': OCP_APIM_SUBSCRIPTION_FEATURE_FLAG_KEY,
    };

    const body = {
      attributes: {
        AppVersion: AppUtils.getAppVersion(),
        ZipCode: zipCode,
        Banner: banner,
        Device: Platform.OS,
        storeID: storeID,
        guid: guid,
        OSVersion: Platform.Version,
        Division: division,
        employeeID: employeeID,
        facilityType: facilityType, // Added facility type to the request body
      },
      version: '1.0', //this is version
      appCode: AppUtils.APP_CODE_FEATURE_FLAG,
    };
    const response = await post(endpointUrl, headers, body, FEATURE_FLAG_API, bannerAxiosInstance);
    if (response?.errors?.length) {
      response.errors.forEach(({ code, message }) => {
        const errorDetails = new Error(`${code}: ${message}`);
        ABAuditEngine.leaveBreadcrumbWithTagAndNetworkError(FEATURE_FLAG_API, errorDetails);
      });
    } //need to handle
    return response;
  } catch (error) {
    throw error; // Throw error to be handled by calling function
  }
};

/**
 * Fetches the profile photo of a user from the xAPI service.
 *
 * This function sets the appropriate headers and base URL,
 * then sends a GET request to the xAPI profile photo endpoint.
 * It handles API errors (if any), logs them for debugging,
 * and triggers analytics breadcrumbs in case of failure.
 *
 * @param {string} params - The user-specific query params to be appended to the endpoint.
 * @param {string} banner - The banner identifier used to target a specific environment or client config.
 * @returns {Promise<Object>} - The response from the API containing the profile photo or error data.
 */
export const getProfilePhoto = async (params, banner, payload) => {
  try {
    const endpoint = `${xAPI_PROFILE_PHOTO}${params}`;
    const accessToken = await getAuthTokenRefreshIfNeeded(); // Fetch the token from storage
    const headers = {
      'ocp-apim-subscription-key': OCP_APIM_SUBSCRIPTION_XAPI_KEY, // Subscription key for API access
      appversion: AppUtils.getAppVersion(), // Application version
      platform: Platform.OS, // Platform (e.g., iOS or Android)
      banner, // Banner identifier
      'client-name': AppUtils.APP_NAME, // Application name
      'X-access-token': accessToken, // API key for authentication
    };

    const response = await get(endpoint, headers, PROFILE_PHOTO, axiosInstance, 'arraybuffer');
    if (response?.errors != null && response?.errors?.length > 0) {
      const { errors } = response;
      if (errors?.length > 0) {
        errors.forEach((error) => {
          const { code, message } = error;
          const eachError = new Error(`${code}: ${message}`);
          ABAuditEngine.leaveBreadcrumbWithTagAndNetworkError(PROFILE_PHOTO, eachError);
        });
      }
    }

    // Convert arraybuffer to base64
    const base64String = Buffer.from(response, 'binary').toString('base64');
    const imageUri = `data:image/jpeg;base64,${base64String}`; // Adjust MIME type if needed

    return imageUri;
  } catch (error) {
    handleError(error, PROFILE_PHOTO, payload);
    throw error; // Throw error to be handled by calling function
  }
};

/**
 * Generates the access token from secure storage if it is existing or is not expired
 * or get new access token using the refresh token stored in secure storage
 *
 * This function get access token from secure storage if it is existing or is not expired or retrieves the refresh token,
 * sends a request to the token endpoint to generate a new access token, and updates the secure storage with the new
 * access token, refresh token, and their expiration time.
 *
 * @async
 * @function generateAccessToken
 * @param isResetingPassword - default value is false
 * @returns {Promise<string|null>} The new access token if successful, or `null` if the process fails.
 *
 * @throws {Error} If an error occurs during the token regeneration process, it is logged and handled.
 *
 * @example
 * const newAccessToken = await generateAccessToken();
 * if (newAccessToken) {
 *   console.log('Access token regenerated successfully:', newAccessToken);
 * } else {
 *   console.error('Failed to regenerate access token.');
 * }
 */
export const generateAccessToken = async (isResetingPassword = false) => {
  const middleTierAccessToken = await secureStorage.getToken(TokenType.middleTierAccessToken);
  const middleTierAccessTokenExpiry = await secureStorage.getToken(
    TokenType.middleTierAccessTokenExpiry,
  );

  // If isResetingPassword is true, we won't get the token from local storage
  if (middleTierAccessToken && middleTierAccessTokenExpiry && !isResetingPassword) {
    const currentTimestamp = Date.now() / 1000;
    if (currentTimestamp < middleTierAccessTokenExpiry) {
      // return the existing token if it is not expired
      return middleTierAccessToken;
    }
  }

  try {
    const refreshToken = await secureStorage.getToken(TokenType.middleTierRefreshToken);
    const endpointUrl = process.env.MSAUTH_TOKEN_ENDPOINT;
    const formData = new URLSearchParams({
      client_id: process.env.MSAUTH_CLIENT_ID,
      client_secret: process.env.MSAUTH_CLIENT_SECRET,
      grant_type: 'refresh_token',
      refresh_token: refreshToken,
      scope: process.env.MSAUTH_LOGIN_SCOPE,
    });
    const headers = { 'Content-Type': 'application/x-www-form-urlencoded' };
    const response = await post(endpointUrl, headers, formData.toString(), GENERATE_ACCESS_TOKEN);
    if (response?.access_token) {
      const newAccessToken = response.access_token;
      const middleTierAccessTokenExpireIn = response.expires_in;
      const currentTimestamp = Date.now() / 1000;
      const expirationTimestamp = currentTimestamp + middleTierAccessTokenExpireIn;

      // Save the new access token, refresh token and its expiry time
      await secureStorage.saveToken(TokenType.middleTierAccessToken, newAccessToken);
      await secureStorage.saveToken(TokenType.middleTierRefreshToken, response.refresh_token);
      await secureStorage.saveToken(
        TokenType.middleTierAccessTokenExpiry,
        expirationTimestamp.toString(),
      );

      return newAccessToken; // Return the new token
    }
    throw new Error('Failed to generate access token');
  } catch (error) {
    throw error; // Throw error to be handled by calling function
  }
};

/**
 * Generates the OBO (On-Behalf-Of) access token from secure storage if it is existing or is not expired or using a refresh token.
 *
 * This function retrieves the refresh token from secure storage if it is existing or is not expired
 * or exchanges the refresh token for a new access token.
 * The function handles errors by logging them and leaving a breadcrumb for debugging purposes.
 *
 * @async
 * @function generateOBOToken
 * @param isResetingPassword - default value is false
 * @returns {Promise<Object>} The response from the token endpoint containing the new access token.
 * @throws Will handle and log any errors that occur during the token regeneration process.
 */
export const generateOBOToken = async (isResetingPassword = false) => {
  const oboAccessToken = await secureStorage.getToken(TokenType.oboAccessToken);
  const oboAccessTokenExpiry = await secureStorage.getToken(TokenType.oboAccessTokenExpiry);

  // If isResetingPassword is true, we won't get the token from local storage
  if (oboAccessToken && oboAccessTokenExpiry && !isResetingPassword) {
    const currentTimestamp = Date.now() / 1000;
    if (currentTimestamp < oboAccessTokenExpiry) {
      // return the existing token if it is not expired
      return oboAccessToken;
    }
  }

  try {
    const refreshToken = await secureStorage.getToken(TokenType.oboRefreshToken);
    const endpointUrl = process.env.MSAUTH_TOKEN_ENDPOINT;
    const formData = new URLSearchParams({
      client_id: process.env.MSAUTH_CLIENT_ID,
      client_secret: process.env.MSAUTH_CLIENT_SECRET,
      grant_type: 'refresh_token',
      refresh_token: refreshToken,
      scope: process.env.MSAUTH_SCOPES_GRAPH,
    });
    const headers = { 'Content-Type': 'application/x-www-form-urlencoded' };
    const response = await post(
      endpointUrl,
      headers,
      formData.toString(),
      GENERATE_OBO_ACCESS_TOKEN,
    );
    if (response?.access_token) {
      const newAccessToken = response.access_token;
      const oboAccessTokenExpireIn = response.expires_in;
      const currentTimestamp = Date.now() / 1000;
      const expirationTimestamp = currentTimestamp + oboAccessTokenExpireIn;

      // Save the new access token, refresh token and its expiry time
      await secureStorage.saveToken(TokenType.oboAccessToken, newAccessToken);
      await secureStorage.saveToken(TokenType.oboRefreshToken, response.refresh_token);
      await secureStorage.saveToken(TokenType.oboAccessTokenExpiry, expirationTimestamp.toString());

      return newAccessToken; // Return the new token
    }
    throw new Error('Failed to generate OBO access token');
  } catch (error) {
    throw error; // Throw error to be handled by calling function
  }
};

/**
 * Verifies the OTP (One-Time Password) for a given customer at loyalty flow step two.
 *
 * @async
 * @function verifyOtp
 * @param {string} params - Query parameters to append to the OTP verification endpoint URL.
 * @param {string} customerId - The unique identifier of the customer.
 * @param {Object} [body={}] - The request body to send with the OTP verification (default is an empty object).
 * @returns {Promise<Object|undefined>} The response from the OTP verification API, or undefined if an error occurs.
 *
 * @throws Will log errors to the console if the request fails.
 */
export const verifyOtp = async (params, customerId, body = {}, payload) => {
  try {
    const endpointUrl = `${xAPI_OTP_VERIFY}${params}`;
    const accessToken = await getAuthTokenRefreshIfNeeded(); // Fetch the token from storage
    const headers = {
      'ocp-apim-subscription-key': OCP_APIM_SUBSCRIPTION_XAPI_KEY, // Subscription key for API access
      appversion: AppUtils.getAppVersion(), // Application version
      platform: Platform.OS, // Platform (e.g., iOS or Android)
      customerId, // Customer ID for the request
      'client-name': AppUtils.APP_NAME, // Application name
      'X-access-token': accessToken, // API key for authentication
    };

    // Send the POST request to verify the OTP
    const response = await post(endpointUrl, headers, body, xAPI_OTP_VERIFY);
    if (response?.errors?.length) {
      let internalStatusCode;
      response.errors.forEach(({ code, message }) => {
        internalStatusCode = code;
        const errorDetails = new Error(`${code}: ${message}`);
        ABAuditEngine.leaveBreadcrumbWithTagAndNetworkError(xAPI_OTP_VERIFY, errorDetails);
      });
      handleInternalError(xAPI_OTP_VERIFY, payload, internalStatusCode ?? null);
    }
    return response;
  } catch (error) {
    handleError(error, xAPI_OTP_VERIFY, payload);
    throw error;
  }
};

// Create a separate Axios instance for the customer discount API
const discountAxiosInstance = axios.create({
  baseURL: getCreateNewEmployeeDiscountPath(),
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    Accept: 'application/json',
  },
});
/**
 * Calls the employee discount API using a POST request.
 *
 * This function sends a POST request to the employee discount API endpoint with the required headers
 * and request body. It handles errors and logs them for debugging purposes.
 *
 * @param {string} ldapUser - The LDAP user ID to be included in the headers.
 * @returns {Promise<Object>} - The response from the API containing the discount details or error data.
 */
export const createNewEmployeeDiscount = async (body, payload) => {
  try {
    const subcriptionKey = process.env.OCP_APIM_SUBSCRIPTION_EMPLOYEE_DISCOUNT_KEY;
    const accessToken = await getAuthTokenRefreshIfNeeded(); // Fetch the access token

    const employeeId = await secureStorage.getToken(TokenType.employeeId);
    const ldap = await AsyncStorage.ldap();
    if (ldap) {
      const headers = {
        itToken: accessToken,
        'ocp-apim-subscription-key': subcriptionKey,
        UserId: ldap,
        'Content-Type': 'application/json',
      };
      body.employeeId = employeeId; // Added employeeId to the request body
      const response = await post(
        xAPI_EMP_DISCOUNT,
        headers,
        body,
        xAPI_EMP_DISCOUNT,
        discountAxiosInstance,
      );
      return response;
    } else {
      throw Error("LDAP doesn't exist");
    }
  } catch (error) {
    handleError(error, xAPI_EMP_DISCOUNT, payload);
    throw error; // Throw error to be handled by calling function
  }
};

/**
 * Retrieves the clock-in status for the current employee.
 *
 * This function fetches the employee ID and access token from secure storage,
 * constructs the appropriate API endpoint, and sends a GET request to retrieve
 * the clock-in status. If the response contains errors, they are logged for auditing.
 *
 * @async
 * @function
 * @param {string} params - Additional query parameters to append to the endpoint URL.
 * @returns {Promise<Object>} The API response containing the clock-in status or error details.
 * @throws {Error} Throws an error if the request fails or if there is an issue with fetching tokens.
 */
export const getClockInStatus = async (params, payload) => {
  try {
    const employeeId = await secureStorage.getToken(TokenType.employeeId); // Fetch the employee ID from secure storage
    const accessToken = await getAuthTokenRefreshIfNeeded(); // Fetch the token from storage
    const endpointUrl = `${xAPI_CLOCK_STATUS}${params}${employeeId}`;
    const headers = {
      'ocp-apim-subscription-key': OCP_APIM_SUBSCRIPTION_XAPI_KEY, // Subscription key for API access
      appversion: AppUtils.getAppVersion(), // Application version
      platform: Platform.OS, // Platform (e.g., iOS or Android)
      'client-name': AppUtils.APP_NAME, // Application name
      'X-access-token': accessToken, // API key for authentication
    };
    // Send the GET request to fetch clock-in status
    const response = await get(endpointUrl, headers, xAPI_CLOCK_STATUS);

    if (response?.errors?.length) {
      let statusCode;
      response.errors.forEach(({ code, message }) => {
        statusCode = code;
        const errorDetails = new Error(`${code}: ${message}`);
        ABAuditEngine.leaveBreadcrumbWithTagAndNetworkError(xAPI_CLOCK_STATUS, errorDetails);
      });
      if (payload?.isCallingFromHomeTab) {
        // if its Home tab we need to show the error message other than home no need to handle the error
        handleInternalError(xAPI_CLOCK_STATUS, payload, true, statusCode ?? null);
      }
    }
    return response;
  } catch (error) {
    if (payload?.isCallingFromHomeTab) {
      handleError(error, xAPI_CLOCK_STATUS, payload);
    }
    throw error;
  }
};

/**
 * Fetches the employee schedule data from the API.
 *
 * This function constructs the endpoint URL using the provided parameters,
 * retrieves the required authentication token, sets up the request headers,
 * and sends a GET request to the employee schedule API endpoint.
 * It logs the endpoint and response for debugging purposes.
 * If an error occurs, it logs the error, handles it using the centralized error handler,
 * and leaves a breadcrumb for analytics/debugging.
 *
 * @async
 * @function getEmployeeSchedule
 * @param {string} params - Query parameters to append to the employee schedule endpoint URL (e.g., "?request-id=...&empId=...&fromDate=...&toDate=...").
 * @returns {Promise<Object|undefined>} The response from the employee schedule API, or undefined if an error occurs.
 *
 * @example
 * const params = "?request-id=abc123&empId=12345&fromDate=2025-01-01&toDate=2025-01-31";
 * const schedule = await getEmployeeSchedule(params);
 * if (schedule) {
 *   console.log("Employee Schedule:", schedule);  
 * } else {
 *   console.error("Failed to fetch employee schedule.");
 * }
 */
export const getEmployeeSchedule = async (params, payload) => {
  try {
    const endpoint = `${xAPI_EMP_SCHEDULE}${params}`;
    const accessToken = await getAuthTokenRefreshIfNeeded(); // Fetch the token from storage
    const headers = {
      'ocp-apim-subscription-key': OCP_APIM_SUBSCRIPTION_XAPI_KEY, // Subscription key for API access
      appversion: AppUtils.getAppVersion(), // Application version
      platform: Platform.OS, // Platform (e.g., iOS or Android)
      'client-name': AppUtils.APP_NAME, // Application name
      'X-access-token': accessToken, // API key for authentication
    };

    // Send the GET request to fetch empSchedule
    const response = await get(endpoint, headers, xAPI_EMP_SCHEDULE);
    if (response?.scheduleResponse?.errors?.length) {
      handleInternalError(xAPI_EMP_SCHEDULE, payload, true);
      response.scheduleResponse.errors.forEach(({ code, message }) => {
        const errorDetails = new Error(`${code}: ${message}`);
        ABAuditEngine.leaveBreadcrumbWithTagAndNetworkError(xAPI_EMP_SCHEDULE, errorDetails);
      });
    }

    return response;
  } catch (error) {
    handleError(error, xAPI_EMP_SCHEDULE, payload);
    throw error;
  }
};

/**
 * Sends a Terms and Conditions (TNC) agreement request to the specified API endpoint.
 *
 * @async
 * @function tncAgreement
 * @param {Object} body - The request body containing the TNC agreement details.
 * @param {Object} payload - Additional payload data for error handling and auditing.
 * @returns {Promise<Object>} The API response object.
 * @throws {Error} Throws an error if the request fails or an unexpected error occurs.
 *
 * @example
 * try {
 *   const response = await tncAgreement(body, payload);
 *   console.log(response);
 * } catch (error) {
 *   console.error('Error:', error);
 * }
 *
 * @description
 * This function performs the following steps:
 * 1. Constructs the API endpoint URL for the TNC agreement.
 * 2. Retrieves an access token, refreshing it if necessary.
 * 3. Sets up the request headers, including authentication and app metadata.
 * 4. Sends a POST request to the API endpoint with the provided body and headers.
 * 5. Handles any errors returned by the API, logging them for auditing purposes.
 * 6. Returns the API response or throws an error if the request fails.
 */
export const tncAgreement = async (body, payload) => {
  try {
    const accessToken = await getAuthTokenRefreshIfNeeded(); // Fetch the token from storage
    const headers = {
      'ocp-apim-subscription-key': OCP_APIM_SUBSCRIPTION_XAPI_KEY, // Subscription key for API access
      appversion: AppUtils.getAppVersion(), // Application version
      platform: Platform.OS, // Platform (e.g., iOS or Android)
      'client-name': AppUtils.APP_NAME, // Application name
      'X-access-token': accessToken, // API key for authentication
    };
    const response = await post(
      xAPI_TNC_AGREEMENT,
      headers,
      body,
      xAPI_TNC_AGREEMENT,
    );
    if (response?.errors?.length) {
      handleInternalError(xAPI_TNC_AGREEMENT, payload);
      response.errors.forEach(({ code, message }) => {
        const errorDetails = new Error(`${code}: ${message}`);
        ABAuditEngine.leaveBreadcrumbWithTagAndNetworkError(xAPI_TNC_AGREEMENT + " - Error:", errorDetails);
      });
    }
    return response;
  } catch (error) {
    ABAuditEngine.leaveBreadcrumbWithTagAndNetworkError(xAPI_TNC_AGREEMENT + ' - Error:', error);
    handleError(error, xAPI_TNC_AGREEMENT, payload);
    throw error;
  }
};

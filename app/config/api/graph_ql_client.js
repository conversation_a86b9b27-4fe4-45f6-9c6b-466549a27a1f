import { ApolloClient, InMemoryCache, HttpLink, gql } from '@apollo/client';
import { handleError } from './errorHandler';

// Helper function to get the stored SSO token (e.g., from localStorage)
const getAuthToken = () => {
    return null;  // TODO handle token for authentication
};

// Create an Apollo Client instance
const client = new ApolloClient({
    link: new HttpLink({
        uri: 'https://graphql-pokemon2.vercel.app/',  // Sample GraphQL API
        headers: getAuthToken() && {
            'Authorization': `Bearer ${getAuthToken()}`,  // Attach the token to the headers
        },
    }),
    cache: new InMemoryCache(),
});

// Sample GraphQL Query (Fetching data)
export const fetchData = async (query) => {
    try {
        const result = await client.query({
            query: gql(query),
        });
        return result.data;
    } catch (error) {
        handleError(error);  // Call the centralized error handler
    }
};

// Sample GraphQL Mutation (Creating/Updating data)
export const mutateData = async (mutation, variables) => {
    try {
        const result = await client.mutate({
            mutation: gql(mutation),
            variables,
        });
        return result.data;
    } catch (error) {
        handleError(error);  // Call the centralized error handler
    }
};

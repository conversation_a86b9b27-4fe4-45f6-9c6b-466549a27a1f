// handleError.test.ts
import { networkCodes } from '../../../../app/shared/constants';
import { store } from '../../../store';
// Mock dependencies
import { showErrorAlert } from '../../../utils/AppUtils';
import { HttpStatusCode } from '../../errorCodes';
import { handleError, setNavigationRef, setTranslation } from '../errorHandler';

// Import the action for verification
jest.mock('../../../store');
jest.mock('../../../utils/AppUtils');
jest.mock('../../../store/reducers/errorSlice', () => ({
    setAnnouncementServerError: jest.fn(),
}));
jest.mock('../../../store/reducers/announcementSlice', () => ({
    fetchAnnouncementsRequest: jest.fn(),
}));
jest.mock('../../../store/reducers/profileSlice', () => ({
    fetchProfileRequest: jest.fn(),
}));
jest.mock('../../../store/reducers/profilePhotoSlice', () => ({
    fetchProfilePhotoRequest: jest.fn(),
}));
jest.mock('../../../store/reducers/otpVerifySlice', () => ({
    fetchOtpVerifyRequest: jest.fn(),
}));
jest.mock('../../../store/reducers/discountSlice', () => ({
    fetchDiscountRequest: jest.fn(),
}));
jest.mock('../../endPoints', () => ({
    xAPI_PROFILE: 'xAPI_PROFILE',
    PROFILE_PHOTO: 'PROFILE_PHOTO',
    xAPI_OTP_VERIFY: 'xAPI_OTP_VERIFY',
    xAPI_EMP_DISCOUNT: 'xAPI_EMP_DISCOUNT',
})); // Mock console methods
jest.spyOn(console, 'error').mockImplementation(() => { });
jest.spyOn(console, 'warn').mockImplementation(() => { });
jest.spyOn(console, 'log').mockImplementation(() => { });

describe('handleError', () => {
    const apiMethod = 'testApiMethod';
    let mockDispatch: jest.Mock;

    beforeEach(() => {
        mockDispatch = jest.fn();
        (store.dispatch as jest.Mock) = mockDispatch;
        jest.clearAllMocks();
    });

    it('should handle network errors', () => {
        const networkError = {
            code: networkCodes[0], // Use first network code
            message: 'Network error',
        };

        expect(() => handleError(networkError, apiMethod, null)).toThrow(networkError);
        expect(console.error).toHaveBeenCalledWith('Network Error:', apiMethod, networkError.message);
    });

    it('should handle rate limit errors', () => {
        const rateLimitError = {
            response: {
                status: HttpStatusCode.RateLimitError,
                statusText: 'Too Many Requests',
            },
        };

        expect(() => handleError(rateLimitError, apiMethod, null)).toThrow();
        // Rate limit errors are handled by handleAlert function when payload is not null
        // No specific console.warn is called for rate limits in the actual implementation
    });

    it('should handle unauthorized errors', () => {
        const unauthorizedError = {
            response: {
                status: HttpStatusCode.Unauthorized,
                statusText: 'Unauthorized',
            },
        };

        expect(() => handleError(unauthorizedError, apiMethod, null)).toThrow();
    });

    it('should handle server errors', () => {
        const serverErrors = [
            HttpStatusCode.InternalServerError,
            HttpStatusCode.BadRequest,
            HttpStatusCode.GatewayTimeout,
            HttpStatusCode.ServiceUnavailable,
            HttpStatusCode.NotFound,
        ];

        serverErrors.forEach((status) => {
            const error = { response: { status, statusText: 'Server Error' } };
            expect(() => handleError(error, apiMethod, null)).toThrow();
        });
    });

    it('should handle axios response errors', () => {
        const axiosError = {
            response: {
                status: 400,
                statusText: 'Bad Request',
                data: { message: 'Invalid input' },
            },
        };

        expect(() => handleError(axiosError, apiMethod, null)).toThrow();
        expect(console.error).toHaveBeenCalledWith('Axios Error:', apiMethod, 400, 'Bad Request');
        expect(console.error).toHaveBeenCalledWith('Error Details:', apiMethod, {
            message: 'Invalid input',
        });
    });

    it('should handle apollo network errors', () => {
        const apolloError = {
            networkError: { message: 'Network failure' },
        };

        expect(() => handleError(apolloError, apiMethod, null)).toThrow();
        expect(console.error).toHaveBeenCalledWith('Apollo Network Error:', apiMethod, {
            message: 'Network failure',
        });
    });

    it('should handle graphql errors', () => {
        const graphQLError = {
            graphQLErrors: [{ message: 'Validation failed' }, { message: 'Access denied' }],
        };

        expect(() => handleError(graphQLError, apiMethod, null)).toThrow();
        expect(console.error).toHaveBeenCalledWith('GraphQL Error:', apiMethod, 'Validation failed');
        expect(console.error).toHaveBeenCalledWith('GraphQL Error:', apiMethod, 'Access denied');
    });

    it('should handle unexpected errors', () => {
        const unexpectedError = { message: 'Unknown error' };
        expect(() => handleError(unexpectedError, apiMethod, null)).toThrow(unexpectedError);
        expect(console.error).toHaveBeenCalledWith('Unexpected Error:', apiMethod, 'Unknown error');
    });

    it('should handle string errors', () => {
        const stringError = 'Something went wrong';
        expect(() => handleError(stringError, apiMethod, null)).toThrow(stringError);
        expect(console.error).toHaveBeenCalledWith(
            'Unexpected Error:',
            apiMethod,
            'Something went wrong',
        );
    });

    // Test cases for utility functions
    describe('setNavigationRef', () => {
        it('should set navigation reference', () => {
            const mockNavigationRef = { navigate: jest.fn() };
            expect(() => setNavigationRef(mockNavigationRef)).not.toThrow();
        });
    });

    describe('setTranslation', () => {
        it('should set translation function', () => {
            const mockTranslate = jest.fn();
            expect(() => setTranslation(mockTranslate)).not.toThrow();
        });
    });

    // Test cases for different payload scenarios
    describe('handleError with different payloads', () => {
        beforeEach(() => {
            const mockTranslate = jest.fn((key) => `translated_${key}`);
            setTranslation(mockTranslate);
        });

        it('should handle rate limit errors with non-null payload', () => {
            const rateLimitError = {
                response: {
                    status: HttpStatusCode.RateLimitError,
                    statusText: 'Too Many Requests',
                },
            };
            const payload = { userId: 123 };

            expect(() => handleError(rateLimitError, apiMethod, payload)).toThrow();
            expect(showErrorAlert).toHaveBeenCalled();
        });

        it('should handle unauthorized errors with payload', () => {
            const unauthorizedError = {
                response: {
                    status: HttpStatusCode.Unauthorized,
                    statusText: 'Unauthorized',
                },
            };
            const payload = { userId: 123 };

            expect(() => handleError(unauthorizedError, apiMethod, payload)).toThrow();
            expect(showErrorAlert).toHaveBeenCalled();
        });

        it('should handle forbidden errors with payload', () => {
            const forbiddenError = {
                response: {
                    status: HttpStatusCode.Forbidden,
                    statusText: 'Forbidden',
                },
            };
            const payload = { userId: 123 };

            expect(() => handleError(forbiddenError, apiMethod, payload)).toThrow();
            expect(showErrorAlert).toHaveBeenCalled();
        });

        it('should handle bad gateway errors with payload', () => {
            const badGatewayError = {
                response: {
                    status: HttpStatusCode.BadGateway,
                    statusText: 'Bad Gateway',
                },
            };
            const payload = { userId: 123 };

            expect(() => handleError(badGatewayError, apiMethod, payload)).toThrow();
            expect(showErrorAlert).toHaveBeenCalled();
        });

        it('should handle announcements server errors', () => {
            const serverError = {
                response: {
                    status: HttpStatusCode.InternalServerError,
                    statusText: 'Internal Server Error',
                },
            };
            const payload = { userId: 123 };

            expect(() => handleError(serverError, 'announcements', payload)).toThrow();
            // The announcements case has special handling in the code
        });

        it('should handle conflict errors with payload', () => {
            const conflictError = {
                response: {
                    status: HttpStatusCode.ConflictError,
                    statusText: 'Conflict',
                },
            };
            const payload = { userId: 123 };

            expect(() => handleError(conflictError, apiMethod, payload)).toThrow();
        });
    });

    // Test cases for different API methods that trigger specific dispatches
    describe('API method specific handling', () => {
        beforeEach(() => {
            const mockTranslate = jest.fn((key) => `translated_${key}`);
            setTranslation(mockTranslate);
        });

        it('should handle xAPI_PROFILE errors with dispatch', () => {
            const serverError = {
                response: {
                    status: HttpStatusCode.InternalServerError,
                    statusText: 'Internal Server Error',
                },
            };
            const payload = { userId: 123 };

            expect(() => handleError(serverError, 'xAPI_PROFILE', payload)).toThrow();
            expect(showErrorAlert).toHaveBeenCalled();
        });

        it('should handle PROFILE_PHOTO errors with dispatch', () => {
            const serverError = {
                response: {
                    status: HttpStatusCode.InternalServerError,
                    statusText: 'Internal Server Error',
                },
            };
            const payload = { userId: 123 };

            expect(() => handleError(serverError, 'PROFILE_PHOTO', payload)).toThrow();
            expect(showErrorAlert).toHaveBeenCalled();
        });

        it('should handle fetchAnnouncements errors with dispatch', () => {
            const serverError = {
                response: {
                    status: HttpStatusCode.InternalServerError,
                    statusText: 'Internal Server Error',
                },
            };
            const payload = { page: 1 };

            expect(() => handleError(serverError, 'fetchAnnouncements', payload)).toThrow();
            expect(showErrorAlert).toHaveBeenCalled();
        });

        it('should handle xAPI_OTP_VERIFY errors with dispatch', () => {
            const serverError = {
                response: {
                    status: HttpStatusCode.InternalServerError,
                    statusText: 'Internal Server Error',
                },
            };
            const payload = { otp: '123456' };

            expect(() => handleError(serverError, 'xAPI_OTP_VERIFY', payload)).toThrow();
            expect(showErrorAlert).toHaveBeenCalled();
        });

        it('should handle xAPI_EMP_DISCOUNT errors with dispatch', () => {
            const serverError = {
                response: {
                    status: HttpStatusCode.InternalServerError,
                    statusText: 'Internal Server Error',
                },
            };
            const payload = { employeeId: '123' };

            expect(() => handleError(serverError, 'xAPI_EMP_DISCOUNT', payload)).toThrow();
            expect(showErrorAlert).toHaveBeenCalled();
        });
    });

    // Test cases for network error variations
    describe('Network error variations', () => {
        it('should handle Network Error message', () => {
            const networkError = {
                message: 'Network Error',
            };

            expect(() => handleError(networkError, apiMethod, null)).toThrow(networkError);
            expect(console.error).toHaveBeenCalledWith('Network Error:', apiMethod, 'Network Error');
        });

        it('should handle different network codes', () => {
            networkCodes.forEach((code) => {
                const networkError = {
                    code,
                    message: `Network error with code ${code}`,
                };

                expect(() => handleError(networkError, apiMethod, null)).toThrow(networkError);
                expect(console.error).toHaveBeenCalledWith(
                    'Network Error:',
                    apiMethod,
                    networkError.message,
                );
            });
        });
    });

    // Test handleInternalError function
    describe('handleInternalError', () => {
        beforeEach(() => {
            const mockTranslate = jest.fn((key) => `translated_${key}`);
            setTranslation(mockTranslate);
        });

        it('should handle internal errors with translation function', () => {
            const payload = { userId: 123 };
        });
    }); // Test error handling without translation function
    describe('Error handling without translation', () => {
        beforeEach(() => {
            setTranslation(null);
        });

        it('should skip alert when translation is not set', () => {
            const rateLimitError = {
                response: {
                    status: HttpStatusCode.RateLimitError,
                    statusText: 'Too Many Requests',
                },
            };
            const payload = { userId: 123 };

            expect(() => handleError(rateLimitError, apiMethod, payload)).toThrow();
            expect(console.error).toHaveBeenCalledWith('Translation function not set, skipping alert');
        });
    });

    // Test navigation functionality
    describe('Navigation functionality', () => {
        it('should navigate to logout when navigation ref is set', () => {
            const mockNavigate = jest.fn();
            const mockNavigationRef = { navigate: mockNavigate };
            const mockTranslate = jest.fn((key) => `translated_${key}`);

            setNavigationRef(mockNavigationRef);
            setTranslation(mockTranslate);

            const unauthorizedError = {
                response: {
                    status: HttpStatusCode.Unauthorized,
                    statusText: 'Unauthorized',
                },
            };
            const payload = { userId: 123 };

            expect(() => handleError(unauthorizedError, apiMethod, payload)).toThrow();
            expect(showErrorAlert).toHaveBeenCalled();
        });

        it('should not navigate when navigation ref is not set', () => {
            setNavigationRef(null);
            const mockTranslate = jest.fn((key) => `translated_${key}`);
            setTranslation(mockTranslate);

            const unauthorizedError = {
                response: {
                    status: HttpStatusCode.Unauthorized,
                    statusText: 'Unauthorized',
                },
            };
            const payload = { userId: 123 };

            expect(() => handleError(unauthorizedError, apiMethod, payload)).toThrow();
            expect(showErrorAlert).toHaveBeenCalled();
        });
    });

    // Additional test cases to improve coverage
    describe('Additional coverage tests', () => {
        beforeEach(() => {
            const mockTranslate = jest.fn((key) => `translated_${key}`);
            setTranslation(mockTranslate);
        });

        it('should handle announcements errors without payload', () => {
            const serverError = {
                response: {
                    status: HttpStatusCode.InternalServerError,
                    statusText: 'Internal Server Error',
                },
            };

            expect(() => handleError(serverError, 'announcements', null)).toThrow();
            // No alert should be called when payload is null
            expect(showErrorAlert).not.toHaveBeenCalled();
        });

        it('should handle service unavailable errors', () => {
            const serviceError = {
                response: {
                    status: HttpStatusCode.ServiceUnavailable,
                    statusText: 'Service Unavailable',
                },
            };
            const payload = { test: true };

            expect(() => handleError(serviceError, apiMethod, payload)).toThrow();
            expect(showErrorAlert).toHaveBeenCalled();
        });

        it('should handle errors with undefined payload', () => {
            const serverError = {
                response: {
                    status: HttpStatusCode.InternalServerError,
                    statusText: 'Internal Server Error',
                },
            };

            expect(() => handleError(serverError, apiMethod, undefined)).toThrow();
            // No alert should be called when payload is undefined
            expect(showErrorAlert).not.toHaveBeenCalled();
        });

        it('should test navigation function coverage', () => {
            const mockNavigate = jest.fn();
            const mockNavigationRef = { navigate: mockNavigate };
            setNavigationRef(mockNavigationRef);

            // This will trigger the navigation function inside handleAlert
            const unauthorizedError = {
                response: {
                    status: HttpStatusCode.Unauthorized,
                    statusText: 'Unauthorized',
                },
            };
            const payload = { userId: 123 };

            expect(() => handleError(unauthorizedError, apiMethod, payload)).toThrow();
            expect(showErrorAlert).toHaveBeenCalled();
        });
    });
});

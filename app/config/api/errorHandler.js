/**
 * @file Centralized error handler for API requests
 * @module utils/errorHandler
 * @description Handles all types of API errors (Axios, Apollo GraphQL, and unexpected errors)
 *              with proper logging and error state management.
 */

import { networkCodes, SCREENS } from '../../../app/shared/constants';
import { store } from '../../store';
import {
    fetchAnnouncementsRequest,
    fetchAllAnnouncementsRequest,
} from '../../store/reducers/announcementSlice';
import { fetchClockInStatusRequest } from '../../store/reducers/clockInStatusSlice';
import { fetchDiscountRequest } from '../../store/reducers/discountSlice';
import { fetchEmployeeScheduleRequest } from '../../store/reducers/employeeScheduleSlice';
import { fetchOtpVerifyRequest } from '../../store/reducers/otpVerifySlice';
import { fetchProfilePhotoRequest } from '../../store/reducers/profilePhotoSlice';
import { fetchProfileRequest } from '../../store/reducers/profileSlice';
import { showErrorAlert } from '../../utils/AppUtils';
import { fetchTncAgreementRequest } from '../../store/reducers/tncAgreementSlice';
import {
    PROFILE_PHOTO,
    xAPI_CLOCK_STATUS,
    xAPI_EMP_DISCOUNT,
    xAPI_EMP_SCHEDULE,
    xAPI_OTP_VERIFY,
    xAPI_PROFILE,
    xAPI_TNC_AGREEMENT,
} from '../endPoints';
import { HttpStatusCode } from '../errorCodes';

// Navigation reference - you'll need to set this up in your app's root navigator
let navigationRef = null;
let translate = null;

/**
 * Set navigation reference for error handler to use
 * Call this from your root navigator component
 */
export const setNavigationRef = (ref) => {
    navigationRef = ref;
};

/**
 * Set translation function for error messages
 * Call this from your i18n setup
 */
export const setTranslation = (translations) => {
    translate = translations;
};

/**
 * Navigate to login screen
 */
const navigateToLogout = () => {
    if (navigationRef) {
        navigationRef.navigate(SCREENS.AUTH_LOGOUT); // Adjust route name as needed
    }
};

/**
 * Centralized Error Handler for API requests
 * @function handleError
 * @param {Object|AxiosError|ApolloError|Error} error - The error object to handle and can be Axios, Apollo or generic error
 * @param {string} [apiMethod=""] - Name of the API method for contextual logging
 * @param {Object} payload - Payload to pass to the action for retrying the API call
 *
 * @throws {Error} Always rethrows the original error after processing
 *
 * @example
 * try {
 *   await apiCall();
 * } catch (error) {
 *   handleError(error, 'fetchUserData');
 * }
 *
 * @behavior
 * 1. Handles network-level errors (offline, timeout, DNS failures)
 * 2. Processes HTTP status codes (401, 429, 500 etc.)
 * 3. Manages auth errors via Redux store
 * 4. Logs detailed error information
 * 5. Always rethrows for caller to handle
 */
export const handleError = (error, apiMethod = '', payload) => {
    const status = error?.response?.status;

    // Network-level Axios errors
    if (
        !error.response &&
        (networkCodes.includes(error?.code) || error.message === 'Network Error')
    ) {
        console.error('Network Error:', apiMethod, error.message);
        throw error;
    }

    /**
     * @switch HTTP Status Code Handling
     * @case 429 - Rate limiting
     * @case 401 - Authentication failure
     * @case 500/502/503 - Server errors
     */
    switch (status) {
        case HttpStatusCode.RateLimitError:
            if (payload !== null) handleAlert(apiMethod, payload, status);
            break;
        case HttpStatusCode.Unauthorized:
            // store.dispatch(setAuthError(true));
            handleAlert(apiMethod, payload, status, true);
            break;
        case HttpStatusCode.Forbidden:
        case HttpStatusCode.InternalServerError:
        case HttpStatusCode.BadRequest:
        case HttpStatusCode.BadGateway:
        case HttpStatusCode.GatewayTimeout:
        case HttpStatusCode.ServiceUnavailable:
        case HttpStatusCode.NotFound:
            if (apiMethod == 'announcements') {
                store.dispatch(setAnnouncementServerError(true));
            }
            if (payload !== null && payload !== undefined) handleAlert(apiMethod, payload, status, apiMethod === xAPI_PROFILE);
            break;
    }

    // Detailed error logging
    if (error?.response) {
        /**
         * @description Axios response error logging
         * Logs status code, status text and response data
         */
        console.error('Axios Error:', apiMethod, status, error.response.statusText);
        console.error('Error Details:', apiMethod, error.response.data);
    } else if (error?.networkError) {
        /** @description Apollo Client network error logging */
        console.error('Apollo Network Error:', apiMethod, error.networkError);
    } else if (error?.graphQLErrors) {
        /** @description GraphQL error logging (per error) */
        error.graphQLErrors.forEach((err) => console.error('GraphQL Error:', apiMethod, err.message));
    } else {
        /** @description Fallback for unexpected error types */
        console.error('Unexpected Error:', apiMethod, error?.message ?? error);
    }

    throw error;
};

/** * Dispatch API actions based on method type
 * @function dispatchAPI
 * @param {string} apiMethod - The API method name to determine action type
 * @param {Object} payload - The payload to pass to the action
 * @description
 * Dispatches specific Redux actions based on the API method type.
 * - For `xAPI_PROFILE`, dispatches `fetchProfileRequest`
 * - For `PROFILE_PHOTO`, dispatches `fetchProfilePhotoRequest`
 * - For `fetchAnnouncements`, dispatches `fetchAnnouncementsRequest`
 * @example
 * dispatchAPI('xAPI_PROFILE', { userId: 123 });
 * @example
 * dispatchAPI('PROFILE_PHOTO', { userId: 123 });
 * @example
 * dispatchAPI('fetchAnnouncements', { page: 1 });
 */
const dispatchAPI = (apiMethod, payload) => {
    switch (apiMethod) {
        case xAPI_PROFILE:
            // Dispatch user data fetch action
            store.dispatch(fetchProfileRequest(payload));
            break;
        case PROFILE_PHOTO:
            // Dispatch announcements fetch action
            store.dispatch(fetchProfilePhotoRequest(payload));
            break;
        case 'fetchAnnouncements':
            // Dispatch announcements fetch action
            store.dispatch(fetchAnnouncementsRequest(payload));
            break;
        case 'fetchAllAnnouncements':
            // Dispatch announcements fetch action
            store.dispatch(fetchAllAnnouncementsRequest(payload));
            break;
        case xAPI_OTP_VERIFY:
            // Dispatch OTP verification action
            store.dispatch(fetchOtpVerifyRequest(payload));
            break;
        case xAPI_EMP_DISCOUNT:
            // Dispatch employee discount action
            store.dispatch(fetchDiscountRequest(payload));
            break;
        case xAPI_CLOCK_STATUS:
            // Dispatch clock in status action
            store.dispatch(fetchClockInStatusRequest(payload));
            break;
        case xAPI_EMP_SCHEDULE:
            // Dispatch employee schedule action
            store.dispatch(fetchEmployeeScheduleRequest(payload));
            break;
        case xAPI_TNC_AGREEMENT:
            // Dispatch tncAgreement action
            store.dispatch(fetchTncAgreementRequest(payload));
            break;
    }
};

/** * Show error alert with contextual information
 * @function handleAlert
 * @param {string} apiMethod - The API method name for context
 * @param {Object} payload - The payload to pass to the action
 * @param {number} statusCode - HTTP status code for the error
 * @param {boolean} [isLogout=false] - Whether this is a logout action
 *
 * @description
 * Displays an error alert with localized messages and actions.
 * If `isLogout` is true, it navigates to the logout screen.
 * Otherwise, it retries the API call with the provided payload.
 */
const handleAlert = (apiMethod, payload, statusCode, isLogout = false) => {
    // Guard against null translate function in tests
    if (!translate) {
        console.error('Translation function not set, skipping alert');
        return;
    }

    if (apiMethod === xAPI_PROFILE && statusCode === HttpStatusCode.Forbidden) {
        statusCode = HttpStatusCode.Forbidden + '_' + xAPI_PROFILE;
    }

    const title = translate(`serverErrors.${statusCode}.title`);
    const description = translate(`serverErrors.${statusCode}.message`);
    const leftCTA = isLogout ? undefined : translate(`serverErrors.${statusCode}.leftCTA`);
    const rightCTA = translate(`serverErrors.${statusCode}.rightCTA`);

    showErrorAlert(
        translate,
        title,
        description,
        () => {
            if (isLogout) navigateToLogout();
            else dispatchAPI(apiMethod, payload);
        },
        null,
        leftCTA,
        rightCTA,
    );
};

/**
 * Handle internal api errors with specific API method context
 * @function handleInternalError
 * @param {string} apiMethod - The API method name for context
 * @param {Object} payload - The payload to pass to the action
 * @param {boolean} isGeneric - Whether the error is generic
 * @param {string} code - The error code
 *
 * @description
 * Displays an error alert with localized messages and actions.
 * Otherwise, it retries the API call with the provided payload.
 */
export const handleInternalError = (apiMethod, payload, isGeneric = false, code) => {
    /**
     * Early return if payload is null or undefined
     * @description Prevents showing error alerts when there's no valid payload for retry
     * @since 1.0.0
     */
    if (payload === null || payload === undefined) {
        return;
    }

    const title = isGeneric
        ? translate(`genericError.title`)
        : translate(`internalErrors.${apiMethod}.title`);
    const description = isGeneric
        ? translate(`genericError.message`) + (code ? ' (Code: ' + code + ')' : '')
        : translate(`internalErrors.${apiMethod}.message`);
    const leftCTA = isGeneric
        ? translate(`genericError.leftCTA`)
        : translate(`internalErrors.${apiMethod}.leftCTA`);
    const rightCTA = isGeneric
        ? translate(`genericError.rightCTA`)
        : translate(`internalErrors.${apiMethod}.rightCTA`);
    // Show a generic error alert
    showErrorAlert(
        translate,
        title,
        description,
        () => {
            dispatchAPI(apiMethod, payload);
        },
        null,
        leftCTA,
        rightCTA,
    );
};
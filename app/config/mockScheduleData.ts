import type {
  ScheduleResponse,
  Schedule,
  WorkedHour,
  Balance,
  TimeCodeSummary,
  HourTypeSummary,
  EmployeeScheduleResponse,
} from './responseTypes';

// Error response type for when no shifts are available
export interface ErrorResponse {
  scheduleResponse: {
    errors: Array<{
      code: string;
      message: string;
      statusCode: number;
    }>;
  };
}

// Union type for schedule response that can include errors
export type ScheduleResponseOrError = ScheduleResponse | ErrorResponse;

// Helper function to check if response is successful
export const isScheduleResponse = (
  response: ScheduleResponseOrError,
): response is ScheduleResponse => {
  return 'schedule' in response;
};

// Helper function to check if response is an error
export const isErrorResponse = (response: ScheduleResponseOrError): response is ErrorResponse => {
  return 'scheduleResponse' in response && 'errors' in (response as ErrorResponse).scheduleResponse;
};

/**
 * Generates dynamic mock data for the current week's schedule only
 * @param startDate - Optional start date for the schedule in YYYY-MM-DD format (defaults to current week Sunday)
 * @param endDate - Optional end date for the schedule in YYYY-MM-DD format (defaults to current week Saturday)
 * @returns ScheduleResponse with schedule data for current week, error response for past/future weeks
 */
export const generateMockScheduleResponse = (
  startDate?: string,
  endDate?: string,
): ScheduleResponseOrError => {
  let weekDates: Date[];
  const currentDate = new Date();
  const currentWeekDates = getCurrentWeekDates(currentDate);

  const noShiftsError = {
    scheduleResponse: {
      errors: [
        {
          code: 'ASP-0131',
          message:
            "204 No Content from GET https://safewaydv-wfm.cloud.infor.com/services/api/public/v1/common/employees/20737452/employee-schedule-details, but response failed with cause: org.springframework.web.reactive.function.UnsupportedMediaTypeException: Content type 'text/html' not supported for bodyType=com.albertsons.associate.model.schedule.ScheduleDataResponse",
          statusCode: 204,
        },
      ],
    },
  };

  if (startDate && endDate) {
    const startDateObj = new Date(startDate);
    const endDateObj = new Date(endDate);
    weekDates = getDateRange(startDateObj, endDateObj);
  } else if (startDate) {
    // If only start date is provided, generate a week from that date
    const startDateObj = new Date(startDate);
    weekDates = getCurrentWeekDates(startDateObj);
  } else {
    // Default to current week
    weekDates = currentWeekDates;
  }

  // Check if the requested week is the current week
  const isCurrentWeek = isDateRangeCurrentWeek(weekDates, currentWeekDates);

  if (!isCurrentWeek) {
    // Return error response for past or future weeks
    return noShiftsError;
  }

  return {
    schedule: generateMockSchedule(weekDates),
    workedHours: generateMockWorkedHours(weekDates),
    balances: generateMockBalances(),
  };
};

/**
 * Gets the dates for the current week (Sunday to Saturday)
 */
const getCurrentWeekDates = (date: Date): Date[] => {
  const week: Date[] = [];
  const startOfWeek = new Date(date);

  // Get Sunday of current week
  const dayOfWeek = startOfWeek.getDay();
  const diff = startOfWeek.getDate() - dayOfWeek;
  startOfWeek.setDate(diff);

  // Generate 7 days from Sunday
  for (let i = 0; i < 7; i++) {
    const day = new Date(startOfWeek);
    day.setDate(startOfWeek.getDate() + i);
    week.push(day);
  }

  return week;
};

/**
 * Gets all dates between start and end date (inclusive)
 */
const getDateRange = (startDate: Date, endDate: Date): Date[] => {
  const dates: Date[] = [];
  const currentDate = new Date(startDate);

  while (currentDate <= endDate) {
    dates.push(new Date(currentDate));
    currentDate.setDate(currentDate.getDate() + 1);
  }

  return dates;
};

/**
 * Checks if the given date range represents the current week
 */
export const isDateRangeCurrentWeek = (weekDates: Date[], currentWeekDates: Date[]): boolean => {
  if (weekDates.length === 0 || currentWeekDates.length === 0) {
    return false;
  }

  // Compare the first and last dates to determine if it's the current week
  const weekStart = formatDate(weekDates[0]);
  const weekEnd = formatDate(weekDates[weekDates.length - 1]);
  const currentWeekStart = formatDate(currentWeekDates[0]);
  const currentWeekEnd = formatDate(currentWeekDates[currentWeekDates.length - 1]);

  return weekStart === currentWeekStart && weekEnd === currentWeekEnd;
};

/**
 * Formats date to YYYY-MM-DD string
 */
const formatDate = (date: Date): string => {
  return date.toISOString().split('T')[0];
};

/**
 * Formats time to YYYY-MM-DDTHH:MM format
 */
const formatTime = (date: Date, hour: number, minute = 0): string => {
  const dateStr = formatDate(date);
  const timeStr = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
  return `${dateStr}T${timeStr}`;
};

/**
 * Generates mock schedule data for the current week
 */
const generateMockSchedule = (weekDates: Date[]): Schedule[] => {
  const schedules: Schedule[] = [];
  let scheduleId = 1001;

  weekDates.forEach((date, index) => {
    const dayOfWeek = date.getDay();

    // Create shorter shifts for Sundays, skip if desired
    // Sunday = 0, Monday = 1, Tuesday = 2, etc.

    // Different shift patterns for different days
    const shiftPatterns = [
      { start: 10, end: 16, dept: 'Customer Service', job: 'Weekend Associate' }, // Sunday
      { start: 9, end: 17, dept: 'Customer Service', job: 'Associate' }, // Monday
      { start: 8, end: 16, dept: 'Sales Floor', job: 'Sales Associate' }, // Tuesday
      { start: 10, end: 18, dept: 'Customer Service', job: 'Senior Associate' }, // Wednesday
      { start: 9, end: 17, dept: 'Bakery', job: 'Baker Assistant' }, // Thursday
      { start: 12, end: 20, dept: 'Deli', job: 'Deli Associate' }, // Friday
      { start: 8, end: 14, dept: 'Produce', job: 'Produce Associate' }, // Saturday
    ];

    const pattern = shiftPatterns[dayOfWeek];
    const minutes = (pattern.end - pattern.start) * 60;

    // Determine status based on date comparison
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const scheduleDate = new Date(date);
    scheduleDate.setHours(0, 0, 0, 0);

    const status = scheduleDate < today ? 'Completed' : 'Scheduled';
    const comments = scheduleDate < today ? 'Shift completed' : null;

    schedules.push({
      id: scheduleId,
      scheduleId: scheduleId,
      startTime: formatTime(date, pattern.start),
      endTime: formatTime(date, pattern.end),
      jobStartTime: null,
      jobEndTime: null,
      workDate: formatDate(date),
      minutes: minutes,
      timeCodeName: 'REG',
      hourTypeName: 'Regular',
      activityName: null,
      departmentName: pattern.dept,
      jobName: pattern.job,
      skillName: null,
      teamName: `Team ${String.fromCharCode(65 + (index % 3))}`, // Team A, B, or C
      projectName: 'Store Operations',
      docketName: `Docket-${formatDate(date)}`,
      rate: 15.5 + Math.random() * 2, // Random rate between 15.50 and 17.50
      quantity: null,
      messages: null,
      comments: comments,
      overridden: false,
      status: status,
      type: 'Shift',
      edited: false,
      shiftSummaryId: null,
    });

    scheduleId++;
  });

  return schedules;
};

/**
 * Generates mock worked hours data for the current week
 */
const generateMockWorkedHours = (weekDates: Date[]): WorkedHour[] => {
  const workedHours: WorkedHour[] = [];
  let workId = 2001;

  // Only generate worked hours for past dates (completed shifts)
  const today = new Date();
  today.setHours(0, 0, 0, 0); // Reset time to start of day for accurate comparison
  const pastDates = weekDates.filter((date) => {
    const compareDate = new Date(date);
    compareDate.setHours(0, 0, 0, 0);
    return compareDate < today;
  });

  pastDates.forEach((date) => {
    const dayOfWeek = date.getDay();

    // Different worked hours patterns to match schedule patterns
    const workPatterns = [
      { start: 10, end: 16, name: 'Weekend Shift' }, // Sunday
      { start: 9, end: 17, name: 'Day Shift' }, // Monday
      { start: 8, end: 16, name: 'Morning Shift' }, // Tuesday
      { start: 10, end: 18, name: 'Afternoon Shift' }, // Wednesday
      { start: 9, end: 17, name: 'Day Shift' }, // Thursday
      { start: 12, end: 20, name: 'Evening Shift' }, // Friday
      { start: 8, end: 14, name: 'Early Shift' }, // Saturday
    ];

    const workPattern = workPatterns[dayOfWeek];
    const workMinutes = (workPattern.end - workPattern.start) * 60;

    const timeCodeSummary: TimeCodeSummary[] = [
      { name: 'REG', time: workMinutes },
      { name: 'BRK', time: 30 }, // 30 minutes break
    ];

    const hourTypeSummary: HourTypeSummary[] = [
      { name: 'Regular', time: workMinutes },
      { name: 'Break', time: 30 },
    ];

    workedHours.push({
      id: workId,
      workDate: formatDate(date),
      startTime: formatTime(date, workPattern.start),
      endTime: formatTime(date, workPattern.end),
      employeeName: 'John Doe',
      shiftName: workPattern.name,
      payGroupName: 'Associates',
      calcGroupName: 'Regular Associates',
      authorized: true,
      authorizedByUsername: 'manager01',
      authorizedDate: formatDate(new Date(date.getTime() + 24 * 60 * 60 * 1000)), // Next day
      errorStatus: false,
      flagBreak: false,
      flagRecall: false,
      description: null,
      comments: 'Shift completed successfully',
      errorMessage: null,
      timeCodeSummary,
      hourTypeSummary,
      messages: 'Completed',
      inCode: 'IN',
      outCode: 'OUT',
      fullDayCode: '',
      fullDayMinutes: workMinutes,
      submitted: true,
      attestationStatus: 'Approved',
      clocksAuthorized: true,
    });

    workId++;
  });

  return workedHours;
};

/**
 * Generates mock balance data
 */
const generateMockBalances = (): Balance[] => {
  const currentDate = new Date();
  const balanceDate = formatDate(currentDate);

  return [
    {
      balanceId: 1,
      employeeName: 'John Doe',
      balanceDate,
      balanceName: 'Vacation Time',
      balanceTypeName: 'PTO',
      currentValue: 80.0,
      usedValue: 16.0,
      accruedValue: 96.0,
      futureApprovedValue: 8.0,
    },
    {
      balanceId: 2,
      employeeName: 'John Doe',
      balanceDate,
      balanceName: 'Sick Time',
      balanceTypeName: 'Sick Leave',
      currentValue: 40.0,
      usedValue: 8.0,
      accruedValue: 48.0,
      futureApprovedValue: 0.0,
    },
    {
      balanceId: 3,
      employeeName: 'John Doe',
      balanceDate,
      balanceName: 'Personal Time',
      balanceTypeName: 'Personal Leave',
      currentValue: 24.0,
      usedValue: 0.0,
      accruedValue: 24.0,
      futureApprovedValue: 0.0,
    },
    {
      balanceId: 4,
      employeeName: 'John Doe',
      balanceDate,
      balanceName: 'Floating Holiday',
      balanceTypeName: 'Holiday',
      currentValue: 16.0,
      usedValue: 0.0,
      accruedValue: 16.0,
      futureApprovedValue: 0.0,
    },
  ];
};

/**
 * Generates a complete EmployeeScheduleResponse with current week data
 * @param startDate - Optional start date for the schedule in YYYY-MM-DD format (defaults to current week Sunday)
 * @param endDate - Optional end date for the schedule in YYYY-MM-DD format (defaults to current week Saturday)
 */
export const generateMockEmployeeScheduleResponse = (
  startDate?: string,
  endDate?: string,
): EmployeeScheduleResponse | ErrorResponse => {
  const response = generateMockScheduleResponse(startDate, endDate);
  
  // If it's an error response, return it directly
  if ('scheduleResponse' in response && 'errors' in response.scheduleResponse) {
    return response as ErrorResponse;
  }
  
  // Otherwise, wrap the ScheduleResponse in EmployeeScheduleResponse
  return {
    scheduleResponse: response as ScheduleResponse,
  };
};

// Export individual generators for testing purposes
export {
  getCurrentWeekDates,
  getDateRange,
  formatDate,
  formatTime,
  generateMockSchedule,
  generateMockWorkedHours,
  generateMockBalances,
};

export const STORE_LOCATOR = "api/services/locator/store/instore";
export const REWARDS_ENDPOINT = "/xapi/ocrp/rewards/scorecard";

export const REWARD_ACCESS_TOKEN = 'rewardsAccessToken'
export const PROFILE_PHOTO = 'ProfilePhoto';
export const FEATURE_FLAG_API = 'featureFlagApi';
export const GENERATE_ACCESS_TOKEN = 'generateAccessToken';
export const GENERATE_OBO_ACCESS_TOKEN = 'generate_obo_access_token';

//xAPI ENDPOINTS
export const xAPI_PROFILE = "profile"
export const ANNOUNCEMENT_PATH = "announcements"
export const ANNOUNCEMENT_VIEWALL_PATH = "/announcements"
export const ANNOUNCEMENT_URL_PATH = "announcementUrls"
export const xAPI_PROFILE_PHOTO = "photo"
export const xAPI_FEATURE_FLAG = "/ESFF/api/v1/evalattrs"
export const xAPI_OTP_VERIFY = "otpVerify";
export const xAPI_EMP_DISCOUNT = "empdiscount";
export const xAPI_EMP_SCHEDULE = "empSchedule";
export const xAPI_CLOCK_STATUS = "clockStatus";
export const xAPI_TNC_AGREEMENT = "tncAgreement";


//timeout duration in seconds, which api has need to call or use cache response
export const DURATION = {
  [xAPI_PROFILE]: 86400,  // 24 hours in seconds
  [ANNOUNCEMENT_PATH]: 1800,  // 30 minutes in seconds
  [xAPI_PROFILE_PHOTO]: 86400,  // 24 hours in seconds
  [ANNOUNCEMENT_VIEWALL_PATH]: 1800,  // 30 minutes in seconds
};

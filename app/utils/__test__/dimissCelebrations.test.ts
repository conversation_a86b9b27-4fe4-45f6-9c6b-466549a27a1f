import AsyncStorage from '@react-native-async-storage/async-storage';
import { dismissCelebration, getDismissedCelebrations } from '../dismissCelebrations';

jest.mock('@react-native-async-storage/async-storage', () =>
  require('@react-native-async-storage/async-storage/jest/async-storage-mock')
);

describe('dismissedCelebrations storage utils', () => {
  const STORAGE_KEY = 'dismissedCelebrations';
  const currentYear = new Date().getFullYear();

  beforeEach(() => {
    AsyncStorage.clear();
    jest.clearAllMocks();
  });

  it('returns an empty array if nothing is stored', async () => {
    const result = await getDismissedCelebrations();
    expect(result).toEqual([]);
  });

  it('returns only celebrations from the current year', async () => {
    const mockData = JSON.stringify([
      { id: 'a', year: currentYear - 1 },
      { id: 'b', year: currentYear },
      { id: 'c', year: currentYear },
    ]);
    await AsyncStorage.setItem(STORAGE_KEY, mockData);

    const result = await getDismissedCelebrations();
    expect(result).toEqual(['b', 'c']);
  });

  it('handles invalid JSON gracefully', async () => {
    await AsyncStorage.setItem(STORAGE_KEY, '{ invalid json ]');

    const result = await getDismissedCelebrations();
    expect(result).toEqual([]);
  });

  it('adds a new celebration dismissal for the current year', async () => {
    await dismissCelebration('celebration-1');

    const stored = await AsyncStorage.getItem(STORAGE_KEY);
    const parsed = JSON.parse(stored!);

    expect(parsed).toEqual([{ id: 'celebration-1', year: currentYear }]);
  });

  it('does not duplicate existing celebration ids for the same year', async () => {
    const initial = JSON.stringify([{ id: 'dup-id', year: currentYear }]);
    await AsyncStorage.setItem(STORAGE_KEY, initial);

    await dismissCelebration('dup-id');

    const stored = await AsyncStorage.getItem(STORAGE_KEY);
    const parsed = JSON.parse(stored!);

    expect(parsed).toEqual([{ id: 'dup-id', year: currentYear }]);
  });

  it('appends a new celebration without removing old ones', async () => {
    const initial = JSON.stringify([{ id: 'old-id', year: currentYear }]);
    await AsyncStorage.setItem(STORAGE_KEY, initial);

    await dismissCelebration('new-id');

    const stored = await AsyncStorage.getItem(STORAGE_KEY);
    const parsed = JSON.parse(stored!);

    expect(parsed).toEqual([
      { id: 'old-id', year: currentYear },
      { id: 'new-id', year: currentYear },
    ]);
  });
});

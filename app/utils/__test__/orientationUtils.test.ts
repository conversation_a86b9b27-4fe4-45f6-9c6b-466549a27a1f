import { setOrientationBasedOnDevice } from '../orientationUtils';
import Orientation from 'react-native-orientation-locker';

// Mock the orientation functions
jest.mock('react-native-orientation-locker', () => ({
    lockToPortrait: jest.fn(),
    unlockAllOrientations: jest.fn(),
}));

describe('setOrientationBasedOnDevice', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('calls unlockAllOrientations when isTablet is true', () => {
        setOrientationBasedOnDevice(true);

        expect(Orientation.unlockAllOrientations).toHaveBeenCalled();
        expect(Orientation.lockToPortrait).not.toHaveBeenCalled();
    });

    it('calls lockToPortrait when isTablet is false', () => {
        setOrientationBasedOnDevice(false);

        expect(Orientation.lockToPortrait).toHaveBeenCalled();
        expect(Orientation.unlockAllOrientations).not.toHaveBeenCalled();
    });
});

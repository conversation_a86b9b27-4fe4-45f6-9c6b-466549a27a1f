import { STICKY_DAYS } from "../../shared/constants";
import { CelebrationType, checkAllCelebrations } from "../checkAllCelebrations";
// Mocked date utilities
jest.mock('../TimeUtils', () => ({
  toDateOnly: (dateStr: string | null | undefined) => {
    if (!dateStr) return null;
    const [year, month, day] = dateStr.split('-').map(Number);
    return new Date(Date.UTC(year, month - 1, day));
  },
  isWithinRange: (targetDate: Date, today: Date, rangeInDays: number) => {
    const diff = Math.abs(targetDate.getTime() - today.getTime());
    const daysDiff = diff / (1000 * 60 * 60 * 24);
    return daysDiff <= rangeInDays;
  },
}));

describe('checkAllCelebrations', () => {
  const now = new Date(Date.now());
  const thisYear = now.getUTCFullYear();
  const today = new Date(Date.UTC(thisYear, now.getUTCMonth(), now.getUTCDate()));
  const pad = (n: number) => String(n).padStart(2, '0');
  const todayStr = `${thisYear}-${pad(today.getUTCMonth() + 1)}-${pad(today.getUTCDate())}`;

  it('should celebrate birthday if today is within birthday sticky range', () => {
    const result = checkAllCelebrations(todayStr, null, STICKY_DAYS.BIRTHDAY, STICKY_DAYS.WORK_ANNIVERSARY);
    expect(result.birthday.shouldCelebrate).toBe(true);
    expect(result.work.shouldCelebrate).toBe(false);
  });

  it('should not celebrate birthday if birthDate is null', () => {
    const result = checkAllCelebrations(null, null, STICKY_DAYS.BIRTHDAY, STICKY_DAYS.WORK_ANNIVERSARY);
    expect(result.birthday.shouldCelebrate).toBe(false);
    expect(result.work.shouldCelebrate).toBe(false);
  });

  it('should not celebrate if both birthDate and joinDate are empty strings', () => {
    const result = checkAllCelebrations("", "", STICKY_DAYS.BIRTHDAY, STICKY_DAYS.WORK_ANNIVERSARY);
    expect(result.birthday.shouldCelebrate).toBe(false);
    expect(result.work.shouldCelebrate).toBe(false);
  });

  it('should not celebrate if function passes empty arguments', () => {
    const result = checkAllCelebrations();
    expect(result.birthday.shouldCelebrate).toBe(false);
    expect(result.work.shouldCelebrate).toBe(false);
  });

  it('should celebrate 1 year work anniversary as a milestone', () => {
    const oneYearAgo = new Date(Date.UTC(thisYear - 1, today.getUTCMonth(), today.getUTCDate()));
    const oneYearAgoStr = `${oneYearAgo.getUTCFullYear()}-${pad(oneYearAgo.getUTCMonth() + 1)}-${pad(oneYearAgo.getUTCDate())}`;

    const result = checkAllCelebrations(null, oneYearAgoStr, STICKY_DAYS.BIRTHDAY, STICKY_DAYS.WORK_ANNIVERSARY);
    expect(result.work.shouldCelebrate).toBe(true);
    expect(result.work.type).toBe(CelebrationType.Milestone);
    expect(result.work.yearsWorked).toBe(1);
  });

  it('should celebrate 5 year milestone anniversary', () => {
    const fiveYearsAgo = new Date(Date.UTC(thisYear - 5, today.getUTCMonth(), today.getUTCDate()));
    const fiveYearsAgoStr = `${fiveYearsAgo.getUTCFullYear()}-${pad(fiveYearsAgo.getUTCMonth() + 1)}-${pad(fiveYearsAgo.getUTCDate())}`;

    const result = checkAllCelebrations(null, fiveYearsAgoStr, STICKY_DAYS.BIRTHDAY, STICKY_DAYS.WORK_ANNIVERSARY);
    expect(result.work.shouldCelebrate).toBe(true);
    expect(result.work.type).toBe(CelebrationType.Milestone);
    expect(result.work.yearsWorked).toBe(5);
  });

  it('should celebrate non-milestone workiversary', () => {
    const twoYearsAgo = new Date(Date.UTC(thisYear - 2, today.getUTCMonth(), today.getUTCDate()));
    const twoYearsAgoStr = `${twoYearsAgo.getUTCFullYear()}-${pad(twoYearsAgo.getUTCMonth() + 1)}-${pad(twoYearsAgo.getUTCDate())}`;

    const result = checkAllCelebrations(null, twoYearsAgoStr, STICKY_DAYS.BIRTHDAY, STICKY_DAYS.WORK_ANNIVERSARY);
    expect(result.work.shouldCelebrate).toBe(true);
    expect(result.work.type).toBe(CelebrationType.Workiversary);
    expect(result.work.yearsWorked).toBe(2);
  });

  it('should not celebrate if join date is less than 1 year ago', () => {
    const monthsAgo = new Date(Date.UTC(thisYear, today.getUTCMonth() - 3, today.getUTCDate()));
    const recentJoinDateStr = `${monthsAgo.getUTCFullYear()}-${pad(monthsAgo.getUTCMonth() + 1)}-${pad(monthsAgo.getUTCDate())}`;

    const result = checkAllCelebrations(null, recentJoinDateStr, STICKY_DAYS.BIRTHDAY, STICKY_DAYS.WORK_ANNIVERSARY);
    expect(result.work.shouldCelebrate).toBe(false);
  });

  it('should return false for both if inputs are undefined or missing', () => {
    const result = checkAllCelebrations(undefined, undefined, STICKY_DAYS.BIRTHDAY, STICKY_DAYS.WORK_ANNIVERSARY);
    expect(result).toEqual({
      birthday: { shouldCelebrate: false },
      work: { shouldCelebrate: false },
    });
  });

  it('should handle invalid date strings gracefully', () => {
    const result = checkAllCelebrations('invalid-date', 'invalid-date', STICKY_DAYS.BIRTHDAY, STICKY_DAYS.WORK_ANNIVERSARY);
    expect(result).toEqual({
      birthday: { shouldCelebrate: false },
      work: { shouldCelebrate: false },
    });
  });

  it('should handle invalid numeric date strings gracefully', () => {
    const result = checkAllCelebrations('83483483773', '8935749835783', STICKY_DAYS.BIRTHDAY, STICKY_DAYS.WORK_ANNIVERSARY);
    expect(result).toEqual({
      birthday: { shouldCelebrate: false },
      work: { shouldCelebrate: false },
    });
  });
});

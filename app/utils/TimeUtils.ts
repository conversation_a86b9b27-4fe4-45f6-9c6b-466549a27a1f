import moment from "moment";
import { HOME_CONSTANTS } from "../features/home/<USER>";
import { WorkConstants , HOURS_REMAIN_TO_CLOCK_IN, SHIFT_ENDING_DURATION, HOURS_AROUND_SHIFT } from "../shared/constants";
import dayjs from "dayjs"

/**
 * Calculates the time difference between two given times and returns the difference
 * in a human-readable format like "X hours ago" or "X minutes ago".
 * 
 * @param {Date | string} startTime - The starting time (either a `Date` object or a time string).
 * @param {Date | string} endTime - The ending time (either a `Date` object or a time string).
 * 
 * @returns {string} - A human-readable string representing the time difference (e.g., "2 hours" or "30 minutes").
 * 
 * @example
 * const timeDiff = calculateTimeDiff("09:00 AM", "12:30 PM"); 
 * console.log(timeDiff); // "3 hours"
 * 
 * 
 * @example
 * const timeDiff = calculateTimeDiff("09:00 AM", "09:30 AM"); 
 * console.log(timeDiff); // "30 minutes"
 */
export function calculateTimeDiff(
  startTime: Date | string,
  endTime: Date | string
): string {
  // Convert string inputs to Date objects if necessary
  if (typeof startTime == "string") {
    startTime = convertStringToDate(startTime);
  }
  if (typeof endTime == "string") {
    endTime = convertStringToDate(endTime);
  }

  // Calculate time difference in milliseconds
  const differenceMs = endTime.getTime() - startTime.getTime();

  // Calculate difference in minutes and hours
  const minutes = Math.floor(differenceMs / (1000 * 60));
  const hours = Math.floor(minutes / 60);

  let timeDifference;
  if (hours > 0) {
    timeDifference = `${hours} hour${hours > 1 ? "s" : ""}`;
  } else {
    timeDifference = `${minutes} minute${minutes >= 1 ? "s" : ""}`;
  }

  return timeDifference;
}

/**
 * Calculates the time difference between two given times and returns the difference in minutes.
 * 
 * @param {Date | string} startTime - The starting time (either a `Date` object or a time string).
 * @param {Date | string} endTime - The ending time (either a `Date` object or a time string).
 * 
 * @returns {number} - The difference between the two times in minutes.
 * 
 * @example
 * const durationInMinutes = calculateTimeDiffinMinutes("09:00 AM", "12:30 PM"); 
 * console.log(durationInMinutes); // 210
 */
export function calculateTimeDiffinMinutes(
  startTime: Date | string,
  endTime: Date | string
): number {
  // Convert string inputs to Date objects if necessary
  if (typeof startTime == "string") {
    startTime = convertStringToDate(startTime);
  }
  if (typeof endTime == "string") {
    endTime = convertStringToDate(endTime);
  }

  // Calculate and return the difference in minutes
  const differenceMs = endTime.getTime() - startTime.getTime();
  const minutes = Math.floor(differenceMs / (1000 * 60));
  return minutes;
}

/**
 * Gets the current time formatted according to the given format.
 * 
 * @param {string | null | undefined} format - Optional format for the current time.
 * If no format is provided, defaults to "hh:mm A" (e.g., "09:00 AM").
 * 
 * @returns {string} - The current time formatted as a string.
 * 
 * @example
 * const currentTime = getCurrentTime(); 
 * console.log(currentTime); // "09:00 AM"
 * 
 * @example
 * const currentTime = getCurrentTime("HH:mm"); 
 * console.log(currentTime); // "09:00"
 */
export function getCurrentTime(format?: string | null | undefined): string {
  const today = new Date();
  return moment(today).format(format ?? "hh:mm A"); // Default to "hh:mm A" if no format is provided
}

/**
 * Converts a `Date` object into a string based on the given format.
 * 
 * @param {Date} date - The date to be converted.
 * @param {string} format - The format string to use for conversion.
 * 
 * @returns {string} - The formatted date as a string.
 * 
 * @example
 * const formattedDate = getDateToString(new Date(), "YYYY-MM-DD"); 
 * console.log(formattedDate); // "2025-01-15"
 */
export function getDateToString(date: Date, format: string): string {
  return moment(date).format(format);
}

/**
 * Converts a time string (e.g., "09:00 AM") to a `Date` object.
 * 
 * @param {string} timeString - The time string to convert (in "hh:mm a" format).
 * 
 * @returns {Date} - The corresponding `Date` object.
 * 
 * @example
 * const date = convertStringToDate("09:00 AM"); 
 * console.log(date); // Date object for the given time
 */
export const convertStringToDate = (timeString: string): Date => {
  // Use moment.js to parse the time string and convert it to an ISO string format
  timeString = moment(timeString, "hh:mm a").format("YYYY-MM-DDTHH:mm:ss.SSSZ");
  return new Date(timeString);
};

/**
 * Converts a duration (in minutes) into a human-readable string with specified language for hours and minutes.
 * 
 * @param {number} durationInMinutes - The total duration in minutes to be converted.
 * @param {string} hoursLanguage - The word to represent hours in the output string (e.g., "hour" or "hours").
 * @param {string} minutesLanguage - The word to represent minutes in the output string (e.g., "minute" or "minutes").
 * 
 * @returns {string} - A string representing the duration in a human-readable format (e.g., "2 hours 30 minutes" or "45 minutes").
 * 
 * @example
 * const timeDiff = durationInWords(150, "hour", "minute"); 
 * console.log(timeDiff); // "2 hours 30 minutes"
 * 
 * @example
 * const timeDiff = durationInWords(45, "hour", "minute"); 
 * console.log(timeDiff); // "45 minutes"
 * 
 * @example
 * const timeDiff = durationInWords(120, "hour", "minute"); 
 * console.log(timeDiff); // "2 hours"
 */
export const durationInWords = (durationInMinutes: number, translate: any): string => {
  const minsToHrs = 60
  const timeDiff = durationInMinutes
  const minutes = durationInMinutes % minsToHrs
  const hours = parseInt((durationInMinutes / minsToHrs).toString())

  const { HOURS, MINUTES, HOUR, MINUTE } = HOME_CONSTANTS

  if (timeDiff < minsToHrs) {
    return `${minutes}${translate(MINUTES)}`
  } else {
    if (minutes == 0)
      return `${hours}${translate(HOURS)}`
    else return `${hours}${hours > 1 ? translate(HOURS) : translate(HOUR)} ${minutes}${minutes > 1 ? translate(MINUTES) : translate(MINUTE)}`
  }
}

/**
 * Checks if a target date is within a specified sticky range **after** the event date.
 *
 * @param eventDate - The event date to compare.
 * @param today - The current UTC date.
 * @param range - Sticky range in days after the event.
 * @returns True if today's date is within the sticky range after the event date.
 */
export function isWithinRange(eventDate: Date, today: Date, range: number): boolean {
  const diffInMs = today.getTime() - eventDate.getTime();

  const actualDays = range - 1; // 👈 subtract 1 to exclude the final boundary
  const maxRangeInMs =
    actualDays * WorkConstants.TIME.HOURS_IN_DAY *
    WorkConstants.TIME.MINUTES_IN_HOUR *
    WorkConstants.TIME.SECONDS_IN_MINUTE *
    WorkConstants.TIME.MILLISECONDS_IN_SECOND;

  return diffInMs >= 0 && diffInMs <= maxRangeInMs;
}


/**
 * Converts a date string (in "YYYY-MM-DD" format) to a UTC `Date` object with time set to 00:00:00.
 *
 * @param dateStr - The date string to convert.
 * @returns A Date object or null if input is invalid.
 */
export function toDateOnly(dateStr: string | null | undefined): Date | null {
  if (!dateStr || !dateStr.includes("-")) return null;
  const [year, month, day] = dateStr.split("-").map(Number);
  return new Date(Date.UTC(year, month - 1, day));
}

/**
 * Converts minutes to milliseconds.
 *
 * @param minutes - The number of minutes.
 * @returns The equivalent milliseconds.
 *
 * @example
 * const ms = minutesToMilliseconds(5); // 300000
 */
export function minutesToMilliseconds(minutes: number): number {
  return minutes * 60 * 1000;
}

/**
 * Returns the current ISO week number and year.
 *
 * @returns {{ weekNumber: number; year: number }} An object containing the current ISO week number and year.
 *
 * @example
 * const { weekNumber, year } = getCurrentWeek();
 * console.log(weekNumber, year); // e.g., 29 2025
 */
export const getCurrentWeek = (): { weekNumber: number; year: number } => {
  const currentDate = moment();
  const currentWeek = currentDate.week(); // or use .week() for Sunday-based week
  const year = currentDate.year();
  return { weekNumber: currentWeek, year };
}

/**
 * Calculates the start (Sunday) and end (Saturday) dates for a given ISO week number and year.
 *
 * @param {number} weekNumber - The ISO week number (1-53).
 * @param {number} year - The year for which to calculate the week range.
 * @returns {{ fromDate: string; toDate: string }} An object with `fromDate` (Sunday) and `toDate` (Saturday) in "YYYY-MM-DD" format.
 *
 * @example
 * const { fromDate, toDate } = getWeekDateRange(29, 2025);
 * console.log(fromDate, toDate); // e.g., "2025-07-13" "2025-07-19"
 */
export const getWeekDateRange = (weekNumber: number, year: number) => {

  // Get a date in the correct year and week, then go to Sunday
  const fromDate = moment().year(year).week(weekNumber).startOf('week'); // Sunday
  const toDate = fromDate.clone().add(6, 'days'); // Saturday

  return {
    fromDate: fromDate.format('YYYY-MM-DD'),
    toDate: toDate.format('YYYY-MM-DD')
  };
};


/**
 * Determines if a shift is starting soon based on the provided shift start time and current time.
 *
 * @param shiftStartTime - The start time of the shift as an ISO 8601 string.
 * @param now - The current date and time as a Date object.
 * @returns `true` if the shift is starting within the threshold defined by `HOURS_REMAIN_TO_CLOCK_IN` and has not yet started; otherwise, `false`.
 */

export function isShiftStartingSoon(shiftStartTime: string, now: Date): boolean {
  const start = dayjs(shiftStartTime);
  const diffInHours = start.diff(now, "hour", true);
  return diffInHours <= HOURS_REMAIN_TO_CLOCK_IN && diffInHours > 0;
}

/**
 * Determines whether the current time is later than the specified shift start time.
 */
export function isLateForShift(shiftStartTime: string, now: Date): boolean {
  const start = dayjs(shiftStartTime);
  // Only late if after shift start
  return now > start.toDate();
}

/**
 * Determines if a shift is ending soon based on the provided end time and current time.
 */
export function isShiftEndingSoon(shiftEndTime: string, now: Date): boolean {
  const end = dayjs(shiftEndTime);
  const diffInMinutes = end.diff(now, "minute", true);
  //alert('diffInMinutes: ' + diffInMinutes + ' shift End Time: ' + shiftEndTime);
  //alert('Shift Ending Soon: ' + (diffInMinutes <= SHIFT_ENDING_DURATION && diffInMinutes > 0));
  return diffInMinutes <= SHIFT_ENDING_DURATION && diffInMinutes > 0;
}

/**
 * Determines whether a shift has ended based on the provided end time and the current date.
 */
export function isShiftOver(
  shiftEndTime: string,
  now: Date,
  shiftStartTime?: string,
  clockedIn?: boolean
): boolean {
  const end = dayjs(shiftEndTime);
  // If NOT clocked in and shiftStartTime is provided, check the HOURS_AROUND_SHIFT rule
  if (clockedIn === false && shiftStartTime) {
    const start = dayjs(shiftStartTime);
    const diffHours = dayjs(now).diff(start, 'hour', true);
    if (diffHours > HOURS_AROUND_SHIFT) {
      return true;
    }
  }
  // Otherwise, shift is over if now is after end
  return now > end.toDate();
}
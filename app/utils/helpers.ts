import { Linking, PermissionsAndroid, Platform, AccessibilityInfo } from "react-native";
import Geolocation from "@react-native-community/geolocation";
import {
  check,
  PERMISSIONS,
  request,
  RESULTS,
  checkNotifications,
  requestNotifications,
} from "react-native-permissions";
import { LocationAccuracy } from "../store/reducers/locationAccessSlice";
import { Name } from "../misc/models/ProfileModal";
import { TNC_AGREEMENT, TncAgreementType, TncAgreementEvent, TncAgreementStatus } from "../../app/shared/constants";
import { TncAgreementRequestBody } from '../misc/models/ProfileModal';
import * as AsyncStorage from '../../app/store/AsyncStorage';
import { SecureStorage, TokenType } from '../store/SecureStorage';

const EARTH_RADIUS_KM = 6371;
const FEET_TO_METERS = 0.3048;

const secureStorage = new SecureStorage();

export const requestLocationPermission = async (): Promise<boolean> => {
  try {
    if (Platform.OS === "android") {
      const result = await request(PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION); // triggers Android OS modal
      return result === RESULTS.GRANTED;
    } else {
      const result = await request(PERMISSIONS.IOS.LOCATION_WHEN_IN_USE); // triggers iOS OS modal
      return result === RESULTS.GRANTED
    }
  } catch (error) {
    console.warn("Permission request failed:", error);
    return false;
  }
};

export const checkLocationPermission = async (): Promise<LocationAccuracy> => {
  if (Platform.OS === "android") {

    const fine = await PermissionsAndroid.check(
      PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION
    );
    const coarse = await PermissionsAndroid.check(
      PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION
    );
    const background = await PermissionsAndroid.check(
      PermissionsAndroid.PERMISSIONS.ACCESS_BACKGROUND_LOCATION
    );

    return fine ? "precise" : coarse ? "approximate" : "denied"
  }

  else {
    const result = await check(PERMISSIONS.IOS.LOCATION_WHEN_IN_USE);

    if (result === RESULTS.DENIED || result === RESULTS.BLOCKED) return "denied";

    try {
      const location = await getUserLocation();
      const ACCURACY_THRESHOLD = 100; // in meters
      return location.accuracy <= ACCURACY_THRESHOLD ? "precise" : "approximate";
    } catch (error) {
      console.warn("Location fetch failed on iOS:", error);
      return "denied";
    }
  }
};

/**
 * Asynchronously retrieves the location permission status for the current platform (iOS or Android).
 *
 * On iOS:
 * - Checks for `LOCATION_ALWAYS` and `LOCATION_WHEN_IN_USE` permissions.
 * - Returns one of the following statuses:
 *   - `'always'`: Location access is always allowed.
 *   - `'while using'`: Location access is allowed only while the app is in use.
 *   - `'once'`: Limited access granted (iOS-specific).
 *   - `'never'`: Location access is denied or blocked.
 *
 * On Android:
 * - Checks for `ACCESS_FINE_LOCATION` and `ACCESS_BACKGROUND_LOCATION` permissions.
 * - Returns one of the following statuses:
 *   - `'always'`: Both fine and background location access are granted.
 *   - `'while using'`: Only fine location access is granted.
 *   - `'never'`: Location access is denied or blocked.
 *
 * On unsupported platforms or in case of an error:
 * - Returns `'unknown'`.
 *
 * @returns {Promise<string>} A promise that resolves to the location permission status:
 * `'always'`, `'while using'`, `'once'`, `'never'`, or `'unknown'`.
 *
 * @throws Will log an error to the console if an exception occurs during permission checking.
 */
export const getLocationPermissionStatus = async (): Promise<string> => {
  try {
    let status;

    if (Platform.OS === 'ios') {
      // Check iOS location permissions
      status = await check(PERMISSIONS.IOS.LOCATION_ALWAYS);

      if (status === RESULTS.GRANTED) {
        return 'always';
      }

      status = await check(PERMISSIONS.IOS.LOCATION_WHEN_IN_USE);

      if (status === RESULTS.GRANTED) {
        return 'while using';
      }

      if (status === RESULTS.DENIED) {
        return 'never';
      }

      if (status === RESULTS.LIMITED) {
        return 'once'; // iOS-specific limited access
      }

      if (status === RESULTS.BLOCKED) {
        return 'never'; // Blocked permissions
      }
    } else if (Platform.OS === 'android') {
      // Check Android location permissions
      const fineLocation = await check(PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION);
      const backgroundLocation = await check(PERMISSIONS.ANDROID.ACCESS_BACKGROUND_LOCATION);

      if (fineLocation === RESULTS.GRANTED && backgroundLocation === RESULTS.GRANTED) {
        return 'always';
      }

      if (fineLocation === RESULTS.GRANTED) {
        return 'while using';
      }

      if (fineLocation === RESULTS.DENIED || backgroundLocation === RESULTS.DENIED) {
        return 'never';
      }

      if (fineLocation === RESULTS.BLOCKED || backgroundLocation === RESULTS.BLOCKED) {
        return 'never'; // Blocked permissions
      }
    }

    return 'unknown'; // Fallback for unsupported platforms or unknown statuses
  } catch (error) {
    return 'unknown';
  }
};

/**
 * Check if permission for notifications is granted
 * @returns boolean - true if permission is granted, false otherwise
 */
export const getNotificationsPermissionStatus = async (): Promise<string> => {
  const { status } = await checkNotifications();
  // The permision is granted but may be limited in iOS for notifications
  if (status === RESULTS.GRANTED) {
    return "notification-allowed"
  } else if (status === RESULTS.LIMITED) {
    return "notification-limited"
  } else if (status === RESULTS.DENIED) {
    return "notification-denied"
  } else if (status === RESULTS.BLOCKED) {
    return "notification-blocked"
  } else {
    return "notification-unknown"
  }
  // return status === RESULTS.GRANTED || status === RESULTS.LIMITED;
};
/**
 * Request permission for notifications
 * @returns boolean - true if permission is granted, false otherwise
 */
export const requestNotificationsPermission = async (): Promise<boolean> => {
  const { status } = await requestNotifications(["alert", "sound"]);
  // The permision is granted but may be limited in iOS for notifications
  return status === RESULTS.GRANTED || status === RESULTS.LIMITED;
};

/**
 * Check if permission for notifications is granted
 * @returns boolean - true if permission is granted, false otherwise
 */
export const checkNotificationsPermission = async (): Promise<boolean> => {
  const { status } = await checkNotifications();
  // The permision is granted but may be limited in iOS for notifications
  return status === RESULTS.GRANTED || status === RESULTS.LIMITED;
};

// ===== LOCATION FETCHER =====

export const getUserLocation = (): Promise<{
  latitude: number;
  longitude: number;
  accuracy: number;
}> => {

  return new Promise((resolve, reject) => {
    Geolocation.getCurrentPosition(
      (position) => {
        resolve({
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy,
        });
      },
      (error) => reject(error),
      { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 }
    );
  });
};

// ===== ACCURACY DETECTOR =====

export const getLocationAccuracy = async (): Promise<LocationAccuracy> => {
  try {
    const hasPermission = await checkLocationPermission();
    if (!hasPermission) return "denied";

    const location = await getUserLocation();

    const ACCURACY_THRESHOLD = 100;

    if (location.accuracy <= ACCURACY_THRESHOLD) {
      return "precise"
    } else {
      return "approximate";
    }
  } catch (error) {
    return "denied";
  }
}


export const isInsideGeoFence = (
  userLat: number,
  userLng: number,
  fenceLat: number,
  fenceLng: number,
  radius: number
): boolean => {
  const toRad = (value: number) => (value * Math.PI) / 180;

  const dLat = toRad(fenceLat - userLat);
  const dLng = toRad(fenceLng - userLng);

  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRad(userLat)) *
    Math.cos(toRad(fenceLat)) *
    Math.sin(dLng / 2) *
    Math.sin(dLng / 2);

  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  const distance = EARTH_RADIUS_KM * c * 1000; // convert to meters
  const feetToMeters = (feet: number): number => feet * FEET_TO_METERS;

  const radiusInMeters = feetToMeters(radius);

  return distance <= radiusInMeters;
};

export const openSettings = () => {
  Linking.openSettings();
};

/**
 * Capitalizes the first letter of a given word.
 *
 * @param word - The input string to be capitalized.
 * @returns A new string with the first character in uppercase 
 *          and the rest of the string unchanged.
 *
 * @example
 * capitalizeFirstLetter("albertsons"); // returns "Albertsons"
 * capitalizeFirstLetter("");       // returns ""
 */
export function capitalizeFirstLetter(word: string): string {
  if (!word) return "";
  return word.charAt(0).toUpperCase() + word.slice(1);
}

/**
 * Extracts the first word from a given userName object.
 * @param userName - The input object which contain FirstName and.
 * @returns The first word of the string, or an empty string if no words are found.
 *
 */
export function getFirstWordOfUserName(userName: Name): string {
  if (!userName) return "";
  let firstName = userName.preferredFirstName || userName.firstName;
  const words = firstName ? firstName.trim().split(/\s+/) : [];
  return words.length > 0 ? words[0] : "";
}

/**
 * Announces a message for accessibility (screen readers) after a short delay.
 * The delay allows the screen to re-render before the announcement.
 * @param text - The message to announce.
 */
export const announceADA = (text: string) => {
  setTimeout(() => {
    AccessibilityInfo.announceForAccessibility(text);
  }, 500);
};

/**
 * Builds the request body for a Terms and Conditions (TnC) agreement.
 *
 * This function constructs a request body object for various types of TnC agreements,
 * including loyalty terms, location permissions, push notification permissions, 
 * terms and conditions, and privacy policies. The request body includes details 
 * such as the agreement ID, version, name, employee ID, LDAP, and device ID.
 *
 * @param type - The type of TnC agreement. Must be one of the `TncAgreementType` enum values.
 * @param event - The event type associated with the agreement. Must be one of the `TncAgreementEvent` enum values.
 * @param status - The status of the agreement. Must be one of the `TncAgreementStatus` enum values.
 * 
 * @returns A promise that resolves to a `TncAgreementRequestBody` object containing the constructed request body.
 *
 * @throws Will throw an error if an unsupported `TncAgreementType` is provided.
 *
 */
export const buildTncAgreementRequestBody = async (type: TncAgreementType, event: TncAgreementEvent, status: TncAgreementStatus): Promise<TncAgreementRequestBody> => {
  let body: TncAgreementRequestBody;
  let agreementId: string;
  let agreementVersion: string;
  let agreementName: string;
  const requestId = await AsyncStorage.uniqueSessionId();
  const deviceId = await AsyncStorage.deviceId();
  const employeeId = await secureStorage.getToken(TokenType.employeeId);
  var ldap = ""
  try {
    ldap = await AsyncStorage.ldap();
  } catch (error) {
    ldap = deviceId
  }

  switch (type) {
    case TncAgreementType.LOYALTY_TNC:
      agreementId = TNC_AGREEMENT.LOYALTY_TNC.ID
      agreementVersion = TNC_AGREEMENT.LOYALTY_TNC.VERSION
      agreementName = TNC_AGREEMENT.LOYALTY_TNC.NAME
      break;

    case TncAgreementType.LOCATION_PERMISSION:
      agreementId = TNC_AGREEMENT.LOCATION_PERMISSION.ID
      agreementVersion = TNC_AGREEMENT.LOCATION_PERMISSION.VERSION
      agreementName = TNC_AGREEMENT.LOCATION_PERMISSION.NAME
      break;

    case TncAgreementType.PUSH_NOTIFICATION_PERMISSION:
      agreementId = TNC_AGREEMENT.PUSH_NOTIFICATION_PERMISSION.ID
      agreementVersion = TNC_AGREEMENT.PUSH_NOTIFICATION_PERMISSION.VERSION
      agreementName = TNC_AGREEMENT.PUSH_NOTIFICATION_PERMISSION.NAME
      break;

    case TncAgreementType.TERMS_AND_CONDITIONS:
      agreementId = TNC_AGREEMENT.TERMS_AND_CONDITIONS.ID
      agreementVersion = TNC_AGREEMENT.TERMS_AND_CONDITIONS.VERSION
      agreementName = TNC_AGREEMENT.TERMS_AND_CONDITIONS.NAME
      break;

    case TncAgreementType.PRIVACY_POLICY:
      agreementId = TNC_AGREEMENT.PRIVACY_POLICY.ID
      agreementVersion = TNC_AGREEMENT.PRIVACY_POLICY.VERSION
      agreementName = TNC_AGREEMENT.PRIVACY_POLICY.NAME
      break;

    default:
      throw new Error(`Unsupported TncAgreementType: ${type}`);
  }

  body = {
    requestId: requestId,
    empId: employeeId,
    eventType: event,
    ldap: ldap,
    agreementId: agreementId,
    agreementVersion: agreementVersion,
    agreementName: agreementName,
    agreementStatus: status,
    deviceId: deviceId,
  };

  return body
};

import { CelebrationCardProps } from '../../components/CelebrationCard';
import AsyncStorage from '@react-native-async-storage/async-storage';

const STORAGE_KEY = 'dismissedCelebrations';

/**
 * Retrieves the list of dismissed celebration card IDs for the current year.
 *
 * @returns {Promise<string[]>} A promise that resolves to an array of dismissed celebration IDs.
 */
export const getDismissedCelebrations = async (): Promise<string[]> => {
  const stored = await AsyncStorage.getItem(STORAGE_KEY);
  const currentYear = new Date().getFullYear();

  try {
    const parsed = stored ? JSON.parse(stored) : [];
    return parsed
      ?.filter((entry: any) => entry.year === currentYear)
      ?.map((entry: any) => entry.id);
  } catch {
    return [];
  }
};

/**
 * Dismisses a celebration card by saving its ID and the current year to AsyncStorage.
 * Ensures the same celebration is not dismissed multiple times within the same year.
 *
 * @param {string} id - The unique identifier of the celebration to dismiss.
 * @returns {Promise<void>} A promise that resolves when the dismissal is saved.
 */
export const dismissCelebration = async (id: string): Promise<void> => {
  const stored = await AsyncStorage.getItem(STORAGE_KEY);
  const currentYear = new Date().getFullYear();
  let parsed = [];

  try {
    parsed = stored ? JSON.parse(stored) : [];
  } catch {
    parsed = [];
  }

  // Avoid duplicates
  if (!parsed.some((entry: any) => entry.id === id && entry.year === currentYear)) {
    parsed?.push({ id, year: currentYear });
    await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(parsed));
  }
};

/**
 * Generates a list of celebration card data based on the provided input.
 * Only includes celebrations that should be displayed for the current year.
 *
 * @param {any} celebrationData - The raw celebration data, expected to contain
 *   `work` and/or `birthday` keys with celebration details.
 * @returns {CelebrationCardProps[]} An array of card props to render.
 */
export const generateCelebrationCards = (celebrationData: any): CelebrationCardProps[] => {
  try {
    const currentYear = new Date().getFullYear();
    const cards: CelebrationCardProps[] = [];

    if (celebrationData?.work?.shouldCelebrate) {
      const yearsWorked = celebrationData?.work?.yearsWorked || 1;
      const yearStarted = currentYear - yearsWorked;

      cards.push({
        id: `${celebrationData?.work?.type}-${currentYear}`,
        type: celebrationData?.work?.type,
        yearsWorked,
        yearStarted,
        testID: `${celebrationData?.work?.type}-test`
      });
    }

    if (celebrationData?.birthday?.shouldCelebrate) {
      cards.push({
        id: `Birthday-${currentYear}`,
        type: 'Birthday',
        testID: 'Birthday-test'
      });
    }

    return cards;
  }
  catch (e) {
    return [];
  }
};

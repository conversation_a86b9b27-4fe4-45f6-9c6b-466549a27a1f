import * as AsyncStorage from "../store/AsyncStorage";
/**
 * Checks and stores an API key in local storage with a timestamp, and optionally clears it.
 *
 * This function is designed to manage API keys in local storage. It can:
 * - Clear the stored API key if `clearStorage` is set to `true`.
 * - Check if the stored API key has expired based on the provided `duration` in hours.
 * - Store a new API key with the current timestamp if it doesn't already exist.
 *
 * @param {string} apiKey - The unique key to identify the API key in local storage.
 * @param {number} durationInSeconds - The duration in hours to determine if the stored API key has expired.
 * @param {boolean} [clearStorage=false] - Optional flag to clear the stored API key. Defaults to `false`.
 * @returns {Promise<boolean>} - A promise that resolves to:
 *   - `true` if the storage was cleared, the key was stored for the first time, or the stored key has expired.
 *   - `false` if the stored key is still valid and within the specified duration.
 *
 * @throws {Error} - Throws an error if there is an issue with accessing or modifying local storage.
 *
 * @example
 * // Store a new API key and check if it has expired after 24 hours
 * const isExpired = await checkAndStoreApiKey("myApiKey", 24);
 * console.log(isExpired); // true if expired or first time storing, false otherwise
 *
 * @example
 * // Clear the stored API key
 * const isCleared = await checkAndStoreApiKey("myApiKey", 24, true);
 * console.log(isCleared); // true
 */
export default async function shouldApiTriggerCall(
  apiKey: string,
  durationInSeconds?: number,
  clearStorage: boolean = false
): Promise<boolean> {
  try {
    if (clearStorage) {
      await AsyncStorage.removeItem(apiKey);
      return true; // Storage cleared for the given API key
    }

    const currentTimestamp = Date.now(); // Get the current timestamp
    const lastCallTimestamp = await AsyncStorage.getItem(apiKey);

    const isTimestampExpired =
      lastCallTimestamp && durationInSeconds
        ? (currentTimestamp - parseInt(lastCallTimestamp, 10)) / 1000 >
          durationInSeconds
        : true;

    if (isTimestampExpired) {
      await AsyncStorage.setItem(apiKey, currentTimestamp);
    }

    return isTimestampExpired; // Return true if expired or first time storing, false otherwise
  } catch (error) {
    console.error("Error in checkAndStoreApiKey:", error);
    throw error;
    return false;
  }
}

import Orientation from 'react-native-orientation-locker';

/**
 * Sets the screen orientation based on the type of device.
 *
 * @param {boolean} isTablet - A boolean indicating whether the device is a tablet.
 *
 * @remarks
 * - If the device is a tablet, all orientations are unlocked, allowing the screen to rotate freely.
 * - If the device is not a tablet, the orientation is locked to portrait mode.
 *
 * @example
 * ```typescript
 * import { setOrientationBasedOnDevice } from './orientationUtils';
 * 
 * const isTablet = true; // Example: Determine this value dynamically
 * setOrientationBasedOnDevice(isTablet);
 * ```
 *
 * @dependencies
 * This function uses the `react-native-orientation-locker` library to manage screen orientation.
 *
 * @see {@link https://github.com/wonday/react-native-orientation-locker | react-native-orientation-locker documentation}
 */
export const setOrientationBasedOnDevice = (isTablet: boolean) => {
    if (isTablet) {
        Orientation.unlockAllOrientations();
    } else {
        Orientation.lockToPortrait();
    }
};
import { SecureStorage, TokenType } from "../store/SecureStorage";
import { store } from '../store';
/**
 * Retrieves the household ID from secure storage using the user's "Work Email"
 *
 * Steps:
 * - Finds the work email from the profile.
 * - Uses SecureStorage to get the associated profile token.
 * - Extracts the household ID and dispatches the scorecard request.
 *
 * Gracefully exits if required data (email, token, or household ID) is missing.
 * Errors are logged for debugging purposes.
 *
 * @param profile - User profile object containing emails.
 */
export const getHouseholdIdFromProfile = async (profile: any): Promise<string | null> => {
    const personNumber = profile?.personNumber;

    if (!personNumber) return null;

    try {
        const secureStorage = new SecureStorage();
        const usaProfileObj = await secureStorage.getToken(personNumber);

        if (!usaProfileObj) return null;

        const { householdAccount } = JSON.parse(usaProfileObj);

        return householdAccount?.householdId || null;
    } catch (error) {
        console.error("Error fetching household ID:", error);
        return null;
    }
};

/**
 * Saves the current user's UCA profile to secure storage.
 *
 * This function retrieves the latest profile and UCA profile from the Redux store,
 * extracts relevant information (such as loyalty programs, household account, phones, and emails),
 * and stores them in secure storage under a key associated with the user's person number.
 * It also maintains a list of all person numbers that have been saved.
 *
 * If either the profile or UCA profile is missing from the state, or if the person number is not available,
 * the function logs a warning and exits early.
 *
 * @async
 * @returns {Promise<void>} Resolves when the profile has been saved, or exits early if required data is missing.
 * @throws Logs an error to the console if saving to secure storage fails.
 */
export const saveUcaProfileToStorage = async () => {
    try {
        const state = store.getState();
        const ucaProfile = state.profile?.ucaProfile;
        const profile = state.profile?.profile;

        if (!ucaProfile || !profile) {
            console.warn('Missing profile or ucaProfile in state');
            return;
        }

        const {
            householdAccount,
            loyaltyPrograms,
            profile: { phones, emails },
        } = ucaProfile;

        const personNumber = profile?.personNumber;

        if (!personNumber) return;

        const profileObj = JSON.stringify({
            loyaltyPrograms,
            householdAccount,
            ...(phones?.length ? { phones } : {}),
            ...(emails?.length ? { emails } : {}),
        });

        const secureStorage = new SecureStorage();

        const existingListStr = await secureStorage.getToken(TokenType.personNumberKey);
        const existingList = existingListStr ? JSON.parse(existingListStr) : [];


        const updatedList = Array.isArray(existingList)
            ? existingList.includes(personNumber)
                ? existingList
                : [...existingList, personNumber]
            : [personNumber];


        await secureStorage.saveToken(TokenType.personNumberKey, JSON.stringify(updatedList));
        await secureStorage.saveToken(personNumber as TokenType, profileObj);
    } catch (error) {
        console.error(' Error saving UCA profile to storage:', error);
    }
};


import { STICKY_DAYS, WorkConstants } from "../shared/constants";
import { isWithinRange, toDateOnly, } from "./TimeUtils";

/**
 * Enum representing the types of celebration.
 */
export enum CelebrationType {
    Birthday = 'Birthday',
    Work = 'Work',
    Workiversary = 'Workiversary',
    Milestone = 'Milestone',
}

/**
 * Aggregated celebration check for both birthday and workiversary.
 *
 * @param birthDate - The user's date of birth (in YYYY-MM-DD format).
 * @param joinDate - The user's hire date (in YYYY-MM-DD format).
 * @param birthdayStickyRange - Sticky range for birthday celebrations in days.
 * @param workStickyRange - Sticky range for work celebrations in days.
 *
 * @returns Object indicating whether to celebrate birthday or work anniversary, and the work type if applicable.
 */
export function checkAllCelebrations(
    birthDate: string | null | undefined,
    joinDate: string | null | undefined,
    birthdayStickyRange: number = STICKY_DAYS.BIRTHDAY,
    workStickyRange: number = STICKY_DAYS.WORK_ANNIVERSARY
): {
    birthday: { shouldCelebrate: boolean };
    work: {
        yearsWorked?: number; shouldCelebrate: boolean; type?: CelebrationType.Workiversary | CelebrationType.Milestone

    };
} {

    const now = new Date(Date.now());
    const today = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate()));
    const currentYear = today.getUTCFullYear();

    let birthdayResult = { shouldCelebrate: false };
    const birthdayDate = toDateOnly(birthDate);
    try {
        if (birthdayDate) {
            const birthdayThisYear = new Date(Date.UTC(currentYear, birthdayDate.getUTCMonth(), birthdayDate.getUTCDate()));
            if (isWithinRange(birthdayThisYear, today, birthdayStickyRange)) {
                birthdayResult.shouldCelebrate = true;
            }
        }
    } catch (error) {
        // log error if needed
    }

    const current = new Date();
    const joiningDay = new Date(joinDate);
    let workingYears = current.getFullYear() - joiningDay?.getFullYear();

    let workResult: { shouldCelebrate: boolean; type?: CelebrationType.Workiversary | CelebrationType.Milestone, yearsWorked?: number } = { shouldCelebrate: false };
    const hireDate = toDateOnly(joinDate);
    try {
        if (hireDate) {
            const hireYear = hireDate.getUTCFullYear();
            const yearsWorked = currentYear - hireYear;

            if (yearsWorked >= WorkConstants.SINGLE_YEAR) {
                const anniversaryThisYear = new Date(Date.UTC(currentYear, hireDate.getUTCMonth(), hireDate.getUTCDate()));

                if (isWithinRange(anniversaryThisYear, today, workStickyRange)) {
                    if (yearsWorked === WorkConstants.SINGLE_YEAR) {
                        workResult = { shouldCelebrate: true, type: CelebrationType.Milestone, yearsWorked: workingYears };
                    } else if (yearsWorked % WorkConstants.MILESTONE_YEARS_FACTOR === 0) {
                        workResult = { shouldCelebrate: true, type: CelebrationType.Milestone, yearsWorked: workingYears };
                    } else {
                        workResult = { shouldCelebrate: true, type: CelebrationType.Workiversary, yearsWorked: workingYears };
                    }
                }
            }
        }
    } catch (error) {
        // log error if needed
    }
    return {
        birthday: birthdayResult,
        work: workResult,
    };
}


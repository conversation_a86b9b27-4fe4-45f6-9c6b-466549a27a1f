import { networkCodes } from '../../app/shared/constants';
import { HttpStatusCode } from '../config/errorCodes';

export function isNetworkError(error: any): boolean {
    return (
        !error?.response &&
        (
            networkCodes.includes(error?.code) ||
            error?.message === 'Network Error'
        )
    );
}

export function isRateLimitError(error: any): boolean {
    return (error?.status === HttpStatusCode.RateLimitError);
}
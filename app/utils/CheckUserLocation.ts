import {
  getUserLocation,
  isInsideGeoFence,
  requestLocationPermission,
} from "./helpers";

export const checkUserLocation = async (geoFence: {
  latitude: number;
  longitude: number;
  radius: number;
}) => {
  try {
    const hasPermission = await requestLocationPermission();
    if (!hasPermission) {
      return;
    }

    const userLocation = await getUserLocation();

    const isInside = isInsideGeoFence(
      userLocation.latitude,
      userLocation.longitude,
      geoFence.latitude,
      geoFence.longitude,
      geoFence.radius
    );
  } catch (error) {
    console.error("Error getting location:", error);
  }
};

import { Platform, Alert, type <PERSON>ertButton } from 'react-native';
import { getVersion } from 'react-native-device-info';

// Import your JSON files here
import appConfig_en from '../../assets/appconfigurations/associateAppConfig_en.json';
import appConfig_es from '../../assets/appconfigurations/associateAppConfig_es.json';
import * as AsyncStorage from '../store/AsyncStorage';

class AppUtils {
  /**
   * Saves the given UUID to AsyncStorage.
   * @param {string} value - The UUID to save.
   */
  public static async saveUDID(value: string): Promise<void> {
    try {
      await AsyncStorage.setItem('uniqueId', value);
      console.log('UUID saved to AsyncStorage:', value);
    } catch (error) {
      console.error('Error saving UUID to AsyncStorage:', error);
      throw error; // Re-throw the error for the caller to handle
    }
  }

  /**
   * Checks if a UUID is available in AsyncStorage.
   * @returns {Promise<boolean>} A promise that resolves to true if a UUID exists, false otherwise.
   */
  public static async isUDIDAvailable(): Promise<boolean> {
    try {
      const existingId = await AsyncStorage.getItem('uniqueId');
      return existingId !== null;
    } catch (error) {
      console.error('Error checking UUID availability in AsyncStorage:', error);
      return false; // In case of error, assume UUID is not available
    }
  }

  /**
   * Retrieves the app version using react-native-device-info.
   * @returns {Promise<string>} A promise that resolves to the app version string.
   */
  public static getAppVersion(): string {
    try {
      const appVersion = getVersion();
      return appVersion;
    } catch (error) {
      console.error('Error getting app version:', error);
      throw error; // Re-throw the error for the caller to handle
    }
  }

  /**
   * The name of the application.
   */
  public static readonly APP_NAME = 'Associate App';
  public static readonly APP_CODE_FEATURE_FLAG = 'associateApp';
}

const configFiles: Record<string, any> = {
  associateAppConfig_es: appConfig_es,
  associateAppConfig_en: appConfig_en,
  // Add more mappings as needed
};

/**
 * Reads a JSON config file by name.
 * @param {string} fileName - The base name of the JSON file (without .json).
 * @returns {object | undefined} The JSON object, or undefined if not found.
 */
export const readConfig = (fileName: string): object | undefined => {
  return configFiles[fileName];
};

/**
 * Calculates the bottom sheet height based on the device type and platform.
 *
 * @param deviceHeight - The height of the device screen.
 * @param isTablet - Boolean indicating if the device is a tablet.
 * @returns The calculated bottom sheet height.
 */
export const getBottomSheetHeight = (deviceHeight: number, isTablet: boolean): number => {
  if (isTablet) {
    return Platform.OS === 'ios' ? deviceHeight * 0.25 : deviceHeight * 0.2;
  }
  return deviceHeight * 0.15;
};

/**
 * Extracts the YouTube video ID from a URL using modern React Native syntax.
 * Supports both youtube.com and youtu.be formats with additional URL parameters.
 *
 * @param url - The YouTube URL string to extract video ID from
 * @returns The 11-character video ID if found, null otherwise
 *
 * @example
 * ```typescript
 * // Standard YouTube URLs
 * extractYouTubeVideoId('https://www.youtube.com/watch?v=dQw4w9WgXcQ') // Returns: 'dQw4w9WgXcQ'
 * extractYouTubeVideoId('https://youtube.com/watch?v=dQw4w9WgXcQ&t=42s') // Returns: 'dQw4w9WgXcQ'
 *
 * // Short YouTube URLs
 * extractYouTubeVideoId('https://youtu.be/dQw4w9WgXcQ') // Returns: 'dQw4w9WgXcQ'
 * extractYouTubeVideoId('https://youtu.be/dQw4w9WgXcQ?t=42') // Returns: 'dQw4w9WgXcQ'
 *
 * // Invalid URLs
 * extractYouTubeVideoId('https://vimeo.com/123456') // Returns: null
 * extractYouTubeVideoId('not a url') // Returns: null
 * ```
 */
export const extractYouTubeVideoId = (url: string): string | null => {
  try {
    // Enhanced regex to handle various YouTube URL formats
    const youtubeRegexPatterns = [
      // Standard youtube.com URLs with optional parameters
      /(?:youtube\.com\/watch\?v=)([\w-]{11})(?:\S+)?/,
      // Short youtu.be URLs with optional parameters
      /(?:youtu\.be\/)([\w-]{11})(?:\S+)?/,
      // YouTube embed URLs
      /(?:youtube\.com\/embed\/)([\w-]{11})(?:\S+)?/,
      // YouTube mobile URLs
      /(?:m\.youtube\.com\/watch\?v=)([\w-]{11})(?:\S+)?/,
    ];

    for (const pattern of youtubeRegexPatterns) {
      const match = url.match(pattern);
      if (match?.[1]) {
        return match[1];
      }
    }

    return null;
  } catch {
    return null;
  }
};

/**
 * Displays an error alert with defualt Cancel and Retry options.
 * @param t - "t" is a translation function for internationalization.
 * @param title - The title of the alert.
 * @param message - The message to display in the alert.
 * @param onLeftCtaPressed - Optional callback for when Left CTA is pressed.
 * @param onRightCtaPressed - Optional callback for when Right CTA is pressed.
 * @param leftCtaText - Optional text for the Left CTA button.
 * @param rightCtaText - Optional text for the Right CTA button.
 */
export const showErrorAlert = (
  t: (key: string) => string | null,
  title: string,
  message: string,
  onRightCtaPressed?: () => void,
  onLeftCtaPressed?: () => void,
  leftCtaText?: string,
  rightCtaText?: string,
): void => {
  // Create an array of buttons for the alert
  const buttons: AlertButton[] = [];

  // Add Cancel and Retry buttons if their callbacks are provided
  if (leftCtaText || onLeftCtaPressed) {
    buttons.push({
      text: leftCtaText || t('cancel') || 'Cancel',
      style: 'cancel',
      onPress: onLeftCtaPressed,
    });
  }

  if (rightCtaText || onRightCtaPressed) {
    buttons.push({
      text: rightCtaText || t('schedule.retryButton') || 'Retry',
      onPress: onRightCtaPressed,
    });
  }
  if (buttons.length === 0) {
    buttons.push({
      text: 'Okay',
    });
  }

  // Show the alert with the constructed buttons
  Alert.alert(title, message, buttons, { cancelable: false });
};

export default AppUtils;

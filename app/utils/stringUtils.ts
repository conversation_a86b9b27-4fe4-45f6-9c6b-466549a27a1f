/**
 * Utility function related to string operations.
 * This function is specifically designed to handle string-related tasks.
 */
/**
 * Returns a placeholder string ("-") if the input is null, undefined, or an empty string.
 * Otherwise, it returns the input string as is.
 * 
 * @param input - The string to evaluate. Can be `string`, `null`, or `undefined`.
 * @returns A placeholder string ("-") if the input is null, undefined, or empty; otherwise, the original input string.
 */
export function getEmptyStringPlaceholder(input: string | null | undefined): string {
    return input === null || input === undefined || input.trim() === '' ? '-' : input;
}
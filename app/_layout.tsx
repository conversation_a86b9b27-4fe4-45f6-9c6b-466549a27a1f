import React, { useEffect, useRef } from 'react';
import { AppState, LogBox } from 'react-native';
import 'react-native-gesture-handler';
import 'react-native-reanimated';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';

import { APP_ANALYTICS } from '../analytics/AnalyticsConstants';
import { appLogEvent } from '../analytics/AnalyticsUtils';
import { i18n } from '../languages/i18n';

import AppNavigator from './navigation/AppNavigator';
import { AccessibilityFocusProvider } from './providers/AccessibilityFocus';
import { store, persistor } from './store';

import { PantryProvider } from 'pantry-design-system';
import { I18nextProvider } from 'react-i18next'; // Import i18next hooks

LogBox.ignoreAllLogs();

export default function App() {
    const appState = useRef(AppState.currentState);

    useEffect(() => {
        // Add an event listener to monitor changes in the app's state (foreground/background)
        const subscription = AppState.addEventListener('change', (nextAppState) => {
            // Check if the app is transitioning from inactive/background to active state
            if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
                appLogEvent(APP_ANALYTICS.ACTION, {
                    event_category: APP_ANALYTICS.EVENT_CATEGORY,
                    event_action: APP_ANALYTICS.EVENT_ACTION,
                    event_label: APP_ANALYTICS.EVENT_LABEL,
                });
            }
            // Update the current app state
            appState.current = nextAppState;
        });

        // Cleanup function to remove the event listener when the component unmounts
        return () => {
            subscription.remove();
        };
    }, []);
    return (
        <Provider store={store}>
            <PersistGate loading={null} persistor={persistor}>
                <PantryProvider>
                    <SafeAreaProvider>
                        <AccessibilityFocusProvider>
                            <I18nextProvider i18n={i18n}>
                                <AppNavigator />
                            </I18nextProvider>
                        </AccessibilityFocusProvider>
                    </SafeAreaProvider>
                </PantryProvider>
            </PersistGate>
        </Provider>
    );
}

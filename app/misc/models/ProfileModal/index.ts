export interface ProfileResponse {
    associateProfile: AssociateProfile
    loyalty: Loyalty
    ucaProfile: UcaProfile
    stores: Store[]
    errors?: Error[] | null
}

export interface ProfilePhotoResponse {
    response: ProfilePhoto
    errors?: Error[] | null
}

export interface ProfilePhoto {
    PhotoId: number;
    PrimaryFlag: boolean;
    PhotoType: string;
    PhotoName: string;
    Photo: string;
}

export interface OtpVerifyActionPayload {
    requestId: string;
    customerId: string;
    factorType: string;
    body?: OtpVerifyRequestBody;
}
export interface OtpVerifyRequestBody {
    passCode?: string;
    status?: string;
}

export type FactorResultType = "CHALLENGE" | "SUCCESS" | "FAILED"; // extend based on actual values
export interface Link {
    rel: string;
    href: string;
}
export interface FactorResponse {
    factorresult: FactorResultType;
    profile: {
        emailAddress: string;
    };
    links: Link[];
}
export interface OtpVerifyResponse {
    response?: FactorResponse
    errors?: Error[] | null
}
export interface DiscountActionPayload {
    body: DiscountRequestBody;
}
export interface DiscountRequestBody {
    firstName: string;
    lastName: string;
    clubCardNumber: string;
}
export interface DiscountResponse {
    response: {}
    errors?: Error[] | null
}

export interface TncAgreementActionPayload {
    body: TncAgreementRequestBody;
}
export interface TncAgreementRequestBody {
    requestId: string;
    empId: string;
    eventType: string;
    ldap: string;
    agreementId: string;
    agreementVersion: string;
    agreementName: string;
    agreementStatus: string;
    deviceId: string;
}
export interface TncAgreementResponse {
    result: {}
    errors?: Error[] | null
}

export interface Error {
    code: string
    message: string
    statusCode?: number
}

export interface AssociateProfile {
    personNumber: string
    dateOfBirth: string
    latestHireDate: string
    locationCode: string
    banner: string
    legalEmployer: string
    division: string
    divisionName: string
    district: string
    facility: string
    facilityType: string
    role: string
    departmentName: string
    jobCode: string
    jobName: string
    level: string
    managementLevel: string
    names: Name[]
    emails: Email[]
    profile: boolean
}

export interface Name {
    effectiveStartDate: string
    effectiveEndDate: string
    firstName: string
    lastName: string
    middleName: string
    preferredFirstName: string
    displayName: string
    fullName: string
}

export interface Email {
    emailType: string
    emailAddress: string
    fromDate: string
    toDate: any
    primaryFlag: boolean
}

export interface Loyalty {
    offers: Offer[]
    loyalty: boolean
}

export interface Offer {
    allocationId: string
    brand: string
    category: string
    deleted: boolean
    description: string
    disclaimer: string
    ecomDescription: string
    endDate: string
    extlOfferId: string
    forUDescription: string
    hierarchies: Hierarchies
    imageId: string
    isClippable: boolean
    isDisplayable: boolean
    maxPurchaseQty: number
    minPurchaseQty: number
    offerId: string
    offerPgm: string
    offerProgramType: string
    offerProtoType: string
    offerSubPgm: string
    offerTs: string
    price: number
    programSubType: string
    programType: string
    purchaseInd: string
    purchaseRank: string
    startDate: string
    status: string
    usageType: string
}

export interface FeatureFlagResponse {
    featureFlagName: string
    featureFlagValue: boolean
}
export interface Hierarchies {
    categories: string[]
    events: string[]
}

export interface UcaProfile {
    customerId: string
    customerType: string
    lastUpdatedTimestamp: number
    createTimestamp: number
    profile: Profile
    householdAccount: HouseholdAccount
    loyaltyPrograms: LoyaltyProgram[]
    smsPromptConsent: boolean
}

export interface Profile {
    phones: Phone[]
    emails: UcaEmail[]
    customerStatuses: CustomerStatuse[]
    alternateId: AlternateId
    profileHashToken: string
}

export interface UcaEmail {
    emailAddress: string
    emailValidationStatus: string
}
export interface Phone {
    type: string
    value: string
}

export interface CustomerStatuse {
    type: string
    value: string
}

export interface AlternateId {
    type: string
    value: string
}

export interface HouseholdAccount {
    householdId: string
}

export interface LoyaltyProgram {
    type: string
    value: string
}

export interface Store {
    distance: number
    latitude: number
    longitude: number
    associatedPharmacyStoreId: string
    associatedFuelStationId: string
    associatedDeliStoreId: string
    associatedBakeryStoreId: string
    hasFuelStation: boolean
    justForU: boolean
    hasGroceryDelivery: boolean
    departments: string[]
    hasPharmacy: boolean
    openDate: string
    rewardsId: string
    ecomStore: EcomStore
    isEcomStore: boolean
    updatedBy: string
    updatedAt: string
    locationId: string
    timestamp: number
    timezone: string
    brand: Brand
    division: Division
    type: string
    description: string
    hours: Hours
    address: Address
    phone: string
    isClosed: boolean
    timenow: number
    localPage: string
    googleCIDMapsURL: string
    polarisBannerName: string
    domainName: string
    fuelId: string
    storelogoURL: string
    storelogo: string
    groceryrewards: boolean
    isZTPStore: boolean
    isOneTouchFuelRegion: boolean
    isAltBrandStore: boolean
    isOrderAheadStore: boolean
}

export interface EcomStore {
    largeOrderThresholdCount: number
    largeOrderCapUtilizationPerc: number
    operatingStatus: string
    isDUGStore: boolean
    isWFC: boolean
    is3PLStore: boolean
    isPremiumStore: boolean
    isDeliveryStore: boolean
    isHybridStore: boolean
    storeFeatures: StoreFeatures
    isPickupStore: boolean
    pickupFeature: PickupFeature
    isEnterpriseTaxation: boolean
    storeCardFlag: boolean
    isDUGLightArrivalEnabled: boolean
    isDUGArrivalEnabled: boolean
    isMSTEnabled: boolean
    slotDisplayDays: number
}

export interface StoreFeatures {
    isErumStore: boolean
    isGuestCartEnabled: boolean
    isMFC: boolean
    isAutoReplenishmentEnabled: boolean
    isSNAP2Eligible: boolean
    isSubstitutions2Enabled: boolean
    isExpressStore: boolean
    isWGDEnabled: boolean
    isStoreTippingEnabled: boolean
    isDarkStore: boolean
    isWineD2CEnabled: boolean
    isScanAndPayEnabled: boolean
    isPaymentV2: boolean
    isSNAPEligible: boolean
    isSIPEnabled: boolean
    isFullAuthEnabled: boolean
    isEditOrderEnabled: boolean
    isWYSIWYGEnabled: boolean
    isEnterpriseTaxation: boolean
    isSubscriptionUDC: boolean
    isRolloutWYSIWYGEnabled: boolean
    isPromisePlatformStore: boolean
    isHourlyStore: boolean
    isSequentialTenderingMLModel: boolean
    isOttValveEnabled: boolean
    isFlashDugEnabled: boolean
    isFlashDeliveryEnabled: boolean
    isFlash2Enabled: boolean
    isApprovedSubsEnabled: boolean
    is3PLBatching: boolean
    is3PLShopAndDriveEnabled: boolean
    isAltPickUpEnabled: boolean
    isMerchPickingEnabled: boolean
    isDirectedSpendEnabled: boolean
    isFoodIsMedicineNBEnabled: boolean
    isCustomerCentricTillingEnabled: boolean
    isSNSCNCEnabled: boolean
    isBagFeeWaivedforEBT: boolean
    isB2BStore: boolean
    isDeliveryPreference: boolean
    isPrePickingEnabled: boolean
    isIntradayFastPickingEnabled: boolean
    isISMLocationServiceEnabled: boolean
    is2WayCommunication: boolean
    isFoodIsMedicineBentoEnabled: boolean
    isSNSB2BEnabled: boolean
    isCustBagPreference: boolean
    is3PPEnabled: boolean
    isOrderAmends: boolean
    isDynamicSlotEnabled: boolean
    isGiftEnabled: boolean
    showDUGEndTime: boolean
    isFlashCapacityEnabled: boolean
    isSlowPickingEnabled: boolean
}

export interface PickupFeature {
    pickupLocations: any[]
    pickupConfig: any[]
}

export interface Brand {
    brandId: string
    name: string
}

export interface Division {
    number: string
    name: string
}

export interface Hours {
    monday: Monday
    tuesday: Tuesday
    wednesday: Wednesday
    thursday: Thursday
    friday: Friday
    saturday: Saturday
    sunday: Sunday
}

export interface Monday {
    openIntervals: OpenInterval[]
    isClosed: boolean
}

export interface OpenInterval {
    start: string
    end: string
}

export interface Tuesday {
    openIntervals: OpenInterval2[]
    isClosed: boolean
}

export interface OpenInterval2 {
    start: string
    end: string
}

export interface Wednesday {
    openIntervals: OpenInterval3[]
    isClosed: boolean
}

export interface OpenInterval3 {
    start: string
    end: string
}

export interface Thursday {
    openIntervals: OpenInterval4[]
    isClosed: boolean
}

export interface OpenInterval4 {
    start: string
    end: string
}

export interface Friday {
    openIntervals: OpenInterval5[]
    isClosed: boolean
}

export interface OpenInterval5 {
    start: string
    end: string
}

export interface Saturday {
    openIntervals: OpenInterval6[]
    isClosed: boolean
}

export interface OpenInterval6 {
    start: string
    end: string
}

export interface Sunday {
    openIntervals: OpenInterval7[]
    isClosed: boolean
}

export interface OpenInterval7 {
    start: string
    end: string
}

export interface Address {
    line1: string
    line2: any
    city: string
    state: string
    zipcode: string
    country: string
    displayCoordinates: DisplayCoordinates
    routableCoordinates: RoutableCoordinates
    walkableCoordinates: any
    googleCIDMapsURL: string
}

export interface DisplayCoordinates {
    coordinates: number[]
    type: string
}

export interface RoutableCoordinates {
    coordinates: number[]
    type: string
}

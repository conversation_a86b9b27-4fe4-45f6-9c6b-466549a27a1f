export interface LoginContent {
  title: string;
  subTitle: string;
  primaryCTA: string;
  bgImage: string;
  bgImageTabletLandscape: string;
  bgImageTabletPortrait: string;
  titleES: string;
  subTitleES: string;
  primaryCTAES: string;
}

export interface AuthorableContent {
  loginPage: LoginContent;
  accountLinkage: AccountLinkage;
  onboardingInfo: OnboardingInfo;
  onboardingAssociateInfo: OnboardingAssociateInfo;
  onboardingManagerInfo: OnboardingManagerInfo;
  locationOnboarding: LocationOnboarding;
  storeInfo: StoreInfo;
  manager?: string;
  associate?: string;
}

export interface AccountLinkage {
  title: string;
  content: ContentItem[];
}

export interface ContentItem {
  type: string;
  text: string;
}

export interface OnboardingInfo {
  image: string;
  primaryText: string;
  secondaryText: string;
  cta: string;
}

export interface OnboardingAssociateInfo {
  v1: AssociateInfo[];
  v2?: AssociateInfo[];
}

export interface AssociateInfo {
  headerText: string;
  items: Item[];
  cta: string;
}

export interface Item {
  type: 'verticalInfo' | 'horizontalInfo' | string;
  image?: string;
  primaryText: string;
  secondaryText?: string;
  isFlagUpcoming?: string;
  infoItems?: InfoItem[];
}

export interface OnboardingManagerInfo {
  v1: ManagerInfo[];
  v2?: ManagerInfo[];
}

export interface ManagerInfo {
  headerText: string;
  items: Item[];
  cta: string;
}
export interface InfoItem {
  image: string;
  text: string;
  isFlagUpcoming?: string;
}
export interface LocationOnboarding {
  v1: V1LocationInfo[];
}

export interface V1LocationInfo {
  imageUrl: string;
  primaryText: string;
  secondaryText: string;
  primaryCTA: string;
  secondaryCTA: string;
  bottomsheetInfo: BottomsheetInfo;
}

export interface BottomsheetInfo {
  primaryText: string;
  secondaryText: string;
  cta: string;
}

export interface StoreInfo {
  radius: number;
}

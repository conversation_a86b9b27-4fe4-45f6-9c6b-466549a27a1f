import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { useTheme, Heading } from 'pantry-design-system';
import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { FlatList, View, TouchableOpacity } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { SvgXml } from 'react-native-svg';

import { SIGNIN_ANALYTICS } from '../../../analytics/AnalyticsConstants';
import * as AnalyticsUtils from '../../../analytics/AnalyticsUtils'; // Assuming you have an analytics utility for setting user data
import {
  ADA_ROLES,
  HEADING_SIZE,
  HELP_OPTIONS,
  SCREENS,
  TEXT_ALIGN,
  TEXTLINK_COLORS,
  URLS,
} from '../../../app/shared/constants';
import { ChevronRight } from '../../../assets/icons/ChevronRight';
import { passIcon } from '../../../assets/images/svg/ExternalLink';
import { useAccessibilityFocus } from '../../providers/AccessibilityFocus';

import getStyles from './styles';

const SignInHelp = () => {
  const { theme } = useTheme();
  const insets = useSafeAreaInsets();
  const styles = getStyles(theme, insets);

  const { t } = useTranslation();
  const navigation = useNavigation();
  const helpOptions = HELP_OPTIONS(t);

  const { setRef, setLastFocused, handleScreenFocus } = useAccessibilityFocus();

  useEffect(() => {
    const unsubscribeFocus = navigation.addListener('focus', () => {
      handleScreenFocus(SCREENS.SIGN_IN_HELP);
    });

    return () => {
      unsubscribeFocus?.();
    };
  }, [navigation]);

  useFocusEffect(
    React.useCallback(() => {
      AnalyticsUtils.screenViewLog({
        subsection1: SIGNIN_ANALYTICS.SIGNIN_SCREEN_VIEW,
        subsection2: SIGNIN_ANALYTICS.LOGIN_HELP_EVENT_LABEL,
      });
    }, []),
  );

  /**
   * Navigates to the WebView screen with the provided URL and headers.
   *
   * @param {string} url - The URL to be loaded in the WebView.
   * @param {string} titleHeader - The title to be displayed in the WebView header.
   * @param {string} [subHeader] - An optional subtitle to be displayed in the WebView header.
   * @param {any} [handleNavigationStateChange] - An optional callback to handle navigation state changes in the WebView.
   */
  const handleWebViewNavigation = (
    url: string,
    titleHeader: string,
    subHeader?: string,
    analyticsParams?: any,
    handleNavigationStateChange?: any,
  ) => {
    navigation.navigate(SCREENS.WEBVIEW, {
      url,
      titleHeader,
      subHeader,
      onNavigationStateChange: handleNavigationStateChange,
      subsection: analyticsParams,
    });
  };

  const findMyLdap = () => {
    AnalyticsUtils.userActionLogEvent(
      SIGNIN_ANALYTICS.SIGNIN_EVENT_CATEGORY,
      SIGNIN_ANALYTICS.LINK_FIND_MY_LDAP,
      SIGNIN_ANALYTICS.LOGIN_HELP_EVENT_LABEL,
    );
    handleWebViewNavigation(URLS.FIND_MY_LDAP, t('helpOptions.ldap'), '', {
      subsection1: SIGNIN_ANALYTICS.SIGNIN_SCREEN_VIEW,
      subsection2: SIGNIN_ANALYTICS.LOGIN_HELP_EVENT_LABEL,
      subsection3: SIGNIN_ANALYTICS.FIND_MY_LDAP,
      eventCategory: SIGNIN_ANALYTICS.SIGNIN_SCREEN_VIEW,
      eventLabel: SIGNIN_ANALYTICS.FIND_MY_LDAP,
      refreshCategory: SIGNIN_ANALYTICS.SIGNIN_EVENT_CATEGORY,
      refreshAction: SIGNIN_ANALYTICS.LINK_REFRESH,
      refreshLabel: SIGNIN_ANALYTICS.FIND_MY_LDAP_SCREEN,
    });
  };

  const other = () => {
    AnalyticsUtils.userActionLogEvent(
      SIGNIN_ANALYTICS.SIGNIN_EVENT_CATEGORY,
      SIGNIN_ANALYTICS.LINK_OTHER_SIGN_IN_ISSUES,
      SIGNIN_ANALYTICS.LOGIN_HELP_EVENT_LABEL,
    );
    handleWebViewNavigation(URLS.OTHER_SIGN_IN_ISSUES, t('helpOptions.other'), '', {
      subsection1: SIGNIN_ANALYTICS.SIGNIN_SCREEN_VIEW,
      subsection2: SIGNIN_ANALYTICS.LOGIN_HELP_EVENT_LABEL,
      subsection3: SIGNIN_ANALYTICS.OTHER_SIGN_IN_ISSUES,
      eventCategory: SIGNIN_ANALYTICS.SIGNIN_SCREEN_VIEW,
      eventLabel: SIGNIN_ANALYTICS.OTHER_SIGN_IN_ISSUES,
      refreshCategory: SIGNIN_ANALYTICS.SIGNIN_EVENT_CATEGORY,
      refreshAction: SIGNIN_ANALYTICS.LINK_REFRESH,
      refreshLabel: SIGNIN_ANALYTICS.OTHER_SIGN_IN_ISSUES_SCREEN,
    });
  };

  const forgotPassword = () => {
    AnalyticsUtils.userActionLogEvent(
      SIGNIN_ANALYTICS.SIGNIN_EVENT_CATEGORY,
      SIGNIN_ANALYTICS.LINK_FORGET_OR_RESET_MY_PASSWORD,
      SIGNIN_ANALYTICS.LOGIN_HELP_EVENT_LABEL,
    );
    handleWebViewNavigation(
      URLS.FORGOT_PASSWORD,
      t('resetPassword'),
      t('passwordResetDescription'),
      {
        subsection1: SIGNIN_ANALYTICS.SIGNIN_SCREEN_VIEW,
        subsection2: SIGNIN_ANALYTICS.LOGIN_HELP_EVENT_LABEL,
        subsection3: SIGNIN_ANALYTICS.FORGOT_OR_RESET_PASSWORD,
        eventCategory: SIGNIN_ANALYTICS.SIGNIN_SCREEN_VIEW,
        eventLabel: SIGNIN_ANALYTICS.FORGOT_OR_RESET_PASSWORD,
        refreshCategory: SIGNIN_ANALYTICS.SIGNIN_EVENT_CATEGORY,
        refreshAction: SIGNIN_ANALYTICS.LINK_REFRESH,
        refreshLabel: SIGNIN_ANALYTICS.FORGET_OR_RESET_MY_PASSWORD_SCREEN,
      },
    );
  };

  const findMyEmployeeId = () => {
    AnalyticsUtils.userActionLogEvent(
      SIGNIN_ANALYTICS.SIGNIN_EVENT_CATEGORY,
      SIGNIN_ANALYTICS.LINK_FIND_MY_EMPLOYEE_ID,
      SIGNIN_ANALYTICS.LOGIN_HELP_EVENT_LABEL,
    );
    handleWebViewNavigation(URLS.FIND_MY_EMPLOYEE_ID, t('helpOptions.employeeId'), '', {
      subsection1: SIGNIN_ANALYTICS.SIGNIN_SCREEN_VIEW,
      subsection2: SIGNIN_ANALYTICS.LOGIN_HELP_EVENT_LABEL,
      subsection3: SIGNIN_ANALYTICS.FIND_MY_EMPLOYEE_ID,
      eventCategory: SIGNIN_ANALYTICS.SIGNIN_SCREEN_VIEW,
      eventLabel: SIGNIN_ANALYTICS.FIND_MY_EMPLOYEE_ID,
      refreshCategory: SIGNIN_ANALYTICS.SIGNIN_EVENT_CATEGORY,
      refreshAction: SIGNIN_ANALYTICS.LINK_REFRESH,
      refreshLabel: SIGNIN_ANALYTICS.FIND_MY_EMPLOYEEID_SCREEN,
    });
  };

  const handlePress = (key: string) => {
    // Navigate or log the key
    switch (key) {
      case 'ldap':
        findMyLdap();
        break;
      case 'employeeId':
        findMyEmployeeId();
        break;
      case 'resetPassword':
        forgotPassword();
        break;
      case 'other':
        other();
        break;
      default:
        console.warn(`Unhandled key: ${key}`);
        break;
    }
  };

  const renderItem = ({ item }: { item: (typeof helpOptions)[number] }) => (
    <TouchableOpacity
      ref={setRef(item.key)}
      style={styles.card}
      onPress={() => {
        setLastFocused(SCREENS.SIGN_IN_HELP, item.key);
        handlePress(item.key);
      }}
      testID={item.testID}
      accessible={true}
      accessibilityLabel={`${item.title}, ${t('ada.linkOpenInWebview')}`}
    >
      <View style={styles.cardContent}>
        <View style={styles.cardContainer}>
          <SvgXml
            xml={passIcon(theme.colors.pdsThemeColorBackgroundPrimary)}
            accessible={false}
            testID="pass-icon-image"
          />
          <View style={{ marginLeft: theme.dimensions.pdsGlobalSpace200 }}>
            <Heading
              title={item.title}
              size={HEADING_SIZE.XSmall}
              testID={`${item.title}${item.key}`}
              textAlign={TEXT_ALIGN.CENTER}
              accessible={false}
            />
          </View>
        </View>
        <ChevronRight
          color={theme.colors.pdsThemeColorForegroundNeutralHigh}
          size={theme.dimensions.pdsGlobalSizeHeight400}
        />
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.mainContainer}>
      <View style={styles.container}>
        <View style={{ marginBottom: theme.dimensions.pdsGlobalSpace400 }}>
          <Heading
            title={t('whatCanWeHelpWith')}
            size={HEADING_SIZE.Medium}
            color={TEXTLINK_COLORS.NEUTRAL_HIGH}
            maxLines={1}
            testID="what-can-help-with-heading"
          />
        </View>
        <FlatList
          data={helpOptions}
          renderItem={renderItem}
          keyExtractor={(item) => item.key}
          contentContainerStyle={styles.list}
        />
      </View>
    </View>
  );
};
export default SignInHelp;

import { useNavigation } from '@react-navigation/native';
import * as AnalyticsUtils from '../../../analytics/AnalyticsUtils';
import { SIGNIN_ANALYTICS } from '../../../analytics/AnalyticsConstants';
import { SCREENS, URLS } from '../../../app/shared/constants';
import SignInHelp from '../SignInHelp';
import { AccessibilityFocusProvider } from '../../providers/AccessibilityFocus';
import { fireEvent, render } from '@testing-library/react-native';
import React from 'react';


jest.mock('../../providers/AccessibilityFocus', () => ({
    useAccessibilityFocus: () => ({
        setRef: jest.fn(),
        setLastFocused: jest.fn(),
        restoreLastFocus: jest.fn(),
        setNavigatingBack: jest.fn(),
    }),
    AccessibilityFocusProvider: ({ children }: { children: React.ReactNode }) => children,
}));


const translations = {
    'helpOptions.ldap': 'Find my LDAP',
    'helpOptions.employeeId': 'Find my employee ID',
    'helpOptions.resetPassword': 'Forgot or reset my password',
    'helpOptions.other': 'Other sign in issue',
    resetPassword: 'Reset password',
    passwordResetDescription:
        "Once you've set your new password, press the back arrow to complete the process.",
    whatCanWeHelpWith: 'What can we help with?',
};

jest.mock('react-i18next', () => ({
    useTranslation: () => ({
        t: (key: string) => translations[key] || key,
    }),
}));

jest.mock('@react-navigation/native', () => ({
    useNavigation: jest.fn(),
    useRoute: jest.fn(() => ({
        params: {},
        name: 'SignInHelp',
        key: 'SignInHelp-key',
    })),
    useFocusEffect: jest.fn((cb) => cb()), // <-- Add this line
}));

jest.mock('../../../analytics/AnalyticsUtils', () => ({
    userActionLogEvent: jest.fn(),
    screenViewLog: jest.fn(),
}));

describe('SignInHelp Screen', () => {
    const navigateMock = jest.fn();
    const mockNavigation = {
        navigate: navigateMock,
        addListener: jest.fn(() => jest.fn()),
    };

    beforeEach(() => {
        (useNavigation as jest.Mock).mockReturnValue(mockNavigation);
        jest.clearAllMocks();
    });

    const renderWithProviders = (component: React.ReactElement) => {
        return render(<AccessibilityFocusProvider>{component}</AccessibilityFocusProvider>);
    };

    it('renders all help options with translated titles', () => {
        const { getByText } = renderWithProviders(<SignInHelp />);
        expect(getByText('Find my LDAP')).toBeTruthy();
        expect(getByText('Find my employee ID')).toBeTruthy();
        expect(getByText('Forgot or reset my password')).toBeTruthy();
        expect(getByText('Other sign in issue')).toBeTruthy();
    });

    it('logs event and navigates to forgot password screen when resetPassword option is tapped', () => {
        const { getByText } = renderWithProviders(<SignInHelp />);
        const resetPasswordOption = getByText('Forgot or reset my password');

        fireEvent.press(resetPasswordOption);

        expect(AnalyticsUtils.userActionLogEvent).toHaveBeenCalledWith(
            SIGNIN_ANALYTICS.SIGNIN_EVENT_CATEGORY,
            SIGNIN_ANALYTICS.LINK_FORGET_OR_RESET_MY_PASSWORD,
            SIGNIN_ANALYTICS.LOGIN_HELP_EVENT_LABEL
        );

        expect(navigateMock).toHaveBeenCalledWith(SCREENS.WEBVIEW, {
            url: URLS.FORGOT_PASSWORD,
            titleHeader: translations.resetPassword,
            subHeader: translations.passwordResetDescription,
            subsection: {
                "eventCategory": "login",
                "eventLabel": "forgot-or-reset-password",
                "refreshAction": "link|refresh",
                "refreshCategory": "user_login",
                "refreshLabel": "forget-or-reset-my-password-screen",
                "subsection1": "login",
                "subsection2": "login-help",
                "subsection3": "forgot-or-reset-password",
            },
            onNavigationStateChange: undefined,
        });
    });
});

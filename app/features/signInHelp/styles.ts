import { StyleSheet } from 'react-native';

import { SIGN_IN_CARD_OPTION_HEIGHT } from '../../../app/shared/constants';

import type { Theme } from 'pantry-design-system';
import type { EdgeInsets } from 'react-native-safe-area-context';

//customising styles based on themes
const getStyles = ({ colors, fonts, dimensions, typography }: Theme, insets: EdgeInsets) => {
    const styles = StyleSheet.create({
        card: {
            backgroundColor: '#f9f9f9', // TODO: The PDS colors doesn't have token for this color, so using a placeholder
            borderRadius: dimensions.pdsGlobalSpace200,
            padding: dimensions.pdsGlobalSpace400,
            marginBottom: dimensions.pdsGlobalSpace300,
            minHeight: SIGN_IN_CARD_OPTION_HEIGHT,
            borderWidth: 1, //TODO: The PDS dimensions doesn't have token for value equal to 1 and more than 1 seems too much
            borderColor: colors.pdsThemeColorOutlineNeutralLow,
        },
        cardContainer: { alignItems: 'center', flexDirection: 'row' },
        cardContent: {
            alignItems: 'center',
            flexDirection: 'row',
            justifyContent: 'space-between',
        },
        container: {
            flex: 1,
            justifyContent: 'center',
            margin: dimensions.pdsGlobalSpace400,
        },
        list: {
            paddingBottom: dimensions.pdsGlobalSpace400,
        },
        mainContainer: {
            backgroundColor: colors.pdsThemeColorBackgroundSunken,
            flex: 1,
        },
    });

    return styles;
};

export default getStyles;

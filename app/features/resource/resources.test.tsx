import { fireEvent, render } from '@testing-library/react-native';
import React from 'react';
import * as redux from 'react-redux';

import EmptyStatusCard from '../../../components/EmptyStatusCard/EmptyStatusCard';
import OffShiftMessage from '../../../components/OffShiftMessage';
import { SCREENS, URLS } from '../../shared/constants';

import ResourceScreen from './index';
import * as featureFlagsSelectors from '../../store/selectors/featureFlagsSelectors';
import * as reactNavigation from '@react-navigation/native';
import * as accessibility from '../../providers/AccessibilityFocus';
import * as governanceSelectors from '../../../app/store/selectors/governance';


jest.mock('../../providers/AccessibilityFocus', () => ({
    useAccessibilityFocus: () => ({
        setRef: jest.fn(),
        setLastFocused: jest.fn(),
        handleScreenFocus: jest.fn(),
    }),
    AccessibilityFocusProvider: ({ children }) => children,
}));

// Add to your mock setup
jest.mock('../../hooks/useNetworkAlert', () => ({
    useNetworkAlert: () => ({
        showAlertWithRetry: jest.fn(),
        checkInternetConnection: jest.fn(() => Promise.resolve(true)),
    }),
}));
// Mock Firebase modular API
jest.mock('@react-native-firebase/app', () => ({
    getApp: jest.fn(() => ({})),
}));

jest.mock('../../../analytics/AnalyticsUtils', () => ({
    screenViewLog: jest.fn(),
    userActionLogEvent: jest.fn(),
    setUserProperties: jest.fn(),
}));

jest.mock('../../utils/AppUtils', () => ({
    showErrorAlert: jest.fn(),
}));

jest.mock('../../store/reducers/clockInStatusSlice', () => ({
    clearClockInStatus: jest.fn(),
}));

jest.mock('../home/<USER>', () => ({
    callClockStatusAPI: jest.fn(),
}));

jest.mock('../../../components/EmptyStateCard', () => jest.fn(() => <></>));

jest.mock('../profile/section', () => {
    const React = require('react');
    return React.forwardRef((props, ref) => {
        const { testID, onPress } = props;
        return (
            <button ref={ref} testID={testID} onClick={onPress}>
                {testID}
            </button>
        );
    });
});


jest.mock('react-redux', () => ({
    useSelector: jest.fn(),
    useDispatch: () => jest.fn(),
}));

const mockNavigate = jest.fn();

jest.mock('@react-navigation/native', () => ({
    useNavigation: () => ({
        navigate: mockNavigate,
        replace: jest.fn(),
        addListener: jest.fn(() => jest.fn()),
    }),
    useIsFocused: jest.fn(() => true),
    useFocusEffect: jest.fn((cb) => {
        cb();
    }),
}));

// Mock useFetchStoreLocation
jest.mock('../../hooks/useFetchStoreLocation', () => jest.fn());

// Mock useFontScaleRerender
jest.mock('../../hooks', () => ({
    useUpdateEffect: jest.fn(),
    useFontScaleRerender: jest.fn(() => 0),
}));

// Mock pantry-design-system
jest.mock('pantry-design-system', () => ({
    useTheme: jest.fn(() => ({
        theme: {
            colors: {
                pdsThemeColorBackgroundPrimary: '#000000',
                pdsThemeColorNeutralLow: '#FFFFFF',
                pdsThemeColorBackgroundSunken: '#fff',
                pdsThemeColorBrandPrimary: '#007AFF',
            },
            fonts: { pdsGlobalFontFamilyPoppins: 'Poppins' },
            dimensions: { pdsGlobalSpace400: 16 },
            typography: { pdsGlobalFontSize500: 18 },
            borderDimens: {
                pdsGlobalBorderRadius200: 8,
                pdsGlobalBorderWidth100: 1,
            },
        },
    })),
    Heading: jest.fn(({ title, testID }) => <text testID={testID}>{title}</text>),
    Text: jest.fn(({ text }) => <text>{text}</text>),
    TextLink: jest.fn(({ onPress, label, testID }) => (
        <button onClick={onPress} testID={testID}>
            {label}
        </button>
    )),
    BottomSheet: jest.fn(({ children, testID }) => <div testID={testID}>{children}</div>),
    Button: jest.fn(({ onPress, label, testID }) => (
        <button onClick={onPress} testID={testID}>
            {label}
        </button>
    )),
}));

jest.mock("../../../assets/icons/WarningIcon", () => ({
    WarningIcon: () => <></>,
}));

// Mock i18n
jest.mock('react-i18next', () => ({
    useTranslation: () => ({
        t: (key: string) => key,
    }),
}));

// Mock children components
jest.mock('../../../components/EmptyStatusCard/EmptyStatusCard', () => jest.fn(() => <></>));

jest.mock('../../../components/OffShiftMessage', () => jest.fn(() => <></>));


jest.mock('../../../app/store/selectors/governance', () => ({
    shouldDisplayFeature: jest.fn(() => true),
}));

describe('ResourceScreen Accessibility', () => {
    let mockSetRef: jest.Mock;
    let mockSetLastFocused: jest.Mock;
    let mockHandleScreenFocus: jest.Mock;
    beforeEach(() => {
        mockSetRef = jest.fn();
        mockSetLastFocused = jest.fn();
        mockHandleScreenFocus = jest.fn();

        jest.spyOn(governanceSelectors, 'shouldDisplayFeature').mockReturnValue(true);
        // Mock the accessibility focus hook
        jest.spyOn(accessibility, 'useAccessibilityFocus').mockReturnValue({
            setRef: mockSetRef,
            setLastFocused: mockSetLastFocused,
            handleScreenFocus: mockHandleScreenFocus,
        });
        jest.clearAllMocks();
        jest.spyOn(redux, 'useSelector').mockImplementation((selector) => {
            if (selector === featureFlagsSelectors.resourceTabEnabled) return true;
            if (selector === featureFlagsSelectors.resourcesLearningEnabled) return true;
            return true;
        });
    });
    it('should hide learning section when shouldDisplayFeature returns false', () => {
        jest.spyOn(governanceSelectors, 'shouldDisplayFeature').mockReturnValue(false);

        const { queryByTestId } = render(<ResourceScreen />);
        expect(queryByTestId('resources-section-learning')).toBeNull();
    });

    it('should not render learning sections when feature flag is disabled', () => {
        jest.spyOn(redux, 'useSelector').mockImplementation((selector) => {
            if (selector === featureFlagsSelectors.resourcesLearningEnabled) return false;
            return true;
        });

        const { queryByTestId } = render(<ResourceScreen />);

        expect(queryByTestId('resources-section-learning')).toBeNull();
        expect(queryByTestId('assign-learning-testID')).toBeNull();
        expect(queryByTestId('journeys-testID')).toBeNull();
    });

    it('should render empty state when resource tab is disabled', () => {
        jest.spyOn(redux, 'useSelector').mockImplementation((selector) => {
            if (selector === featureFlagsSelectors.resourceTabEnabled) return false;
            return true;
        });

        render(<ResourceScreen />);
        expect(EmptyStatusCard).toHaveBeenCalled();
    });
});

describe('ResourceScreen', () => {
    jest.spyOn(reactNavigation, 'useNavigation').mockReturnValue({
        navigate: mockNavigate,
        addListener: jest.fn(),
    });
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should render EmptyStatusCard when resourceTabEnabled is false', () => {
        jest.spyOn(redux, 'useSelector').mockReturnValue(false);
        const { getByTestId } = render(<ResourceScreen />);
        expect(EmptyStatusCard).toHaveBeenCalled();
    });

    it('should not render EmptyStatusCard when resourceTabEnabled is true', () => {
        jest.spyOn(redux, 'useSelector').mockReturnValue(true);
        render(<ResourceScreen />);
        expect(EmptyStatusCard).not.toHaveBeenCalled();
    });

    it('should call setLastFocused and navigate on send feedback press', () => {
        jest.spyOn(redux, 'useSelector').mockReturnValue(true);
        const { getByTestId } = render(<ResourceScreen />);
        const sendFeedbackSection = getByTestId('resources-section-send-feedback');
        fireEvent.press(sendFeedbackSection);
        expect(mockNavigate).toHaveBeenCalledWith('WebViewScreen', {
            url: URLS.SEND_FEEDBACK,
            titleHeader: 'resourcesScreen.sendFeedback',
            previousTab: SCREENS.RESOURCES,
        });
    });

    it('should navigate to Hr-Payroll on second section press', () => {
        jest.spyOn(redux, 'useSelector').mockReturnValue(true);

        const { getByTestId } = render(<ResourceScreen />);
        const hrPayrollSection = getByTestId('resources-section-hr-payroll');

        fireEvent.press(hrPayrollSection);

        expect(mockNavigate).toHaveBeenCalledWith('Hr-Payroll');
    });

    it('should navigate to Learning on section press', () => {
        jest.spyOn(redux, 'useSelector').mockReturnValue(true);

        const { getByTestId } = render(<ResourceScreen />);
        const learningSection = getByTestId('resources-section-learning');
        fireEvent.press(learningSection);

        expect(mockNavigate).toHaveBeenCalledWith('WebViewScreen', {
            url: URLS.LEARNING,
            titleHeader: 'resourcesScreen.learning+',
            previousTab: SCREENS.RESOURCES,
        });
    });

    it('renders all SectionPreference components from RESOURCES_LINKS', () => {
        const { getByTestId } = render(<ResourceScreen />);

        expect(getByTestId('assign-learning-testID')).toBeTruthy();
        expect(getByTestId('journeys-testID')).toBeTruthy();
    });

    it('navigates to correct URL when Assigned Learning is pressed', () => {
        const { getByTestId } = render(<ResourceScreen />);

        fireEvent.press(getByTestId('assign-learning-testID'));

        expect(mockNavigate).toHaveBeenCalledWith(SCREENS.WEBVIEW, {
            url: URLS.ASSIGNED_LEARNINGS,
            titleHeader: 'resourcesScreen.assignedLearning',
            previousTab: SCREENS.RESOURCES,
        });
    });

    it('navigates to correct URL when Journeys is pressed', () => {
        const { getByTestId } = render(<ResourceScreen />);

        fireEvent.press(getByTestId('journeys-testID'));

        expect(mockNavigate).toHaveBeenCalledWith(SCREENS.WEBVIEW, {
            url: URLS.JOURNEYS,
            titleHeader: 'resourcesScreen.journeys',
            previousTab: SCREENS.RESOURCES,
        });
    });


    it('should render Learning section when isLearningEnabled and isLearningGovernanceEnabled is true', () => {
        jest.spyOn(redux, 'useSelector').mockImplementation((selector) => {
            if (selector === featureFlagsSelectors.resourceTabEnabled) return true;
            if (selector === featureFlagsSelectors.resourcesLearningEnabled) return true;
            return true;
        });

        jest.spyOn(governanceSelectors, 'shouldDisplayFeature').mockReturnValue(true);

        const { getByTestId } = render(<ResourceScreen />);
        expect(getByTestId('resources-section-learning')).toBeTruthy();
    });

    it('should not render Learning section when isLearningEnabled and isLearningGovernanceEnabled is false', () => {
        jest.spyOn(redux, 'useSelector').mockImplementation((selector) => {
            if (selector === featureFlagsSelectors.resourceTabEnabled) return true;
            if (selector === featureFlagsSelectors.resourcesLearningEnabled) return false;
            return true;
        });

        jest.spyOn(governanceSelectors, 'shouldDisplayFeature').mockReturnValue(false);

        const { queryByTestId } = render(<ResourceScreen />);
        expect(queryByTestId('resources-section-learning')).toBeNull();
    });

    it('should render learning resource links when isLearningEnabled and isLearningGovernanceEnabled is true', () => {
        jest.spyOn(redux, 'useSelector').mockImplementation((selector) => {
            if (selector === featureFlagsSelectors.resourceTabEnabled) return true;
            if (selector === featureFlagsSelectors.resourcesLearningEnabled) return true;
            return true;
        });

        jest.spyOn(governanceSelectors, 'shouldDisplayFeature').mockReturnValue(true);

        const { getByTestId } = render(<ResourceScreen />);
        expect(getByTestId('assign-learning-testID')).toBeTruthy();
        expect(getByTestId('journeys-testID')).toBeTruthy();
    });

    it('should not render learning resource links when isLearningEnabled and isLearningGovernanceEnabled is false', () => {
        jest.spyOn(redux, 'useSelector').mockImplementation((selector) => {
            if (selector === featureFlagsSelectors.resourceTabEnabled) return true;
            if (selector === featureFlagsSelectors.resourcesLearningEnabled) return false;
            return true;
        });

        jest.spyOn(governanceSelectors, 'shouldDisplayFeature').mockReturnValue(false);

        const { queryByTestId } = render(<ResourceScreen />);
        expect(queryByTestId('assign-learning-testID')).toBeNull();
        expect(queryByTestId('journeys-testID')).toBeNull();
    });

});

describe('ResourceScreen Learning Section rendering', () => {
    const setupSelectors = (isLearningEnabled: boolean, isFeatureValid: boolean) => {
        jest.spyOn(redux, 'useSelector').mockImplementation((selector) => {
            if (selector === featureFlagsSelectors.resourceTabEnabled) return true;
            if (selector === featureFlagsSelectors.resourcesLearningEnabled) return isLearningEnabled;
            return true;
        });
        jest.spyOn(governanceSelectors, 'shouldDisplayFeature').mockReturnValue(isFeatureValid);
    };

    const scenarios = [
        { isLearningEnabled: true, isLearningGovernanceEnabled: true, shouldRender: true },
        { isLearningEnabled: true, isLearningGovernanceEnabled: false, shouldRender: false },
        { isLearningEnabled: false, isLearningGovernanceEnabled: true, shouldRender: false },
        { isLearningEnabled: false, isLearningGovernanceEnabled: false, shouldRender: false },
    ];

    it.each(scenarios)(
        'should %s render Learning section when isLearningEnabled=%s, isLearningGovernanceEnabled=%s',
        ({ isLearningEnabled, isLearningGovernanceEnabled, shouldRender }) => {
            setupSelectors(isLearningEnabled, isLearningGovernanceEnabled);

            const { queryByTestId, getByTestId } = render(<ResourceScreen />);
            if (shouldRender) {
                expect(getByTestId('resources-section-learning')).toBeTruthy();
                expect(getByTestId('assign-learning-testID')).toBeTruthy();
                expect(getByTestId('journeys-testID')).toBeTruthy();
            } else {
                expect(queryByTestId('resources-section-learning')).toBeNull();
                expect(queryByTestId('assign-learning-testID')).toBeNull();
                expect(queryByTestId('journeys-testID')).toBeNull();
            }
        }
    );
});

import { StyleSheet } from 'react-native';

import { IPAD_WIDTH } from '../../shared/constants';

import type { Theme } from 'pantry-design-system';

export const IMAGE_HEIGHT = 195;
export const TABLET_IMAGE_HEIGHT = 400;

const getStyles = (
  { colors, dimensions, fonts, borderDimens, typography }: Theme,
  isTablet: boolean,
) => {
  const styles = StyleSheet.create({
    borderLine: {
      borderBottomWidth: 0.5,
      borderColor: colors.pdsThemeColorOutlineNeutralLow,
      height: 1,
      width: '100%',
    },
    learningSection: {
      backgroundColor: colors.pdsThemeColorBackgroundBaseline,
      borderColor: colors.pdsThemeColorOutlineNeutralLow,
      borderRadius: borderDimens.pdsGlobalBorderRadius200,
      borderWidth: borderDimens.pdsGlobalBorderWidth100,
      marginHorizontal: dimensions.pdsGlobalSpace400,
      marginTop: dimensions.pdsGlobalSpace400,
    },
    contentContainer: {
      alignSelf: 'center',
      flex: 1,
      width: isTablet ? IPAD_WIDTH : '100%',
    },
    heading1: {
      color: colors.pdsThemeColorForegroundNeutralHigh,
      fontFamily: fonts.pdsGlobalFontFamilyPoppins,
      fontSize: typography.pdsGlobalFontSize400,
      fontWeight: typography.pdsGlobalFontWeight500,
    },
    innerCard: {
      padding: dimensions.pdsGlobalSpace400,
      rowGap: dimensions.pdsGlobalSpace300,
    },
    resourceImage: {
      borderTopLeftRadius: borderDimens.pdsGlobalBorderRadius100,
      borderTopRightRadius: borderDimens.pdsGlobalBorderRadius100,
      height: isTablet ? TABLET_IMAGE_HEIGHT : IMAGE_HEIGHT,
      width: '100%',
    },
    row: {
      alignItems: 'center',
      flexDirection: 'row',
      gap: dimensions.pdsGlobalSpace200,
    },
    safeAreaContainer: {
      backgroundColor: colors.pdsThemeColorBackgroundSunken,
      flex: 1,
    },
    sectionStyles: {
      marginTop: dimensions.pdsGlobalSpace100,
    },
    sidePadding: {
      paddingVertical: dimensions.pdsGlobalSpace400,
    },
    headingStyles: {
      paddingHorizontal: dimensions.pdsGlobalSpace500,
      paddingBottom: dimensions.pdsGlobalSpace200,
    },
    descriptionContainer: {
      alignItems: 'center',
      gap: dimensions.pdsGlobalSpace600,
      justifyContent: 'center',
      marginHorizontal: dimensions.pdsGlobalSpace200,
      marginTop: dimensions.pdsGlobalSpace200,
    },
    buttonView: {
      gap: dimensions.pdsGlobalSpace500,
      marginBottom: dimensions.pdsGlobalSpace800,
      marginTop: dimensions.pdsGlobalSpace700,
      paddingHorizontal: dimensions.pdsGlobalSpace400,
    },
    buttonContainer: {
      alignItems: 'center',
      backgroundColor: colors.pdsThemeColorBackgroundBaseline,
      borderColor: colors.pdsThemeColorBackgroundNeutralLow,
      borderRadius: borderDimens.pdsGlobalBorderRadius1000,
      borderWidth: borderDimens.pdsGlobalBorderWidth100,
      flexDirection: 'row',
      height: dimensions.pdsGlobalSpace1000,
      justifyContent: 'center',
      width: '100%',
    },
  });
  return styles;
};

export default getStyles;

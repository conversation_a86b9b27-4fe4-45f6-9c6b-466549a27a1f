import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { Heading, Text, useTheme, BottomSheet, Button } from 'pantry-design-system';
import React, { useCallback, useEffect, useMemo, useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { Image, ScrollView, View, Pressable, Linking, Platform, AppState } from 'react-native';
import { SvgXml } from 'react-native-svg';
import { useSelector, useDispatch } from 'react-redux';
import images from '../../../assets/images';
import { help, learning, learningList, wallet } from '../../../assets/images/svg/profileIcons';
import { ResourcesEmptyImage } from '../../../assets/images/svg/resourcesEmptyImage';
import EmptyStatusCard from '../../../components/EmptyStatusCard/EmptyStatusCard';
import OffShiftMessage from '../../../components/OffShiftMessage';
import { useFontScaleRerender } from '../../hooks';
import useFetchStoreLocation from '../../hooks/useFetchStoreLocation';
import { useNetworkAlert } from '../../hooks/useNetworkAlert';
import {
  ADA_ROLES,
  HEADING_SIZE,
  RESOURCES_LINKS,
  SCREENS,
  TEXT_ALIGN,
  TEXT_PANTRY_COLORS,
  TEXT_SIZE,
  TEXT_WEIGHT,
  TEXTLINK_COLORS,
  URLS,
  ImportantForAccessibility,
  BUTTON_SIZE,
  FeatureKey
} from '../../shared/constants';
import { clearClockInStatus } from '../../store/reducers/clockInStatusSlice';
import {
  resourcesLearningEnabled,
  resourceTabEnabled,
  roleGovernanceEnabled,
} from '../../store/selectors/featureFlagsSelectors';
import { callClockStatusAPI } from '../home/<USER>'; // Importing the function to handle clock in status
import SectionPreference from '../profile/profilePreference/section';
import Section from '../profile/section';
import getStyles from './styles';
import { userActionLogEvent, screenViewLog } from '../../../analytics/AnalyticsUtils';
import { RESOURCES_ANALYTICS } from '../../../analytics/AnalyticsConstants';
import { useAccessibilityFocus } from '../../providers/AccessibilityFocus';
import { WarningIcon } from '../../../assets/icons/WarningIcon';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { setPreventTabsAccessibility, setPreventHeaderAccessibility } from '../../store/reducers/accessibilitySlice';
import { shouldDisplayFeature } from '../../../app/store/selectors/governance';
import { scheduleTabEnabled } from '../../store/selectors/featureFlagsSelectors';

export default function ResourceScreen() {
  useFetchStoreLocation();
  const { t } = useTranslation();
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const key = useFontScaleRerender();
  const scheduleEnabled = useSelector(scheduleTabEnabled);

  const isTablet = useSelector(
    (state: { deviceInfo: { isTablet: boolean } }) => state.deviceInfo.isTablet,
  );
  const { theme } = useTheme(); // Fetching themes from ThemeProvider
  const styles = getStyles(theme, isTablet); // Fetching styles based on theme
  const resourceEnabled = useSelector(resourceTabEnabled);
  const isLearningEnabled = useSelector(resourcesLearningEnabled);
  const primaryColor = theme.colors.pdsThemeColorBackgroundPrimary; // Primary color from theme
  const { setLastFocused, setRef, handleScreenFocus, restoreLastFocus } = useAccessibilityFocus();
  const resourceLinks = RESOURCES_LINKS(t);
  const { showAlertWithRetry, checkInternetConnection } = useNetworkAlert();
  const [externalView, setexternalView] = useState<boolean>(false);
  const [externalUrl, setexternalUrl] = useState<string>("");
  const appState = useRef(AppState.currentState);
  const { isUserInStore, userType } = useSelector((state: any) => state?.profile);
  const isClockedIn = useSelector((state: any) => state.shift.clockedIn);
  const isRoleGoveranceEnabled = useSelector(roleGovernanceEnabled);

  useEffect(() => {
    const unsubscribeFocus = navigation.addListener('focus', () => {
      handleScreenFocus(SCREENS.RESOURCES);
    });

    return () => {
      unsubscribeFocus?.();
    };
  }, [navigation]);

  // Calls the clock status API and handles network connectivity.
  const clockStatusAPIcall = async () => {
    const isOnline = await checkInternetConnection();
    // Checks network connection and optionally retries the given method if offline.
    if (!isOnline) await showAlertWithRetry(clockStatusAPIcall);
    callClockStatusAPI(dispatch); // get clock in status on home screen load
  };

  /**
   * - Handles the press action for the "HR and Payroll" section.
   * - Saves the accessibility focus state for restoring later.
   * - Navigates to the HR and Payroll screen
   */
  const onPressHrAndPayroll = (): void => {
    setLastFocused(SCREENS.RESOURCES, 'resources-section-hr-payroll');
    navigation.navigate(SCREENS.HR_AND_PAYROLL);
    userActionLogEvent(
      RESOURCES_ANALYTICS.RESOURCES_CATEGORY,
      RESOURCES_ANALYTICS.HR_AND_PAYSTUBS,
      RESOURCES_ANALYTICS.RESOURCES_LABEL,
    );

  };

  /**
   * Handles the press action for the "Send Feedback" section.
   * - Saves the accessibility focus state for restoring later.
   * - Navigates to a WebView screen showing the feedback form.
   *
   * @param {string} url - URL of the feedback form.
   * @param {string} titleHeader - Localized header title for the screen.
   * @param {string} previousTab - Indicates the previous tab for back navigation.
   */
  const onPressSendFeedback = (): void => {
    setLastFocused(SCREENS.RESOURCES, 'resources-section-send-feedback');
    navigation.navigate(SCREENS.WEBVIEW, {
      url: URLS.SEND_FEEDBACK,
      titleHeader: t('resourcesScreen.sendFeedback'),
      previousTab: SCREENS.RESOURCES,
    });
    userActionLogEvent(
      RESOURCES_ANALYTICS.RESOURCES_CATEGORY,
      RESOURCES_ANALYTICS.SEND_FEEDBACK,
      RESOURCES_ANALYTICS.RESOURCES_LABEL,
    );
  };

  /**
   * Handles press event for the "Learning" resource section.
   * - Stores the last focused accessibility element ID for focus restoration.
   * - Navigates to the WebView screen with learning URL and title.
   */
  const onPressLearning = (): void => {
    setLastFocused(SCREENS.RESOURCES, 'resources-section-learning');
    navigation.navigate(SCREENS.WEBVIEW, {
      url: URLS.LEARNING,
      titleHeader: `${t('resourcesScreen.learning')}+`,
      previousTab: SCREENS.RESOURCES,
    });
    userActionLogEvent(
      RESOURCES_ANALYTICS.RESOURCES_CATEGORY,
      RESOURCES_ANALYTICS.LEARNING,
      RESOURCES_ANALYTICS.RESOURCES_LABEL,
    );
  };

  useEffect(() => {
    dispatch(setPreventTabsAccessibility(externalView));
    dispatch(setPreventHeaderAccessibility(externalView));
  }, [externalView, dispatch]);

  /**
   * Handles press event for general resource links.
   * - Stores the last focused accessibility element ID for focus restoration.
   * - Navigates to the WebView screen with the specified URL and title.
   *
   * @param url - The URL to open in the WebView.
   * @param titleHeader - The title to display in the WebView header.
   * @param testID - A unique testID used to track which resource was last focused.
   */
  const onPressLinks = (url: string, titleHeader: string, testID: string, openInExternalBrowser?: boolean, event_action?: string): void => {
    setLastFocused(SCREENS.RESOURCES, testID);
    if (openInExternalBrowser && Platform.OS === 'android') {
      setexternalView(true);
      setexternalUrl(url);
    } else {
      navigation.navigate(SCREENS.WEBVIEW, {
        url,
        titleHeader,
        previousTab: SCREENS.RESOURCES,
      });
      userActionLogEvent(
        RESOURCES_ANALYTICS.RESOURCES_CATEGORY,
        event_action,
        RESOURCES_ANALYTICS.LEARNING_EVENT_LABEL,
      );
    }
  };

  const handleContinueInExternal = () => {
    if (externalUrl) userActionLogEvent(RESOURCES_ANALYTICS.RESOURCES_CATEGORY, RESOURCES_ANALYTICS.JOURNEYS, RESOURCES_ANALYTICS.LEARNING_EVENT_LABEL);
    Linking.openURL(externalUrl);
    handleExternalLinkBackPress();
  }

  const handleExternalLinkBackPress = () => {
    restoreLastFocus(SCREENS.RESOURCES);
    setexternalView(false);
  }

  const isLearningGovernanceEnabled = useMemo(() => {
    return shouldDisplayFeature(
      FeatureKey.RESOURCES_LEARNING,
      isUserInStore,
      isClockedIn,
      userType,
      true,
      isRoleGoveranceEnabled
    );
  }, [isUserInStore, isClockedIn, userType, isRoleGoveranceEnabled]);

  const logLearningScreenView = useCallback(() => {
    if (isLearningGovernanceEnabled && isLearningEnabled) {
      screenViewLog({ subsection1: RESOURCES_ANALYTICS.RESOURCES_CATEGORY });
    } else {
      screenViewLog({ subsection1: RESOURCES_ANALYTICS.RESOURCES_OFF_THE_CLOCK });
    }
  }, [isLearningGovernanceEnabled, isLearningEnabled]);

  useFocusEffect(
    useCallback(() => {
      if (scheduleEnabled) clockStatusAPIcall(); // get clock in status on resources screen load if schedule feature flag is enabled.
      return () => {
        if (scheduleEnabled) dispatch(clearClockInStatus()); // Clear clock in status on unmount
      };
    }, [scheduleEnabled]),
  );

  useEffect(() => {
    // Add an event listener to monitor changes in the app's state (foreground/background)
    const subscription = AppState.addEventListener('change', (nextAppState) => {
      // Check if the app is transitioning from inactive/background to active state
      if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
        logLearningScreenView()
      }
      // Update the current app state
      appState.current = nextAppState;
    });

    // Cleanup function to remove the event listener when the component unmounts
    return () => {
      subscription.remove();
    };
  }, []);

  const insets = useSafeAreaInsets();

  return (
    <View key={key} style={styles.safeAreaContainer} accessibilityElementsHidden={externalView} importantForAccessibility={externalView ? ImportantForAccessibility.NO_HIDE : ImportantForAccessibility.AUTO}>
      {!resourceEnabled ? (
        <EmptyStatusCard
          source={ResourcesEmptyImage}
          titleText={t('ResourcesEmptyView.emptyMessageTitle')}
          subTitleText={t('ResourcesEmptyView.emptyMessageSubTitle')}
          descriptionText={t('ResourcesEmptyView.emptyMessageDescription')}
        />
      ) : (
        <ScrollView style={styles.contentContainer} showsVerticalScrollIndicator={false}>
          {!isLearningGovernanceEnabled &&
            <OffShiftMessage />
          }
          {(isLearningEnabled && isLearningGovernanceEnabled) && (
            <View style={styles.learningSection}>
              <Image
                testID="profile-header-points-image"
                source={images.RESOURCES_IMAGE}
                style={styles.resourceImage}
                accessible={false}
                accessibilityIgnoresInvertColors
              />
              <View style={styles.innerCard}>
                <View style={styles.row}>
                  <SvgXml
                    testID="learning-icon"
                    xml={learningList(primaryColor)}
                    fill={theme.colors.pdsThemeColorBackgroundPrimary}
                  />
                  <Heading
                    testID="learning-section-heading"
                    size={TEXT_SIZE.MEDIUM}
                    color={TEXTLINK_COLORS.NEUTRAL_HIGH}
                    title={t('resourcesScreen.learning')}
                    headingAccessibilityLabel={`${t('resourcesScreen.learning')}`}
                  />
                </View>
                <Text
                  size={TEXT_SIZE.MEDIUM}
                  weight={TEXT_WEIGHT.REGULAR}
                  color={TEXTLINK_COLORS.NEUTRAL_MEDIUM}
                  text={t('resourcesScreen.learningDescription')}
                  accessibilityLabel={t('resourcesScreen.learningDescription')}
                />

                {resourceLinks.map((item, index) => (
                  <React.Fragment key={item.testID}>
                    {index < resourceLinks.length && <View style={styles.borderLine} />}
                    <SectionPreference
                      ref={setRef(item.testID)}
                      pdsHeading={t(item.label)}
                      onPress={() => onPressLinks(item.url, item.label, item.testID, item.openInExternalBrowser, item.event_action)}
                      testID={item.testID}
                      accessible={true}
                      text={t(item.text)}
                      accessibilityLabel={`${t(item.label)} ${t(item.text)}`}
                      accessibilityRole={ADA_ROLES.NONE}
                      accessibilityHint={t('ada.linkOpenInWebview')}
                    />
                  </React.Fragment>
                ))}
              </View>
            </View>
          )}
          <View style={styles.sidePadding}>
            <View style={styles.headingStyles}>
              <Heading
                testID={'more-resources-header-title'}
                textAlign={TEXT_ALIGN.LEFT}
                title={`${t('resourcesScreen.heading')}`}
                color={TEXT_PANTRY_COLORS.NEUTRAL_HIGH}
                size={HEADING_SIZE.Medium}
                headingAccessibilityLabel={`${t('resourcesScreen.heading')}`}
              />
            </View>
            {(isLearningEnabled && isLearningGovernanceEnabled) && (
              <Section
                ref={setRef('resources-section-learning')}
                testID="resources-section-learning"
                svgXml={learning(primaryColor)}
                pdsHeading={`${t('resourcesScreen.learning')}+`}
                onPress={onPressLearning}
                accessibilityRole={ADA_ROLES.NONE}
                headingAccessibilityLabel={`${t('resourcesScreen.learning')} + ${t('ada.linkOpenInWebview')}`}
                customStyles={styles.sectionStyles}
              />
            )}
            <Section
              ref={setRef('resources-section-hr-payroll')}
              testID="resources-section-hr-payroll"
              svgXml={wallet(primaryColor)}
              pdsHeading={t('resourcesScreen.hrAndPaystubs')}
              onPress={onPressHrAndPayroll}
              accessibilityRole={ADA_ROLES.NONE}
              headingAccessibilityLabel={`${t('resourcesScreen.hrAndPaystubs')} ${t('ada.button')}`}
              customStyles={styles.sectionStyles}
            />
            <Section
              ref={setRef('resources-section-send-feedback')}
              testID="resources-section-send-feedback"
              svgXml={help(primaryColor)}
              pdsHeading={t('resourcesScreen.sendFeedback')}
              onPress={onPressSendFeedback}
              accessibilityRole={ADA_ROLES.NONE}
              headingAccessibilityLabel={`${t('resourcesScreen.sendFeedback')} ${t('ada.linkOpenInWebview')}`}
              customStyles={styles.sectionStyles}
            />
          </View>
        </ScrollView>
      )}
      <BottomSheet
        sidePadding
        variant={'Modal'}
        theme={theme}
        testID="welcome-bottom-sheet"
        visibility={externalView}
        closeAccessibilityLabel={t('ada.closeButton')}
        onClose={handleExternalLinkBackPress}
        accessibilityViewIsModal={true}
        importantForAccessibility={ImportantForAccessibility.YES}
        renderHeader={<WarningIcon size={theme.dimensions.pdsGlobalSizeHeight800} />}
        renderContent={
          <View style={styles.descriptionContainer}>
            <Heading
              textAlign={TEXT_ALIGN.CENTER}
              title={t('externalModal.heading')}
              accessibilityLabel={t('externalModal.heading')}
              color={TEXTLINK_COLORS.NEUTRAL_HIGH}
              size={HEADING_SIZE.Medium}
              weight={TEXT_WEIGHT.SEMI_BOLD}
            />
            <Text
              text={t('externalModal.subtext')}
              size={TEXT_SIZE.LARGE}
              textAlign={TEXT_ALIGN.CENTER}
              weight={TEXT_WEIGHT.SEMI_BOLD}
              color={TEXTLINK_COLORS.NEUTRAL_HIGH}
            />
          </View>
        }
        renderAction={
          <View style={[styles.buttonView, { paddingBottom: insets.bottom }]}>
            <Button
              fullWidth
              theme={theme}
              size={BUTTON_SIZE.SMALL}
              label={t('externalModal.continueInExternal')}
              accessibilityHint={''}
              accessible
              accessibilityRole="none"
              accessibilityLabel={t('externalModal.continueInExternalADALabel')}
              onPress={handleContinueInExternal}
            />
            <Pressable
              onPress={handleExternalLinkBackPress}
              style={styles.buttonContainer}
              accessible={true}
              accessibilityHint={''}
              accessibilityLabel={t('ada.backButton')}
            >
              <Text
                text={t('externalModal.back')}
                size={TEXT_SIZE.LARGE}
                color={theme.colors.pdsThemeColorBackgroundPrimary}
                weight={TEXT_WEIGHT.SEMI_BOLD}
                textAlign={TEXT_ALIGN.CENTER}
              />
            </Pressable>
          </View>
        }
      />
    </View>
  );
}
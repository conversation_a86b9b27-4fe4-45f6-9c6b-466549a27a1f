import { StyleSheet, Dimensions, Platform } from "react-native";
import { Theme } from "pantry-design-system";

const getStyles = ({ colors, dimensions }: Theme) => {
    const styles = StyleSheet.create({
        container: {
            flex: 1,
            backgroundColor: colors.pdsThemeColorBackgroundSunken,
        },
        video: {
            width: '100%',
            height: Dimensions.get('window').height / 2.5,
            backgroundColor: Platform.OS === 'android' ? colors.pdsThemeColorForegroundNeutralHigh : colors.pdsThemeColorBackgroundBaseline,
        },
        loadingContainer: {
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0
        },
        contentContainer: {
            flexGrow: 1,
            alignItems: 'center',
            justifyContent: 'flex-start',
        },
    })
    return styles;

}
export default getStyles;
import FastImage from '@d11/react-native-fast-image';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { useTheme } from 'pantry-design-system';
import React, { useState, useCallback, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { View, ActivityIndicator, AppState, Dimensions, ScrollView } from 'react-native';
import RNFS from 'react-native-fs';
import Pdf from 'react-native-pdf';
import { SafeAreaView } from 'react-native-safe-area-context';
import Video from 'react-native-video';
import { useDispatch, useSelector } from 'react-redux';

import { LOGIN_ANALYTICS } from '../../../../analytics/AnalyticsConstants';
import { screenViewLog } from '../../../../analytics/AnalyticsUtils';
import images from '../../../../assets/images';
import { NoServer } from '../../../../assets/images/svg/NoServerConnection';
import StatusPlaceholder from '../../../../components/StatusPlaceholder/StatusPlaceholder';
import * as AsyncStorage from '../../../store/AsyncStorage';
import {
  fetchAnnouncementUrlRequest,
  setcacheImgUrls,
} from '../../../store/reducers/announcementSlice';
import {
  ANNOUNCEMENTS_DETAILS_PDF,
  ANNOUNCEMENTS_DETAILS_VIDEO,
  ANNOUNCEMENTS_DETAILS_IMAGE,
} from '../../home/<USER>';
import { getCachePathsForImages, removeCachedImages } from '../../home/<USER>';

import getStyles from './styles';

import type { RouteProp } from '@react-navigation/native';

const AnnouncementDetails = ({
  route,
}: {
  route: RouteProp<
    {
      params: {
        cardTitle: string;
        contentType: string;
        assetName: string;
        detailPageUrl: string;
        linkName?: string;
        position?: number;
      };
    },
    'params'
  >;
}) => {
  const fileType = route?.params?.contentType;
  const {
    urldetails: { content: details },
    urlDetailsLoading,
    urlDetailsError,
    cacheImgUrls,
  } = useSelector((state: any) => state.announcements);
  const navigation = useNavigation();
  const { theme } = useTheme();
  const styles = getStyles(theme);
  const dispatch = useDispatch();
  const isMedia =
    fileType === ANNOUNCEMENTS_DETAILS_PDF ||
    fileType === ANNOUNCEMENTS_DETAILS_VIDEO ||
    fileType === ANNOUNCEMENTS_DETAILS_IMAGE;
  const { cardTitle, linkName, position } = route?.params;
  const eventLabel = `${cardTitle}#${position}`;
  const [loading, setLoading] = useState(false);
  const [paused, setPaused] = useState(false);
  const pdfPathRef = useRef<string | null>(null);
  const imageCache = useRef<{ path: string; url: any }[]>([]);
  const { t } = useTranslation();
  let content = null;
  const appState = useRef(AppState.currentState);
  const [imageLoadError, setImageLoadError] = useState(false);
  const { height, width } = Dimensions.get('window');


  const fetchAnnouncementUrl = async () => {
    const requestId = await AsyncStorage.uniqueSessionId();
    dispatch(
      fetchAnnouncementUrlRequest({
        params: {
          'request-id': requestId,
          assetNames: route?.params?.assetName,
        },
      }),
    );
  };

  useFocusEffect(
    useCallback(() => {
      screenViewLog({
        subsection1: LOGIN_ANALYTICS.HOME,
        subsection2: LOGIN_ANALYTICS.ANNOUNCEMENTS,
        subsection3: cardTitle,
        event_category_link: linkName,
        event_label_link: eventLabel,
      });
      if (isMedia) fetchAnnouncementUrl();
      const parent = navigation.getParent();
      parent?.setOptions({
        tabBarStyle: { display: 'none' },
        headerTitle: route?.params?.cardTitle,
      });

      // Listen for app state changes to manage image cache URLs
      const subscription = AppState.addEventListener('change', (nextAppState) => {
        const downloadableUrl = imageCache?.current?.[0]?.url;
        if (!cacheImgUrls.includes(downloadableUrl) && fileType === ANNOUNCEMENTS_DETAILS_IMAGE) {
          dispatch(setcacheImgUrls([...cacheImgUrls, downloadableUrl]));
        }
        appState.current = nextAppState;
      });

      return () => {
        parent?.setOptions({
          tabBarStyle: undefined,
        });
        setPaused(true);
        clearCacheTempFiles();
        subscription.remove();
        content = null;
      };
    }, [navigation, route?.params?.assetName]),
  );

  // retryAgain retries fetching the announcement URL by invoking fetchAnnouncementUrl.
  const retryAgain = () => {
    fetchAnnouncementUrl();
  };

  // clearCacheTempFiles deletes temporary files related to the current announcement's media type.
  // For PDFs, it removes all .pdf and .pdf.tmp files from the cache directory.
  // For images, it removes the cached image corresponding to the current downloadable URL.
  const clearCacheTempFiles = async () => {
    if (fileType == ANNOUNCEMENTS_DETAILS_PDF) {
      const files = await RNFS.readDir(RNFS.CachesDirectoryPath);
      for (const file of files) {
        if (file.name.endsWith('.pdf') || file.name.endsWith('.pdf.tmp')) {
          await RNFS.unlink(file.path);
        }
      }
    } else if (fileType === ANNOUNCEMENTS_DETAILS_IMAGE) {
      for (const img of imageCache?.current) {
        await removeCachedImages([img?.path]);
      }
    }
  };

  // setCachePath fetches and stores the cache paths for the current image's downloadable URL.
  const setCachePath = async () => {
    const path = await getCachePathsForImages([details?.[0]?.downloadableUrl]);
    if (path) imageCache?.current?.push({ path: path[0], url: details?.[0]?.downloadableUrl });
  };
  // LoadingIndicator is a functional component that renders a loading spinner centered in the view.
  const LoadingIndicator = () => (
    <View style={styles.loadingContainer}>
      <ActivityIndicator color={theme.colors.pdsThemeColorBackgroundPrimary} />
    </View>
  );

  if (isMedia) {
    if (fileType === ANNOUNCEMENTS_DETAILS_PDF) {
      content = (
        <Pdf
          key={'Pdf-Viewer'}
          style={{ flex: 1, width: '100%' }}
          trustAllCerts={false}
          source={{ uri: `${details?.[0]?.downloadableUrl}}` }}
          renderActivityIndicator={() => <LoadingIndicator />}
          onLoadComplete={(numberofpages, filepath) => (pdfPathRef.current = filepath)}
          onError={(error) => {
            console.error('PDF Error: ', error);
          }}
        />
      );
    } else if (fileType === ANNOUNCEMENTS_DETAILS_VIDEO) {
      content = (
        <>
          {loading && <LoadingIndicator />}
          <Video
            key={details?.[0]?.downloadableUrl}
            source={{ uri: details?.[0]?.downloadableUrl }}
            style={styles.video}
            controls
            resizeMode="contain"
            onLoadStart={() => setLoading(true)}
            onLoad={() => setLoading(false)}
            paused={paused}
          />
        </>
      );
    } else if (fileType === ANNOUNCEMENTS_DETAILS_IMAGE) {
      content = (
        <ScrollView
          style={styles.container}
          contentContainerStyle={styles.contentContainer}
          maximumZoomScale={3}
          minimumZoomScale={1}
          showsHorizontalScrollIndicator={false}
          showsVerticalScrollIndicator={false}
          bounces={false}
          centerContent={true}
        >
          <FastImage
            source={
              imageLoadError && !urlDetailsLoading
                ? images.ANNOUNCEMENTS_ERROR_IMAGE
                : { uri: details?.[0]?.downloadableUrl }
            }
            style={{ width, height: '100%' }}
            resizeMode={FastImage.resizeMode.contain}
            onLoadEnd={setCachePath}
            onError={() => setImageLoadError(true)}
          />
        </ScrollView>
      );
    }
  }

  return (
    <SafeAreaView
      edges={['top', 'bottom']}
      style={styles.container}
      testID="announcement-details-screen"
    >
      {urlDetailsLoading ? <LoadingIndicator /> : content}
      {!urlDetailsLoading && !loading && urlDetailsError && (
        <StatusPlaceholder
          source={NoServer}
          status={t('noServerConnection')}
          styles={styles}
          showButton={true}
          buttonText={t('schedule.retryButton')}
          onPress={() => retryAgain()}
          buttonAccessHint={t('schedule.retryServerAccessibilityHint')}
        />
      )}
    </SafeAreaView>
  );
};

export default AnnouncementDetails;

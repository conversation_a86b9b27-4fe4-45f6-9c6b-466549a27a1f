import React from 'react';
import { render } from '@testing-library/react-native';
import AnnouncementDetails from './index';
import { useNavigation } from '@react-navigation/native';
import { useSelector, useDispatch } from 'react-redux';

// Mocks
jest.mock('@react-navigation/native', () => ({
    useNavigation: jest.fn(() => ({
        setOptions: jest.fn(),
    })),
    useFocusEffect: jest.fn((cb) => {
        cb();
    }),
    useRoute: jest.fn(() => ({
        params: {
          someParam: 'testValue', // customize based on your screen
        },
    })),
}));

jest.mock('../../../store/AsyncStorage', () => ({
    setItem: jest.fn(),
    getItem: jest.fn(),
    uniqueSessionId: jest.fn(),
}));
jest.mock('react-native-video', () => 'Video');
jest.mock('react-native-pdf', () => 'Pdf');

jest.mock('@react-native-firebase/app', () => ({
    getApp: jest.fn(() => ({
        name: '[DEFAULT]',
    })),
}));

jest.mock('react-redux', () => ({
    useSelector: jest.fn(),
    useDispatch: jest.fn(),
}));

jest.mock('../../../store/AsyncStorage', () => ({
    uniqueSessionId: jest.fn(() => Promise.resolve('mock-request-id')),
}));

describe('AnnouncementDetails Screen', () => {
    const mockSetOptions = jest.fn();
    const mockDispatch = jest.fn();

    beforeEach(() => {
        jest.clearAllMocks();
        (useDispatch as jest.Mock).mockReturnValue(mockDispatch);
        (useNavigation as jest.Mock).mockReturnValue({
            setOptions: mockSetOptions,
            getParent: () => ({ setOptions: jest.fn() }),
        });
    });

    const baseMockRoute = {
        params: {
            cardTitle: 'Test Title',
            contentType: 'PDF',
            assetName: 'test-asset.pdf',
            detailPageUrl: 'https://example.com',
        },
    };

    it('renders PDF viewer when contentType is PDF', () => {
        (useSelector as jest.Mock).mockImplementation((selectorFn: any) =>
            selectorFn({
                announcements: {
                    urldetails: { content: [{ downloadableUrl: 'https://example.com/file.pdf' }] },
                    urlDetailsLoading: false,
                    urlDetailsError: false,
                },
                profile: {
                    profile: { role: 'Associate' },
                },
            })
        );

        const { getByTestId } = render(<AnnouncementDetails route={baseMockRoute} />);
        expect(getByTestId('announcement-details-screen')).toBeTruthy();
    });

    it('renders Video player when contentType is Video', () => {
        const mockRoute = { ...baseMockRoute, params: { ...baseMockRoute.params, contentType: 'Video' } };

        (useSelector as jest.Mock).mockImplementation((selectorFn: any) =>
            selectorFn({
                announcements: {
                    urldetails: { content: [{ downloadableUrl: 'https://example.com/video.mp4' }] },
                    urlDetailsLoading: false,
                    urlDetailsError: false,
                },
                profile: {
                    profile: { role: 'Associate' },
                },
            })
        );

        const { getByTestId } = render(<AnnouncementDetails route={mockRoute} />);
        expect(getByTestId('announcement-details-screen')).toBeTruthy();
    });

    it('renders WebView for non-Video and non-PDF content', () => {
        const mockRoute = { ...baseMockRoute, params: { ...baseMockRoute.params, contentType: 'HTML' } };

        (useSelector as jest.Mock).mockImplementation((selectorFn: any) =>
            selectorFn({
                announcements: {
                    urldetails: { content: [{ downloadableUrl: 'https://example.com/page' }] },
                    urlDetailsLoading: false,
                    urlDetailsError: false,
                },
                profile: {
                    profile: { role: 'Associate' },
                },
            })
        );

        const { getByTestId } = render(<AnnouncementDetails route={mockRoute} />);
        expect(getByTestId('announcement-details-screen')).toBeTruthy();
    });

    it('renders fallback WebView when role is not Associate', () => {
        (useSelector as jest.Mock).mockImplementation((selectorFn: any) =>
            selectorFn({
                announcements: {
                    urldetails: { content: [] },
                    urlDetailsLoading: false,
                    urlDetailsError: false,
                },
                profile: {
                    profile: { role: 'Admin' },
                },
            })
        );

        const { getByTestId } = render(<AnnouncementDetails route={baseMockRoute} />);
        expect(getByTestId('announcement-details-screen')).toBeTruthy();
    });

    it('shows ActivityIndicator while loading', () => {
        (useSelector as jest.Mock).mockImplementation((selectorFn: any) =>
            selectorFn({
                announcements: {
                    urldetails: { content: [] },
                    urlDetailsLoading: true,
                    urlDetailsError: false,
                },
                profile: {
                    profile: { role: 'Associate' },
                },
            })
        );

        const { getByTestId } = render(<AnnouncementDetails route={baseMockRoute} />);
        expect(getByTestId('announcement-details-screen')).toBeTruthy();
    });
});

import { Theme } from "pantry-design-system";
import { StyleSheet } from "react-native";
import { IPAD_WIDTH } from "../../../shared/constants";
//customising styles based on themes
const getStyles = ({ colors, dimensions }: Theme) => {
  const styles = StyleSheet.create({
    mainContainer: {
      alignItems: "center",
      justifyContent: "center",
      flex: 1,
    },
    container: {
      padding: dimensions.pdsGlobalSpace500,
      backgroundColor: colors.pdsThemeColorBackgroundSunken,
      height: "100%",
    },
    tabContainer: {
      width: IPAD_WIDTH,
      alignSelf: "center",
      flex: 1,
      padding: dimensions.pdsGlobalSpace500,
      paddingBottom: dimensions.pdsGlobalSpace700,
    },
    chipsContainer: {
      marginBottom: dimensions.pdsGlobalSpace500,
      marginLeft: -dimensions.pdsGlobalSpace100,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0
    }
  });
  return styles;
};
export default getStyles;

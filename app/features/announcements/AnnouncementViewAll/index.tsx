import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { useTheme, Chip as FilterChips } from 'pantry-design-system';
import React, { useMemo, useRef, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { View, FlatList, AppState, ActivityIndicator } from 'react-native';
import { useSelector, useDispatch } from 'react-redux';

import ABAuditEngine from '../../../../analytics/ABAuditEngine';
import { LOGIN_ANALYTICS } from '../../../../analytics/AnalyticsConstants';
import { screenViewLog, userActionLogEvent } from '../../../../analytics/AnalyticsUtils';
import { APPD_PAGE_VIEW_METRIC_CONSTANTS } from '../../../../analytics/AppDynamicsConstants';
import { NoNetwork } from '../../../../assets/images/svg/NoNetworkConnection';
import { NoServer } from '../../../../assets/images/svg/NoServerConnection';
import AnnouncementsCard from '../../../../components/AnnouncementsCard';
import StatusPlaceholder from '../../../../components/StatusPlaceholder/StatusPlaceholder';
import { DURATION, ANNOUNCEMENT_VIEWALL_PATH } from '../../../config/endPoints';
import { useAccessibilityFocus } from '../../../providers/AccessibilityFocus';
import { SCREENS, USER_ROLES } from '../../../shared/constants';
import * as AsyncStorage from '../../../store/AsyncStorage';
import {
  fetchAllAnnouncementsRequest,
  setAllcacheImgUrls,
} from '../../../store/reducers/announcementSlice';
import { extractYouTubeVideoId } from '../../../utils/AppUtils';
import { isNetworkError } from '../../../utils/errorUtils';
import shouldApiTriggerCall from '../../../utils/LocalStorageUtil';
import {
  ANNOUNCEMENTS_VIEWALL_DAYS,
  ANNOUNCEMENTS_CHIPS,
  ANNOUNCEMENTS_VISIBILITY_ALL,
  ANNOUNCEMENTS_VISIBILITY_CORP,
  ANNOUNCEMENTS_DETAILS_IMAGE,
  ANNOUNCEMENTS_DETAILS_PDF,
  ANNOUNCEMENTS_DETAILS_VIDEO,
} from '../../home/<USER>';
import { getAllPreviewDownloadableUrls, cleanupImageCache } from '../../home/<USER>';

import getStyles from './styles';

const AnnouncementViewAll = ({ route }: any) => {
  const { t } = useTranslation();

  const { theme } = useTheme();
  const styles = getStyles(theme);
  const startTimeRef = useRef<number | null>(null);
  const navigation = useNavigation();

  const { isTablet } = useSelector((state: any) => state.deviceInfo);
  const {
    allAnnouncemnts: { content: announcements },
    allAnnouncemntsLoading,
    allAnnouncemntsError,
    allCacheImgUrls,
  } = useSelector((state: any) => state.announcements);
  const profileData = useSelector((state: any) => state.profile);
  const [selected, setSelected] = React.useState(t('announcements.chips.All'));
  const dispatch = useDispatch();
  const appState = useRef(AppState.currentState); // Ref to keep track of the current app state
  const intervalIdRef = useRef<NodeJS.Timeout | null>(null);
  const { setRef, setLastFocused, handleScreenFocus } = useAccessibilityFocus();

  useEffect(() => {
    const unsubscribeFocus = navigation.addListener('focus', () => {
      handleScreenFocus(SCREENS.ANNOUNCEMENTS_VIEW_ALL);
    });

    return () => {
      unsubscribeFocus?.();
    };
  }, [navigation]);

  // Fetch announcements from the API and dispatch to Redux store
  const fetchAnnouncements = async () => {
    const requestId = await AsyncStorage.uniqueSessionId();
    dispatch(
      fetchAllAnnouncementsRequest({
        params: {
          associateRole: profileData?.profile?.role ?? USER_ROLES.ASSOCIATE,
          divisionName: profileData?.profile?.divisionName ?? '',
          'request-id': requestId,
          visibility: profileData?.profile?.divisionName
            ? ANNOUNCEMENTS_VISIBILITY_ALL
            : ANNOUNCEMENTS_VISIBILITY_CORP,
          numDays: ANNOUNCEMENTS_VIEWALL_DAYS,
          requireAtLeastOneAnnouncement: true,
        },
      }),
    );
  };

  // Helper to check if announcements API should be called and fetch if needed
  const fetchAnnouncementsIfDue = useCallback(() => {
    shouldApiTriggerCall(ANNOUNCEMENT_VIEWALL_PATH, DURATION[ANNOUNCEMENT_VIEWALL_PATH])
      .then((shouldCallApi) => {
        if (shouldCallApi) fetchAnnouncements();
      })
      .catch((error) => {
        // Error
      });
  }, [fetchAnnouncements]);

  const clearAnnouncementsInterval = () => {
    if (intervalIdRef.current) {
      clearInterval(intervalIdRef.current);
      intervalIdRef.current = null;
    }
  };

  const startAnnouncementsInterval = useCallback(() => {
    fetchAnnouncementsIfDue();
    intervalIdRef.current = setInterval(
      fetchAnnouncementsIfDue,
      DURATION[ANNOUNCEMENT_VIEWALL_PATH] * 1000,
    );
  }, [fetchAnnouncementsIfDue]);

  const resetAnnouncementsInterval = async () => {
    await AsyncStorage.setItem(ANNOUNCEMENT_VIEWALL_PATH, '');
    if (intervalIdRef.current) clearInterval(intervalIdRef.current);
    startAnnouncementsInterval();
  };

  const linkName = route?.params?.linkName ?? '';

  useFocusEffect(
    useCallback(() => {
      screenViewLog({
        subsection1: LOGIN_ANALYTICS.HOME,
        subsection2: LOGIN_ANALYTICS.WHATS_NEW,
        event_label_link: linkName,
      });
      // Set up periodic fetch
      startAnnouncementsInterval();
      // Start the timer for measuring What's new Screen load start time
      startTimeRef.current = Date.now();
      ABAuditEngine.customTimerStart(
        APPD_PAGE_VIEW_METRIC_CONSTANTS.ANNOUNCEMENTS_DETAILS_VIEW_METRIC,
      );

      // Listen for app state changes to log screen view when app comes to foreground
      const subscription = AppState.addEventListener('change', (nextAppState) => {
        if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
          screenViewLog({
            subsection1: LOGIN_ANALYTICS.HOME,
            subsection2: LOGIN_ANALYTICS.WHATS_NEW,
            event_label_link: linkName,
          });
          fetchAnnouncementsIfDue();
        }
        appState.current = nextAppState;
      });

      // Cleanup function to cancel the interaction task and reset the timer reference.
      return () => {
        startTimeRef.current = null;
        subscription.remove();
        clearAnnouncementsInterval();
      };
    }, []),
  );

  // End the custom timer for Home Screen load when announcements finish loading (success or error)
  useEffect(() => {
    if (
      (!allAnnouncemntsLoading && (allAnnouncemntsError == null || !allAnnouncemntsError)) ||
      (!allAnnouncemntsLoading && allAnnouncemntsError)
    ) {
      const startTime = startTimeRef.current;
      if (startTime)
        ABAuditEngine.customTimerEnd(
          APPD_PAGE_VIEW_METRIC_CONSTANTS.ANNOUNCEMENTS_DETAILS_VIEW_METRIC,
        );
    }
    if (!allAnnouncemntsLoading && allAnnouncemntsError) {
      // Log an event when there is an error fetching announcements
      userActionLogEvent(
        LOGIN_ANALYTICS.HOME,
        LOGIN_ANALYTICS.CONNECTION_ERROR,
        LOGIN_ANALYTICS.WHATS_NEW,
      );
    }
  }, [allAnnouncemntsLoading, allAnnouncemntsError]);

  // Update cached image URLs when announcements change
  useEffect(() => {
    if (!announcements?.length) return;
    const newUrls = getAllPreviewDownloadableUrls(announcements);
    // Clean up old cached images and set new URLs in the store
    const cleanupAndSetUrls = async () => {
      await cleanupImageCache(newUrls, allCacheImgUrls);
      dispatch(setAllcacheImgUrls(newUrls));
    };
    cleanupAndSetUrls();
  }, [announcements]);

  const retryAgain = () => {
    userActionLogEvent(
      LOGIN_ANALYTICS.HOME,
      LOGIN_ANALYTICS.CONNECTION_ERROR_REFRESH_CLICK,
      LOGIN_ANALYTICS.WHATS_NEW,
    );
    resetAnnouncementsInterval();
  };

  // Handler for filter chip selection
  const handleFilterSelected = (value: string) => {
    const chip = `filter-${value}`;
    userActionLogEvent(LOGIN_ANALYTICS.WHATS_NEW, chip, LOGIN_ANALYTICS.ANNOUNCEMENTS);
    setSelected(value || ANNOUNCEMENTS_VISIBILITY_ALL);
  };

  // Memoize announcement filter chips, adding user's division if available
  const announcementChips = useMemo(() => {
    const chips = ANNOUNCEMENTS_CHIPS.map((chip) => ({
      ...chip,
      label: t(chip.label),
    }));
    const divisionName = profileData?.profile?.divisionName;
    if (divisionName) {
      chips.push({ label: t(divisionName), value: divisionName });
    }
    return chips;
  }, [profileData?.profile?.divisionName, t]);

  const filteredAnnouncements = useMemo(() => {
    const selectedChip = announcementChips.find((chip) => chip.label === selected);
    const selectedValue = selectedChip?.value ?? ANNOUNCEMENTS_VISIBILITY_ALL;
    if (selectedValue === ANNOUNCEMENTS_VISIBILITY_ALL) return announcements;
    return announcements?.filter((item: any) => item?.division?.includes(selectedValue));
  }, [selected, announcements, announcementChips]);

  // Navigate to the announcement details screen
  const readMore = (
    cardTitle: string,
    contentType: string,
    assetName: string,
    detailPageUrl: string,
    position: number,
    testID: string,
  ) => {
    if (
      contentType === ANNOUNCEMENTS_DETAILS_IMAGE ||
      contentType === ANNOUNCEMENTS_DETAILS_VIDEO ||
      contentType === ANNOUNCEMENTS_DETAILS_PDF
    ) {
      navigation.navigate(SCREENS.ANNOUNCEMENTS_DETAILS, {
        cardTitle,
        contentType,
        assetName,
        detailPageUrl,
        linkName: LOGIN_ANALYTICS.ANNOUNCEMENTS_WHATS_NEW,
        position,
      });
    } else {
      let updatedDetailPageUrl = detailPageUrl;
      const youTubeId = extractYouTubeVideoId(updatedDetailPageUrl || '');
      if (updatedDetailPageUrl && youTubeId) {
        // Generate the YouTube embed URL
        updatedDetailPageUrl = `https://www.youtube.com/embed/${youTubeId}`;
      }
      setLastFocused(SCREENS.ANNOUNCEMENTS_VIEW_ALL, testID);
      navigation.navigate(SCREENS.WEBVIEW, {
        url: updatedDetailPageUrl,
        titleHeader: cardTitle,
        showExternalLink: false,
      });
    }
  };

  return (
    <View style={isTablet ? styles.mainContainer : null}>
      {!allAnnouncemntsError && (
        <View
          testID="announcement-view-all-screen"
          style={isTablet ? styles.tabContainer : styles.container}
        >
          <View style={styles.chipsContainer}>
            <FilterChips
              testID="filter-chips"
              items={announcementChips?.map((item) => item.label)}
              selectedItems={selected}
              disabled={false}
              onSelected={handleFilterSelected}
              variant="Filter chip"
              accessible={true}
            />
          </View>
          {/* FlatList that displays the filtered announcements */}
          <FlatList
            testID="announcement-list"
            numColumns={isTablet ? 2 : 1}
            showsVerticalScrollIndicator={false}
            data={filteredAnnouncements}
            keyExtractor={(item, index) => item.id || index.toString()}
            renderItem={({ item, index }) => (
              <AnnouncementsCard
                key={index}
                index={index}
                itemsLength={filteredAnnouncements.length}
                testID={`announcement-card-` + index}
                announcement={item}
                onButtonPress={() =>
                  readMore(
                    item.title,
                    item.contentType,
                    item.assetName,
                    item.detailPageUrl,
                    index + 1,
                  )
                }
              />
            )}
            contentContainerStyle={{
              gap: 20,
              paddingBottom: isTablet ? 0 : theme.dimensions.pdsGlobalSpace1800,
            }}
            columnWrapperStyle={isTablet ? { gap: 20 } : null}
          />
        </View>
      )}
      {!allAnnouncemntsLoading &&
        allAnnouncemntsError &&
        (isNetworkError(allAnnouncemntsError) ? (
          <StatusPlaceholder
            source={NoNetwork}
            status={t('weekOrNoNetwork')}
            styles={styles}
            showButton={true}
            buttonText={t('errorPlaceholder.tryAgain')}
            onPress={() => retryAgain()}
            buttonAccessHint={t('schedule.retryNetworkAccessibilityHint')}
          />
        ) : (
          <StatusPlaceholder
            source={NoServer}
            status={t('noServerConnection')}
            styles={styles}
            showButton={true}
            buttonText={t('schedule.retryButton')}
            onPress={() => retryAgain()}
            buttonAccessHint={t('schedule.retryServerAccessibilityHint')}
          />
        ))}
      {allAnnouncemntsLoading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator color={theme.colors.pdsThemeColorBackgroundPrimary} />
        </View>
      )}
    </View>
  );
};

export default AnnouncementViewAll;

import React, { act } from "react";
import { render, screen, fireEvent } from "@testing-library/react-native";
import { Provider } from "react-redux";
import configureS<PERSON> from "redux-mock-store";
import AnnouncementViewAll from "../AnnouncementViewAll";
import { APPD_PAGE_VIEW_METRIC_CONSTANTS } from "../../../../analytics/AppDynamicsConstants";
import ABAuditEngine from "../../../../analytics/ABAuditEngine";
import { useNavigation } from '@react-navigation/native';

// Mocks for navigation and accessibility
jest.mock('../../../providers/AccessibilityFocus', () => ({
  useAccessibilityFocus: () => ({
    setRef: jest.fn(),
    setLastFocused: jest.fn(),
    restoreLastFocus: jest.fn(),
    setNavigatingBack: jest.fn(),
  }),
  AccessibilityFocusProvider: ({ children }) => children,
}));


jest.mock('@react-native-firebase/app', () => ({
  getApp: jest.fn(() => ({
    name: '[DEFAULT]',
  })),
}));

// Mock pantry-design-system
jest.mock("pantry-design-system", () => {
  const React = require("react");
  const { View, Text } = require("react-native");

  return {
    useTheme: jest.fn(() => ({
      theme: {
        colors: { pdsThemeColorBackgroundBaseline: "#ffffff" },
        fonts: { pdsGlobalFontFamilyPoppins: "Poppins" },
        dimensions: { pdsGlobalSpace400: 16 },
        typography: { pdsGlobalFontSize500: 18 },
        borderDimens: { pdsGlobalBorderRadius300: 8 },
      },
    })),
    Chip: jest.fn(({ items, selectedItems, onSelected }) => (
      <View testID="filter-chips">
        {items?.map((item) => (
          <Text
            key={item}
            testID={`chip-${item}`}
            onPress={() => onSelected(item)}
            style={{ fontWeight: selectedItems === item ? "bold" : "normal" }}
          >
            {item}
          </Text>
        ))}
      </View>
    )),
  };
});

// Mock react-navigation
jest.mock("@react-navigation/native", () => ({
  useNavigation: jest.fn(() => ({
    navigate: jest.fn(),
    addListener: jest.fn(),
  })),
  useFocusEffect: jest.fn((cb) => {
    cb();
  }),
}));

jest.mock("../../../../analytics/ABAuditEngine", () => ({
  customTimerStart: jest.fn(),
  customTimerEnd: jest.fn(),
}));
jest.spyOn(ABAuditEngine, 'customTimerStart').mockImplementation(jest.fn());
jest.spyOn(ABAuditEngine, 'customTimerEnd').mockImplementation(jest.fn());

// Mock AnnouncementsCard component
jest.mock("../../../../components/AnnouncementsCard", () => {
  const React = require("react");
  const { View, Text } = require("react-native");

  return ({ cardTitle, testID }) => (
    <View testID={`${testID}`}>
      <Text>{cardTitle}</Text>
    </View>
  );
});
jest.mock('../../../../components/StatusPlaceholder/StatusPlaceholder', () => 'StatusPlaceholder');
jest.mock("../../../store/AsyncStorage", () => ({
  uniqueSessionId: jest.fn(),
}));

const initialState = {
  shift: { clockedIn: false, clockedOut: false },
  deviceInfo: { isTablet: false },
  announcements: {
    allAnnouncemnts: {
      content: [
        {
          title: "Update One",
          linkTitle: "Read More",
          detailPageUrl: "http://example.com/1",
          division: ["Corporate"],
          contentType: "Announcement",
        },
        {
          title: "Update Two",
          linkTitle: "Read More",
          detailPageUrl: "http://example.com/2",
          division: ["Sales"],
          contentType: "Announcement",
        },
      ],
    },
  },
};

const mockStore = configureStore([]);
describe("AnnouncementViewAll", () => {
  let store = mockStore(initialState);
  const mockNavigate = jest.fn();

  beforeEach(() => {
    store = mockStore(initialState);
    useNavigation.mockReturnValue({ navigate: mockNavigate, addListener: jest.fn(), });

  });

  it("renders the component without crashing", () => {
    render(
      <Provider store={store}>
        <AnnouncementViewAll />
      </Provider>
    );
    expect(screen.getByTestId("announcement-view-all-screen")).toBeTruthy();
  });

  it("renders all announcement cards", () => {
    render(
      <Provider store={store}>
        <AnnouncementViewAll />
      </Provider>
    );

    ["1", "2"].forEach((id, index) => {
      expect(screen.getByTestId(`announcement-card-${index}`)).toBeTruthy();
    });
  });

  it("changes filter selection on chip press", () => {
    render(
      <Provider store={store}>
        <AnnouncementViewAll />
      </Provider>
    );

    const salesChip = screen.getByTestId("filter-chips");
    expect(salesChip).toBeTruthy();

    fireEvent.press(salesChip);
  });
  it("calls ABAuditEngine timers on focus and after interactions", async () => {
    jest.useFakeTimers();
    // Render screen — triggers useFocusEffect
    render(
      <Provider store={store}>
        <AnnouncementViewAll />
      </Provider>
    );

    // Fast-forward all timers
    await act(async () => {
      jest.advanceTimersByTime(2000);
    });

    // Now assert calls
    expect(ABAuditEngine.customTimerStart).toHaveBeenCalledWith(APPD_PAGE_VIEW_METRIC_CONSTANTS.ANNOUNCEMENTS_DETAILS_VIEW_METRIC);
    expect(ABAuditEngine.customTimerEnd).toHaveBeenCalledWith(APPD_PAGE_VIEW_METRIC_CONSTANTS.ANNOUNCEMENTS_DETAILS_VIEW_METRIC);
    jest.useRealTimers();
  });
  it('renders division-specific chip if divisionName exists in profileData', () => {
    const customState = {
      ...initialState,
      profile: {
        profile: {
          role: "Employee",
          divisionName: "Marketing",
        }
      }
    };
    const store = mockStore(customState);
    render(
      <Provider store={store}>
        <AnnouncementViewAll />
      </Provider>
    );

    expect(screen.getByTestId('chip-Marketing')).toBeTruthy();
  });
  it('calls handleFilterSelected when a chip is pressed', () => {
    const store = mockStore(initialState);
    render(
      <Provider store={store}>
        <AnnouncementViewAll />
      </Provider>
    );

    const chip = screen.getByTestId('chip-announcements.chips.Corporate');
    fireEvent.press(chip);

    // After chip press, selected filter should trigger re-fetch
    expect(chip.props.style.fontWeight).toBe("bold");
  });
  it('renders announcement cards with correct testIDs', () => {
    const store = mockStore(initialState);
    render(
      <Provider store={store}>
        <AnnouncementViewAll />
      </Provider>
    );

    expect(screen.getByTestId('announcement-card-0')).toBeTruthy();
    expect(screen.getByTestId('announcement-card-1')).toBeTruthy();
  })
  it('navigates to announcement details on button press', () => {
    const store = mockStore(initialState);
    render(
      <Provider store={store}>
        <AnnouncementViewAll />
      </Provider>
    );
    // Simulate button press from inside the announcement card
    const card = screen.getByTestId('announcement-card-0');
    fireEvent.press(card);
  });
  it("renders chip labels including division chip", () => {
    const customState = {
      ...initialState,
      profile: {
        profile: {
          role: "Employee",
          divisionName: "Marketing",
        }
      }
    };
    const store = mockStore(customState);
    render(
      <Provider store={store}>
        <AnnouncementViewAll />
      </Provider>
    );

    // Check for static chips
    expect(screen.getByTestId("chip-announcements.chips.All")).toBeTruthy();
    expect(screen.getByTestId("chip-announcements.chips.Corporate")).toBeTruthy();

    // Check for dynamic division chip
    expect(screen.getByTestId("chip-Marketing")).toBeTruthy();
  });

  it("calls navigation with correct params on card button press", () => {
    const customState = {
      ...initialState,
      profile: {
        profile: {
          role: "Employee",
          divisionName: "Marketing",
        }
      }
    };
    const store = mockStore(customState);
    render(
      <Provider store={store}>
        <AnnouncementViewAll />
      </Provider>
    );

    const firstCard = screen.getByTestId("announcement-card-0");
    fireEvent.press(firstCard);
  });
});

import { Dimensions, StyleSheet } from "react-native";
import { Theme } from "pantry-design-system";
import { IPAD_WIDTH } from "../../../shared/constants";

export default ({ colors, borderDimens, typography, fonts, dimensions }: Theme, isTablet: boolean) => {
    const { width } = Dimensions.get('window');
    const LAST_CARD_WIDTH = width * 0.6;
    const IPAD_LAST_CARD_WIDTH = IPAD_WIDTH * 0.55;
    const WIDTH_ADJUSTMENT = 50; // Adjusts the width for the carousel layout
    const CARD_WIDTH = width * 0.88;
    const IPAD_CARD_WIDTH = IPAD_WIDTH * 0.8;
    const cardWidth = isTablet ? IPAD_CARD_WIDTH : CARD_WIDTH;
    const MOBILE_FULL_FULL_WIDTH = width - WIDTH_ADJUSTMENT;
    const screenWidth = isTablet ? IPAD_WIDTH : MOBILE_FULL_FULL_WIDTH;
    const CARD_HEIGHT = dimensions.pdsGlobalSizeWidthMaxW2000 - dimensions.pdsGlobalSpace200;
    return StyleSheet.create({
        container: {
            alignSelf: 'center',
        },
        lastSlide: {
            backgroundColor: colors.pdsThemeColorBackgroundBaseline,
            width: isTablet ? IPAD_LAST_CARD_WIDTH : LAST_CARD_WIDTH,
            borderWidth: 1,
            flexWrap: "wrap",
            flex: 1,
            overflow: "hidden",
            borderColor: colors.pdsThemeColorOutlineNeutralLow,
            padding: dimensions.pdsGlobalSpace500,
            justifyContent: 'center',
            alignItems: 'center',
            borderRadius: borderDimens.pdsGlobalBorderRadius200,
            height: CARD_HEIGHT,
            marginLeft: dimensions.pdsGlobalSpace500,
            marginRight: dimensions.pdsGlobalSpace500,
        },
        text: {
            fontSize: typography.pdsGlobalFontSize550,
            color: typography.pdsGlobalFontWeight700,
            fontWeight: typography.pdsGlobalFontWeight700,
        },
        customText: {
            fontSize: typography.pdsGlobalFontSize300,
            fontWeight: typography.pdsGlobalFontWeight500,
            fontFamily: fonts.pdsGlobalFontFamilyPoppins,
            flexWrap: "wrap",
            width: isTablet ? "85%" : "100%",
            alignSelf: "center",
            alignContent: "center",
            textAlignVertical: "center",
            color: "#000000", // not mentioned in figma
            overflow: "hidden",
            textAlign: "center"
        },
        viewAllButton: {
            borderRadius: borderDimens.pdsGlobalBorderRadius300,
            backgroundColor: colors.pdsThemeColorForegroundPrimary,
            minWidth: dimensions.pdsGlobalSpace1600,
            minHeight: dimensions.pdsGlobalSpace800,
            overflow: "hidden",
            width: 162,
            alignItems: "center",
            justifyContent: "center",
            marginTop: dimensions.pdsGlobalSpace500,
        },
        viewAllText: {
            color: colors.pdsThemeColorBackgroundBaseline,
            fontSize: typography.pdsGlobalFontSize200,
            fontWeight: typography.pdsGlobalFontWeight600,
            fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
            textAlign: "center",
        },
        pagination: {
            flexDirection: 'row',
            alignSelf: 'center',
            marginTop: dimensions.pdsGlobalSpace200,
        },
        dot: {
            width: dimensions.pdsGlobalSpace100,
            height: dimensions.pdsGlobalSpace100,
            borderRadius: borderDimens.pdsGlobalBorderRadius100,
            backgroundColor: colors.pdsThemeColorBackgroundDisabled,
            margin: 5,
        },
        activeDot: {
            backgroundColor: colors.pdsThemeColorForegroundPrimary,
        },
        cardStyle: {
            marginHorizontal: dimensions.pdsGlobalSpace500,
            marginLeft: dimensions.pdsGlobalSpace500,
        },
        cardStylelast: {
            marginHorizontal: dimensions.pdsGlobalSpace1000,
            marginLeft: dimensions.pdsGlobalSpace500,
        },
        flatListStyle: {
            flexGrow: 0,
            width: screenWidth + WIDTH_ADJUSTMENT,
            paddingLeft: (screenWidth - cardWidth) / 2,
            paddingRight: (screenWidth - cardWidth) / 2 + dimensions.pdsGlobalSizeHeight500, // Adjust for the last card
        },
        lastCardContainer: {
            marginRight: dimensions.pdsGlobalSpace0
        }
    });
};

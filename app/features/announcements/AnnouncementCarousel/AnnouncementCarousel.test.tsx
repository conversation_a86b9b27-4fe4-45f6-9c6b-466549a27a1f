import React from "react";
import { render, fireEvent, act } from "@testing-library/react-native";
import AnnouncementCarousel from "../AnnouncementCarousel";
import { Provider } from "react-redux";
import configureStore from "redux-mock-store";
import getStyles from "./styles";
import { useTheme } from "pantry-design-system";
import moment from "moment";

jest.mock("pantry-design-system", () => ({
  useTheme: jest.fn(() => ({
    theme: {
      colors: {},
      fonts: {},
      dimensions: {},
      typography: {},
      borderDimens: {},
    }
  })),
  Heading: jest.fn(({ title, testID }) => <text testID={testID}>{title}</text>),
  Text: jest.fn(({ text, testID }) => <text testID={testID}>{text}</text>),
  TextLink: jest.fn(({ text, testID }) => <text testID={testID}>{text}</text>),

}));

jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

const mockStore = configureStore([]);
const store = mockStore({
  deviceInfo: { isTablet: false },
});

const mockAnnouncements = [
  { division: ["News"], title: "Announcement 1", startDate: moment().format("YYYY-MM-DD") },
  { division: ["Update"], title: "Announcement 2", startDate: "2025-03-17" },
  { division: ["Event"], title: "Announcement 3", startDate: "2025-03-16" },
  { division: ["Reminder"], title: "Announcement 4", startDate: "2025-03-15" },
  { division: ["New"], title: "Announcement 5", startDate: "2025-03-14" },
];

describe("AnnouncementCarousel Component", () => {
  const { theme } = useTheme();
  const styles = getStyles(theme, false);

  it("renders the component correctly", () => {
    const { getAllByTestId } = render(
      <Provider store={store}>
        <AnnouncementCarousel
          announcements={mockAnnouncements}
          onPressViewAll={jest.fn()}
          onPressReadMore={jest.fn()}
        />
      </Provider>
    );
    getAllByTestId("announcement-card-0").map((title) => title.children[0]);
  });

  it("calls onPressReadMore when clicking an announcement card", () => {
    const mockOnPressReadMore = jest.fn();
    const { getAllByTestId } = render(
      <Provider store={store}>
        <AnnouncementCarousel
          announcements={mockAnnouncements}
          onPressViewAll={jest.fn()}
          onPressReadMore={mockOnPressReadMore}
        />
      </Provider>
    );
    const firstAnnouncementButton = getAllByTestId("announcement-card-0")[0]
    fireEvent.press(firstAnnouncementButton);

    expect(mockOnPressReadMore).toHaveBeenCalled();
  });
});

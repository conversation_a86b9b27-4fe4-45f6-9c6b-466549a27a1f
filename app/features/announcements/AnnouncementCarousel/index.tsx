import moment from 'moment';
import { useTheme } from 'pantry-design-system';
import React, { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { View, Text, Dimensions, TouchableOpacity, Platform, FlatList } from 'react-native';
import { useSelector } from 'react-redux';

import AnnouncementsCard from '../../../../components/AnnouncementsCard';
import { IPAD_WIDTH } from '../../../shared/constants';

import getStyles from './styles';

import type { AnnouncementContent } from '../../../config/responseTypes';

const { width } = Dimensions.get('window');
const CARD_WIDTH = width * 0.88;
const IPAD_CARD_WIDTH = IPAD_WIDTH * 0.8;
const MAX_CARDS_COUNT = 4;
// Spacing between carousel cards
const CARD_SPACING = 8;
interface AnnouncementCarouselProps {
    announcements: AnnouncementContent[];
    todayPostsCount: number;
    onPressViewAll: () => void;
    testID: string;
    onPressReadMore: (
        title: string,
        contentType: string,
        assetName: string,
        detailPageUrl: string,
        position: number,
    ) => void;
}

const AnnouncementCarousel: React.FC<AnnouncementCarouselProps> = ({
    announcements,
    onPressViewAll,
    onPressReadMore,
    todayPostsCount,
    testID,
}) => {
    const { t } = useTranslation();
    const flatListRef = useRef<FlatList>(null);

    const [activeIndex, setActiveIndex] = useState(0);
    const [todaysAnnouncements, setTodaysAnnouncements] = useState<AnnouncementContent[]>([]);
    const [latestAnnouncement, setLatestAnnouncement] = useState<AnnouncementContent[]>([]);
    const isTablet = useSelector(
        (state: { deviceInfo: { isTablet: boolean } }) => state.deviceInfo.isTablet,
    );
    const { theme } = useTheme();
    const styles = getStyles(theme, isTablet);
    const cardWidth = isTablet ? IPAD_CARD_WIDTH : CARD_WIDTH;
    const carousalWidth = cardWidth + CARD_SPACING;

    const [cardHeights, setCardHeights] = useState<{ [key: number]: number }>({});

    const [listHeight, setListHeight] = useState<number>(0);

    const [fontScale, setFontScale] = useState(1);

    // Reset card heights when fontScale changes
    useEffect(() => {
        setCardHeights({});
        setListHeight(0);
    }, [fontScale, announcements]);

    // Handler to update FlatList height based on the tallest item
    const handleItemLayout = (index: number) => (event: any) => {
        const { height } = event.nativeEvent.layout;
        setCardHeights((prev) => {
            const newHeights = { ...prev, [index]: height };
            const maxHeight = Math.max(...Object.values(newHeights));
            setListHeight(maxHeight);
            return newHeights;
        });
    };

    useEffect(() => {
        if (!Array.isArray(announcements) || announcements?.length === 0) {
            setTodaysAnnouncements([]);
            setLatestAnnouncement([]);
            return;
        }
        const todayPosts = announcements?.filter(({ startDate }) =>
            moment.utc(startDate).isSame(moment.utc(), 'day'),
        );
        setTodaysAnnouncements(todayPosts?.slice(0, MAX_CARDS_COUNT + 1));
        const recent = findRecentAnnouncement();
        setLatestAnnouncement(todayPosts?.length ? [] : recent ? [recent] : []);
    }, [announcements]);

    const findRecentAnnouncement = (): AnnouncementContent | undefined => {
        const recentAnnouncements = announcements?.slice().sort((a, b) =>
            moment(b?.startDate)?.valueOf() - moment(a?.startDate).valueOf()
        );
        if (recentAnnouncements?.length === 0) return undefined;
        return recentAnnouncements?.reduce((latest, current) =>
            moment(current?.startDate)?.isAfter(moment(latest?.startDate)) ? current : latest,
        );
    };

    const onPressPagination = (index: number) => {
        flatListRef.current?.scrollToIndex({ index, animated: true });
        setActiveIndex(Math.round(index));
    };

    const todaysAnnouncementsLength = todaysAnnouncements?.length;
    const onMomentumScrollEnd = (event: any) => {
        const offsetX = event.nativeEvent.contentOffset.x;
        const index = Math.round(offsetX / carousalWidth);
        setActiveIndex(index);
    };
    return (
        <View style={styles.container} testID={testID}>
            <FlatList
                key={fontScale}
                ref={flatListRef}
                data={todaysAnnouncementsLength ? todaysAnnouncements : latestAnnouncement}
                onMomentumScrollEnd={onMomentumScrollEnd}
                renderItem={({ item, index }) => {
                    const isLastCard =
                        index === todaysAnnouncementsLength - 1 && todaysAnnouncementsLength > MAX_CARDS_COUNT;
                    if (isLastCard) {
                        return (
                            <View style={styles.lastCardContainer}>
                                <View style={styles.lastSlide} onLayout={handleItemLayout}>
                                    <Text numberOfLines={3} style={styles.customText}>
                                        {todayPostsCount - MAX_CARDS_COUNT} {t('announcements.announcementsMoreText')}
                                    </Text>
                                    <TouchableOpacity
                                        style={styles.viewAllButton}
                                        testID="view-all-button"
                                        onPress={onPressViewAll}
                                        accessible
                                        accessibilityRole="button"
                                        accessibilityLabel={t('viewAllAnnouncements')}
                                        accessibilityHint={
                                            Platform.OS === 'ios' ? t('viewAllAnnouncementsAccessibilityHint') : ''
                                        }
                                    >
                                        <Text style={styles.viewAllText}>{t('VIEWALL')}</Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        );
                    }
                    return (
                        <View style={{ marginRight: -30 }} onLayout={handleItemLayout}>
                            <AnnouncementsCard
                                key={index}
                                testID={`announcement-card-${index}`}
                                announcement={item}
                                onButtonPress={() =>
                                    onPressReadMore(
                                        item.title,
                                        item.contentType,
                                        item.assetName,
                                        item.detailPageUrl,
                                        index + 1,
                                    )
                                }
                                width={cardWidth}
                                style={
                                    index === todaysAnnouncementsLength - 1 ? styles.cardStylelast : styles.cardStyle
                                }
                                index={index}
                                itemsLength={todaysAnnouncementsLength}
                            />
                        </View>
                    );
                }}
                keyExtractor={(_, index) => index.toString()}
                horizontal
                pagingEnabled
                showsHorizontalScrollIndicator={false}
                style={[styles.flatListStyle, listHeight ? { height: listHeight } : null]}
                snapToInterval={carousalWidth}
                snapToAlignment="start"
                decelerationRate="fast"
                scrollEventThrottle={16} // smoother experience
                bounces={false}
            />
            {todaysAnnouncementsLength > 1 && (
                <View style={styles.pagination} accessible={false}>
                    {todaysAnnouncements.map((_, index) => (
                        <TouchableOpacity
                            key={index}
                            onPress={() => onPressPagination(index)}
                            accessible={false}
                        >
                            <View
                                testID="pagination-dot"
                                style={[styles.dot, activeIndex === index && styles.activeDot]}
                            />
                        </TouchableOpacity>
                    ))}
                </View>
            )}
        </View>
    );
};
export default AnnouncementCarousel;

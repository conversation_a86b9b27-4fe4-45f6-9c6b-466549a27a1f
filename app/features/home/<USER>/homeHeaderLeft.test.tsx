import React from "react";
import { render, screen, waitFor } from "@testing-library/react-native";
import { Provider } from "react-redux";
import HomeHeaderLeft from "../homeHeaderLeft";
import configureStore from "redux-mock-store";
import { useTheme } from "pantry-design-system";
import * as helpers from "../../../utils/helpers"; // Import the actual helpers to spy/mock later
import { Name } from "../../../misc/models/ProfileModal";
// Mock helpers
jest.mock("../../../utils/helpers", () => ({
  getUserLocation: jest.fn(() =>
    Promise.resolve({ latitude: 37.7749, longitude: -122.4194 })
  ),
  isInsideGeoFence: jest.fn(() => true),
  getFirstWordOfUserName: jest.fn((userName: Name) => {
    if (!userName) return "";
    const firstName = userName.preferredFirstName || userName.firstName;
    const words = (firstName ?? "").trim().split(" ");
    return words.length > 0 ? words[0] : "";
  }), // Mock the missing function
}));

jest.mock("react-native-permissions", () => ({
  PERMISSIONS: {
    ANDROID: {},
    IOS: {},
  },
  check: jest.fn(),
  request: jest.fn(),
}));

jest.mock("@react-native-community/geolocation", () => ({
  getCurrentPosition: jest.fn(),
  watchPosition: jest.fn(),
  clearWatch: jest.fn(),
  stopObserving: jest.fn(),
}));


// Mock pantry design system theme
jest.mock("pantry-design-system", () => ({
  useTheme: jest.fn(() => ({
    theme: {
      colors: { pdsThemeColorBackgroundBaseline: "#ffffff" },
      fonts: { pdsGlobalFontFamilyPoppins: "Poppins" },
      dimensions: { pdsGlobalSpace400: 16 },
      typography: { pdsGlobalFontSize500: 18 },
      borderDimens: { pdsGlobalBorderRadius300: 8 },
    },
  })),
}));

jest.mock("../styles", () =>
  jest.fn(() => ({
    headerLeft: {},
    addressText: {},
    welcomeText: {},
  }))
);

describe("HomeHeaderLeft", () => {
  const mockStore = configureStore([]);
  let store: any;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  store = mockStore({
    profile: {
      profile: {
        names: [{ firstName: "John", lastName: "Doe", preferredName: "John Do" }],
      },
      isUserInStore: true,
      slocData: {
        latitude: 37.7749,
        longitude: -122.4194,
        locationId: "123",
        address: {
          line1: "123 Main St",
          city: "City",
          state: "Country",
        },
      },
    },
    authorableContent: {
      authorableContent: {
        storeInfo: {
          storeRadius: 50
        }
      }
    }
  });

  // Default: clocked in
  jest.spyOn(helpers, "getUserLocation").mockResolvedValue({
    latitude: 37.7749,
    longitude: -122.4194,
    accuracy: 10,
  });

  jest.spyOn(helpers, "isInsideGeoFence").mockReturnValue(true);

  (useTheme as jest.Mock).mockReturnValue({ theme: "light" });

  it("renders the header when the user is clocked in", async () => {
    render(
      <Provider store={store}>
        <HomeHeaderLeft />
      </Provider>
    );

    await waitFor(() => {
      expect(screen.getByTestId("home-header-store-number")).toBeTruthy();
      expect(screen.getByTestId("home-header-store-address")).toBeTruthy();
      expect(screen.getByText("Store #123")).toBeTruthy();
      expect(screen.getByText("123 Main St, City, Country")).toBeTruthy();
    });
  });

  it("renders the banner image correctly when the user is clocked in", async () => {
    const { getByTestId } = render(
      <Provider store={store}>
        <HomeHeaderLeft />
      </Provider>
    );

    const image = await waitFor(() => getByTestId("header-banner-image"));
    expect(image.props.source).toEqual(
      require("../../../../assets/images/albertsons/home-logo.png")
    );
  });

  it("renders the welcome message when the user is not clocked in", async () => {
    store = mockStore({
      profile: {
        profile: {
          names: [{ firstName: "John", lastName: "Doe", preferredName: "John Do" }],
        },
        slocData: {
          latitude: 37.7749,
          longitude: -122.4194,
          locationId: "123",
          address: {
            line1: "123 Main St",
            city: "City",
            state: "Country",
          },
        },
      },
      authorableContent: {
        authorableContent: {
          storeInfo: {
            storeRadius: 50
          }
        }
      }
    });

    jest.spyOn(helpers, "getUserLocation").mockResolvedValue({
      latitude: 10.0,
      longitude: 10.0,
      accuracy: 10,
    });

    jest.spyOn(helpers, "isInsideGeoFence").mockReturnValue(false);

    const { getByTestId } = render(
      <Provider store={store}>
        <HomeHeaderLeft />
      </Provider>
    );

    await waitFor(() => {
      expect(getByTestId("home-header-firstname")).toBeTruthy();
      expect(getByTestId("home-header-firstname").props.children).toContain("John");
    });
  });
});
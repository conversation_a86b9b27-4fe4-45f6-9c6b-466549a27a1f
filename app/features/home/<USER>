/**
 * Component for displaying the shift starting soon.
 * Renders different content based on the user's in-store status.
 * Allows the user to clock in or check their schedule.
 */
import { useFocusEffect, useNavigation, useIsFocused, useRoute } from '@react-navigation/native';
import moment from 'moment';
import { useTheme, Heading, BottomSheet, Text, Button } from 'pantry-design-system';
import React, { useState, useMemo, useEffect, useRef, useCallback, RefObject } from 'react';
import { useTranslation } from 'react-i18next';
import {
  View,
  Text as RnText,
  Image,
  AppState,
  ActivityIndicator,
  ScrollView,
  TouchableOpacity,
  Platform,
  findNodeHandle,
  AccessibilityInfo,
} from 'react-native';
import { useSelector, useDispatch } from 'react-redux';

import ABAuditEngine from '../../../analytics/ABAuditEngine';
import { HOME_ANALYTICS, LOGIN_ANALYTICS } from '../../../analytics/AnalyticsConstants';
import { screenViewLog, userActionLogEvent } from '../../../analytics/AnalyticsUtils';
import { APPD_PAGE_VIEW_METRIC_CONSTANTS } from '../../../analytics/AppDynamicsConstants';
import useAccessibilityFocus from '../../../app/hooks/useAccessibilityFocus';
import { setPreciseLocationGranted } from '../../../app/store/reducers/locationAccessSlice';
import { shouldDisplayFeature } from '../../../app/store/selectors/governance';
import { checkLocationPermission, getUserLocation } from '../../../app/utils/helpers';
import icons from '../../../assets/icons';
import images from '../../../assets/images';
import { NoNetworkConnection } from '../../../assets/images/svg/NoNetworkConnection';
import { NoServerConnection } from '../../../assets/images/svg/NoServerConnection';
import EmptyStateCard from '../../../components/EmptyStateCard';
import RateLimitModal from '../../../components/RateLimitModal';
import StatusCard from '../../../components/StatusCard';
import StatusPlaceholder from '../../../components/StatusPlaceholder/StatusPlaceholder';
import { DURATION, ANNOUNCEMENT_PATH } from '../../config/endPoints';
import useFetchStoreLocation from '../../hooks/useFetchStoreLocation';
import { useNetworkAlert } from '../../hooks/useNetworkAlert';
import {
  SCREENS,
  STICKY_DAYS,
  TEXTLINK_SIZE,
  HEADING_SIZE,
  TEXTLINK_COLORS,
  FeatureKey,
  TEXT_DECORATION,
  USER_ROLES,
  STORE,
  ERROR_CODES,
} from '../../shared/constants';
import * as AsyncStorage from '../../store/AsyncStorage';
import { fetchAnnouncementsRequest, setcacheImgUrls } from '../../store/reducers/announcementSlice';
import { clearClockInStatus } from '../../store/reducers/clockInStatusSlice';
import { setHomeCelebrationCards, fetchProfileRequest, ProfileState } from '../../store/reducers/profileSlice';
import { setLunchStarted, setClockedIn } from '../../store/reducers/shiftSlice';
import {
  roleGovernanceEnabled,
  sharePointEnabled,
} from '../../store/selectors/featureFlagsSelectors';
import { showErrorAlert, extractYouTubeVideoId } from '../../utils/AppUtils';
import { checkAllCelebrations } from '../../utils/checkAllCelebrations';
import {
  generateCelebrationCards,
  getDismissedCelebrations,
} from '../../utils/dismissCelebrations';
import { isNetworkError, isRateLimitError } from '../../utils/errorUtils';
import shouldApiTriggerCall from '../../utils/LocalStorageUtil';
import { getCurrentTime } from '../../utils/TimeUtils';
import AnnouncementCarousel from '../announcements/AnnouncementCarousel';
import CelebrationCarousel from '../celebration';
import FullscreenConfetti from '../celebration/fullScreenConfetti';
import { SCHEDULE_STATUS } from '../schedule/scheduleConstants';

import {
  HOME_CONSTANTS,
  ANNOUNCEMENTS_DAYS,
  ANNOUNCEMENTS_VISIBILITY_ALL,
  ANNOUNCEMENTS_VISIBILITY_CORP,
  ANNOUNCEMENTS_DETAILS_IMAGE,
  ANNOUNCEMENTS_DETAILS_VIDEO,
  ANNOUNCEMENTS_DETAILS_PDF,
} from './homeConstants';
import { getAllPreviewDownloadableUrls, cleanupImageCache, callClockStatusAPI } from './homeUtils';
import getStyles from './styles';
import { CELEBRATION_ANALYTICS } from '../../../analytics/AnalyticsConstants';
import { scheduleTabEnabled } from '../../store/selectors/featureFlagsSelectors';
import { useUpdateEffect } from '../../hooks';
import { HttpStatusCode } from '../../config/errorCodes';

/**
 * Type for route params for Setting screen.
 *
 * @property {RefObject<View>} [backButtonRef] - Optional ref to the header back button.
 *   - On iOS, this ref is passed from the navigator to allow the screen to set accessibility focus
 *     to the back button when the screen is opened, improving VoiceOver accessibility.
 *   - On Android, this is not used; the header component itself manages focus.
 */
type RouteParams = { backButtonRef?: RefObject<View> };



const HomeScreen: React.FC = (_props) => {
  useFetchStoreLocation();
  const { t } = useTranslation();
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  const dateOptions = { weekday: 'long', month: 'short', day: 'numeric' };
  const tomorrowDate = tomorrow.toLocaleDateString('en-us', dateOptions);
  const currentTime = tomorrow.toLocaleTimeString([], {
    hour: '2-digit',
    minute: '2-digit',
  });

  const navigation = useNavigation();
  const dispatch = useDispatch();
  const isFocused = useIsFocused();
  const { showAlertWithRetry, checkInternetConnection } = useNetworkAlert();

  const {
    clockedIn,
    clockInTime,
    lunchStarted,
    lunchEnded,
    clockedOut,
    lateClockIn,
    lateClockedOut,
  } = useSelector((state: any) => state.shift);
  const shiftDuration = useSelector((state: any) => state.shift.shiftDuration);
  const isTablet = useSelector((state: any) => state.deviceInfo?.isTablet);
  const scheduleEnabled = useSelector(scheduleTabEnabled);

  const { theme } = useTheme(); // Fething themes from ThemeProvider
  // const { width: screenWidth } = useDimensions();
  const styles = getStyles(theme, isTablet); // Get styles from theme
  const [modalVisible, setModalVisible] = useState(false); // State to manage modal visibility
  const [lunchWillStart, setLunchWillStart] = useState(false); // New state to track lunch status
  const {
    data: { content: announcements },
    loading,
    error,
    cacheImgUrls,
  } = useSelector((state: any) => state.announcements);
  const profileData: ProfileState = useSelector((state: any) => state.profile);
  const isSharePointEnabled = useSelector(sharePointEnabled);
  const isGovernanceEnabled = useSelector(roleGovernanceEnabled);
  const appState = useRef(AppState.currentState); // Ref to keep track of the current app state
  const startTimeRef = useRef<number | null>(null); // Ref to keep track of the Home Screen load start time for analytics
  const intervalIdRef = useRef<NodeJS.Timeout | null>(null);
  const isInGeofence = useSelector((state: any) => state.profile.isUserInStore);
  const [isClockedIn, setIsClockedIn] = useState(false);
  const userState = profileData.userType;
  // Use shouldDisplayFeature to determine if Announcements should be shown
  const [isAnnouncementsVisible, setIsAnnouncementsVisible] = useState<boolean | undefined>(false);
  const { error: profileError } = useSelector((state: any) => state.profile);
  const { setLastFocused, setRef } = useAccessibilityFocus();
  const primaryColor = theme.colors.pdsThemeColorBackgroundPrimary; // Primary color from theme

  const {
    response: clockInResponse,
    error: clockInError,
    loading: clockInLoading,
  } = useSelector((state: any) => state.clockInStatus);

  const {
    TIME_TO_CLOCKOUT,
    CLOCKED_IN_AT,
    CLOCKED_IN,
    CLOCK_OUT_EARLY_CTA,
    ON_LUNCH,
    LUNCH_STARTED,
    LUNCH_END,
    ON_SHIFT,
    SHIFT_START,
    SHIFT_ALREADY_STARTED,
    SHIFT_STARTING_SOON,
    NEXT_SHIFT,
    READY_FOR_LUNCH,
    LUNCH_INFO,
    START_LUNCH,
    START_SHIFT,
    CLOCK_IN_TIME,
    CLOCK_IN_CTA,
    CLOKC_OUT_CTA,
    CHECK_SCHEDULE_CTA,
    SHIFT_ALREADY_ENDED,
  } = HOME_CONSTANTS;


  const navigatonRoute = useRoute<RouteProp<Record<string, RouteParams>, string>>();

  /**
 * Accessibility Focus Management for Back Button (iOS only)
 *
 * On iOS, when navigating to this screen, we want VoiceOver to focus the header back button for better accessibility.
 * This is achieved by retrieving the backButtonRef from navigation params (passed from the navigator)
 * and calling AccessibilityInfo.setAccessibilityFocus on it after a short delay.
 *
 * On Android, this logic is not needed here; the header component (ProfileHeaderLeft) manages its own focus.
 *
 * Note: This logic uses a timeout for reliability, as the header may not be ready immediately.
 */
  useFocusEffect(
    React.useCallback(() => {
      const backButtonRef = navigatonRoute.params?.backButtonRef;
      if (backButtonRef && backButtonRef.current) {
        setTimeout(() => {
          const tag = findNodeHandle(backButtonRef.current);
          if (tag) AccessibilityInfo.setAccessibilityFocus(tag);
        }, 700);
      }
    }, [navigatonRoute.params?.backButtonRef])
  );


  // Fetch announcements from the API and dispatch to Redux store
  const fetchAnnouncements = async () => {
    const requestId = await AsyncStorage.uniqueSessionId();
    dispatch(
      fetchAnnouncementsRequest({
        params: {
          associateRole: profileData?.profile?.role ?? USER_ROLES.ASSOCIATE,
          divisionName: profileData?.profile?.divisionName
            ? profileData?.profile?.divisionName
            : '',
          'request-id': requestId,
          visibility: profileData?.profile?.divisionName
            ? ANNOUNCEMENTS_VISIBILITY_ALL
            : ANNOUNCEMENTS_VISIBILITY_CORP,
          numDays: ANNOUNCEMENTS_DAYS,
          requireAtLeastOneAnnouncement: true,
        },
      }),
    );
  };

  // Helper to check if announcements API should be called and fetch if needed
  const fetchAnnouncementsIfDue = useCallback(() => {
    shouldApiTriggerCall(ANNOUNCEMENT_PATH, DURATION[ANNOUNCEMENT_PATH])
      .then((shouldCallApi) => {
        if (shouldCallApi) fetchAnnouncements();
      })
      .catch((error) => {
        // Error
      });
  }, [fetchAnnouncements]);

  const startAnnouncementsInterval = useCallback(() => {
    fetchAnnouncementsIfDue();
    intervalIdRef.current = setInterval(
      fetchAnnouncementsIfDue,
      DURATION[ANNOUNCEMENT_PATH] * 1000,
    );
  }, [fetchAnnouncementsIfDue]);

  // Calls the clock status API and handles network connectivity.
  const clockStatusAPIcall = async () => {
    const isOnline = await checkInternetConnection();
    // Checks network connection and optionally retries the given method if offline.
    if (!isOnline) await showAlertWithRetry(clockStatusAPIcall);
    callClockStatusAPI(dispatch, true); // get clock in status on home screen load
  };

  /**
   * Handles the event when the component loses focus.

   */
  const onLooseFocus = () => {
    dispatch(clearClockInStatus()); // Clear clock in status on unmount
  };

  useFocusEffect(
    useCallback(() => {
      screenViewLog({ subsection1: HOME_ANALYTICS.HOME_PAGE_VIEW });
      // Set up periodic fetch
      if (isAnnouncementsVisible) startAnnouncementsInterval();
      // checking existing celebrations
      const initCelebrations = async () => {
        try {
          const dismissed = await getDismissedCelebrations(); // checking dismissed celebrations
          const data = checkAllCelebrations(
            profileData?.profile?.dateOfBirth,
            profileData?.profile?.latestHireDate,
            STICKY_DAYS.BIRTHDAY,
            STICKY_DAYS.WORK_ANNIVERSARY,
          );

          const allCards = generateCelebrationCards(data);
          const filteredCards = allCards?.filter((card) => !dismissed?.includes(card?.id));
          dispatch(setHomeCelebrationCards(filteredCards)); //home celebration cards
        } catch (e) {
          // Fallback to empty arrays if error occurs
          dispatch(setHomeCelebrationCards([]));
        }
      };
      initCelebrations();

      // Listen for app state changes to log screen view when app comes to foreground
      const subscription = AppState.addEventListener('change', (nextAppState) => {
        if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
          screenViewLog({ subsection1: HOME_ANALYTICS.HOME_PAGE_VIEW });
          fetchAnnouncementsIfDue();

          checkLocationPermission().then((permissionStatus) => {
            dispatch(setPreciseLocationGranted(permissionStatus));
          });
        }
        appState.current = nextAppState;
      });

      // Start the timer for measuring Home Screen load start time
      startTimeRef.current = Date.now();
      ABAuditEngine.customTimerStart(APPD_PAGE_VIEW_METRIC_CONSTANTS.HOME_PAGE_VIEW_METRIC);
      if (scheduleEnabled) clockStatusAPIcall(); // Call clock status API on Home Screen load clockStatusAPIcall
      return () => {
        startTimeRef.current = null;
        subscription.remove();
        if (intervalIdRef.current) clearInterval(intervalIdRef.current);
        onLooseFocus(); // Call onLooseFocus when the screen loses focus
      };
    }, []),
  );

  const resetAnnouncementsInterval = async (): Promise<void> => {
    await AsyncStorage.setItem(ANNOUNCEMENT_PATH, '');
    if (intervalIdRef.current) clearInterval(intervalIdRef.current);
    startAnnouncementsInterval();
  };

  useEffect(() => {
    if (isAnnouncementsVisible) resetAnnouncementsInterval();
  }, [isAnnouncementsVisible]);

  useEffect(() => {
    const isAnnouncementEnable = shouldDisplayFeature(
      FeatureKey.ANNOUNCEMENTS,
      isInGeofence,
      isClockedIn,
      userState,
      isSharePointEnabled,
      isGovernanceEnabled,
    );
    setIsAnnouncementsVisible(isAnnouncementEnable);
  }, [isInGeofence, isClockedIn]);

  useEffect(() => {
    // End the custom timer for Home Screen load when announcements finish loading (success or error)
    if ((!loading && (error == null || !error)) || (!loading && error)) {
      const startTime = startTimeRef.current;
      if (startTime)
        ABAuditEngine.customTimerEnd(APPD_PAGE_VIEW_METRIC_CONSTANTS.HOME_PAGE_VIEW_METRIC);
    }
    if (!loading && error) {
      userActionLogEvent(
        LOGIN_ANALYTICS.HOME,
        LOGIN_ANALYTICS.CONNECTION_ERROR,
        LOGIN_ANALYTICS.CAROUSEL,
      );
    }
  }, [loading, error]);

  // Filter announcements to only those posted today
  const todayAnnouncements = useMemo(() => {
    const today = moment().utc().format('YYYY-MM-DD');
    return announcements?.filter(
      (item: any) => moment(item?.startDate).utc().format('YYYY-MM-DD') === today,
    );
  }, [announcements]);

  // Update cached image URLs when announcements change
  useEffect(() => {
    if (!announcements?.length) return;
    const newUrls = getAllPreviewDownloadableUrls(announcements);
    // Clean up old cached images and set new URLs in the store
    const cleanupAndSetUrls = async (): Promise<void> => {
      await cleanupImageCache(newUrls, cacheImgUrls);
      dispatch(setcacheImgUrls(newUrls));
    };
    cleanupAndSetUrls();
  }, [announcements]);

  useEffect(() => {
    if (!clockInResponse || !isFocused || clockInLoading) return;
    if (clockInResponse?.errors && clockInResponse?.errors.length > 0) {
      if (isClockedIn) setIsClockedIn(false); // Reset clocked in status only if it was true 
    } else if (clockInResponse && !clockInResponse?.errors) {
      setIsClockedIn(clockInResponse?.clockedIn ?? false);
    }
  }, [clockInResponse]);

  /**
   * Custom effect hook that triggers when the `clockInError` changes and the screen is focused.
   * Handles specific HTTP error statuses by displaying an API error alert and logs unhandled errors to the console.
   */
  useUpdateEffect(() => {
    if (isFocused && clockInError) {
      if (isClockedIn) setIsClockedIn(false); // Reset clocked in status only if it was true
    }
  }, [clockInError]);

  

  /* const handleClockIn = () => {
     setModalVisible(true);
   };
  
  const handleCheckSchedule = (event: GestureResponderEvent) => { };
  
  const handleLunchWillStart = () => {
    setLunchWillStart(true);
  };
  
  const handleEndLunch = () => {
    dispatch(setLunchEnded(true));
  };
  
  const handleClockOutEarly = () => {
    setTimeToClockOut(true);
  };
  
  const handleClockOut = () => {
    dispatch(setClockedOut(getCurrentTime("hh:mm A")));
    setTimeToClockOut(false);
  };*/

  //Navigate to View All Announcements
  const viewAll = (linkName: string): void => {
    setLastFocused('view-all');
    navigation.navigate(SCREENS.ANNOUNCEMENTS_VIEW_ALL, { linkName });
  };

  //Navigate to View All Announcements
  const readMore = (
    cardTitle: string,
    contentType: string,
    assetName: string,
    detailPageUrl: string,
    position: number,
  ) => {
    if (
      contentType === ANNOUNCEMENTS_DETAILS_IMAGE ||
      contentType === ANNOUNCEMENTS_DETAILS_VIDEO ||
      contentType === ANNOUNCEMENTS_DETAILS_PDF
    ) {
      navigation.navigate(SCREENS.ANNOUNCEMENTS_DETAILS, {
        cardTitle,
        contentType,
        assetName,
        detailPageUrl,
        position,
        linkName: LOGIN_ANALYTICS.ANNOUNCEMENTS_CAROUSEL,
      });
    } else {
      let updatedDetailPageUrl = detailPageUrl;
      const youTubeId = extractYouTubeVideoId(updatedDetailPageUrl || '');
      if (updatedDetailPageUrl && youTubeId) {
        // Generate the YouTube embed URL
        updatedDetailPageUrl = `https://www.youtube.com/embed/${youTubeId}`;
      }

      navigation.navigate(SCREENS.WEBVIEW, {
        url: updatedDetailPageUrl,
        titleHeader: cardTitle,
        showExternalLink: false,
      });
    }
  };

  const retryAgain = () => {
    userActionLogEvent(
      LOGIN_ANALYTICS.HOME,
      LOGIN_ANALYTICS.CONNECTION_ERROR_REFRESH_CLICK,
      LOGIN_ANALYTICS.CAROUSEL,
    );
    resetAnnouncementsInterval();
  };

  const handleTryagain = async (): Promise<void> => {
    const userLocation = await getUserLocation();
    const requestId = await AsyncStorage.uniqueSessionId();
    dispatch(
      fetchProfileRequest({
        requestId,
        fl: STORE,
        location: userLocation,
        isSilent: true,
      }),
    );
  };

  if (isRateLimitError(profileError)) {
    return <RateLimitModal handleTryagain={handleTryagain} />;
  }

  return (
    <View style={{ flex: 1 }}>
      <ScrollView style={styles.mainContainer}>
        {profileData?.homeCelebrationCards?.length > 0 && (
          <View style={styles.celebrationContainer}>
            <CelebrationCarousel
              showDismiss={true}
              celebrationCards={profileData?.homeCelebrationCards ?? []}
              analyticsCategory={CELEBRATION_ANALYTICS.HOME_EVENT_LABEL}
            />
          </View>
        )}
        <View style={isTablet ? styles.tabContainer : styles.container}>
          <View style={styles.emptyStyle}>

            {scheduleEnabled ? (
              <StatusCard
                clockedIn={isClockedIn}
              />
            ) : null}

            {/* Modal Component */}
            {lunchWillStart ? (
              <BottomSheet
                sidePadding
                height={0.3}
                variant="Modal"
                theme={theme}
                testID="lunch-bottom-sheet"
                visibility={lunchWillStart}
                heading={t(READY_FOR_LUNCH)}
                onClose={() => {
                  setLunchWillStart(false);
                }}
                renderHeader={
                  <Image
                    source={images.LUNCH_SHIFT}
                    tintColor={theme.colors.pdsThemeColorBackgroundPrimary}
                    style={styles.icon}
                  />
                }
                renderContent={
                  <View style={[styles.modalContent, { height: 160 }]}>
                    <Text
                      size="Large"
                      textAlign="center"
                      text={t(LUNCH_INFO)
                        .replace('{startTime}', clockInTime)
                        .replace('{lunchTime}', currentTime)}
                    />
                  </View>
                }
                renderAction={
                  <Button
                    fullWidth
                    theme={theme}
                    size={'Small'}
                    testID="confirm-lunch-bottom-sheet"
                    label={t(START_LUNCH)}
                    onPress={() => {
                      setLunchWillStart(false);
                      const formattedLunchTime = getCurrentTime();
                      dispatch(setLunchStarted(formattedLunchTime));
                    }}
                  />
                }
              />
            ) : (
              <BottomSheet
                sidePadding
                height={0.3}
                variant="Modal"
                theme={theme}
                testID="clock-in-bottom-sheet"
                visibility={modalVisible}
                heading={t(START_SHIFT)}
                onClose={() => {
                  setModalVisible(false);
                }}
                renderHeader={
                  <Image
                    source={icons.TIME_CLOCK_IN}
                    tintColor={theme.colors.pdsThemeColorBackgroundPrimary}
                    style={styles.icon}
                  />
                }
                renderContent={
                  <View style={styles.modalContent}>
                    <Text
                      size="Large"
                      textAlign="center"
                      text={t(CLOCK_IN_TIME).replace(
                        '{time}',
                        clockInTime != '' && clockInTime != null
                          ? clockInTime
                          : getCurrentTime('hh:mm A'),
                      )}
                    />
                  </View>
                }
                renderAction={
                  <Button
                    fullWidth
                    theme={theme}
                    testID="confirm-clock-in-button"
                    size={'Small'}
                    label={t(CLOCK_IN_CTA)}
                    onPress={() => {
                      setModalVisible(false);
                      dispatch(setClockedIn(getCurrentTime()));
                    }}
                  />
                }
              />
            )}
          </View>
          {/* Display the "View All" link and Announcement Carousel only when the diaaSharepoint feature flag is enabled and announcements contains data */}

          {/* Announcements Section */}
          {isAnnouncementsVisible ? (
            <View style={styles.announcementsContainer}>
              <View style={styles.causalCardSpace} />
              <View style={styles.announcements}>
                <View style={styles.badgeTagFlexBox}>
                  <Heading
                    size={HEADING_SIZE.Medium}
                    title={t('WHATSNEW')}
                    code={TEXTLINK_COLORS.NEUTRAL_HIGH}
                  />
                  {todayAnnouncements?.length > 0 && (
                    <View style={styles.badge}>
                      <RnText style={styles.badgeText}>
                        {todayAnnouncements?.length + t('announcementsPostedToday')}
                      </RnText>
                    </View>
                  )}
                </View>

                <TouchableOpacity
                  testID="view-all"
                  ref={setRef('view-all')}
                  onPress={() => viewAll(LOGIN_ANALYTICS.VIEW_ALL_ABOVE_CAROUSEL)}
                  accessibilityLabel={t('viewAllAnnouncements')}
                  accessible
                  accessibilityRole="button"
                  accessibilityHint={
                    Platform.OS === 'ios' ? t('viewAllAnnouncementsAccessibilityHint') : ''
                  }
                >
                  <Text
                    color={primaryColor}
                    text={t('VIEWALL')}
                    size={TEXTLINK_SIZE.LARGE}
                    textDecoration={TEXT_DECORATION.UNDERLINE}
                  />
                </TouchableOpacity>
              </View>
              {announcements && (
                <AnnouncementCarousel
                  announcements={announcements}
                  todayPostsCount={todayAnnouncements?.length}
                  onPressViewAll={() => viewAll(LOGIN_ANALYTICS.VIEW_ALL_CAROUSEL_CARD)}
                  onPressReadMore={readMore}
                  testID="announcement-carousel"
                />
              )}
              {!loading &&
                error &&
                (isNetworkError(error) ? (
                  <StatusPlaceholder
                    source={NoNetworkConnection}
                    status={t('weekOrNoNetwork')}
                    styles={styles}
                    showButton={true}
                    buttonText={t('errorPlaceholder.tryAgain')}
                    onPress={retryAgain}
                    buttonAccessHint={t('schedule.retryNetworkAccessibilityHint')}
                  />
                ) : (
                  <StatusPlaceholder
                    source={NoServerConnection}
                    status={t('noServerConnection')}
                    styles={styles}
                    showButton={true}
                    buttonText={t('schedule.retryButton')}
                    onPress={retryAgain}
                    buttonAccessHint={t('schedule.retryServerAccessibilityHint')}
                  />
                ))}
            </View>
          ) : (
            <EmptyStateCard
              title={t('commingSoonFeature')}
              accessibilityLabel={t('commingSoonFeatureDescription')}
              text={t('commingSoonFeatureDescription')}
            />
          )}
        </View>
      </ScrollView>
      <FullscreenConfetti />
      {loading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator color={theme.colors.pdsThemeColorBackgroundPrimary} />
        </View>
      )}
    </View>
  );
};

export default HomeScreen;

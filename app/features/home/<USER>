import React, { forwardRef } from "react";
import { TouchableOpacity } from "react-native";
import getStyles from "./styles";
import { useTheme } from "pantry-design-system";
import { SvgXml } from "react-native-svg";
import { CrossIcon } from "../../../assets/images/svg/ShiftDetailIcons";
import { useTranslation } from 'react-i18next';

/**
 * Header right cross component for app header commponent.
 * @param {Object} props - The props object.
 * @returns {React.FC} The header right cross component.
 */
interface HomeHeaderRightCrossProps {
  onBackPress: () => void;
}

const HomeHeaderRightCross = forwardRef<any, HomeHeaderRightCrossProps>(
  ({ onBackPress }, ref) => {
    const { theme } = useTheme();
    const styles = getStyles(theme);
    const { t } = useTranslation();
    return (
      <TouchableOpacity
        style={styles.headerRight}
        onPress={onBackPress}
        ref={ref}
        accessibilityLabel={t('ada.closeButton')}
      >
        <SvgXml
          xml={CrossIcon(theme.colors.pdsThemeColorOutlinePrimary)}
          accessible={false}
          style={styles.headerRight}
          testID="cross-icon"
        />
      </TouchableOpacity>
    );
  }
);

export default HomeHeaderRightCross;

/* eslint-disable @typescript-eslint/no-explicit-any */
import { useTheme } from 'pantry-design-system';
import React, { forwardRef } from 'react';
import { useTranslation } from 'react-i18next';
import { View, Text, Image } from 'react-native';
import { useSelector } from 'react-redux';

import Images from '../../../assets/images';
import { FALLBACK_BANNER } from '../../shared/constants';
import { getFirstWordOfUserName } from '../../utils/helpers';

import getStyles from './styles';

import type { AssociateProfile, Store, Name } from '../../misc/models/ProfileModal';
import type { ImageSourcePropType, View as RNView } from 'react-native';

const HomeHeaderLeft = forwardRef<RNView, {}>((_, ref) => {
  const { t } = useTranslation();

  const { theme } = useTheme(); // Fething themes from ThemeProvider
  const styles = getStyles(theme); // Fetching styles based on theme
  const profile: AssociateProfile = useSelector((state: any) => state.profile?.profile);
  const { banner, isUserInStore } = useSelector((state: any) => state.profile);
  const slocData: Store = useSelector((state: any) => state.profile?.slocData);

  const images: { logos: { [key: string]: ImageSourcePropType } } = {
    logos: {
      albertsons: Images.ALBERTSONS.ICON,
      safeway: Images.SAFEWAY.ICON,
      vons: Images.VONS.ICON,
      randalls: Images.RANDALLS.ICON,
      tomthumb: Images.TOMTHUMB.ICON,
      acmemarkets: Images.ACME.ICON,
      acme: Images.ACME.ICON,
      jewelosco: Images.JEWEL_OSCO.ICON,
      carrs: Images.CARRS.ICON,
      pavilions: Images.PAVILIONS.ICON,
      kingsfoodmarkets: Images.KINGS.ICON,
      shaws: Images.SHAWS.ICON,
      haggen: Images.HAGGEN.ICON,
      andronicos: Images.ANDRONICOS.ICON,
      starmarket: Images.START_MARKET.ICON,
      balduccis: Images.BALDUCCIS.ICON,
    },
  };

  const getBannerHeaderImage = (): ImageSourcePropType => {
    const storeImage = banner || slocData?.polarisBannerName || FALLBACK_BANNER;
    return images.logos[storeImage?.toLowerCase()?.replace(/[^\w]/g, '')] ?? Images.ALBERTSONS.ICON;
  };

  /**
   * Retrieves the first word of the user's first name, checking `preferredName` first
   * and falling back to `firstName` if `preferredName` does not exist.
   */
  const getUserName = (): string => {
    // Will return the first word from firstName or preferredName of the user data
    return profile?.names && profile?.names.length > 0
      ? getFirstWordOfUserName(profile?.names[0] as Name)
      : '';
  };

  return isUserInStore ? (
    // This is the header for the Home screen when the user is clocked in
    <View style={styles.headerLeft} ref={ref}>
      <Image
        testID="header-banner-image"
        source={getBannerHeaderImage()}
        accessibilityIgnoresInvertColors={false}
      />
      <View>
        <Text
          style={styles.addressText}
          testID="home-header-store-number"
          accessibilityIgnoresInvertColors={false}
          allowFontScaling={false}
        >
          Store #{slocData?.locationId}
        </Text>
        <Text
          style={styles.addressText}
          numberOfLines={1}
          allowFontScaling={false}
          testID="home-header-store-address"
        >
          {`${slocData?.address?.line1}, ${slocData?.address?.city}, ${slocData?.address?.state}`}
        </Text>
      </View>
    </View>
  ) : (
    // This is the header for the Home screen when the user is not clocked in
    <View style={styles.headerLeft}>
      <Text testID="home-header-firstname" style={styles.welcomeText} allowFontScaling={false}>
        {t('corporateError.hi')}, {getUserName()}!{' '}
      </Text>
    </View>
  );
});

export default HomeHeaderLeft;

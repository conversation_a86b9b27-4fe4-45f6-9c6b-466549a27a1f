import { NativeModules } from 'react-native';
import RNFS from 'react-native-fs';
const { FastImageCache } = NativeModules;
import * as AsyncStorage from "../../store/AsyncStorage";
import { fetchClockInStatusRequest } from "../../store/reducers/clockInStatusSlice";

// Extracts preview image URLs from an array of items
export const getAllPreviewDownloadableUrls = (items: any[]) => {
    return items.map(item => item.previewDownloadableImageUrl);
}

// Gets the cache file paths for a list of image URLs
export const getCachePathsForImages = async (urls: string[]): Promise<string[]> => {
    if (!urls?.length) return [];
    try {
        const cachePath = await FastImageCache.getSDWebImageDiskCachePath();
        const paths = await Promise.all(
            urls?.map(async url => {
                if (!url) return "";
                try {
                    const key = await FastImageCache.getSDWebImageCacheKey(url);
                    return `${cachePath}/${key}`;
                } catch {
                    return "";
                }
            })
        );
        return paths?.filter(Boolean);
    } catch {
        return [];
    }
};

// Finds URLs that are in existingImageUrls but not in newImageUrls
export const filterUrls = (existingImageUrls: string[], newImageUrls: string[]): string[] => {
    const newUrlSet = new Set(newImageUrls);
    return existingImageUrls?.length ? existingImageUrls?.filter(url => !newUrlSet.has(url)) : newImageUrls
}

// Removes cached image files from the file system
export const removeCachedImages = async (filePaths: string[]) => {
    await Promise.allSettled(
        filePaths?.map(async (path) => {
            const exists = await RNFS.exists(path);
            if (exists) await RNFS.unlink(path);
        })
    );
}

// Cleans up cache for images that are no longer needed
export const cleanupImageCache = (newUrls: string[], cachedImageUrls: string[]) => {
    const oldUrls = filterUrls(cachedImageUrls, newUrls);
    if (oldUrls?.length) {
        getCachePathsForImages(oldUrls)
            .then(cachePaths => removeCachedImages(cachePaths))
            .catch(console.error);
    }
}

/**
  * Handles the process of fetching the clock-in status for an employee.
  * 
  * This asynchronous function generates a unique session ID using AsyncStorage,
  * then dispatches a Redux action to request the clock-in status for a specific employee.
  */

export const callClockStatusAPI = async (
    dispatch: (action: any) => void,
    isFromHomeScreen?: boolean
): Promise<void> => {
    const requestId: string = await AsyncStorage.uniqueSessionId();
    dispatch(
        fetchClockInStatusRequest({
            requestId,
            isCallingFromHomeTab: isFromHomeScreen ?? undefined,
        })
    );
};



import { StyleSheet } from 'react-native';

import { IPAD_WIDTH } from '../../shared/constants';

import type { Theme } from 'pantry-design-system';

//customising styles based on themes
const getStyles = (
  { colors, borderDimens, dimensions, fonts, typography }: Theme,
  isTablet?: boolean,
) => {
  const styles = StyleSheet.create({
    addressText: {
      color: colors.pdsThemeColorForegroundNeutralHigh,
      display: 'flex',
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
      fontSize: typography.pdsGlobalFontSize100,
      fontStyle: 'normal',
      fontWeight: typography.pdsGlobalFontWeight400,
      lineHeight: 17.4,
      minWidth: dimensions.pdsGlobalSizeWidthMinW2000,
      paddingLeft: dimensions.pdsGlobalSpace200,
    },
    altButton: {
      alignSelf: 'center',
      backgroundColor: colors.pdsThemeColorBackgroundPrimary,
      borderRadius: borderDimens.pdsGlobalBorderRadius1000,
      height: 32,
      justifyContent: 'center',
      width: 124,
    },
    altButtonText: {
      color: colors.pdsThemeColorForegroundOnPrimary,
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
      fontSize: typography.pdsGlobalFontSize200,
      fontStyle: 'normal',
      fontWeight: typography.pdsGlobalFontWeight600,
      lineHeight: 20.3,
      textAlign: 'center',
    },
    announcements: {
      alignItems: 'center',
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
      marginBottom: 5,
      width: '100%',
    },
    announcementsContainer: {
      width: '100%',
    },
    badge: {
      alignSelf: 'center',
      backgroundColor: colors.pdsThemeColorBackgroundPrimaryLow,
      borderRadius: borderDimens.pdsGlobalBorderRadius100,
      marginHorizontal: typography.pdsGlobalFontSize50,
      paddingBottom: dimensions.pdsGlobalSpace50,
      paddingLeft: dimensions.pdsGlobalSpace100,
      paddingRight: dimensions.pdsGlobalSpace100,
      paddingTop: dimensions.pdsGlobalSpace50,
    },
    badgeTagFlexBox: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
    },
    badgeText: {
      color: colors.pdsThemeColorForegroundNeutralHigh,
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
      fontSize: typography.pdsGlobalFontSize50,
      fontWeight: typography.pdsGlobalFontWeight400,
      textAlign: 'center',
    },
    button: {
      alignSelf: 'stretch',
      backgroundColor: colors.pdsThemeColorBackgroundPrimary,
      borderRadius: borderDimens.pdsGlobalBorderRadius1000,
      height: dimensions.pdsGlobalSizeHeightMinH1000,
      justifyContent: 'center',
      marginBottom: dimensions.pdsGlobalSpace400,
      paddingVertical: dimensions.pdsGlobalSizeHeight100,
    },
    buttonText: {
      color: colors.pdsThemeColorBackgroundBaseline,
      fontSize: typography.pdsGlobalFontSize300,
      fontWeight: 'bold',
      textAlign: 'center',
    },
    card: {
      alignItems: 'flex-start',
      backgroundColor: colors.pdsThemeColorBackgroundBaseline,
      borderColor: colors.pdsThemeColorOutlineNeutralHigh,
      borderRadius: borderDimens.pdsGlobalBorderRadius200,
      borderWidth: borderDimens.pdsGlobalBorderWidth100,
      elevation: 8,
      flexDirection: 'row',
      justifyContent: 'space-between',
      padding: dimensions.pdsGlobalSpace400,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.2,
      shadowRadius: 16,
      width: '100%',
    },
    cardShiftStart: {
      alignItems: 'center',
      borderColor: colors.pdsThemeColorOutlineNeutralMedium,
      borderRadius: borderDimens.pdsGlobalBorderRadius200,
      borderWidth: borderDimens.pdsGlobalBorderWidth100,
      elevation: 8,
      justifyContent: 'space-between',
      padding: dimensions.pdsGlobalSpace400,
      shadowColor: '#000000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.2,
      shadowRadius: 16,
      width: '100%',
    },
    causalCardSpace: {
      height: dimensions.pdsGlobalSpace500,
    },
    celebrationContainer: {
      paddingHorizontal: dimensions.pdsGlobalSpace500,
      paddingTop: dimensions.pdsGlobalSpace300,
    },
    celebrationTabContainer: {
      alignSelf: 'center',
      marginTop: dimensions.pdsGlobalSpace100,
      paddingHorizontal: dimensions.pdsGlobalSpace500,
      paddingTop: dimensions.pdsGlobalSpace500,
      width: IPAD_WIDTH,
    },
    container: {
      alignItems: 'flex-start',
      paddingTop: dimensions.pdsGlobalSpace100,
      padding: dimensions.pdsGlobalSpace500,
    },
    containerStyles: {
      backgroundColor: colors.pdsThemeColorBackgroundBaseline,
      borderColor: colors.pdsThemeColorOutlineNeutralLow,
      borderRadius: borderDimens.pdsGlobalBorderRadius200,
      borderWidth: borderDimens.pdsGlobalBorderWidth100,
      height: dimensions.pdsGlobalSizeHeight2200,
      justifyContent: 'center',
      padding: dimensions.pdsGlobalSpace500,
    },
    dateTime: {
      color: colors.pdsThemeColorForegroundNeutralHigh,
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
      fontSize: typography.pdsGlobalFontSize300,
      fontWeight: typography.pdsGlobalFontWeight500,
      lineHeight: 23.2,
    },
    emptyStyle: {
      alignSelf: 'center',
      width: '100%',
    },
    headerLeft: {
      alignItems: 'center',
      flexDirection: 'row',
      width: dimensions.pdsGlobalSizeWidthMinW2000,
    },
    headerRight: {
      alignItems: 'center',
      flexDirection: 'row',
    },
    heading: {
      color: colors.pdsThemeColorForegroundNeutralHigh,
      fontFamily: fonts.pdsGlobalFontFamilyPoppins,
      fontSize: typography.pdsGlobalFontSize500,
      fontWeight: '500',
      lineHeight: 28,
      marginBottom: dimensions.pdsGlobalSpace100,
      textAlign: 'center',
    },
    icon: {
      height: dimensions.pdsGlobalSizeWidth400,
      marginBottom: dimensions.pdsGlobalSpace400,
      tintColor: colors.pdsThemeColorBackgroundPrimary,
      width: dimensions.pdsGlobalSizeWidth400,
    },
    imageStyle: {
      height: dimensions.pdsGlobalSizeHeight200,
      width: dimensions.pdsGlobalSizeWidth200,
    },
    linkText: {
      color: colors.pdsThemeColorForegroundPrimary,
      fontSize: typography.pdsGlobalFontSize300,
      fontWeight: typography.pdsGlobalFontWeight500,
      textDecorationLine: 'underline',
    },
    loadingContainer: {
      alignItems: 'center',
      bottom: 0,
      elevation: 10,
      justifyContent: 'center',
      left: 0,
      position: 'absolute',
      right: 0,
      top: 0,
      zIndex: 9999,
    },
    location: {
      color: colors.pdsThemeColorForegroundNeutralHigh,
      fontFamily: fonts.pdsGlobalFontFamilyPoppins,
      fontSize: typography.pdsGlobalFontSize300,
      fontStyle: 'normal',
      fontWeight: typography.pdsGlobalFontWeight500,
      textTransform: 'capitalize',
    },
    mainContainer: {
      flex: 1,
    },
    modalButton: {
      alignSelf: 'center',
      backgroundColor: colors.pdsThemeColorBackgroundPrimary,
      borderRadius: borderDimens.pdsGlobalBorderRadius1000,
      bottom: 30,
      height: dimensions.pdsGlobalSizeHeightMinH1000,
      justifyContent: 'center',
      marginBottom: dimensions.pdsGlobalSpace200,
      paddingVertical: dimensions.pdsGlobalSpace200,
      position: 'absolute',
      width: '100%',
    },
    modalClose: {
      padding: dimensions.pdsGlobalSpace200,
      position: 'absolute',
      right: dimensions.pdsGlobalSpace400,
      top: 12,
    },
    modalCloseText: {
      color: colors.pdsThemeColorForegroundNeutralHigh,
      fontSize: typography.pdsGlobalFontSize550,
    },
    modalContent: {
      alignItems: 'center',
      backgroundColor: colors.pdsThemeColorForegroundOnPrimary,
      borderTopLeftRadius: borderDimens.pdsGlobalBorderRadius300,
      borderTopRightRadius: borderDimens.pdsGlobalBorderRadius300,
      height: isTablet ? 248 : 160,
      padding: dimensions.pdsGlobalSpace500,
    },
    modalOverlay: {
      backgroundColor: colors.pdsGlobalColorGeneralBlackA50,
      flex: 1,
      justifyContent: 'flex-end',
    },
    modalSubtitle: {
      color: colors.pdsThemeColorForegroundNeutralHigh,
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
      fontSize: typography.pdsGlobalFontSize400,
      fontWeight: typography.pdsGlobalFontWeight400,
      lineHeight: 24,
      marginBottom: dimensions.pdsGlobalSpace600,
      textAlign: 'center',
    },
    modalTitle: {
      color: colors.pdsThemeColorForegroundNeutralHigh,
      fontFamily: fonts.pdsGlobalFontFamilyPoppins,
      fontSize: typography.pdsGlobalFontSize550,
      fontWeight: '500',
      lineHeight: 28,
      marginBottom: dimensions.pdsGlobalSpace400,
      textAlign: 'center',
    },
    notificationBadge: {
      position: 'absolute',
      left: 10, // 10 is not available in design tokens
      top: -dimensions.pdsGlobalSpace200,
      tintColor: colors.pdsThemeColorBackgroundPrimary,
      backgroundColor: colors.pdsThemeColorBackgroundPrimary,
      borderRadius: borderDimens.pdsGlobalBorderRadius1000,
      width: dimensions.pdsGlobalSizeWidth300,
      height: dimensions.pdsGlobalSizeHeight300,
      justifyContent: 'center',
      alignItems: 'center',
    },
    notificationIcon: {
      height: dimensions.pdsGlobalSizeHeight400,
      tintColor: colors.pdsThemeColorBackgroundPrimary,
      width: dimensions.pdsGlobalSizeWidth400,
    },
    notificationText: {
      color: colors.pdsThemeColorForegroundOnPrimary,
      display: 'flex',
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
      fontSize: typography.pdsGlobalFontSize100,
      fontStyle: 'normal',
      fontWeight: typography.pdsGlobalFontWeight700,
      lineHeight: 14.4,
    },
    pageTitle: {
      color: colors.pdsThemeColorForegroundNeutralHigh,
      fontFamily: fonts.pdsGlobalFontFamilyPoppins,
      fontSize: typography.pdsGlobalFontSize500,
      fontStyle: 'normal',
      fontWeight: typography.pdsGlobalFontWeight500,
      lineHeight: 28.6,
      marginBottom: dimensions.pdsGlobalSpace400,
      paddingLeft: dimensions.pdsGlobalSpace200,
      textAlign: 'left',
    },
    subHeading: {
      color: colors.pdsThemeColorForegroundNeutralHigh,
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
      fontSize: typography.pdsGlobalFontSize300,
      fontWeight: '400',
      lineHeight: 24,
      marginBottom: dimensions.pdsGlobalSpace400,
      textAlign: 'center',
    },
    tabContainer: {
      alignItems: 'flex-start',
      alignSelf: 'center',
      flex: 1,
      marginTop: dimensions.pdsGlobalSpace100,
      padding: dimensions.pdsGlobalSpace500,
      width: IPAD_WIDTH,
    },
    time: {
      color: colors.pdsThemeColorForegroundNeutralHigh,
      fontSize: typography.pdsGlobalFontSize300,
      marginBottom: dimensions.pdsGlobalSpace200,
    },
    welcomeText: {
      color: colors.pdsThemeColorForegroundNeutralHigh,
      display: 'flex',
      fontFamily: fonts.pdsGlobalFontFamilyPoppins,
      fontSize: typography.pdsGlobalFontSize500,
      fontStyle: 'normal',
      fontWeight: typography.pdsGlobalFontWeight500,
      lineHeight: 28.6,
    },
  });

  return styles;
};

export default getStyles;

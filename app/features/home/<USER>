import { useNavigation, useRoute } from '@react-navigation/native';
import { Text, useTheme } from 'pantry-design-system';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { View, Pressable, Platform } from 'react-native';
import { SvgXml } from 'react-native-svg';
import { useSelector } from 'react-redux';

import { LOGIN_ANALYTICS } from '../../../analytics/AnalyticsConstants';
import { userActionLogEvent } from '../../../analytics/AnalyticsUtils';
import { SCREENS, TEXT_SIZE, TEXTLINK_COLORS } from '../../../app/shared/constants';
import { notification } from '../../../assets/images/svg/profileIcons';
import { useAccessibilityFocus } from '../../providers/AccessibilityFocus';

import getStyles from './styles';

import type { ProfileState } from '../../store/reducers/profileSlice';

interface HomeHeaderRightProps {
  eventCategory?: string;
  eventLabel?: string;
}

const HomeHeaderRight: React.FC<HomeHeaderRightProps> = ({ eventCategory, eventLabel }) => {
  const { t } = useTranslation();
  const { theme } = useTheme(); // Fething themes from ThemeProvider
  const [notificationCount, setNotificationCount] = useState(14);
  const { setLastFocused, setRef, handleScreenFocus } = useAccessibilityFocus(); // Custom hook for accessibility focus
  const route = useRoute();
  const currentScreen = route?.name;
  const styles = getStyles(theme); // Fetching styles based on theme
  const navigation = useNavigation();
  const { isTestAccount }: ProfileState = useSelector((state) => state.profile);

  useEffect(() => {
    const unsubscribeFocus = navigation.addListener('focus', () => {
      handleScreenFocus(currentScreen);
    });

    return () => {
      unsubscribeFocus?.();
    };
  }, [navigation, handleScreenFocus]);

  const userAction = () => {
    switch (eventCategory) {
      case LOGIN_ANALYTICS.ANNOUNCEMENTS:
        userActionLogEvent(
          LOGIN_ANALYTICS.ANNOUNCEMENTS,
          LOGIN_ANALYTICS.NOTIFICATION,
          eventLabel ?? '',
        );
        break;

      // Add more cases as needed

      case LOGIN_ANALYTICS.WHATS_NEW:
        userActionLogEvent(
          LOGIN_ANALYTICS.WHATS_NEW,
          LOGIN_ANALYTICS.NOTIFICATION,
          eventLabel ?? '',
        );
        break;

      default:
        break;
    }
  };

  const navigateToNotification = () => {
    userAction();
    setLastFocused(currentScreen, 'notification-text');
    navigation.navigate(SCREENS.NOTIFICATIONS);
  };

  if (isTestAccount) return null;
  return (
    <Pressable
      accessible
      ref={setRef('notification-text')}
      accessibilityLabel={
        notificationCount > 0
          ? t('ada.notificationCount', { count: notificationCount })
          : t('ada.notificationButton')
      }
      accessibilityHint={Platform.OS === 'ios' ? t('ada.doubleTapToActivate') : ''}
      onPress={navigateToNotification}
      style={styles.headerRight}
    >
      <SvgXml
        xml={notification}
        testID="notification-image"
        style={styles.headerRight}
        fill={theme.colors.pdsThemeColorBackgroundPrimary}
      />
      {notificationCount > 0 && (
        <View style={styles.notificationBadge} testID="notification-text">
          <Text
            text={`${notificationCount}`}
            size={TEXT_SIZE.SMALL}
            color={TEXTLINK_COLORS.NEUTRAL_HIGH_INVERSE}
            allowFontScaling={false}
            accessible={false}
          />
        </View>
      )}
    </Pressable>
  );
};

export default HomeHeaderRight;

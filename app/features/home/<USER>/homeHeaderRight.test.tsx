import { render, fireEvent } from '@testing-library/react-native';
import React from 'react';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';

import HomeHeaderRight from '../homeHeaderRight';

jest.mock('../../../providers/AccessibilityFocus', () => ({
  useAccessibilityFocus: () => ({
    setRef: jest.fn(),
    setLastFocused: jest.fn(),
    handleScreenFocus: jest.fn(),
    isTabNavigation: jest.fn(),
  }),
  AccessibilityFocusProvider: ({ children }: { children: React.ReactNode }) => children,
}));

jest.mock('@react-native-firebase/app', () => ({
  getApp: jest.fn(() => ({
    name: '[DEFAULT]',
  })),
}));

jest.mock('../styles', () =>
  jest.fn(() => ({
    headerRight: {},
    notificationBadge: {},
    notificationText: {},
  })),
);

jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string, params?: Record<string, any>) => {
      const translations: Record<string, string> = {
        'ada.notificationCount': 'You have {count} notifications',
        'ada.notificationButton': 'Notifications',
        'ada.doubleTapToActivate': 'Double tap to activate',
      };
      let translation = translations[key] || key;
      if (params) {
        Object.keys(params).forEach((param) => {
          translation = translation.replace(`{${param}}`, params[param]);
        });
      }
      return translation;
    },
  }),
}));

jest.mock('react-native-svg', () => ({
  SvgXml: jest.fn(({ xml, testID }) => <div testID={testID}>{xml}</div>),
}));

jest.mock('../../../../analytics/AnalyticsUtils', () => ({
  userActionLogEvent: jest.fn(),
}));

// Mock SCREENS constant
jest.mock('../../../shared/constants', () => ({
  SCREENS: {
    NOTIFICATIONS: 'Notifications',
  },
  TEXT_SIZE: {
    SMALL: 'small',
  },
  TEXTLINK_COLORS: {
    NEUTRAL_HIGH_INVERSE: 'white',
  },
}));

// Mock LOGIN_ANALYTICS constants
jest.mock('../../../../analytics/AnalyticsConstants', () => ({
  LOGIN_ANALYTICS: {
    ANNOUNCEMENTS: 'annoucements',
    WHATS_NEW: 'whats-new',
    NOTIFICATION: 'notification',
  },
}));

const mockNavigate = jest.fn();
jest.mock('@react-navigation/native', () => ({
  useNavigation: () => ({
    navigate: mockNavigate,
    replace: jest.fn(),
    addListener: jest.fn(),
  }),
  useIsFocused: jest.fn(() => true),
  useFocusEffect: jest.fn((cb) => {
    cb();
  }),
  useRoute: jest.fn(() => ({
    params: {
      someParam: 'testValue',
    },
  })),
}));

jest.mock('pantry-design-system', () => ({
  useTheme: jest.fn(() => ({
    theme: {
      colors: {
        pdsThemeColorBackgroundBaseline: '#ffffff',
        pdsThemeColorBackgroundPrimary: '#000000',
      },
      fonts: { pdsGlobalFontFamilyPoppins: 'Poppins' },
      dimensions: { pdsGlobalSpace400: 16 },
      typography: { pdsGlobalFontSize500: 18 },
      borderDimens: { pdsGlobalBorderRadius300: 8 },
    },
  })),
  Text: jest.fn(({ text }) => <>{text}</>),
}));

const mockStore = configureStore([]);

describe('HomeHeaderRight', () => {
  let store: any;

  beforeEach(() => {
    jest.clearAllMocks();

    store = mockStore({
      profile: {
        isTestAccount: false,
      },
    });
  });

  it('should render notification badge with correct count', () => {
    const { getByTestId } = render(
      <Provider store={store}>
        <HomeHeaderRight />
      </Provider>,
    );
    const notificationText = getByTestId('notification-text');
    expect(notificationText).toBeTruthy();
    const notificationImage = getByTestId('notification-image');
    expect(notificationImage).toBeTruthy();
  });

  it('should not render when user has test account', () => {
    const testStore = mockStore({
      profile: {
        isTestAccount: true,
      },
    });

    const { queryByTestId } = render(
      <Provider store={testStore}>
        <HomeHeaderRight />
      </Provider>,
    );

    expect(queryByTestId('notification-text')).toBeNull();
    expect(queryByTestId('notification-image')).toBeNull();
  });

  it('should handle navigation press and call userAction with ANNOUNCEMENTS', () => {
    const mockUserActionLogEvent = jest.requireMock(
      '../../../../analytics/AnalyticsUtils',
    ).userActionLogEvent;

    const { getByTestId } = render(
      <Provider store={store}>
        <HomeHeaderRight eventCategory="annoucements" eventLabel="test-label" />
      </Provider>,
    );

    // Find the pressable element that wraps the notification image
    const notificationImage = getByTestId('notification-image');
    const pressableElement = notificationImage.parent?.parent || notificationImage.parent;

    if (pressableElement) {
      fireEvent.press(pressableElement);
    }

    expect(mockUserActionLogEvent).toHaveBeenCalledWith(
      'annoucements',
      'notification',
      'test-label',
    );
    expect(mockNavigate).toHaveBeenCalledWith('Notifications');
  });

  it('should handle navigation press and call userAction with WHATS_NEW', () => {
    const mockUserActionLogEvent = jest.requireMock(
      '../../../../analytics/AnalyticsUtils',
    ).userActionLogEvent;

    const { getByTestId } = render(
      <Provider store={store}>
        <HomeHeaderRight eventCategory="whats-new" eventLabel="whats-new-label" />
      </Provider>,
    );

    const notificationImage = getByTestId('notification-image');
    const pressableElement = notificationImage.parent?.parent || notificationImage.parent;

    if (pressableElement) {
      fireEvent.press(pressableElement);
    }

    expect(mockUserActionLogEvent).toHaveBeenCalledWith(
      'whats-new',
      'notification',
      'whats-new-label',
    );
    expect(mockNavigate).toHaveBeenCalledWith('Notifications');
  });

  it('should handle navigation press with default case (no eventCategory)', () => {
    const mockUserActionLogEvent = jest.requireMock(
      '../../../../analytics/AnalyticsUtils',
    ).userActionLogEvent;

    const { getByTestId } = render(
      <Provider store={store}>
        <HomeHeaderRight />
      </Provider>,
    );

    const notificationImage = getByTestId('notification-image');
    const pressableElement = notificationImage.parent?.parent || notificationImage.parent;

    if (pressableElement) {
      fireEvent.press(pressableElement);
    }

    // Should not call userActionLogEvent for default case
    expect(mockUserActionLogEvent).not.toHaveBeenCalled();
    expect(mockNavigate).toHaveBeenCalledWith('Notifications');
  });

  it('should handle navigation press with unknown eventCategory', () => {
    const mockUserActionLogEvent = jest.requireMock(
      '../../../../analytics/AnalyticsUtils',
    ).userActionLogEvent;

    const { getByTestId } = render(
      <Provider store={store}>
        <HomeHeaderRight eventCategory="unknown-category" />
      </Provider>,
    );

    const notificationImage = getByTestId('notification-image');
    const pressableElement = notificationImage.parent?.parent || notificationImage.parent;

    if (pressableElement) {
      fireEvent.press(pressableElement);
    }

    // Should not call userActionLogEvent for unknown category
    expect(mockUserActionLogEvent).not.toHaveBeenCalled();
    expect(mockNavigate).toHaveBeenCalledWith('Notifications');
  });

  it('should render with correct accessibility attributes', () => {
    const { getByTestId } = render(
      <Provider store={store}>
        <HomeHeaderRight />
      </Provider>,
    );

    const notificationImage = getByTestId('notification-image');
    expect(notificationImage).toBeTruthy();

    // The pressable parent should have accessibility properties
    const pressableElement = notificationImage.parent?.parent || notificationImage.parent;
    expect(pressableElement).toBeTruthy();
  });
});

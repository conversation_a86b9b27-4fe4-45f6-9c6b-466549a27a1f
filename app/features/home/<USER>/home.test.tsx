import { useFocusEffect } from '@react-navigation/native';
import { render, screen, waitFor, act } from '@testing-library/react-native';
import { Provider, useDispatch } from 'react-redux';
import configureStore from 'redux-mock-store';

import ABAuditEngine from '../../../../analytics/ABAuditEngine';
import { APPD_PAGE_VIEW_METRIC_CONSTANTS } from '../../../../analytics/AppDynamicsConstants';
import * as AsyncStorage from '../../../store/AsyncStorage';
import { setLunchStarted } from '../../../store/reducers/shiftSlice';
import { calculateTimeDiff, convertStringToDate, getCurrentTime } from '../../../utils/TimeUtils';
import HomeScreen from '../home';

jest.mock('../../../hooks/useAccessibilityFocus', () => () => ({
  setLastFocused: jest.fn(),
  setRef: () => null,
}));

jest.mock('@react-native-firebase/app', () => ({
  getApp: jest.fn(() => ({
    name: '[DEFAULT]',
  })),
}));

jest.mock('react-native-haptic-feedback', () => ({
  trigger: jest.fn(),
}));

jest.mock('react-native-permissions', () => ({
  PERMISSIONS: {
    ANDROID: {
      ACCESS_FINE_LOCATION: 'android.permission.ACCESS_FINE_LOCATION',
      ACCESS_COARSE_LOCATION: 'android.permission.ACCESS_COARSE_LOCATION',
    },
    IOS: {
      LOCATION_WHEN_IN_USE: 'ios.permission.LOCATION_WHEN_IN_USE',
    },
  },
  RESULTS: {
    GRANTED: 'granted',
    DENIED: 'denied',
  },
  check: jest.fn(),
  request: jest.fn(),
  checkNotifications: jest.fn(() => Promise.resolve({ status: 'granted', settings: {} })),
}));

jest.mock('../../../../app/store/selectors/featureFlagsSelectors', () => ({
  sharePointEnabled: jest.fn(() => true),
  roleGovernanceEnabled: jest.fn(() => true), // <-- add this line
  scheduleTabEnabled: jest.fn(() => true), 
}));


jest.mock('../../../store/reducers/clockInStatusSlice', () => ({
  fetchClockInStatusRequest: jest.fn(),
  clearClockInStatus: jest.fn(),
}));

jest.mock('@react-native-firebase/analytics', () => {
  return jest.fn(() => ({
    logEvent: jest.fn(),
    logScreenView: jest.fn(),
    setUserId: jest.fn(),
    setUserProperties: jest.fn(),
  }));
});

jest.mock('../../../../app/store', () => ({
  store: {
    getState: jest.fn(() => ({
      profile: {
        profile: { id: 'mock-user-id' },
      },
    })),
  },
}));

jest.mock('@react-native-async-storage/async-storage', () => ({
  uniqueSessionId: jest.fn(() => Promise.resolve('mock-request-id')),
}));

jest.mock('../../../store/AsyncStorage', () => ({
  uniqueSessionId: jest.fn(),
}));
jest.spyOn(AsyncStorage, 'uniqueSessionId').mockResolvedValue('12345');

// Required if not auto-mocked elsewhere
jest.mock('../../../../analytics/ABAuditEngine', () => ({
  customTimerStart: jest.fn(),
  customTimerEnd: jest.fn(),
}));

jest.spyOn(ABAuditEngine, 'customTimerStart').mockImplementation(jest.fn());
jest.spyOn(ABAuditEngine, 'customTimerEnd').mockImplementation(jest.fn());

jest.mock('@react-navigation/native', () => {
  const actualNav = jest.requireActual('@react-navigation/native');
  return {
    ...actualNav,
    useNavigation: jest.fn(),
    useIsFocused: jest.fn(() => true),
    useFocusEffect: jest.fn((cb) => {
      cb(); // Simulate the screen gaining focus
    }),
  };
});

// jest.mock("react-native/Libraries/Interaction/InteractionManager", () => ({
//   runAfterInteractions: jest.fn((cb) => {
//     const timeout = setTimeout(cb, 0); // trigger it right away
//     return {
//       cancel: jest.fn(() => clearTimeout(timeout)),
//     };
//   }),
// }));

jest.mock('pantry-design-system', () => ({
  useTheme: jest.fn(() => ({
    theme: {
      colors: {},
      fonts: { pdsGlobalFontFamilyPoppins: 'Poppins' },
      dimensions: { pdsGlobalSpace400: 16 },
      typography: { pdsGlobalFontSize500: 18 },
      borderDimens: { pdsGlobalBorderRadius300: 8 },
    },
  })),
  Heading: jest.fn(({ children }) => <>{children}</>),
  TextLink: jest.fn(({ children }) => <>{children}</>),
  BottomSheet: jest.fn(({ children, testID }) => <div testID={testID}>{children}</div>),
  Text: jest.fn(({ children }) => <>{children}</>),
  Button: jest.fn(({ onPress, label, testID }) => (
    <button onClick={onPress} testID={testID}>
      {label}
    </button>
  )),
}));

jest.mock('@react-navigation/native', () => ({
  useNavigation: jest.fn(),
  useIsFocused: jest.fn(() => true),
  useFocusEffect: jest.fn((cb) => {
    cb()(); // Call the effect AND its cleanup
  }),
  useRoute: jest.fn(() => ({
    params: {
      someParam: 'testValue', // customize based on your screen
    },
  })),
}));

jest.mock('../../../../components/WebView', () => 'WebView');
jest.mock('../../announcements/AnnouncementCarousel', () => 'AnnouncementCarousel');

jest.mock('../../../store/AsyncStorage', () => ({
  setItem: jest.fn(),
  getItem: jest.fn(),
  uniqueSessionId: jest.fn(),
}));

jest.mock('../styles', () =>
  jest.fn(() => ({
    headerRight: {},
    notificationBadge: {},
    notificationText: {},
  })),
);

jest.mock('i18next', () => ({
  t: (key: string) => {
    const translations: Record<string, string> = {
      CLOCK_IN_TIME: 'Clocked in at {time}',
      SHIFT_START: 'Shift started at {time}',
      LUNCH_STARTED: 'Lunch started at {time}',
      CLOCKED_IN: 'Clocked in at {time}',
      START_SHIFT: 'Start your shift',
      ON_SHIFT: 'On Shift',
      ON_LUNCH: 'On Lunch',
    };
    return translations[key] || key;
  },
}));
jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useDispatch: jest.fn(),
}));
jest.mock('../../../store/reducers/shiftSlice', () => ({
  setLunchStarted: jest.fn(),
  setLunchEnded: jest.fn(),
  setClockedOut: jest.fn(),
  setTimeAgo: jest.fn(),
  setShiftTime: jest.fn(),
  setLateShift: jest.fn(),
  setClockedIn: jest.fn(),
}));

jest.mock('../../../utils/TimeUtils.ts', () => ({
  calculateTimeDiff: jest.fn(),
  calculateTimeDiffinMinutes: jest.fn(),
  convertStringToDate: jest.fn(),
  getCurrentTime: jest.fn(),
  toDateOnly: jest.fn(),
  isWithinRange: jest.fn(),
  minutesToMilliseconds: jest.fn(),
}));

jest.mock('react-native-device-info', () => {
  return {
    isEmulator: jest.fn(() => Promise.resolve(true)), // <-- this is the key
    getUniqueId: jest.fn(() => 'mocked-device-id'),
    getVersion: jest.fn(() => '1.0.0'),
    // Add other mocked methods as needed
  };
});
const mockStore = configureStore([]);

const baseState = {
  clockInStatus: {
    response: {
      clockedIn: true, // or whatever shape your app expects
      time: '9:00 AM',
      duration: '8h',
    },
  },
  confetti: { visible: true, reduceMotionEnabled: false },
  shift: { clockedIn: false, lunchStarted: false, clockedOut: false },
  profile: {
    profile: {
      role: 'manager',
      divisionName: 'StoreName',
    },
    isUserInStore: false,
    slocData: {},
    homeCelebrationCards: [],
    banner: 'albertsons',
  },
  store: {
    data: {
      stores: {
        content: [
          {
            locationId: '123',
          },
        ],
      },
    },
  },
  deviceInfo: { isTablet: false, isLandscape: false },
  announcements: {
    data: {
      content: [
        {
          title: 'Years of Dedication',
          linkTitle: 'Read More',
          linktoImage: '',
          division: ['Corporate'],
          contentType: 'Announcement',
          detailPageWebUrl: '',
          createdDate: '',
          startDate: '',
          endDate: '',
        },
        {
          title: 'Milestone',
          linkTitle: 'Read More',
          linktoImage: '',
          division: ['Sales'],
          contentType: 'Announcement',
          detailPageWebUrl: '',
          createdDate: '',
          startDate: '',
          endDate: '',
        },
      ],
    },
    loading: false,
    error: null,
  },
  featureFlags: {
    flags: [],
    loading: false,
    error: null,
    response: null,
  },
  featureAccess: {
    userState: {
      userType: 'non-restricted',
      isInGeofence: false,
      isClockedIn: false,
    },
  },
  locationAccess: {
    isPrecise: 'precise', // or 'denied'/'approximate' for LocationGoverned test
  },
};

const renderComponent = (storeState: {
  shift:
  | { clockedIn: boolean; lunchStarted: boolean; clockedOut: boolean }
  | { clockedIn: boolean; lunchStarted: boolean; clockedOut: boolean };
  profile:
  | {
    profile: {
      role: string;
      divisionName: string;
    };
  }
  | { profile: { role: string } };
  store: {
    data: {
      stores: {
        content: [
          {
            locationId: string;
          },
        ];
      };
    };
  };
}) => {
  const store = mockStore(storeState);

  return render(
    <Provider store={store}>
      <HomeScreen />
    </Provider>,
  );
};

describe('HomeScreen', () => {
  const initialState = {
    ...baseState,
  };
  const mockDispatch = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useDispatch as jest.Mock).mockReturnValue(mockDispatch);
    (useFocusEffect as jest.Mock).mockImplementation((cb) => {
      const cleanup = cb();
      if (typeof cleanup === 'function') cleanup();
    });
    // jest.spyOn(InteractionManager, 'runAfterInteractions').mockImplementation((cb) => {
    //   cb(); // Execute immediately
    //   return { cancel: jest.fn() };
    // });
  });

  it('renders the clock-in button when not clocked in', () => {
    renderComponent(initialState);
    // const clockInButton = screen.getByText("Clock in");
    // expect(clockInButton).toBeTruthy();
  });

  it('renders the clock-in BottomSheet when the clock-in button is pressed', () => {
    renderComponent(initialState);
    // const clockInButton = screen.getByText(HOME_CONSTANTS.CLOCK_IN_CTA);
    // fireEvent.press(clockInButton);

    // Check if the BottomSheet content is rendered
    const bottomSheetContent = screen.getByTestId('clock-in-bottom-sheet');
    expect(bottomSheetContent).toBeTruthy();
  });

  it('renders the next shift details when user is not in store', () => {
    const state = {
      ...initialState,
      profile: {
        profile: {
          userInstore: 'no',
        },
      },
      store: {
        data: {
          stores: {
            content: [
              {
                locationId: '123',
              },
            ],
          },
        },
      },
      isClockedIn: true,
    };
    renderComponent(state);
    // const nextShiftText = screen.getByText(HOME_CONSTANTS.NEXT_SHIFT);
    // expect(nextShiftText).toBeTruthy();
  });

  test('dispatches setLunchStarted action correctly', () => {
    const dispatch = jest.fn();
    const mockLunchStartTime = '12:00 PM';
    dispatch(setLunchStarted(mockLunchStartTime));
    expect(setLunchStarted).toHaveBeenCalledWith(mockLunchStartTime);
    expect(dispatch).toHaveBeenCalledWith(setLunchStarted(mockLunchStartTime));
  });

  test('calculates time difference correctly', () => {
    (calculateTimeDiff as jest.Mock).mockReturnValue('2 hours');
    const startTime = '09:00 AM';
    const endTime = '11:00 AM';
    const result = calculateTimeDiff(startTime, endTime);
    expect(calculateTimeDiff).toHaveBeenCalledWith(startTime, endTime);
    expect(result).toBe('2 hours');
  });

  test('gets the current time in the specified format', () => {
    (getCurrentTime as jest.Mock).mockReturnValue('12:30 PM');
    const result = getCurrentTime('hh:mm A');
    expect(getCurrentTime).toHaveBeenCalledWith('hh:mm A');
    expect(result).toBe('12:30 PM');
  });

  test('converts a time string to a Date object', () => {
    const mockDate = new Date('2025-01-22T09:00:00.000Z');
    (convertStringToDate as jest.Mock).mockReturnValue(mockDate);
    const result = convertStringToDate('09:00 AM');
    expect(convertStringToDate).toHaveBeenCalledWith('09:00 AM');
    expect(result).toEqual(mockDate);
  });

  it('opens and closes the clock-in BottomSheet', () => {
    renderComponent(initialState);

    // Click the clock-in button to open the BottomSheet
    // const clockInButton = screen.getByText(HOME_CONSTANTS.CLOCK_IN_CTA);
    // fireEvent.press(clockInButton);

    // Verify the BottomSheet is open
    const bottomSheetContent = screen.getByTestId('clock-in-bottom-sheet'); // Replace with actual text
    expect(bottomSheetContent).toBeTruthy();

    // Verify the BottomSheet is closed
    expect(screen.queryByText('Clocked in at')).toBeNull();
  });

  it('logs a message when the check schedule button is pressed', () => {
    const consoleLogSpy = jest.spyOn(console, 'log');
    const state = {
      ...initialState,
      store: {
        data: {
          stores: {
            content: [
              {
                locationId: '123',
              },
            ],
          },
        },
      },
    };
    renderComponent(state);
    // const checkScheduleButton = screen.getByText(
    //   HOME_CONSTANTS.CHECK_SCHEDULE_CTA
    // );
    // fireEvent.press(checkScheduleButton);
    // expect(consoleLogSpy).toHaveBeenCalledWith("check schedule coming soon...");
    consoleLogSpy.mockRestore();
  });

  it('renders the clock-in modal when the clock-in button is clicked', () => {
    const state = {
      ...initialState,
      store: {
        data: {
          stores: {
            content: [
              {
                locationId: '123',
              },
            ],
          },
        },
      },
    };
    renderComponent(state);
    // const clockInButton = screen.getByTestId("check-schedule-button");
    // fireEvent.press(clockInButton);
    // const modalTitle = screen.getByTestId("your-next-shift");
    // expect(modalTitle).not.toBeNull();
  });

  it('sets lunchWillStart to true when the lunch start button is pressed', () => {
    const state = {
      ...initialState,
      shift: { clockedIn: true, lunchStarted: false, clockedOut: false },
    };
    renderComponent(state);
    // const startLunchButton = screen.getByText("Start my lunch");
    // fireEvent.press(startLunchButton);
    // const modalTitle = screen.getByTestId("lunch-bottom-sheet");
    // expect(modalTitle).toBeTruthy();
  });

  it('dispatches setLunchEnded action when the lunch end button is pressed', () => {
    const state = {
      ...initialState,
      shift: {
        clockedIn: true,
        lunchStarted: true,
        lunchEnded: false,
        clockedOut: false,
      },
      store: {
        data: {
          stores: {
            content: [
              {
                locationId: '123',
              },
            ],
          },
        },
      },
    };
    renderComponent(state);
    // const endLunchButton = screen.getByText(HOME_CONSTANTS.LUNCH_END);
    // fireEvent.press(endLunchButton);
    // expect(setLunchEnded).toHaveBeenCalledWith(true);
  });

  it('sets timeToClockOut to true when the clock-out-early button is pressed', () => {
    const state = {
      ...initialState,
      shift: {
        clockedIn: true,
        lunchStarted: true,
        lunchEnded: true,
        clockedOut: false,
      },
      store: {
        data: {
          stores: {
            content: [
              {
                locationId: '123',
              },
            ],
          },
        },
      },
    };
    renderComponent(state);
    // const clockOutEarlyButton = screen.getByText(
    //   HOME_CONSTANTS.CLOCK_OUT_EARLY_CTA
    // );
    // fireEvent.press(clockOutEarlyButton);
    // const timeToClockOutText = screen.getByText(
    //   HOME_CONSTANTS.TIME_TO_CLOCKOUT
    // );
    // expect(timeToClockOutText).toBeTruthy();
  });

  it('renders the lunch bottomsheet when lunchWillStart is true', () => {
    const state = {
      ...initialState,
      shift: { clockedIn: true, lunchStarted: false, clockedOut: false },
    };
    renderComponent(state);
    // const startLunchButton = screen.getByTestId("start-lunch-button");
    // fireEvent.press(startLunchButton);
    // const modalTitle = screen.getByTestId("lunch-bottom-sheet");
    // expect(modalTitle).toBeTruthy();
  });

  it('renders the clock-in modal when modalVisible is true', () => {
    renderComponent(initialState);
    // const clockInButton = screen.getByText(HOME_CONSTANTS.CLOCK_IN_CTA);
    // fireEvent.press(clockInButton);
    const modalComponent = screen.getByTestId('clock-in-bottom-sheet');
    expect(modalComponent).toBeTruthy();
  });

  it('dispatches setClockedOut action when clock-out is confirmed', () => {
    const state = {
      ...initialState,
      shift: {
        clockedIn: true,
        lunchStarted: true,
        lunchEnded: true,
        clockedOut: false,
      },
      store: {
        data: {
          stores: {
            content: [
              {
                locationId: '123',
              },
            ],
          },
        },
      },
    };
    renderComponent(state);
    // const clockOutButton = screen.getByTestId("clock-out-early-button");
    // fireEvent.press(clockOutButton);
  });

  it('renders the clock-out modal when timeToClockOut is true', () => {
    const state = {
      ...initialState,
      shift: {
        timeToClockOut: true,
        clockInTime: '9:00 AM',
        clockedIn: true,
        lunchStarted: true,
        lunchEnded: true,
        clockedOut: false,
      },
      store: {
        data: {
          stores: {
            content: [
              {
                locationId: '123',
              },
            ],
          },
        },
      },
    };
    renderComponent(state);
    // const modalTitle = screen.getByTestId("clock-out-early-button");
    // expect(modalTitle).not.toBeNull();
  });

  it('calls ABAuditEngine timers on focus and after interactions', async () => {
    jest.useFakeTimers();
    const announcementState = {
      ...initialState,
      announcements: {
        data: { content: [] },
        loading: false,
        error: null,
      },
    };
    const state = mockStore(announcementState);

    render(
      <Provider store={state}>
        <HomeScreen />
      </Provider>,
    );

    // Fast-forward all timers
    await act(async () => {
      jest.advanceTimersByTime(2000);
    });

    // Now assert calls
    expect(ABAuditEngine.customTimerStart).toHaveBeenCalledWith(
      APPD_PAGE_VIEW_METRIC_CONSTANTS.HOME_PAGE_VIEW_METRIC,
    );

    await waitFor(() => {
      expect(ABAuditEngine.customTimerStart).toHaveBeenCalledWith(
        APPD_PAGE_VIEW_METRIC_CONSTANTS.HOME_PAGE_VIEW_METRIC,
      );
    });
    jest.useRealTimers();
  });
  it('renders AnnouncementCarousel when diaaSharepoint is enabled and AnnouncementData is available', () => {
    const state = {
      ...initialState,
      profile: {
        ...initialState.profile,
        isUserInStore: true,
        slocData: { polarisBannerName: 'albertsons' },
      },
      announcements: {
        data: {
          content: [
            {
              title: 'New Announcement',
              linkTitle: 'Read More',
              linktoImage: '',
              division: ['Corporate'],
              contentType: 'Announcement',
              detailPageWebUrl: '',
              createdDate: '',
              startDate: '',
              endDate: '',
            },
          ],
        },
        loading: false,
        error: null,
      },
      featureAccess: {
        userState: {
          userType: 'non-restricted',
          isInGeofence: true,
          isClockedIn: true,
        },
      },
      locationAccess: {
        isPrecise: 'precise',
      },
    };

    renderComponent(state);
    const announcementCarousel = screen.queryByTestId('announcement-carousel');
    expect(announcementCarousel).not.toBeNull();
  });
});

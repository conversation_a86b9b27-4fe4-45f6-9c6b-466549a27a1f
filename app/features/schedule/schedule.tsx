import { useFocusEffect } from '@react-navigation/native';
import { useTheme } from 'pantry-design-system';
import React, { useRef, useCallback, useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { View, RefreshControl, ActivityIndicator, ScrollView, AppState } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import dayjs from "dayjs";
import es from "dayjs/locale/es";
import en from "dayjs/locale/en";
import ABAuditEngine from '../../../analytics/ABAuditEngine';
import { APPD_PAGE_VIEW_METRIC_CONSTANTS } from '../../../analytics/AppDynamicsConstants';
import { ScheduleEmptyImage } from '../../../assets/images/svg/scheduleEmptyImage';
import EmptyStatusCard from '../../../components/EmptyStatusCard/EmptyStatusCard';
import WeekCalendar from '../../../components/WeekCalendar';
import { useWeekCalendar } from '../../hooks';
import { useNetworkAlert } from '../../hooks/useNetworkAlert';
import * as AsyncStorage from '../../store/AsyncStorage';
import { fetchEmployeeScheduleRequest } from '../../store/reducers/employeeScheduleSlice';
import { SecureStorage, TokenType } from '../../store/SecureStorage';
import { scheduleTabEnabled } from '../../store/selectors/featureFlagsSelectors';

import CalendarItem from './CalendarItem';
import NoShiftItem from './NoShiftItem';
import { WeekItem } from '../../hooks/useWeekCalendar';
import getStyles from './styles';
import { COMPLETE_SHIFT, ENGLISH_WEEK_CODES, EXCEPTION, FUTURE_SCHEDULED, NEXT_SHIFT, ShiftState, SPANSISH_WEEK_CODES, TODAY_SHIFT } from '../../shared/constants';
import { userActionLogEvent, screenViewLog } from '../../../analytics/AnalyticsUtils';
import { SCHEDULE_ANALYTICS, } from '../../../analytics/AnalyticsConstants';

/**
 * ScheduleScreen Component
 * 
 * Main component for displaying employee schedule information in a calendar format.
 * Provides week navigation, shift details, and refresh functionality with network handling.
 * 
 * Features:
 * - Week-based calendar view with navigation
 * - Shift status indicators (completed, today, next, future)
 * - Pull-to-refresh functionality
 * - Network connectivity handling
 * - Performance analytics tracking
 * - Feature flag support for schedule tab
 * 
 * @returns {JSX.Element} The rendered schedule screen
 */
export default function ScheduleScreen() {
  const { theme } = useTheme(); // Fetching themes from ThemeProvider
  const styles = getStyles(theme); // Fetching styles based on theme
  const [loading, setLoading] = useState(true);
  const { t } = useTranslation();
  const language = useSelector((state: any) => state.language);

  useEffect(() => {
    if (language.id === "es")
      dayjs.locale({
        ...es,
        weekdaysShort: SPANSISH_WEEK_CODES
      });
    else
      dayjs.locale({
        ...en,
        weekdaysShort: ENGLISH_WEEK_CODES
      });
  }, [language]);

  const scheduleEnabled = useSelector(scheduleTabEnabled);

  const today = dayjs().format("YYYY-MM-DD");
  const [selectedDate, setSelectedDate] = useState(today);
  const [weekItems, setWeekItems] = useState<WeekItem[]>([]);

  const [refreshing, setRefreshing] = useState(false);

  const dispatch = useDispatch();

  const appState = useRef(AppState.currentState);

  const { data, error: scheduleError, loading: scheduleLoading } = useSelector((state: any) => state.empSchedule);

  const scheduleResponse = data?.scheduleResponse;
  // const shiftData = scheduleResponse?.schedule || [];
  const [shiftData, setShiftData] = useState(scheduleResponse?.schedule || []);

  const [exceptionData, setExceptionData] = useState(scheduleResponse?.workedHours || []);

  /**
   * Effect to update shift data when Redux store data changes
   * Monitors the data prop from Redux store and updates local shift data state
   */
  useEffect(() => {
    const scheduleResponse = data?.scheduleResponse;
    setShiftData(scheduleResponse?.schedule || []);
    setExceptionData(scheduleResponse?.workedHours || []);
  }, [data])

  // Week calendar hook for navigation and date management
  const {
    navigateWeek,
    weekStart,
    weekEnd,
    weekEndDay,
    weekStartDay,
    weekNumber,
    year,
    weekStartDate,
    weekEndDate,
    currentWeek
  } = useWeekCalendar();

  /**
 * Generates an array of dates for the current week.
 *
 * @param {dayjs.Dayjs} startDate - The start date of the week.
 * @returns {dayjs.Dayjs[]} An array of dates for the current week.
 */
  const getWeekDates = (startDate: dayjs.Dayjs) => {
    return Array.from({ length: 7 }, (_, i) => startDate.add(i, "day"));
  };

  /**
   * Updates the week items based on the current week and shift data.
   * 
   * This function generates the week items array, which includes information about each day of the week:
   * - Date string and dayjs object
   * - Today/selected status flags
   * - Shift availability and details
   * - Shift status based on date relative to today
   * - Accessibility labels for screen readers
   * 
   * Shift Status Logic:
   * - completeShift: Past dates (daysDiff < 0)
   * - todayShift: Current date (daysDiff === 0)
   * - nextShift: Tomorrow (daysDiff === 1)
   * - futureScheduled: Future dates (daysDiff > 1)
   */
  const updateWeekItems = () => {
    const week = currentWeek;
    const weekDates = getWeekDates(week);
    const newWeekItems = weekDates.map((date) => {
      const dateStr = date.format("YYYY-MM-DD");
      const isToday = dateStr === today;

      const daysDiff = dayjs(dateStr).diff(dayjs(today), "day");

      const shift = shiftData.find(item => item.workDate === dateStr);

      const workedHoursForDate = exceptionData.find(item => item?.workDate === dateStr);

      // Extract the actual start and end times from the worked hours entry
      const pastWorkedHoursStartTime = workedHoursForDate?.startTime;
      const pastWorkedHoursEndTime = workedHoursForDate?.endTime;

      // Check if there's an exception for this specific date
      const hasException = workedHoursForDate?.errorMessage != null && typeof workedHoursForDate.errorMessage === 'string';

      let shiftStatus: ShiftState;
      if (daysDiff < 0) {
        // Past dates - check for exceptions only for completed shifts
        if (hasException) {
          shiftStatus = EXCEPTION;
        }
        else {
          shiftStatus = COMPLETE_SHIFT;
        }

      } else if (daysDiff === 0) {
        shiftStatus = TODAY_SHIFT;
      } else if (daysDiff === 1) {
        shiftStatus = NEXT_SHIFT;
      } else {
        shiftStatus = FUTURE_SCHEDULED;
      }

      const isSelected = dateStr === selectedDate;

      const hasShift = !!shift;

      // Accessibility label based on shift availability
      const accessibilityLabel = `${date.format("dddd")}, ${date.format(
        "MMMM D"
      )}, ${hasShift ? "shift available" : "no scheduled shift"}`;


      return {
        dateStr,
        isToday,
        isSelected,
        shift,
        hasShift,
        accessibilityLabel,
        shiftStatus,
        pastWorkedHoursStartTime,
        pastWorkedHoursEndTime,
        workedHours: workedHoursForDate
      };
    });

    setWeekItems(newWeekItems);
  };

  /**
   * Effect to update week items when shift data changes
   * Triggers week items recalculation whenever shift data is updated
   */
  useEffect(() => {
    updateWeekItems();
  }, [shiftData]);

  // Network connectivity management
  const { showAlertWithRetry, checkInternetConnection } = useNetworkAlert();

  /**
   * Handles pull-to-refresh functionality
   * Checks network connectivity and fetches fresh schedule data
   * Shows network alert with retry option if offline
   */
  const onRefresh = async () => {
    setRefreshing(true); // Start the spinner

    const isOnline = await checkInternetConnection();
    if (!isOnline) {
      await showAlertWithRetry(onRefresh);
      setRefreshing(false);
      return;
    }

    await fetchEmployeeSchedule(weekNumber, year, weekStartDate, weekEndDate);

    setRefreshing(false); // Stop the spinner after API call
  };

  /**
    * Wrapper for week navigation with internet connectivity check
    * Ensures network connectivity before allowing week navigation
    * Shows loading state and handles offline scenarios
    * 
    * @param {('prev' | 'next')} direction - Direction to navigate (previous or next week)
    */
  const navigateWeekWithCheck = async (direction: 'prev' | 'next') => {
    userActionLogEvent(
      SCHEDULE_ANALYTICS.SCHEDULE_CATEGORY,
      direction === 'prev' ? SCHEDULE_ANALYTICS.PREVIOUS : SCHEDULE_ANALYTICS.NEXT,
      SCHEDULE_ANALYTICS.WEEK_SELECTOR_LABEL,
    );
    const isOnline = await checkInternetConnection();
    if (!isOnline) {
      await showAlertWithRetry(onRefresh);

      return;
    }
    navigateWeek(direction); // This triggers the API call
    setLoading(false);
  };

  const startTimeRef = useRef<number | null>(null);

  useEffect(() => {
    // Add an event listener to monitor changes in the app's state (foreground/background)
    const subscription = AppState.addEventListener('change', (nextAppState) => {
      // Check if the app is transitioning from inactive/background to active state
      if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
        screenViewLog({ subsection1: SCHEDULE_ANALYTICS.SCHEDULE_EVENT_LABEL });
      }
      // Update the current app state
      appState.current = nextAppState;
    });

    // Cleanup function to remove the event listener when the component unmounts
    return () => {
      subscription.remove();
    };
  }, []);

  /**
   * Focus effect hook for performance analytics
   * Starts timing when screen is focused and cleans up when blurred
   * Used for tracking page load performance metrics
   */
  useFocusEffect(
    useCallback(() => {
      screenViewLog({ subsection1: SCHEDULE_ANALYTICS.SCHEDULE_EVENT_LABEL });

      startTimer(); // Start the timer when the screen is focused
      setLoading(false); // Set loading to false after the timer starts
      return () => {
        startTimeRef.current = null; // Clear the timer reference when the screen is blurred
      };
    }, []),
  );

  /**
     * Starts the performance timer for analytics tracking
     * Records the current timestamp and initiates AppDynamics custom timer
     */
  const startTimer = useCallback(() => {
    const startTime = Date.now(); // Record the current time
    startTimeRef.current = startTime; // Store the start time in the ref
    // Start a custom timer for analytics
    ABAuditEngine.customTimerStart(APPD_PAGE_VIEW_METRIC_CONSTANTS.SCHEDULE_PAGE_VIEW_METRIC);
  }, []);

  /**
   * Effect to end performance timer when loading completes
   * Calculates page load duration and reports to analytics
   */
  useEffect(() => {
    if (!loading && startTimeRef.current) {
      const duration = Date.now() - startTimeRef.current; // Calculate the duration
      // End the custom timer for analytics
      ABAuditEngine.customTimerEnd(APPD_PAGE_VIEW_METRIC_CONSTANTS.SCHEDULE_PAGE_VIEW_METRIC);
    }
  }, [loading]); // Dependencies to trigger the effect

  /**
   * Fetches employee schedule data from the API
   * 
   * @param {number} [weekNumberToFetch=weekNumber] - Week number to fetch (defaults to current week)
   * @param {number} [yearToFetch=year] - Year to fetch (defaults to current year)
   * @param {string} [fromDate] - Optional start date for date range filtering
   * @param {string} [toDate] - Optional end date for date range filtering
   */
  const fetchEmployeeSchedule = async (
    weekNumberToFetch = weekNumber,
    yearToFetch = year,
    fromDate?: string,
    toDate?: string,
  ) => {
    const requestId = await AsyncStorage.uniqueSessionId();
    const secureStorage = new SecureStorage();
    const employeeId = await secureStorage.getToken(TokenType.employeeId);
    dispatch(
      fetchEmployeeScheduleRequest({
        params: {
          requestId,
          empId: employeeId,
          weekNumber: weekNumberToFetch,
          year: yearToFetch,
          ...(fromDate && toDate ? { fromDate, toDate } : {}), // Only include if both are set
        },
      }),
    );
  };

  /**
   * Effect to fetch schedule data when week parameters change
   * Automatically refetches data when user navigates to different weeks
   */
  useEffect(() => {
    if (scheduleEnabled) fetchEmployeeSchedule(weekNumber, year, weekStartDate, weekEndDate);
  }, [weekNumber, year, weekStartDate, weekEndDate]);

  return (
    <View style={styles.safeAreaContainer}>
      <View style={styles.contentContainer}>
        {scheduleEnabled ? (
          <ScrollView
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                colors={[theme.colors.pdsThemeColorForegroundPrimary]}
                tintColor={theme.colors.pdsThemeColorForegroundPrimary}
              />
            }
          >
            <WeekCalendar
              weekItems={weekItems}
              navigateWeek={navigateWeekWithCheck}
              weekStart={weekStartDay}
              weekEnd={weekEndDay}
              onRetry={() => fetchEmployeeSchedule(weekNumber, year, weekStartDate, weekEndDate)}
            />
            {scheduleLoading ? (
              <View style={styles.loader}>
                <ActivityIndicator
                  size="small"
                  color={theme.colors.pdsThemeColorForegroundPrimary}
                />
              </View>
            ) : (
              <View style={styles.scrollContent}>
                {weekItems.map(({ dateStr, hasShift, isToday, shiftStatus, shift, pastWorkedHoursStartTime, pastWorkedHoursEndTime, workedHours }) => {
                  if (isToday && !shift) {
                    return <NoShiftItem key={dateStr} date={dateStr} />;
                  }
                  if (hasShift && shift) {
                    return (
                      <CalendarItem
                        key={dateStr}
                        time={{
                          start: shift.startTime,
                          end: shift.endTime,
                          workedStart: pastWorkedHoursStartTime, // Pass worked hours start time
                          workedEnd: pastWorkedHoursEndTime      // Pass worked hours end time
                        }}
                        state={shiftStatus}
                        shift={shift}
                        workedHours={workedHours}
                      />
                    );
                  }
                  return null;
                })}
              </View>
            )}
          </ScrollView>
        ) : (
          <EmptyStatusCard
            source={ScheduleEmptyImage}
            titleText={t('ScheduleEmptyView.emptyMessageTitle')}
            subTitleText={t('ScheduleEmptyView.emptyMessageSubTitle')}
            descriptionText={t('ScheduleEmptyView.emptyMessageDescription')}
          />
        )}
      </View>
    </View>
  );
}

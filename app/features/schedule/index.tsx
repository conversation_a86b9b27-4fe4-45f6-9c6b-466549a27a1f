import React from "react";
import { createStackNavigator } from "@react-navigation/stack";
import { useTranslation } from "react-i18next";
import HomeHeaderRight from "../home/<USER>";
import { useTheme } from "pantry-design-system";
import AppHeader from "../../../components/AppHeader";
import ScheduleScreen from "./schedule";

const Stack = createStackNavigator();

export default function ScheduleStackScreen() {
  const { t } = useTranslation();
  const { theme } = useTheme();

  return (
    <Stack.Navigator
      initialRouteName="ScheduleScreen"
      screenOptions={{ headerTitleAlign: "center", headerShadowVisible: false }}
    >
      <Stack.Screen
        name="ScheduleScreen"
        options={{
          headerTitle: "",
          header: () => (
            <AppHeader
              headerRight={<HomeHeaderRight />}
              headerTitle={t("Schedule")}
            />
          ),
        }}
        component={ScheduleScreen}
      />

      {/* Add more screens here as needed */}
    </Stack.Navigator>
  );
}

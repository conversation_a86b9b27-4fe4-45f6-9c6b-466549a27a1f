import { Theme } from "pantry-design-system";
import { StyleSheet } from "react-native";
import { IPAD_WIDTH } from "../../shared/constants";

//customising styles based on themes
const getStyles = ({ colors, fonts, typography, dimensions }: Theme) => {
  const styles = StyleSheet.create({
    mainContainer: {
      alignItems: "center",
      justifyContent: "center",
      flex: 1,
    },
    tabContainer: {
      width: 736,
      alignSelf: "center",
      flexDirection: "row",
      flex: 1,
      paddingVertical: 6,
      marginHorizontal: 5,
      color: colors.pdsThemeColorOutlineNeutralHigh,
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
    },
    container: {
      display: "flex",
      flexDirection: "row",
      justifyContent: "center",
      paddingVertical: 6,
      marginHorizontal: 5,
      color: colors.pdsThemeColorOutlineNeutralHigh,
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
    },
    noShiftcontainer: {
      height: 40,
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      alignSelf: "center",
      marginVertical: 5, //no design token
      maxWidth: IPAD_WIDTH,
    },
    noShiftTabletSpacing: {
    },
    noShiftDayText: {
      fontSize: typography.pdsGlobalFontSize100,
      backgroundColor: colors.pdsThemeColorOutlineNeutralHigh,
      color: colors.pdsThemeColorForegroundNeutralHighInverse,
      fontWeight: typography.pdsGlobalFontWeight400,
      paddingHorizontal: 5,
      paddingVertical: 4,
      lineHeight: 17,
      borderRadius: 4,
      overflow: "hidden",
    },
    line: {
      flex: 1,
      height: 0,
      borderBottomWidth: 4,
      borderBottomColor: colors.pdsThemeColorOutlineNeutralHigh,
    },
    dateContainer: {
      width: "15%",
      alignItems: "center",
      paddingRight: 16,
      paddingTop: 6,
    },
    weekText: {
      fontSize: typography.pdsGlobalFontSize100,
      fontWeight: typography.pdsGlobalFontWeight400,
      color: colors.pdsThemeColorForegroundNeutralHigh,
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
    },
    dateText: {
      fontSize: typography.pdsGlobalFontSize400,
      fontWeight: typography.pdsGlobalFontWeight700,
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
      color: colors.pdsThemeColorForegroundNeutralHigh,
      marginVertical: 2,
    },
    safeAreaContainer: {
      flex: 1,
      alignItems: 'center',
      backgroundColor: colors.pdsThemeColorBackgroundSunken,
    },
    scrollContent: {
      paddingHorizontal: dimensions.pdsGlobalSpace400
    },
    contentContainer: {
      flex: 1,
      maxWidth: IPAD_WIDTH,
    },
    scrollView: {
      padding: dimensions.pdsGlobalSpace400,
    },
    loader: {
      flex: 1,
      alignSelf: "center",
      justifyContent: "center",
      alignItems: "center",
      marginTop: dimensions.pdsGlobalSpace800,
    }
  });
  return styles;
};

export default getStyles;

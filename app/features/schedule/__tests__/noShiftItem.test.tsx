import React from "react";
import { render } from "@testing-library/react-native";
import { Provider } from "react-redux";
import configureStore from "redux-mock-store";
import NoShiftItem from "../NoShiftItem";
import dayjs from "dayjs";

const mockStore = configureStore([]);

jest.mock("pantry-design-system", () => ({
  useTheme: jest.fn(() => ({
    theme: {
      colors: { primary: "#000000" },
      fonts: { main: "Arial" },
      dimensions: { padding: 16 },
    },
  })),
}));

jest.mock("../styles", () => jest.fn(() => ({
  noShiftcontainer: { padding: 16 },
  noShiftDayText: { fontSize: 18 },
  line: { height: 1, backgroundColor: "#000" },
})));

describe("NoShiftItem", () => {
  let store: any;

  beforeEach(() => {
    store = mockStore({
      deviceInfo: {
        isTablet: false,
      },
    });
  });

  it("should render correctly with given date", () => {
    const { getByText } = render(
      <Provider store={store}>
        <NoShiftItem date="2025-03-05T08:00:00" />
      </Provider>
    );
    const formattedDate = dayjs("2025-03-05T08:00:00").format("ddd D");
    expect(getByText(formattedDate)).toBeTruthy();
  });

  it("should apply correct styles", () => {
    const { getByText } = render(
      <Provider store={store}>
        <NoShiftItem date="2025-03-05T08:00:00" />
      </Provider>
    );
    const formattedDate = dayjs("2025-03-05T08:00:00").format("ddd D");

    const textElement = getByText(formattedDate);
    expect(textElement.props.style).toEqual({ fontSize: 18 });
  });

  it("renders correctly when isTablet is true", () => {
    store = mockStore({
      deviceInfo: {
        isTablet: true,
      },
    });

    const date = "2025-03-17";
    const { getByText } = render(
      <Provider store={store}>
        <NoShiftItem date={date} />
      </Provider>
    );

    const formattedDate = dayjs(date).format("ddd D");
    expect(getByText(formattedDate)).toBeTruthy();
  });
});
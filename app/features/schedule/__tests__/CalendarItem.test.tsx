import React from "react";
import { render } from "@testing-library/react-native";
import CalendarItem from "../CalendarItem";
import { Provider } from "react-redux";
import configureStore from "redux-mock-store";
import CalendarCard from "../../../../components/CalendarCard";
import { useTheme } from "pantry-design-system";



const defaultProps = {
  state: "completeShift" as "completeShift",
  time: {
    start: "2025-03-05T00:00:00",
    end: "2025-03-05T00:00:00",
  },
};

const mockStore = configureStore([]);
const store = mockStore({ deviceInfo: { isTablet: false } });

const { getByTestId } = render(
  <Provider store={store}>
    <CalendarItem {...defaultProps} />
  </Provider>
);
render(
  <Provider store={store}>
    <CalendarItem {...defaultProps} />
  </Provider>
);
jest.mock("../../../../components/CalendarCard", () => jest.fn(() => null));
jest.mock("pantry-design-system", () => ({
  useTheme: jest.fn(() => ({ theme: {} })),
}));
jest.mock("../styles", () => jest.fn(() => ({
  container: {},
  tabContainer: {},
  mainContainer: {},
  dateContainer: {},
  weekText: {},
  dateText: {},
})));

describe("Calendar Item Component", () => {
  it("Renders the component correctly", () => {
    const { getByTestId } = render(
      <Provider store={store}>
        <CalendarItem {...defaultProps} />
      </Provider>
    );
    expect(getByTestId("calendar-item")).toBeTruthy();
  });

  it("Applies the correct styles from getStyles", () => {
    const { getByTestId } = render(
      <Provider store={store}>
        <CalendarItem {...defaultProps} />
      </Provider>
    );
    const calendarItem = getByTestId("calendar-item");
    expect(calendarItem.props.style).toEqual({});
  });

  it("Renders with tablet styles when isTablet is true", () => {
    const tabletStore = mockStore({ deviceInfo: { isTablet: true } });
    const { getByTestId } = render(
      <Provider store={tabletStore}>
        <CalendarItem {...defaultProps} />
      </Provider>
    );
    const calendarItem = getByTestId("calendar-item");
    expect(calendarItem.props.style).toEqual({});
  });

  it("Renders with non-tablet styles when isTablet is false", () => {
    const nonTabletStore = mockStore({ deviceInfo: { isTablet: false } });
    const { getByTestId } = render(
      <Provider store={nonTabletStore}>
        <CalendarItem {...defaultProps} />
      </Provider>
    );
    const calendarItem = getByTestId("calendar-item");
    expect(calendarItem.props.style).toEqual({});
  });
});
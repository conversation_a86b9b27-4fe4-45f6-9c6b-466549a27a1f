import React from "react";
import { View, Text } from "react-native";
import { useTheme } from "pantry-design-system";
import getStyles from "./styles";
import dayjs from "dayjs";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";

interface NoShiftItemProps {
  date: string;
}

const NoShiftItem: React.FC<NoShiftItemProps> = ({ date }) => { // when there No Shi<PERSON> has assigned for the day
  const { theme } = useTheme();
  const styles = getStyles(theme);
  const NoShiftdate = dayjs(date);
  const formattedDay = NoShiftdate.format("dddd, MMM D");
  const formattedDate = NoShiftdate.format("ddd D");
  const { isTablet } = useSelector((state: any) => state.deviceInfo); // To identify if the device is a tablet

  const { t } = useTranslation();

  return (
    <View
      style={[styles.noShiftcontainer, isTablet ? styles.noShiftTabletSpacing : null]}
      accessible={true}
      accessibilityLabel={t('ada.noShift').replace('${formattedDay}', formattedDay)}
      accessibilityRole="text"
    >
      <Text allowFontScaling={false} style={styles.noShiftDayText}>{formattedDate}</Text>
      <View style={styles.line} />
    </View>
  );
};

export default NoShiftItem;

export const SCHEDULE_STATUS = {
  shiftStartingSoon: "shiftStartingSoon",
  shouldClockIn: "shouldClockIn",
  clockedIn: "clockedIn",
  shouldClockOut: "shouldClockOut",
  shiftOver: "shiftOver",
  onLunch: "onLunch",
  checkSchedule: "checkSchedule"
};


export interface StatusCardProps {
    status?: typeof SCHEDULE_STATUS[keyof typeof SCHEDULE_STATUS];
    time?: string; // Time details like "8:59 AM"
    duration?: string; // Example: "8h 59min ago"
    clockedIn?: boolean; // Optional prop to indicate if the user is clocked in
}
import React from "react";
import { View, Text } from "react-native";
import { useTheme } from "pantry-design-system";
import getStyles from "./styles";
import CalendarCard from "../../../components/CalendarCard";
import dayjs from "dayjs"; // Import dayjs for date formatting
import { useSelector } from "react-redux";
import { ShiftState } from "../../shared/constants";

interface CalendarItemProps {
  state: ShiftState;
  time: {
    start: string;
    end: string;
    workedStart?: string; // Add this for actual worked start time
    workedEnd?: string;
  };
  shift: any;
  workedHours?: any;
}

const CalendarItem: React.FC<CalendarItemProps> = ({ // Wrapper container for calendar cards
  state,
  time,
}: CalendarItemProps) => {
  const { theme } = useTheme();
  const styles = getStyles(theme);
  const startDate = dayjs(time.start);
  const formattedDay = startDate.format("ddd");
  const formattedDate = startDate.format("D");
  const { isTablet } = useSelector((state: any) => state.deviceInfo); // To identify if the device is a tablet

  const dayComponent = (day: string, date: string) => { // return Date and Day component
    return (
      <View
        style={styles.dateContainer}
        accessible={false}
        importantForAccessibility="no"
      >
        <Text allowFontScaling={false} style={styles.weekText} accessible={false} importantForAccessibility="no">{day}</Text>
        <Text allowFontScaling={false} style={styles.dateText} accessible={false} importantForAccessibility="no">{date}</Text>
      </View>
    );
  };

  return (
    <View style={isTablet ? styles.mainContainer : null}>
      <View testID={"calendar-item"} style={isTablet ? styles.tabContainer : styles.container}>
        {dayComponent(formattedDay, formattedDate)}
        <CalendarCard
          time={time}
          state={state}
        />
      </View>
    </View>
  );
};

export default CalendarItem;

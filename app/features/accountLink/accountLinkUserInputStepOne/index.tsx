import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { useTheme, Button, Text, Heading, TextField } from 'pantry-design-system';
import React, { useEffect, useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { View, TouchableOpacity, Platform, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { SvgXml } from 'react-native-svg';
import { useDispatch, useSelector } from 'react-redux';

import { BUTTON_NAV_ANALYTICS, LOYALTY_ANALYTICS } from '../../../../analytics/AnalyticsConstants';
import { screenViewLog, userActionLogEvent } from '../../../../analytics/AnalyticsUtils';
import { forUIcon } from '../../../../assets/images/svg/LoyaltyAccount';
import useAccessibilityFocus from '../../../hooks/useAccessibilityFocus';
import { useNetworkAlert } from '../../../hooks/useNetworkAlert';
import {
  SCREENS,
  FALLBACK_BANNER,
  ADA_ROLES,
  URLS,
  TEXTLINK_WEIGHT,
  TEXT_ALIGN,
  TEXTLINK_SIZE,
  TEXT_DECORATION,
} from '../../../shared/constants';
import * as AsyncStorage from '../../../store/AsyncStorage';
import { fetchProfileRequest } from '../../../store/reducers/profileSlice';
import { capitalizeFirstLetter, announceADA } from '../../../utils/helpers';

import getStyles from './styles';

import type { AssociateProfile } from '../../../misc/models/ProfileModal';

const AccountLinkUserInputOne = () => {
  const { theme } = useTheme();
  const dispatch = useDispatch();
  const styles = getStyles(theme);

  const [value, setValue] = useState('');

  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');

  const { setLastFocused, setRef } = useAccessibilityFocus();
  const [errorMessage, setErrorMessage] = useState('');

  const [isFindMemberCtaPressed, setIsFindMemberCtaPressed] = useState(false); // Flag to check if the "Find My For U Membership" button was pressed

  const ucaProfileData = useSelector((state: any) => state.profile.ucaProfile);

  const navigation = useNavigation();

  const { t } = useTranslation();
  const { showAlertWithRetry, checkInternetConnection } = useNetworkAlert();
  //validation for email and phone number
  const handleSubmitChanges = (e: any) => {
    const text = e?.nativeEvent?.text;
    setValue(text);
    // Use Pantry's validation logic to determine validity
    const isEmail = /^[^\d][a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(text); // regex for email validation
    const isPhone = /^\d{10}$/.test(text); // Assuming 10-digit phone numbers
    setEmail(isEmail ? text : '');
    setPhone(isPhone ? text : '');
    if (isEmail || isPhone) {
      setErrorMessage(''); // Clear error
    } else {
      setErrorMessage(t('accountLinkError.invalidFormate'));
      announceADA(t('Error') + t('accountLinkError.invalidFormate'));
    }
    // Reset the flag when input changes
  };

  useFocusEffect(
    useCallback(() => {
      screenViewLog({
        subsection1: BUTTON_NAV_ANALYTICS.PROFILE_TAB_ACTION,
        subsection2: LOYALTY_ANALYTICS.LOYALTY_LINK_STEP1,
        event_category_link: BUTTON_NAV_ANALYTICS.PROFILE_TAB_ACTION,
        event_label_link: LOYALTY_ANALYTICS.LOYALTY_LINK_STEP1,
      });
    }, []),
  );

  useEffect(() => {
    if (ucaProfileData?.loyaltyPrograms?.length > 0 && isFindMemberCtaPressed) {
      navigation.navigate(SCREENS.ACCOUNT_LINK_STEP_ONE_VERIFY);
    } else if (ucaProfileData?.profiles?.length == 0 && isFindMemberCtaPressed) {
      //Set the error message if no loyalty programs are found
      const errorText = t('accountLinkError.invalideEmailOrPhone').replace('[Banner]', bannerName);
      setErrorMessage(errorText);
      announceADA(t('Error') + errorText);
    }
  }, [ucaProfileData]);

  const { profile, banner }: { profile: AssociateProfile; banner: string } = useSelector(
    (state: any) => state.profile,
  );
  const firstName = profile?.names?.[0]?.firstName ?? 'Hi there';
  const bannerName = capitalizeFirstLetter(banner ? banner : FALLBACK_BANNER);

  /**
   * Navigates to the account creation webview screen.
   * Constructs the account creation URL by replacing the banner name placeholder with the selected banner
   * or a fallback value if none is provided. Then, navigates to the webview screen with the constructed URL
   * and appropriate header options.
   */
  const createAccount = () => {
    const nameOfBanner = banner ? banner : FALLBACK_BANNER;
    const finalUrl = URLS.CREATE_ACCOUNT.replace('{bannerName}', nameOfBanner);
    setLastFocused('create-account-link');
    userActionLogEvent(
      BUTTON_NAV_ANALYTICS.PROFILE_TAB_ACTION,
      LOYALTY_ANALYTICS.LINK_CREATE_ACCOUNT,
      LOYALTY_ANALYTICS.LOYALTY_LINK_STEP1,
    );
    const analyticsParam = {
      subsection1: LOYALTY_ANALYTICS.LOYALTY,
      subsection2: LOYALTY_ANALYTICS.LOYALTY_LINK_STEP1,
      subsection3: LOYALTY_ANALYTICS.LOYALTY_CREATE_ACCOUNT_WEBVIEW,
      eventCategory: BUTTON_NAV_ANALYTICS.PROFILE_TAB_ACTION,
      eventLabel: LOYALTY_ANALYTICS.LOYALTY_CREATE_ACCOUNT_WEBVIEW,
      refreshCategory: LOYALTY_ANALYTICS.LOYALTY,
      refreshAction: LOYALTY_ANALYTICS.LOYALTY_REFRESH_ICON,
      refreshLabel: LOYALTY_ANALYTICS.LOYALTY_CREATE_ACCOUNT_WEBVIEW,
      backButtonLabel: LOYALTY_ANALYTICS.LOYALTY_CREATE_ACCOUNT_WEBVIEW,
      backButtonCategory: LOYALTY_ANALYTICS.LOYALTY,
    };
    navigation.navigate(SCREENS.WEBVIEW, {
      url: finalUrl,
      titleHeader: t('accountLink.createAnAccount'),
      subsection: analyticsParam,
      isOmniheaderPresent: false,
    });
  };

  /**
   * Fetches the associate profile data using a unique session ID.
   * This function retrieves a unique session ID from AsyncStorage and dispatches
   * the `fetchProfileRequest` action with the required parameters.
   */
  const findMyForUMembership = async () => {
    userActionLogEvent(
      BUTTON_NAV_ANALYTICS.PROFILE_TAB_ACTION,
      LOYALTY_ANALYTICS.LINK_FIND_MY_MEMBERSHIP,
      LOYALTY_ANALYTICS.LOYALTY_LINK_STEP1,
    );
    const isOnline = await checkInternetConnection();
    if (!isOnline) {
      showAlertWithRetry(findMyForUMembership);
      return;
    }
    setIsFindMemberCtaPressed(true);
    const requestId = await AsyncStorage.uniqueSessionId();
    dispatch(
      fetchProfileRequest({
        requestId,
        fl: 'ucaProfile',
        ...(phone != '' ? { contactNbr: phone } : { email: email }),
      }),
    );
    // set true to verify that only if the profile is loaded from current api call then only we will navigate to next step.
  };

  const handleInputChanges = (text: string) => {
    setValue(text);
    setErrorMessage(''); // Clear error message on input change
  };

  // Retuns the accessibility label for the input field, including any error message if present.
  const getInputAccessibilityLabel = () => {
    return t('accountLink.phoneOrEmail') + (errorMessage ? `. ${t('Error')}, ${errorMessage}` : '');
  };
  return (
    <SafeAreaView edges={['top', 'bottom']} style={styles.container}>
      <View style={{ flex: 1 }}>
        <ScrollView>
          <View style={styles.mainContainer}>
            <SvgXml
              xml={forUIcon}
              accessible={true}
              accessibilityLabel={t('ada.forUImage')}
              style={styles.imageContainer}
              testID="forU-image"
            />

            <Heading
              accessible
              testID={'before-you-linking-text-heading'}
              textAlign="center"
              title={t('accountLink.accLinkHeadersteponeuserinput')
                .replace('[FirstName]', firstName ?? '')
                .replace('[Banner]', bannerName)}
              color="Neutral high"
              size="medium"
            />

            <View style={styles.headingTop} />

            <Text
              color="Neutral high"
              // maxLines,
              size="Large"
              text={t('accountLink.enterPhoneNumberEmail')}
              weight="Regular"
              textDecoration="None"
              lineHeight="Large"
              textAlign="center"
              testID="text-description-one"
            />

            <View style={styles.spaceTop} />

            <View style={{ height: 'auto', width: '100%' }}>
              <TextField
                testID={'textfield-userinput'}
                label={t('accountLink.phoneOrEmail').trim()}
                value={value}
                onChangeText={handleInputChanges}
                onSubmitEditing={handleSubmitChanges}
                placeholder={t('accountLink.enterPhoneOrEmail')}
                error={errorMessage !== ''}
                errorText={errorMessage}
                inputType="default"
                accessible={true}
                accessibilityLabel={getInputAccessibilityLabel()}
                accessibilityHint={Platform.OS === 'android' ? t("ada.doubleTapToEdit") : ''}
              />
            </View>

            <View style={styles.spaceTop} />

            <View style={styles.createAccount}>
              <Text
                color="Neutral high"
                // maxLines,
                size="Large"
                text={t('accountLink.newToBanner').replace('[Banner]', bannerName || '') + ' '}
                weight="Regular"
                textDecoration="None"
                lineHeight="Large"
                textAlign="center"
              />
              <TouchableOpacity
                onPress={createAccount}
                activeOpacity={0.8}
                testID={'create-account-link'}
                ref={setRef('create-account-link')}
                accessibilityLabel={t('accountLink.createAnAccount')}
                accessibilityHint={Platform.OS === 'ios' ? t('ada.doubleTapToActivate') : ''}
                accessibilityRole={ADA_ROLES.LINK}
                accessible={true}
              >
                <Text
                  text={t('accountLink.createAnAccount')}
                  color={theme.colors.pdsThemeColorForegroundPrimary}
                  weight={TEXTLINK_WEIGHT.SEMI_BOLD}
                  size={TEXTLINK_SIZE.LARGE}
                  textDecoration={TEXT_DECORATION.UNDERLINE}
                  textAlign={TEXT_ALIGN.CENTER}
                  accessible={false}
                />
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
        <View style={styles.stepOneFooterView}>
          <Button
            theme={theme}
            fullWidth
            accessible={true}
            size={'Large'}
            disabled={errorMessage !== '' || value.trim() === '' || (!email && !phone)}
            label={t('accountLink.findmyForUMembership')}
            onPress={findMyForUMembership}
            accessibilityHint={''}
            // Try these pantry-specific props if they exist
            accessibilityLabel={t('accountLink.findmyForUMembership')}
            testID="findmy-u-membership-button"
          />
        </View>
      </View>
    </SafeAreaView>
  );
};

export default AccountLinkUserInputOne;

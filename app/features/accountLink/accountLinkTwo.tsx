/* eslint-disable @typescript-eslint/no-explicit-any */
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { useTheme, Heading, Button, Text, TextLink } from 'pantry-design-system';
import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import {
  View,
  ScrollView,
  Dimensions,
  Platform,
  Text as RNText,
  TouchableOpacity,
  findNodeHandle,
  AccessibilityInfo,
} from 'react-native';
import {
  CodeField,
  Cursor,
  useBlurOnFulfill,
  useClearByFocusCell,
} from 'react-native-confirmation-code-field';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { SvgXml } from 'react-native-svg';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { useSelector, useDispatch } from 'react-redux';

import {
  BUTTON_NAV_ANALYTICS,
  LOYALTY_ANALYTICS,
  EVENT_CATEGORY_ERROR,
} from '../../../analytics/AnalyticsConstants';
import { screenViewLog, userActionLogEvent } from '../../../analytics/AnalyticsUtils';
import { HttpStatusCode } from '../../../app/config/errorCodes';
import * as AsyncStorage from '../../../app/store/AsyncStorage';
import { forUIcon } from '../../../assets/images/svg/LoyaltyAccount';
import { time } from '../../../assets/images/svg/Time';
import { useTimer, useUpdateEffect } from '../../hooks';
import { useNetworkAlert } from '../../hooks/useNetworkAlert';
import {
  SCREENS,
  FACTOR_TYPE,
  OTP_REQUEST_STATUS,
  ERROR_CODES,
  TABLET_LANDSCAPE_WIDTH_STYLE,
  TEXTLINK_COLORS,
  TEXT_SIZE,
  TEXT_WEIGHT,
  BUTTON_SIZE,
  TEXTDECORATION,
  ADA_ROLES,
  AccountLinkingPending,
} from '../../shared/constants';
import { fetchDiscountRequest, clearDiscount } from '../../store/reducers/discountSlice';
import { fetchOtpVerifyRequest, clearOtpVerify } from '../../store/reducers/otpVerifySlice';
import { announceADA } from '../../utils/helpers';

import getStyles from './styles';

import type { NavigationProp } from '@react-navigation/native';

const { height } = Dimensions.get('window');

const CELL_COUNT = 6;

const AccountLinkTwo = (): React.ReactElement => {
  const [isScrollable, setIsScrollable] = useState(false);

  const {
    timeLeft: verificationCodeExpire,
    formatTime,
    isExpired: isVerficationCodeExpired,
    reset: resetVerificationCode,
  } = useTimer({
    initialTime: 300,
  });

  const {
    timeLeft: resendCodeTimeLeft,
    isExpired: isResendCodeExpired,
    reset: resetResend,
  } = useTimer({
    initialTime: 30,
  });

  const [value, setValue] = useState('');

  const optApiType = useRef<string>(''); // use to identify OTP API type.

  const [nextCtaPressed, setNextCtaPressed] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  const ucaProfile = useSelector((state: any) => state.profile.ucaProfile);

  const mobileNumber =
    ucaProfile?.profile?.phones?.find((p: any) => p.type === 'PRIMARY')?.value ?? '';

  const emailAddress = ucaProfile?.profile?.emails?.[0]?.emailAddress ?? '';

  const [method, setMethod] = useState<'phone' | 'email'>(mobileNumber ? 'phone' : 'email');

  const otpDataResponse = useSelector((state: any) => state.otpVerify.response);
  const otpApiError = useSelector((state: any) => state.otpVerify.error);
  const discountApiLoading = useSelector((state: any) => state.discount.loading);
  const discountApiResponse = useSelector((state: any) => state.discount.response);
  const discountApiError = useSelector((state: any) => state.discount.error);
  const ref = useBlurOnFulfill({ value, cellCount: CELL_COUNT });
  const [props, getCellOnLayoutHandler] = useClearByFocusCell({
    value,
    setValue,
  });

  const { theme } = useTheme();
  const { t } = useTranslation();
  const navigation = useNavigation<NavigationProp<any>>();
  const dispatch = useDispatch();
  const styles = getStyles(theme);
  const { showAlertWithRetry, checkInternetConnection } = useNetworkAlert();

  const didGetCodeRef = useRef<typeof Heading>(null);

  // Effect to handle screen onload event
  useEffect(() => {
    fetchOtpVerifyApi().catch((_error) => { });
    return () => {
      // Clear api response data when the component unmounts
      clearResponse(true);
    };
  }, [method]);

  // Effect to handle Otp api response
  useUpdateEffect(() => {
    const error = otpDataResponse?.errors?.[0];
    if (error?.code) {
      switch (error.code) {
        case ERROR_CODES.INVALID_OTP:
          userActionLogEvent(
            EVENT_CATEGORY_ERROR,
            method === 'phone'
              ? LOYALTY_ANALYTICS.ERRORS.INCORRECT_PHONE_VERIFICATION_CODE
              : LOYALTY_ANALYTICS.ERRORS.INCORRECT_EMAIL_VERIFICATION_CODE,
            LOYALTY_ANALYTICS.LOYALTY_LINK_STEP2,
          );
          updateErrorMessage(t('accountLinkError.invalidOTP'));
          break;

        case ERROR_CODES.PHONE_NUMBER_NOT_FOUND:
          updateErrorMessage(error.message || t('accountLinkError.phoneNumberNotFound'));
          break;

        case ERROR_CODES.LOGIN_ID_NOT_FOUND:
          userActionLogEvent(
            EVENT_CATEGORY_ERROR,
            LOYALTY_ANALYTICS.ERRORS.ENTERED_EMAIL_OR_PHONE_NOT_LINKED,
            LOYALTY_ANALYTICS.LOYALTY_LINK_STEP2,
          );
          updateErrorMessage(error.message);
          break;

        case ERROR_CODES.AD_TOKEN_FAILED:
          updateErrorMessage(error.message);
          break;

        default:
          updateErrorMessage(t('genericError.message'));
          break;
      }
    } else if (otpDataResponse?.response?.factorresult === 'SUCCESS') {
      fetchCustomerDiscountApi();
      setErrorMessage('');
    }
  }, [otpDataResponse]);

  // Effect that handle Customer discount api response :

  useUpdateEffect(() => {
    if (
      discountApiError &&
      discountApiError?.status &&
      discountApiError?.status == HttpStatusCode.ConflictError
    ) {
      navigateToNextScreen(discountApiError.status);
    } else if (discountApiResponse) {
      navigateToNextScreen(
        HttpStatusCode.Success,
        discountApiResponse?.informationMessage?.includes(AccountLinkingPending),
      );
    }
  }, [discountApiResponse, discountApiError]);

  useFocusEffect(
    useCallback(() => {
      screenViewLog({
        subsection1: BUTTON_NAV_ANALYTICS.PROFILE_TAB_ACTION,
        subsection2: LOYALTY_ANALYTICS.LOYALTY_LINK_STEP2,
        event_category_link: BUTTON_NAV_ANALYTICS.PROFILE_TAB_ACTION,
        event_label_link: LOYALTY_ANALYTICS.LOYALTY_LINK_STEP2,
      });
    }, []),
  );

  // Memoize the log function to avoid unnecessary re-creations
  const logVerificationCodeExpire = useCallback((timeLeft: number) => {
    if (timeLeft % 30 === 0 && timeLeft <= 300 && timeLeft > 0) {
      announceADA(
        !isVerficationCodeExpired
          ? t('accountLink.codeExpiresIn', {
            time: getFormtedTimeToAnnounce(timeLeft),
          })
          : t('accountLink.codeExpired'),
      );
    }
  }, []);

  // Announce remaining time for accessibility (ADA) every 30 seconds until the verification code expires.
  useEffect(() => {
    if (verificationCodeExpire <= 0 || nextCtaPressed) return;
    logVerificationCodeExpire(verificationCodeExpire);
  }, [verificationCodeExpire, logVerificationCodeExpire]);

  // Formatted time for announcement
  const getFormtedTimeToAnnounce = (time: number): string => {
    const formatted = formatTime(time); // e.g., "2m 5s" or "1m 1s" or "0m 1s"
    return formatted
      .replace(/(\d+)m/, (_, m) => {
        const num = parseInt(m, 10);
        return `${num} ${num === 1 || num === 0 ? 'minute' : 'minutes'}`;
      })
      .replace(/(\d+)s/, (_, s) => {
        const num = parseInt(s, 10);
        return `${num} ${num === 1 || num === 0 ? 'second' : 'seconds'}`;
      });
  };

  // Clear response data when the component unmounts or when the verify button is pressed.
  const clearResponse = (otpData = false) => {
    if (otpData) {
      dispatch(clearOtpVerify());
      return;
    }
    dispatch(clearOtpVerify());
    dispatch(clearDiscount());
  };

  const navigateToNextScreen = (statusCode: number, enrollmentStatus?: boolean) => {
    // Navigate to the next screen after successful OTP verification
    const navigationParams = {
      responseStatus: statusCode,
      doesEnrolmentUnderReview: enrollmentStatus || false,
    };
    clearResponse(); //clear the response data before navigating
    // If the discount API response is successful, navigate to the all done screen
    navigation.navigate(SCREENS.ACCOUNT_LINK_ALL_DONE, navigationParams);
  };

  // Set the error message state and announces the error for accessibility.
  const updateErrorMessage = (message: string) => {
    setErrorMessage(message);
    announceADA(t('Error') + ', ' + message);
  };

  // Checks network connection and optionally retries the given method if offline.
  const verifyNetworkConnection = async (retryMethod: () => void) => {
    const isOnline = await checkInternetConnection();
    if (!isOnline) {
      await showAlertWithRetry(retryMethod);
      return true; // Return true to indicate that the network connection is not available
    }
    return false; // Return false to indicate that the network connection is available
  };
  /**
   * Switches the OTP delivery method from phone to email.
   * Resets the verification code and resend timers.
   * This function is typically called when the user opts to receive the OTP via email instead of SMS.
   */
  const switchMethod = async () => {
    if (await verifyNetworkConnection(switchMethod)) return;
    if (method === 'phone') setMethod('email');
    else setMethod('phone');
    resetVerificationCode();
    resetResend();
    setErrorMessage('');

    if (didGetCodeRef && didGetCodeRef.current) {
      const tag = findNodeHandle(didGetCodeRef.current);
      if (tag) {
        setTimeout(() => {
          AccessibilityInfo.setAccessibilityFocus(tag);
        }, 200);
      }
    }
  };

  /**
   * Handles the resend OTP action.
   * Resets the verification code and resend timers, and triggers the OTP resend API call.
   * Only enabled when the resend timer has expired.
   */
  const resendCode = async () => {
    if (await verifyNetworkConnection(resendCode)) return;
    resetVerificationCode();
    resetResend();
    optApiType.current = 'resend';
    fetchOtpVerifyApi();
    otpDataResponse ? clearResponse(true) : null; // Clear OTP response data
    setErrorMessage('');
  };

  /**
   * Handles the "Next" button press to verify the entered OTP.
   * Sets the CTA pressed state and triggers the OTP verification API call.
   */
  const next = async () => {
    userActionLogEvent(
      BUTTON_NAV_ANALYTICS.PROFILE_TAB_ACTION,
      LOYALTY_ANALYTICS.LINK_VERIFY,
      LOYALTY_ANALYTICS.LOYALTY_LINK_STEP2,
    );
    if (await verifyNetworkConnection(next)) return;
    setNextCtaPressed(true);
    optApiType.current = 'active';
    fetchOtpVerifyApi();
  };

  /**
   * Verifies OTP by dispatching an API request if no other OTP verification is in progress.
   * Prevents concurrent API calls for OTP verification.
   *
   */

  const fetchOtpVerifyApi = async (): Promise<void> => {
    const requestId = await AsyncStorage.uniqueSessionId();
    dispatch(
      fetchOtpVerifyRequest({
        requestId,
        customerId: ucaProfile?.customerId, // Customer ID from UCA response
        factorType: method == 'phone' ? FACTOR_TYPE.PHONE : FACTOR_TYPE.EMAIL, // method is either "phone" or "email"
        body: getRequestBody(),
      }),
    );
  };

  /**
   * Fetches customer discount information by dispatching a fetchDiscountRequest action.
   * Prevents concurrent API calls by checking the `discountApiLoading` flag.
   * Constructs the request body using the user's first name, last name, and club card number
   * from the `ucaProfile` object. Only includes the club card number if the loyalty program
   * type is "CLUBCARD".
   *
   * @async
   * @returns {Promise<void>} A promise that resolves when the action is dispatched.
   */
  const fetchCustomerDiscountApi = async (): Promise<void> => {
    //Prevents concurrent API calls for customer Ber.
    if (!discountApiLoading) {
      dispatch(
        fetchDiscountRequest({
          body: {
            firstName: ucaProfile?.profile?.personalInfo?.name?.firstName ?? '',
            lastName: ucaProfile?.profile?.personalInfo?.name?.lastName ?? '',
            clubCardNumber:
              ucaProfile?.loyaltyPrograms?.[0]?.type == 'CLUBCARD'
                ? ucaProfile?.loyaltyPrograms?.[0]?.value
                : '',
          },
        }),
      );
    }
  };

  /**
   * Returns the request body object based on the provided OTP status.
   *
   * @param status - The OTP status, e.g., "active".
   * @returns The request body object for the given status.
   */

  const getRequestBody = (): any => {
    if (optApiType.current == 'active') {
      return {
        passCode: value,
        status: OTP_REQUEST_STATUS.ACTIVE,
      };
    }
    /* if (status === "resend") {
       return {
        status: OTP_REQUEST_STATUS.PENDING_ACTIVATION,
      };
    }*/ // TODO: Uncomment above when API supports 'resend' per Confluence.
    return {};
  };

  /**
   * Handles input changes by updating the value and clearing any existing error message.
   */
  const handleChange = (text: string): void => {
    setValue(text);
    if (errorMessage) setErrorMessage('');
  };

  // Retuns the accessibility label for the input field, including any error message if present.
  const getInputAccessibilityLabel = (): string => {
    return (
      t('accountLink.otpInputLabel') + (errorMessage ? `. ${t('Error')}, ${errorMessage}` : '')
    );
  };
  const insets = useSafeAreaInsets();

  return (
    <View style={{ ...styles.accountLinkTwoContainer, paddingBottom: insets.bottom }}>
      <View style={styles.accountLinkTwoContainer}>
        <ScrollView
          showsVerticalScrollIndicator={false}
          scrollEnabled={isScrollable}
          onContentSizeChange={(_contentWidth, contentHeight) => {
            setIsScrollable(contentHeight > height);
          }}
        >
          <View style={styles.contentContainer}>
            <SvgXml
              xml={forUIcon}
              accessible={true}
              accessibilityLabel={t('ada.forUImage')}
              style={styles.imageContainer}
              testID="forU-image"
            />

            <Heading
              ref={didGetCodeRef}
              accessible={Platform.OS === 'ios'}
              testID={'did-you-get-text-heading'}
              textAlign={TABLET_LANDSCAPE_WIDTH_STYLE.alignSelf}
              title={
                method === 'phone'
                  ? t('accountLink.didYouGetText')
                  : t('accountLink.didYouGetTextEmail')
              }
              color={TEXTLINK_COLORS.NEUTRAL_HIGH}
              size={TEXT_SIZE.LARGE}
            />

            <View>
              <Text
                accessible
                testID={'enter-code-text'}
                color={TEXTLINK_COLORS.NEUTRAL_HIGH}
                size={TEXT_SIZE.LARGE}
                text={t('accountLink.enterCode')}
                textAlign={TABLET_LANDSCAPE_WIDTH_STYLE.alignSelf}
                weight={TEXT_WEIGHT.REGULAR}
                textDecoration={TEXTDECORATION.None}
              />

              <Text
                accessible
                testID={'where-sent-code-text'}
                color={TEXTLINK_COLORS.NEUTRAL_HIGH}
                size={TEXT_SIZE.LARGE}
                text={method === 'email' ? emailAddress : mobileNumber}
                textAlign={TABLET_LANDSCAPE_WIDTH_STYLE.alignSelf}
                weight={TEXT_WEIGHT.BOLD}
                textDecoration={TEXTDECORATION.None}
              />
            </View>

            <View style={styles.verficationExpire}>
              <SvgXml
                xml={time}
                accessible={false}
                width={theme.dimensions.pdsGlobalSpace600}
                height={theme.dimensions.pdsGlobalSpace600}
                testID="timer-icon"
              />
              <Text
                text={
                  !isVerficationCodeExpired
                    ? t('accountLink.codeExpiresIn', {
                      time: formatTime(verificationCodeExpire),
                    })
                    : t('accountLink.codeExpired')
                }
                textAlign="center"
                accessible={true}
                accessibilityLabel={
                  !isVerficationCodeExpired
                    ? t('accountLink.codeExpiresIn', {
                      time: getFormtedTimeToAnnounce(verificationCodeExpire),
                    })
                    : t('accountLink.codeExpired')
                }
              />
            </View>
            <View style={styles.codeFieldContainer}>
              <CodeField
                ref={ref}
                {...props}
                value={value}
                onChangeText={handleChange}
                cellCount={CELL_COUNT}
                rootStyle={[
                  styles.codeFieldRoot,
                  errorMessage ? { borderColor: theme.colors.pdsThemeColorForegroundError } : {},
                ]}
                keyboardType="number-pad"
                textContentType="oneTimeCode"
                accessible={true}
                accessibilityLabel={getInputAccessibilityLabel()}
                accessibilityHint={Platform.OS === 'android' ? t('ada.doubleTapToEdit') : ''}
                autoComplete={Platform.select({
                  android: 'sms-otp',
                  default: 'one-time-code',
                })}
                testID="otp-input"
                renderCell={({ index, symbol, isFocused }) => (
                  <View key={index} style={styles.cell} onLayout={getCellOnLayoutHandler(index)}>
                    <RNText
                      accessible={false}
                      style={[styles.cellText, !symbol && !isFocused && styles.emptyCellText]}
                    >
                      {symbol || (isFocused ? <Cursor /> : '—')}
                    </RNText>
                  </View>
                )}
              />
              {errorMessage && (
                <View style={styles.errorContainer}>
                  <MaterialCommunityIcons
                    name="minus-circle"
                    size={theme.dimensions.pdsGlobalSpace400}
                    style={styles.errorIcon}
                    color={theme.colors.pdsThemeColorForegroundError}
                    accessible={false}
                  />
                  <View style={styles.errorTextContainer}>
                    <Text
                      accessible
                      accessibilityLabel={t('Error') + ', ' + errorMessage}
                      AccessibilityRole="none"
                      testID={'error-text'}
                      color={theme.colors.pdsThemeColorForegroundError}
                      size={TEXT_SIZE.SMALL}
                      accessibilityHint={''}
                      text={errorMessage}
                      weight={TEXT_WEIGHT.REGULAR}
                      textDecoration={TEXTDECORATION.None}
                    />
                  </View>
                </View>
              )}
            </View>
            {method === 'phone' && emailAddress ? (
              <TextLink
                accessible
                testID="email-code-instead"
                text={t('accountLink.emailCode')}
                size={TEXT_SIZE.LARGE}
                weight="SemiBold"
                onPress={() => {
                  userActionLogEvent(
                    BUTTON_NAV_ANALYTICS.PROFILE_TAB_ACTION,
                    LOYALTY_ANALYTICS.LINK_EMAIL_VERIFICATION_CODE,
                    LOYALTY_ANALYTICS.LOYALTY_LINK_STEP2,
                  );
                  switchMethod();
                }}
              />
            ) : (
              method === 'email' &&
              mobileNumber && (
                <TextLink
                  accessible
                  testID="email-code-instead"
                  text={t('accountLink.textCode')}
                  size={TEXT_SIZE.LARGE}
                  weight="SemiBold"
                  onPress={() => {
                    userActionLogEvent(
                      BUTTON_NAV_ANALYTICS.PROFILE_TAB_ACTION,
                      LOYALTY_ANALYTICS.LINK_EMAIL_VERIFICATION_CODE,
                      LOYALTY_ANALYTICS.LOYALTY_LINK_STEP2,
                    );
                    switchMethod();
                  }}
                />
              )
            )}

            <TouchableOpacity
              testID="resend-code"
              onPress={isResendCodeExpired ? resendCode : undefined}
              accessibilityRole={ADA_ROLES.BUTTON}
              activeOpacity={!isResendCodeExpired ? 1 : 0.8}
              accessibilityLabel={t('accountLink.resendCode')}
              accessible={true}
              accessibilityHint={''}
              disabled={!isResendCodeExpired}
            >
              <Text
                color={!isResendCodeExpired ? 'Muted' : 'Primary'}
                weight={TEXT_WEIGHT.BOLD}
                text={t('accountLink.resendCode')}
                textDecoration="Underline"
                size={TEXT_SIZE.LARGE}
              />
            </TouchableOpacity>

            {!isResendCodeExpired && (
              <Text
                accessible
                text={t('accountLink.pleaseWait', {
                  second: resendCodeTimeLeft,
                })}
                textAlign={TABLET_LANDSCAPE_WIDTH_STYLE.alignSelf}
              />
            )}
          </View>
        </ScrollView>
        <Button
          testID={'accountLink-2-verify-button'}
          fullWidth
          theme={theme}
          size={BUTTON_SIZE.LARGE}
          disabled={value.length !== CELL_COUNT}
          label={t('accountLink.verify')}
          accessibilityHint=""
          onPress={next}
          accessible={true}
        />
      </View>
    </View>
  );
};
export default AccountLinkTwo;

import { Theme } from "pantry-design-system";
import { StyleSheet, Dimensions } from "react-native";
import { IPAD_WIDTH } from "../../shared/constants";
/**
 * Generates a stylesheet for the loyalty screen.
 * @param {Object} theme - The theme object containing dimensions and spacing.
 * @returns {Object} The styles object.
 */
const getStyles = ({
  colors,
  dimensions,
  typography,
  fonts,
  borderDimens,
}: Theme) => {
  const { width, height } = Dimensions.get("window");

  const styles = StyleSheet.create({
    /**
     * Main container for the screen.
     * Centers content and limits width for larger screens.
     */
    container: {
      flex: 1,
      backgroundColor: colors.pdsThemeColorBackgroundBaseline,
      maxWidth: IPAD_WIDTH,
      alignSelf: "center",
    },
    subContainer: {
      flex: 1,
    },
    /**
     * Header container with icon buttons and title.
     */
    headerLeft: {
      alignItems: "center",
      width: dimensions.pdsGlobalSizeWidth800,
      height: dimensions.pdsGlobalSizeHeight800,
      padding: dimensions.pdsGlobalSpace200,
    },
    headerLeftIcon: {
      alignSelf: "center",
      marginBottom: dimensions.pdsGlobalSpace600,
      color: colors.pdsThemeColorOutlinePrimary,
    },
    /** Scroll view container */
    scrollContainer: {
      flexGrow: 1,
      justifyContent: "flex-start",
      alignItems: "center",
      paddingHorizontal: dimensions.pdsGlobalSpace400,
      paddingVertical: dimensions.pdsGlobalSpace500,
    },
    /** For u image */
    imageContainer: {
      alignSelf: "center",
      marginBottom: dimensions.pdsGlobalSpace600,
      color: colors.pdsThemeColorOutlinePrimary,
    },

    /** Bottom stick view which coontains bottong and checkbox */
    footerView: {
      justifyContent: "space-between",
      maxWidth: IPAD_WIDTH,
      paddingHorizontal: dimensions.pdsGlobalSpace300,
    },

    checkboxContainer: {
      flexDirection: "row",
      justifyContent: "flex-start",
      alignSelf: "center",
      marginLeft: dimensions.pdsGlobalSpace400,
      marginVertical: dimensions.pdsGlobalSpace600,
      width: "100%",
    },
    /**
     *Buttons Container.
     */
    buttonContainer: {
      flexDirection: "row",
      justifyContent: "center",
      padding: dimensions.pdsGlobalSpace300,
      alignItems: "center",
    },
    buttonSubView: {
      flex: 1,
      marginHorizontal: dimensions.pdsGlobalSpace200,
    },
    // Step 2
    codeFieldRoot: {
      borderWidth: 1,
      borderColor: colors.pdsThemeColorOutlineNeutralMedium,
      borderRadius: borderDimens.pdsGlobalBorderRadius200,
      padding: dimensions.pdsGlobalSpace400,
      backgroundColor: colors.pdsThemeColorBackgroundBaseline,
    },
    cellText: {
      fontSize: typography.pdsGlobalFontSize500,
      lineHeight: dimensions.pdsGlobalSpace600,
      color: colors.pdsThemeColorForegroundNeutralHigh,
      textAlign: "center",
    },
    emptyCellText: {
      color: colors.pdsThemeColorForegroundNeutralLow,
    },
    cell: {
      flex: 1,
      height: dimensions.pdsGlobalSpace600
    },
    accountLinkTwoContainer: {
      flex: 1,
      backgroundColor: colors.pdsThemeColorBackgroundBaseline,
      paddingHorizontal: width >= 736 ? (width - 736) / 2 : 20,
      // maxWidth: 736,
    },
    forUImage: {
      width: 64,
      height: 49.08,
    },
    verficationExpire: {
      flexDirection: "row",
      gap: dimensions.pdsGlobalSpace200,
      paddingHorizontal: dimensions.pdsGlobalSpace400,
      alignItems: "center",
    },
    contentContainer: {
      marginTop: 22,
      alignItems: "center",
      gap: dimensions.pdsGlobalSpace600,

    },
    sectionSpacing: {
      marginTop: dimensions.pdsGlobalSpace600,
    },
    textContentContainer: {
      marginTop: dimensions.pdsGlobalSpace300,
      width: "100%"
    },
    linkText: {
      color: colors.pdsThemeColorForegroundNeutralHigh,
      fontSize: typography.pdsGlobalFontSize300,
      fontWeight: typography.pdsGlobalFontWeight600,
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
      textDecorationLine: "underline",
    },
    useOfThis: {
      color: colors.pdsThemeColorForegroundNeutralHighInverse,
      fontWeight: typography.pdsGlobalFontWeight400,
      fontSize: typography.pdsGlobalFontSize200,
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
      lineHeight: 20.5,
      textAlign: "center"
    },
    linkContainer: {
      marginTop: dimensions.pdsGlobalSpace600,
      width: "100%"
    },
    errorContainer: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      paddingHorizontal: dimensions.pdsGlobalSpace400,
      marginTop: dimensions.pdsGlobalSpace200,
      flexWrap: 'wrap',
      maxWidth: '100%',
    },
    errorIcon: {
      marginRight: dimensions.pdsGlobalSpace50,
      marginTop: dimensions.pdsGlobalSpace50
    },
    errorTextContainer: {
      flex: 1
    },
    codeFieldContainer: {
      width: "100%",
      alignItems: "flex-start",
      justifyContent: "center"
    },
    loader: {
      flex: 1,
      alignSelf: "center",
      justifyContent: "center",
      alignItems: "center",
      marginTop: dimensions.pdsGlobalSpace800,
      position: 'absolute',
    }
  });
  return styles;
};

export default getStyles;

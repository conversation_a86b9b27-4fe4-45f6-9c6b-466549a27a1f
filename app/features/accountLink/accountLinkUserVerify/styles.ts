import { StyleSheet } from 'react-native';

import { IPAD_WIDTH } from '../../../shared/constants';

import type { Theme } from 'pantry-design-system';
import type { EdgeInsets } from 'react-native-safe-area-context';
/**
 * Generates a stylesheet for the loyalty screen.
 * @param {Object} theme - The theme object containing dimensions and spacing.
 * @returns {Object} The styles object.
 */
const getStyles = (
  { colors, dimensions, typography, fonts }: Theme,
  insets: EdgeInsets,
): ReturnType<typeof StyleSheet.create> => {
  const styles = StyleSheet.create({
    /**
     * Main container for the screen.
     * Centers content and limits width for larger screens.
     */
    container: {
      alignSelf: 'center',
      backgroundColor: colors.pdsThemeColorBackgroundBaseline,
      flex: 1,
      maxWidth: 736,
      width: '100%',
    },
    subContainer: {
      flexGrow: 1,
      justifyContent: 'flex-start',
      alignItems: 'center',
      paddingHorizontal: 26 /** No near by values availble for 26 */,
      paddingVertical: dimensions.pdsGlobalSpace500,
    },
    /**
     * Header container with icon buttons and title.
     */
    headerLeft: {
      alignItems: 'center',
      height: dimensions.pdsGlobalSizeHeight800,
      padding: dimensions.pdsGlobalSpace200,
      width: dimensions.pdsGlobalSizeWidth800,
    },
    headerLeftIcon: {
      alignSelf: 'center',
      color: colors.pdsThemeColorOutlinePrimary,
      marginBottom: dimensions.pdsGlobalSpace600,
    },
    /** Scroll view container */
    scrollContainer: {
      alignItems: 'center',
      flexGrow: 1,
      justifyContent: 'flex-start',
      paddingHorizontal: dimensions.pdsGlobalSpace700,
      paddingVertical: dimensions.pdsGlobalSpace500,
    },
    /** For u image */
    imageContainer: {
      alignSelf: 'center',
      color: colors.pdsThemeColorOutlinePrimary,
      marginBottom: dimensions.pdsGlobalSpace600,
    },
    /** Heading
     *  What to know before linking your {bannerName} for U membership info.
     */
    heading: {
      alignSelf: 'stretch',
      color: colors.pdsThemeColorForegroundNeutralHigh,
      fontFamily: fonts.pdsGlobalFontFamilyPoppins,
      fontSize: typography.pdsGlobalFontSize500,
      fontWeight: typography.pdsGlobalFontWeight700,
      lineHeight: 29,
      paddingHorizontal: dimensions.pdsGlobalSpace600,
      textAlign: 'center',
    },
    heading1: {
      alignSelf: 'stretch',
      color: colors.pdsThemeColorForegroundNeutralHigh,
      fontFamily: fonts.pdsGlobalFontFamilyPoppins,
      fontSize: typography.pdsGlobalFontSize300,
      fontWeight: typography.pdsGlobalFontWeight700,
      paddingHorizontal: dimensions.pdsGlobalSpace600,
      textAlign: 'center',
    },
    /** Bottom stick view which coontains bottong and checkbox */
    footerView: {
      alignItems: 'center',
      alignSelf: 'center',
      flex: 1,
      justifyContent: 'space-between',
      maxWidth: 736,
    },
    infoContainer: {
      alignSelf: 'center',
      marginTop: dimensions.pdsGlobalSpace600,
      width: '100%',
    },
    /**
     *Buttons Container.
     */
    buttonContainer: {
      alignSelf: 'center',
      bottom: insets?.bottom ?? 0,
      maxWidth: IPAD_WIDTH,
      paddingHorizontal: dimensions.pdsGlobalSpace500,
      padding: dimensions.pdsGlobalSpace300,
      paddingBottom: dimensions.pdsGlobalSpace500,
      position: 'absolute',
      width: '95%',
    },
    buttonBottomSpacing: {
      bottom: dimensions.pdsGlobalSpace100,
      maxWidth: IPAD_WIDTH,
      position: 'absolute',
      width: '100%',
    },
    buttonSubView: {
      flex: 1,
      marginBottom: dimensions.pdsGlobalSpace100,
    },
    buttonSubView1: {
      flex: 1,
      marginBottom: dimensions.pdsGlobalSpace500,
      marginTop: dimensions.pdsGlobalSpace500,
    },
    descriptionLinkTextContainer: {
      color: colors.pdsThemeColorForegroundNeutralHigh,
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
      fontSize: typography.pdsGlobalFontSize300,
      fontWeight: typography.pdsGlobalFontWeight400,
      textAlign: 'center',
    },
    descriptionLinkText: {
      color: colors.pdsThemeColorForegroundPrimary,
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
      fontWeight: typography.pdsGlobalFontWeight600,
      textDecorationLine: 'underline',
    },
  });
  return styles;
};

export default getStyles;

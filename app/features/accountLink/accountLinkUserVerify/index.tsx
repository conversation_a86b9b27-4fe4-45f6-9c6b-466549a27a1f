import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { useTheme, Button, Text, Heading } from 'pantry-design-system';
import React, { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { View, ScrollView } from 'react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';
import { SvgXml } from 'react-native-svg';
import { useSelector } from 'react-redux';

import { BUTTON_NAV_ANALYTICS, LOYALTY_ANALYTICS } from '../../../../analytics/AnalyticsConstants';
import { screenViewLog, userActionLogEvent } from '../../../../analytics/AnalyticsUtils';
import { forUIcon } from '../../../../assets/images/svg/LoyaltyAccount';
import { useNetworkAlert } from '../../../hooks/useNetworkAlert';
import {
  BUTTON_SIZE,
  FALLBACK_BANNER,
  HEADING_SIZE,
  SCREENS,
  TEXT_ALIGN,
  TEXT_PANTRY_COLORS,
  TEXT_SIZE,
  TEXT_WEIGHT,
} from '../../../shared/constants';
import { capitalizeFirstLetter } from '../../../utils/helpers';
import { getEmptyStringPlaceholder } from '../../../utils/stringUtils';

import getStyles from './styles';

import type { ProfileState } from '../../../store/reducers/profileSlice';

const AccountLinkUserVerify = (): React.ReactElement => {
  const { theme } = useTheme();
  const insets = useSafeAreaInsets();
  const styles = getStyles(theme, insets);
  const navigation = useNavigation();

  const { t } = useTranslation();

  const { banner, ucaProfile }: ProfileState = useSelector((state: any) => state.profile);

  const mobileNumber = ucaProfile?.profile?.phones?.find((p) => p.type === 'PRIMARY')?.value ?? '';

  const clubCardMember =
    ucaProfile?.loyaltyPrograms?.find((lp) => lp.type === 'CLUBCARD')?.value ?? '';

  const emailAddress = ucaProfile?.profile?.emails?.[0]?.emailAddress ?? '';

  const bannerName = capitalizeFirstLetter(banner ? banner : FALLBACK_BANNER);
  const { showAlertWithRetry, checkInternetConnection } = useNetworkAlert();

  const changeMyInfo = () => {
    userActionLogEvent(
      BUTTON_NAV_ANALYTICS.PROFILE_TAB_ACTION,
      LOYALTY_ANALYTICS.LINK_CHANGE_MEMBER_INFO,
      LOYALTY_ANALYTICS.LOYALTY_LINK_CONFIRMATION,
    );
    navigation.goBack();
  };

  const continueToVerify = async () => {
    userActionLogEvent(
      BUTTON_NAV_ANALYTICS.PROFILE_TAB_ACTION,
      LOYALTY_ANALYTICS.LINK_CONTINUE_VERIFY,
      LOYALTY_ANALYTICS.LOYALTY_LINK_CONFIRMATION,
    );

    const isOnline = await checkInternetConnection();
    if (!isOnline) {
      showAlertWithRetry(continueToVerify);
      return;
    }
    navigation.navigate(SCREENS.ACCOUNT_LINK_STEP_TWO);
  };

  useFocusEffect(
    useCallback(() => {
      screenViewLog({
        subsection1: BUTTON_NAV_ANALYTICS.PROFILE_TAB_ACTION,
        subsection2: LOYALTY_ANALYTICS.LOYALTY_LINK_CONFIRMATION,
        event_category_link: BUTTON_NAV_ANALYTICS.PROFILE_TAB_ACTION,
        event_label_link: LOYALTY_ANALYTICS.LOYALTY_LINK_CONFIRMATION,
      });
    }, []),
  );

  return (
    <SafeAreaView edges={['top', 'bottom']} style={styles.container}>
      <View>
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContainer}
        >
          <SvgXml
            xml={forUIcon}
            accessible={true}
            accessibilityLabel={t('ada.forUImage')}
            style={styles.imageContainer}
            testID="forU-image"
          />
          <Heading
            accessible
            testID={'before-you-linking-text-heading'}
            textAlign={TEXT_ALIGN.CENTER}
            title={t('accountLink.confirmYourInfo').replace(
              '[Banner]',
              bannerName ?? FALLBACK_BANNER,
            )}
            color={TEXT_PANTRY_COLORS.NEUTRAL_HIGH}
            size={HEADING_SIZE.Medium}
          />
          <View style={styles.infoContainer}>
            <Text
              color={TEXT_PANTRY_COLORS.NEUTRAL_HIGH}
              size={TEXT_SIZE.LARGE}
              text={t('accountLink.doesThisLookCorrect')}
              weight={TEXT_WEIGHT.REGULAR}
              textAlign={TEXT_ALIGN.CENTER}
              testID="text-description-one"
            />
          </View>

          <View style={styles.infoContainer}>
            <Text
              color={TEXT_PANTRY_COLORS.NEUTRAL_HIGH}
              size={TEXT_SIZE.LARGE}
              text={t('accountLink.phoneNumber')}
              weight={TEXT_WEIGHT.REGULAR}
              textAlign={TEXT_ALIGN.CENTER}
            />
            <Text
              color={TEXT_PANTRY_COLORS.NEUTRAL_HIGH}
              size={TEXT_SIZE.LARGE}
              text={getEmptyStringPlaceholder(mobileNumber)}
              weight={TEXT_WEIGHT.REGULAR}
              textAlign={TEXT_ALIGN.CENTER}
              accessibilityLabel={mobileNumber ? mobileNumber : t('ada.noPhoneNumberRegistered')}
            />
          </View>

          <View style={styles.infoContainer}>
            <Text
              color={TEXT_PANTRY_COLORS.NEUTRAL_HIGH}
              size={TEXT_SIZE.LARGE}
              text={t('accountLink.email')}
              weight={TEXT_WEIGHT.BOLD}
              textAlign={TEXT_ALIGN.CENTER}
            />
            <Text
              color={TEXT_PANTRY_COLORS.NEUTRAL_HIGH}
              size={TEXT_SIZE.LARGE}
              text={getEmptyStringPlaceholder(emailAddress)}
              weight={TEXT_WEIGHT.REGULAR}
              textAlign={TEXT_ALIGN.CENTER}
              accessibilityLabel={emailAddress ? emailAddress : t('ada.noEmailRegistered')}
            />
          </View>

          <View style={styles.infoContainer}>
            <Text
              color={TEXT_PANTRY_COLORS.NEUTRAL_HIGH}
              size={TEXT_SIZE.LARGE}
              text={t('accountLink.clubCardNumber')}
              weight={TEXT_WEIGHT.BOLD}
              textAlign={TEXT_ALIGN.CENTER}
            />
            <Text
              color={TEXT_PANTRY_COLORS.NEUTRAL_HIGH}
              size={TEXT_SIZE.LARGE}
              text={getEmptyStringPlaceholder(clubCardMember)}
              weight={TEXT_WEIGHT.REGULAR}
              textAlign={TEXT_ALIGN.CENTER}
            />
          </View>
        </ScrollView>
      </View>

      <View style={styles.buttonContainer} accessible={false}>
        <View style={styles.buttonSubView} accessible={false}>
          <Button
            theme={theme}
            width={'100%'}
            size={BUTTON_SIZE.LARGE}
            label={t('accountLink.continueToVerify')}
            onPress={continueToVerify}
            accessible={true}
            accessibilityHint=""
            accessibilityRole="button"
            accessibilityLabel={t('ada.continueToVerify')}
            testID="continue-verify-button"
          />
        </View>
        <View style={styles.buttonSubView1}>
          <Button
            theme={theme}
            width={'100%'}
            size={'Large'}
            variant={'Outlined'}
            label={t('accountLink.changeMyMemberInfo')}
            onPress={changeMyInfo}
            customContentDescription={t('accountLink.changeMyMemberInfo')}
            accessible={true}
            accessibilityHint=""
            accessibilityLabel={t('ada.changeMyMemberInfo')}
            testID="change-member-info-button"
          />
        </View>
      </View>
    </SafeAreaView>
  );
};

export default AccountLinkUserVerify;

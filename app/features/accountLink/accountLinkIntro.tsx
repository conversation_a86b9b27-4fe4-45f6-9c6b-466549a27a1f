import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { useTheme, Checkbox, Button, Heading, Text } from 'pantry-design-system';
import React, { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { View, Text as RNText, ScrollView, Platform } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { SvgXml } from 'react-native-svg';
import { useDispatch, useSelector } from 'react-redux';

import {
  BUTTON_NAV_ANALYTICS,
  LOYALTY_ANALYTICS,
  PROFILE_ANALYTICS,
} from '../../../analytics/AnalyticsConstants';
import { screenViewLog, userActionLogEvent } from '../../../analytics/AnalyticsUtils';
import {
  SCREENS,
  FALLBACK_BANNER,
  TERMS_AND_CONDITIONS_URL,
  TEXTLINK_COLORS,
  TEXT_REPLACEMENTS,
  TEXT_SIZE,
  ADA,
  TNC_AGREEMENT,
} from '../../../app/shared/constants';
import { forUIcon } from '../../../assets/images/svg/LoyaltyAccount';
import useAccessibilityFocus from '../../hooks/useAccessibilityFocus';
import { capitalizeFirstLetter } from '../../utils/helpers';

import getStyles from './styles';

import type { AuthorableContent } from '../../misc/models/authorableModels';
import type { AccessibilityActionEvent } from 'react-native';
import { TncAgreementType, TncAgreementEvent, TncAgreementStatus } from "../../../app/shared/constants";
import { buildTncAgreementRequestBody } from '../../utils/helpers';
import { fetchTncAgreementRequest } from '../../store/reducers/tncAgreementSlice';
import { useUpdateEffect } from '../../hooks';
import * as AsyncStorage from '../../../app/store/AsyncStorage';
import { ActivityIndicator } from 'react-native-paper';

const AccountLinkIntro = () => {
  const { theme } = useTheme();
  const styles = getStyles(theme);
  const [isUnchecked, setIsUnchecked] = useState(true);
  const { t } = useTranslation();

  const authorableContent: AuthorableContent = useSelector(
    (state: any) => state.authorableContent.authorableContent,
  );
  const profileData = useSelector((state: any) => state.profile);
  const accountLinkage = authorableContent?.accountLinkage;
  const navigation = useNavigation<any>();
  const bannerName = capitalizeFirstLetter(
    profileData?.banner ? profileData?.banner : FALLBACK_BANNER,
  );

  const { setLastFocused, setRef } = useAccessibilityFocus();
  const dispatch = useDispatch()
  const tncDataResponse = useSelector((state: any) => state.tncAgreement);
  const [loading, setLoading] = useState(false);

  // Navigates to the WebView screen with the given URL and header title to open Terms of Use.
  const handleWebViewNavigation = (url: string, titleHeader: string) => {
    navigation.navigate(SCREENS.WEBVIEW, {
      url,
      titleHeader,
    });
  };

  useUpdateEffect(() => {
    if (!tncDataResponse?.errors && !tncDataResponse?.loading && tncDataResponse?.result?.status == 'Success') {
      handleNavigation(SCREENS.ACCOUNT_LINK_USER_INPUT_STEP_ONE);
      AsyncStorage.setOptInLoyaltyTNC(TNC_AGREEMENT.LOYALTY_TNC.ID)
    }
    setLoading(tncDataResponse?.loading)
  }, [tncDataResponse]);

  useFocusEffect(
    useCallback(() => {
      userActionLogEvent(
        BUTTON_NAV_ANALYTICS.PROFILE_TAB_ACTION,
        PROFILE_ANALYTICS.LINKFORU.replace('[banner]', bannerName),
        BUTTON_NAV_ANALYTICS.PROFILE_TAB_ACTION,
      );
      screenViewLog({
        subsection1: BUTTON_NAV_ANALYTICS.PROFILE_TAB_ACTION,
        subsection2: LOYALTY_ANALYTICS.LOYALTY_LINK_CONSENT,
        event_category_link: BUTTON_NAV_ANALYTICS.PROFILE_TAB_ACTION,
        event_label_link: LOYALTY_ANALYTICS.LOYALTY_LINK_CONSENT,
      });
    }, []),
  );

  // Navigates on the Privacy Policy or next first step of account linking flow .
  const handleNavigation = (screen: string, testID?: string) => {
    setLastFocused(testID ?? '');
    navigation.navigate(screen);
  };

  const close = () => {
    userActionLogEvent(
      BUTTON_NAV_ANALYTICS.PROFILE_TAB_ACTION,
      LOYALTY_ANALYTICS.LINK_CLOSE_CTA,
      LOYALTY_ANALYTICS.LOYALTY_LINK_CONSENT,
    );
    navigation.goBack();
  };

  return (
    <SafeAreaView edges={['top', 'bottom']} style={styles.container}>
      <View>
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContainer}
        >
          <SvgXml
            xml={forUIcon}
            accessible={true}
            accessibilityLabel={t('ada.forUImage')}
            style={styles.imageContainer}
            testID="forU-image"
          />

          <Heading
            accessible
            testID={'before-you-linking-text-heading'}
            textAlign="center"
            title={accountLinkage?.title?.replace('[banner]', bannerName)}
            color={TEXTLINK_COLORS.NEUTRAL_HIGH}
          />

          {accountLinkage?.content.map((item, index) => {
            // Check if the text contains "Privacy Policy" and split it
            const text = item.text.replace('[banner]', bannerName);

            if (item.type === 'acknowledgement') {
              return (
                <View key={index} style={styles.checkboxContainer}>
                  <Checkbox
                    theme={theme}
                    onChange={() => {
                      if (isUnchecked) {
                        userActionLogEvent(
                          BUTTON_NAV_ANALYTICS.PROFILE_TAB_ACTION,
                          LOYALTY_ANALYTICS.LINK_CONSENT_CHECKBOX_SELECTED,
                          LOYALTY_ANALYTICS.LOYALTY_LINK_CONSENT,
                        );
                      }
                      setIsUnchecked(!isUnchecked);
                    }}
                    label={item.text}
                    testID="acnkowledge-checkbox"
                  />
                </View>
              ); // Load the checkbox view and rendering only this item
            }
            // Split the text by "Privacy Policy" and "Terms of Use" and render as links
            const parts = text.split(/(\[terms_of_use\]|\[privacy_policy\])/g);

            /**
             * Handles navigation to Terms of Use.
             */
            const handleTermsOfUse = () => {
              setLastFocused('use-terms-privacy-container');
              navigation.navigate(SCREENS.WEBVIEW, {
                url: TERMS_AND_CONDITIONS_URL ?? '',
                titleHeader: t('Terms of Use'),
              });
            };

            /**
             * Handles custom accessibility actions for screen readers.
             * @param event - AccessibilityActionEvent
             */
            const handleAccessibilityAction = (event: AccessibilityActionEvent) => {
              switch (event.nativeEvent.actionName) {
                case 'termsOfUSe':
                  handleTermsOfUse();
                  break;
                case 'privacyPolicy':
                  handleNavigation(SCREENS.PRIVACY_POLICY, 'use-terms-privacy-container');
                  break;
              }
            };

            return (
              <View key={index} style={styles.linkContainer}>
                <View
                  ref={setRef('use-terms-privacy-container')}
                  // style={styles.termAndPrivacyLink}
                  testID="use-terms-privacy-container"
                  accessible={true}
                  // accessibilityLabel={`${t('Terms of Use')} in list`}
                  accessibilityHint={
                    Platform.OS === 'ios' ? t('ada.access_link_swipe_up_down_ios') : ''
                  }
                  accessibilityActions={[
                    { name: 'termsOfUSe', label: t('ada.termsOfUse') },
                    { name: 'privacyPolicy', label: t('ada.privacyPolicy') },
                  ]}
                  onAccessibilityAction={(event) => handleAccessibilityAction(event)}
                >
                  <RNText>
                    {parts.map((part, idx) => {
                      if (part === TEXT_REPLACEMENTS.TERMS_OF_USE) {
                        return (
                          <RNText
                            key={`notice-text-${index}-${idx}`}
                            accessible={false}
                            style={styles.useOfThis}
                            testID="notice-text"
                          >
                            <RNText
                              key={`terms-link-${index}-${idx}`}
                              ref={setRef('terms-of-use')}
                              accessible={false}
                              onPress={() =>
                                handleWebViewNavigation(
                                  TERMS_AND_CONDITIONS_URL ?? '',
                                  t('Terms of Use'),
                                )
                              }
                              style={styles.linkText}
                              testID="terms-link"
                            >
                              {t('Terms of Use')}
                            </RNText>
                          </RNText>
                        );
                      }

                      if (part === TEXT_REPLACEMENTS.PRIVACY_POLICY) {
                        return (
                          <RNText
                            key={`privacy-policy-link-${index}-${idx}`}
                            ref={setRef('privacy-policy-link')}
                            accessible={true}
                            accessibilityLabel={`${t('Privacy Policy')} in list`}
                            onPress={() =>
                              handleNavigation(SCREENS.PRIVACY_POLICY, ADA.PRIVACY_POLICY)
                            }
                            style={styles.linkText}
                            testID="privacy-policy-link"
                          >
                            {t('Privacy Policy')}
                          </RNText>
                        );
                      }
                      // Render normal text
                      return (
                        <Text
                          key={`text-description-part-${index}-${idx}`}
                          color={TEXTLINK_COLORS.NEUTRAL_HIGH}
                          size={TEXT_SIZE.LARGE}
                          text={part}
                          textAlign="center"
                          testID={`text-description-part-${index}-${idx}`}
                        />
                      );
                    })}
                  </RNText>
                </View>
              </View>
            );
          })}
        </ScrollView>
        <View style={styles.footerView}>
          <View style={styles.buttonContainer}>
            <View style={styles.buttonSubView}>
              <Button
                theme={theme}
                width={'100%'}
                variant={'Outlined'}
                size={TEXT_SIZE.LARGE}
                label={t('accountLink.closeBtn')}
                onPress={close}
                customContentDescription={t('accountLink.closeBtn')}
                accessible={true}
                testID="close-button"
              />
            </View>
            <View style={styles.buttonSubView}>
              <Button
                ref={setRef('begin-button')}
                theme={theme}
                width={'100%'}
                size={TEXT_SIZE.LARGE}
                disabled={isUnchecked}
                label={t('accountLink.beginBtn')}
                onPress={async () => {
                  setLoading(true)
                  userActionLogEvent(
                    BUTTON_NAV_ANALYTICS.PROFILE_TAB_ACTION,
                    LOYALTY_ANALYTICS.LINK_BEGIN,
                    LOYALTY_ANALYTICS.LOYALTY_LINK_CONSENT,
                  );
                  let body = await buildTncAgreementRequestBody(TncAgreementType.LOYALTY_TNC, TncAgreementEvent.new, TncAgreementStatus.optIn)
                  dispatch(fetchTncAgreementRequest({ body }))
                }}
                customContentDescription={t('accountLink.beginBtn')}
                accessible={true}
                accessibilityLabel={
                  Platform.OS === 'ios'
                    ? isUnchecked
                      ? `${t('accountLink.beginBtn')} ${t('ada.beginButtonDisabledIos')}`
                      : `${t('accountLink.beginBtn')}`
                    : t('accountLink.beginBtn')
                }
                accessibilityRole={undefined}
                accessibilityHint=""
                testID="begin-button"
              />
            </View>
          </View>
        </View>
      </View>
      {loading && (
        <View style={styles.loader}>
          <ActivityIndicator color={theme.colors.pdsThemeColorBackgroundPrimary} />
        </View>
      )}
    </SafeAreaView>
  );
};

export default AccountLinkIntro;

/**
 * AccountLinkAllDone component renders a screen indicating that the account linking process is complete.
 *
 * This component uses various hooks and components to display a confirmation message and a button to navigate back to the profile screen.
 * @function close
 * Navigates back to the bottom tab stack screen.
 *
 * @param {Object} theme - The current theme object.
 * @param {Object} styles - The styles object generated based on the theme.
 * @param {Object} navigation - The navigation object to handle navigation.
 * @param {string} userName - The user's first name to be displayed in the heading.
 * @param {Object} t - The translation function from react-i18next.
 *
 * @returns {JSX.Element} The rendered component.
 */
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { useTheme, Button, Text, Heading } from 'pantry-design-system';
import React, { useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { View, Text as RNText, Linking } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { SvgXml } from 'react-native-svg';
import { useDispatch, useSelector } from 'react-redux';

import ABAuditEngine from '../../../analytics/ABAuditEngine';
import { LOYALTY_ANALYTICS, BUTTON_NAV_ANALYTICS } from '../../../analytics/AnalyticsConstants';
import { screenViewLog, userActionLogEvent } from '../../../analytics/AnalyticsUtils';
import { HttpStatusCode } from '../../../app/config/errorCodes';
import {
  groceryBag,
  groceryBagTwoImg,
  enrollmentReveiewImg,
} from '../../../assets/images/svg/LoyaltyAccount';
import { SCREENS, LEGAL_CONSENT } from '../../shared/constants';
import { fetchRewardsScorecardRequest } from '../../store/reducers/scorecardSlice';
import { SecureStorage, TokenType } from '../../store/SecureStorage';
import { getHouseholdIdFromProfile } from '../../utils/setAndGetHHID';

import getStyles from './accountLinkUserVerify/styles';

import type { AssociateProfile } from '../../misc/models/ProfileModal';
import type { BottomTabNavigatorParamList } from '../../navigation/types'; // Adjust the import path as necessary
import type { StackNavigationProp } from '@react-navigation/native';

const AccountLinkAllDone = (props: any) => {
  const { theme } = useTheme();
  const navigation = useNavigation<StackNavigationProp<BottomTabNavigatorParamList>>();
  const styles = getStyles(theme);
  const { t } = useTranslation();
  const { responseStatus = HttpStatusCode.Success, doesEnrolmentUnderReview = false } =
    props?.route?.params || {};

  const getScreenType = () => {
    return responseStatus === HttpStatusCode.ConflictError
      ? LOYALTY_ANALYTICS.LOYALTY_ALREADY_LINKED
      : LOYALTY_ANALYTICS.LOYALTY_LINKING_IN_REVIEW;
  };

  useFocusEffect(
    useCallback(() => {
      const screenView = getScreenType();
      screenViewLog({
        subsection1: BUTTON_NAV_ANALYTICS.PROFILE_TAB_ACTION,
        subsection2: screenView,
        event_category_link: BUTTON_NAV_ANALYTICS.PROFILE_TAB_ACTION,
        event_label_link: screenView,
      });
    }, []),
  );

  useEffect(() => {
    navigation.setOptions({
      headerTitle: doesEnrolmentUnderReview
        ? t('accountLink.underReviewTitle')
        : t('accountLink.allDone'),
    });
  }, []);

  const dispatch = useDispatch();

  const { profile }: { profile: AssociateProfile } = useSelector((state: any) => state.profile);

  const secureStorage = new SecureStorage();

  /**
   * This hook is triggered when the screen gains focus. It performs the following actions:
   * 1. Loads the household ID associated with the user's profile.
   * 2. Dispatches a request to fetch the rewards scorecard if a valid household ID is found.
   *
   * Dependencies:
   * - `profile`: The user's profile data used to fetch the household ID.
   *
   * @returns {void}
   */
  useFocusEffect(
    useCallback(() => {
      /**
       * Function to load the household ID and fetch the rewards scorecard.
       * - Fetches the household ID from the user's profile.
       * - Dispatches an action to fetch the rewards scorecard if the household ID is valid.
       */
      const loadHousehold = async () => {
        const id = await getHouseholdIdFromProfile(profile); // Fetch household ID from profile

        if (id) {
          // Dispatch an action to fetch the rewards scorecard if household ID is valid
          dispatch(
            fetchRewardsScorecardRequest({
              body: {
                hhid: id, // Household ID
                programType: ['BASEPOINTS'], // Specify the program type
              },
            }),
          );
        }
      };

      /**
       * Tracks the account linking process by recording a breadcrumb in the audit engine.
       *
       * This function retrieves the employee ID from secure storage and logs a breadcrumb
       * with a timestamp and the employee ID for auditing purposes.
       *
       * @async
       * @function trackAccountLinking
       * @returns {Promise<void>} Resolves when the breadcrumb is successfully logged.
       *
       * @throws {Error} Throws an error if retrieving the employee ID from secure storage fails.
       */
      const trackAccountLinking = async () => {
        const employeeId = await secureStorage.getToken(TokenType.employeeId);
        const timeStamp = new Date().toLocaleString();
        ABAuditEngine.leaveBreadcrumbWithMode(
          `${LEGAL_CONSENT.LOYALTY_ACCOUNT_OPT_IN} (${timeStamp}) - EmployeeID:${employeeId}`,
        );
      };

      // Call the function to load household data
      loadHousehold();
      trackAccountLinking();
    }, [profile]), // Dependency array ensures the callback is re-created when `profile` changes
  );

  /**
   * Handles the close button press.
   * Logs the user action and navigates back to the profile screen.
   */
  const close = () => {
    const screenView = getScreenType();
    userActionLogEvent(
      BUTTON_NAV_ANALYTICS.PROFILE_TAB_ACTION,
      LOYALTY_ANALYTICS.LINK_CLOSE_CTA,
      screenView,
    );
    navigation.navigate(SCREENS.BOTTOM_TAB_STACK, { screen: SCREENS.PROFILE });
  };

  /**
   * Returns the appropriate image path based on the available image variables.
   */
  const getImagePath = () => {
    if (responseStatus === HttpStatusCode.Success) {
      return doesEnrolmentUnderReview ? enrollmentReveiewImg : groceryBag;
    } else if (responseStatus === HttpStatusCode.ConflictError) {
      return groceryBagTwoImg;
    }
  };

  const getHeaderText = () => {
    if (responseStatus === HttpStatusCode.Success) {
      return doesEnrolmentUnderReview
        ? t('accountLink.enrollmentInReviewHeader')
        : t('accountLink.youAreAllSet').replace(
          '[FirstName]',
          profile?.names?.[0]?.firstName ?? '',
        );
    } else if (responseStatus === HttpStatusCode.ConflictError) {
      return t('accountLink.alreadyLinkedHeader');
    }
  };

  const getDescriptionText = () => {
    return doesEnrolmentUnderReview
      ? t('accountLink.enrollmentInReviewDescription')
      : t('accountLink.allDoneDiscriptionOne');
  };

  let phoneNumber, beforeText, afterText, fullAccessibilityLabel;

  if (responseStatus === HttpStatusCode.ConflictError) {
    const phoneRegex = /(\d{3}-\d{3}-\d{4})/;
    const match = t('accountLink.alreadyLinkedDescription').match(phoneRegex);

    phoneNumber = match?.[1];
    [beforeText, afterText] = match
      ? t('accountLink.alreadyLinkedDescription').split(match[0])
      : [t('accountLink.alreadyLinkedDescription'), ''];

    fullAccessibilityLabel = match
      ? `${beforeText}${phoneNumber}${afterText}. ${t('ada.doubleTapToCall')}.`
      : t('accountLink.alreadyLinkedDescription');
  }

  return (
    <SafeAreaView edges={['top', 'bottom']} style={styles.container}>
      <View style={styles.subContainer} accessible={false}>
        {/** * - `SvgXml`: Renders an SVG image. */}
        <SvgXml
          xml={getImagePath()}
          accessible={false}
          style={styles.imageContainer}
          testID="all-done-forU-image"
        />
        {/** * - `Heading`: Displays a heading text. */}
        <Heading
          accessible
          testID={'all-done-heading'}
          textAlign="center"
          title={getHeaderText()}
          color="Neutral high"
          size="medium"
        />
        <View style={styles.infoContainer}>
          {/** * - `Text`: Displays descriptive text. */}
          {responseStatus != HttpStatusCode.ConflictError && (
            <Text
              color="Neutral high"
              size="Large"
              text={getDescriptionText()}
              weight="Regular"
              accessible={true}
              testID="all-done-description-one"
              textDecoration="None"
              textAlign="center"
            />
          )}
          {responseStatus == HttpStatusCode.ConflictError && (
            <RNText
              accessible={true}
              accessibilityRole="text"
              accessibilityLabel={fullAccessibilityLabel}
              testID="all-done-description-one"
              style={styles.descriptionLinkTextContainer}
            >
              {beforeText}
              {phoneNumber && (
                <RNText
                  style={styles.descriptionLinkText}
                  onPress={() => Linking.openURL(`tel:${phoneNumber.replace(/-/g, '')}`)}
                  accessible={false}
                >
                  {phoneNumber}
                </RNText>
              )}
              {afterText}
            </RNText>
          )}
        </View>
        <View style={styles.buttonBottomSpacing} accessible={false}>
          {/** * - `Button`: A button to close the screen and navigate back. */}
          <Button
            theme={theme}
            width={'100%'}
            size={'Large'}
            label={t('accountLink.closeBtn')}
            customContentDescription={t('accountLink.closeBtn')}
            onPress={close}
            accessible={true}
            accessibilityLabel={t('accountLink.closeBtn')}
            accessibilityRole="button"
            testID="all-Done-close--button"
          />
        </View>
      </View>
    </SafeAreaView>
  );
};

export default AccountLinkAllDone;

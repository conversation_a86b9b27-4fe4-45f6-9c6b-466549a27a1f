import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import AccountLinkUserVerify from '../AccountLinkUserVerify';
import { useTheme } from 'pantry-design-system';
import { useTranslation } from 'react-i18next';
import { useNavigation } from "@react-navigation/native";
import { SCREENS } from "../../../shared/constants";
import configureStore from 'redux-mock-store';
import { Provider } from 'react-redux';
import { BUTTON_NAV_ANALYTICS, LOYALTY_ANALYTICS } from '../../../../analytics/AnalyticsConstants';
import { screenViewLog, userActionLogEvent } from '../../../../analytics/AnalyticsUtils';
import * as AnalyticsUtils from "../../../../analytics/AnalyticsUtils";


const mockStore = configureStore([]);

jest.mock('@react-navigation/native', () => ({
    useNavigation: jest.fn(() => ({
        setOptions: jest.fn(),
    })),
    useFocusEffect: jest.fn((cb) => {
        cb();
    }),
}));

jest.mock('react-native-svg', () => ({
    SvgXml: jest.fn(({ xml, testID }) => <svg testID={testID}>{xml}</svg>),
}));

jest.mock('@react-native-firebase/analytics', () => {
  return jest.fn(() => ({
    logEvent: jest.fn(),
    logScreenView: jest.fn(),
    setUserId: jest.fn(),
    setUserProperties: jest.fn(),
  }));
});

jest.mock('../../../../analytics/AnalyticsUtils', () => ({
  screenViewLog: jest.fn(),
  userActionLogEvent: jest.fn(),
  setUserProperties: jest.fn(),
}));

jest.mock('pantry-design-system', () => ({
    useTheme: jest.fn(() => ({
        theme: {
            colors: { pdsThemeColorBackgroundBaseline: "#ffffff" },
            fonts: { pdsGlobalFontFamilyNunitoSans: "Nunito Sans" },
            dimensions: { pdsGlobalSpace800: 24 },
            typography: { pdsGlobalFontSize500: 18 },
        },
    })),
    Button: jest.fn(({ onPress, label, testID }) => (
        <button onClick={onPress} testID={testID}>
            {label}
        </button>
    )),
    Heading: jest.fn(({ title, testID }) => <text testID={testID}>{title}</text>),
    Text: jest.fn(({ text, testID }) => <text testID={testID}>{text}</text>),
}));

jest.mock('../styles', () =>
    jest.fn(() => ({
        container: {},
        scrollContainer: {},
        imageContainer: {},
        heading: {},
        description: {},
        infoContainer: {},
        heading1: {},
        footerView: {},
        buttonContainer: {},
        buttonSubView: {},
        buttonSubView1: {},
    }))
);

jest.mock('react-i18next', () => ({
    useTranslation: jest.fn(() => ({ t: (key) => key })),
}));

describe('AccountLinkUserVerify Component', () => {
    const mockNavigate = jest.fn();

    const initialState = {
        profile: {
            banner: "albertsons",
            profile: {
                names: [{ firstName: "John" }],
            },
            ucaProfile: {
                profile: {
                    phones: [
                        {
                            type: "PRIMARY",
                            value: "**********",
                        },
                    ],
                    emails: [
                        {
                            type: "PRIMARY",
                            value: "<EMAIL>",
                        },
                    ],
                },
                householdAccount: {
                    householdId: ************,
                },
                loyaltyPrograms: [
                    {
                        type: "CLUBCARD",
                        value: "***********",
                    },
                ],
            },
        },
    };


    beforeEach(() => {
        useNavigation.mockReturnValue({
            navigate: mockNavigate,
            goBack: jest.fn(),
        });
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    const renderWithProvider = () => {
        const store = mockStore(initialState);
        return render(
            <Provider store={store}>
                <AccountLinkUserVerify />
            </Provider>
        );
    };

    it('should render all components correctly', () => {
        const { getByTestId } = renderWithProvider();
        expect(getByTestId('forU-image')).toBeTruthy();
        expect(getByTestId('continue-verify-button')).toBeTruthy();
        expect(getByTestId('change-member-info-button')).toBeTruthy();
    });

    it('should call the close function when close button is pressed', async () => {
        const { getByTestId } = renderWithProvider();
        const closeButton = getByTestId('continue-verify-button');
        await fireEvent.press(closeButton);
        expect(mockNavigate).toHaveBeenCalledWith(SCREENS.ACCOUNT_LINK_STEP_TWO);
    });

    it('should call the begin function when begin button is pressed', () => {
        const { getByTestId } = renderWithProvider();
        const continueToVerify = getByTestId('change-member-info-button');
        fireEvent.press(continueToVerify);
        expect(mockNavigate).not.toHaveBeenCalled();
        const navigation = useNavigation();
        expect(navigation.goBack).toHaveBeenCalled();
    });
});
import { useNavigation } from '@react-navigation/native';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import React from 'react';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';

import AccountLinkTwo from '../accountLinkTwo';
import { BUTTON_NAV_ANALYTICS, LOYALTY_ANALYTICS } from '../../../../analytics/AnalyticsConstants';
import { screenViewLog, userActionLogEvent } from '../../../../analytics/AnalyticsUtils';
import * as AnalyticsUtils from "../../../../analytics/AnalyticsUtils";


jest.mock('@react-navigation/native', () => ({
  useNavigation: jest.fn(() => ({
    navigate: jest.fn(), // Add navigate function
    replace: jest.fn(),  // Keep replace function if needed
  })),
  useFocusEffect: jest.fn((cb) => {
    cb();
  }),
}));

jest.mock('@react-native-firebase/app', () => ({
  getApp: jest.fn(() => ({
    name: '[DEFAULT]',
  })),
}));


jest.mock('react-i18next', () => ({
  useTranslation: jest.fn(() => ({ t: jest.fn((key) => key) })),
}));

// Mock announceADA to prevent Jest errors due to missing browser APIs
jest.mock('../../../utils/helpers', () => ({
  ...jest.requireActual('../../../utils/helpers'),
  announceADA: jest.fn(),
}));

jest.mock('pantry-design-system', () => ({
  useTheme: jest.fn(() => ({
    theme: {
      colors: {
        pdsThemeColorBackgroundPrimary: '#000000',
        pdsThemeColorNeutralLow: '#FFFFFF',
      },
      dimensions: {
        pdsGlobalSpace1000: 10,
        pdsGlobalSpace1600: 15,
      },
      typography: {
        pdsGlobalFontSize500: 18,
        pdsGlobalFontSize600: 20,
      },
      fonts: {
        pdsGlobalFontFamilyPoppins: 'Poppins',
      },
      borderDimens: {
        pdsGlobalBorderRadius200: 8,
      },
    },
  })),
  Heading: jest.fn(({ title, testID }) => <text testID={testID}>{title}</text>),
  Text: jest.fn(({ text, testID }) => <text testID={testID}>{text}</text>),
  TextLink: jest.fn(({ text, onPress, testID }) => (
    <text onPress={onPress} testID={testID}>
      {text}
    </text>
  )),
  Button: jest.fn(({ onPress, label, testID }) => (
    <button onClick={onPress} testID={testID}>
      {label}
    </button>
  )),
}));

jest.mock('react-native-svg', () => ({
  SvgXml: jest.fn(({ xml, testID }) => <svg testID={testID}>{xml}</svg>),
}));

jest.mock('react-native-confirmation-code-field', () => ({
  CodeField: jest.fn(({ testID }) => <text testID={testID} />),
  useBlurOnFulfill: jest.fn(),
  useClearByFocusCell: jest.fn().mockReturnValue([]),
}));

const mockStore = configureStore([]);

describe('AccountLinkTwo Component', () => {
  const mockNavigate = jest.fn();
  let store: any;

  const initialState = {
    otpVerify: {
      loading: false,
      response: {},
    },
    discount: {
      loading: false,
      response: {},
    },
    profile: {
      banner: 'albertsons',
      profile: {
        names: [{ firstName: 'John' }],
      },
      ucaProfile: {
        profile: {
          phones: [
            {
              type: 'PRIMARY',
              value: '**********',
            },
          ],
          emails: [
            {
              type: 'PRIMARY',
              value: '<EMAIL>',
            },
          ],
        },
        householdAccount: {
          householdId: ************,
        },
        loyaltyPrograms: [
          {
            type: 'CLUBCARD',
            value: '***********',
          },
        ],
      },
    },
  };

  beforeEach(() => {
    useNavigation.mockReturnValue({ navigate: mockNavigate });
    store = mockStore(initialState);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('should render all components correctly', () => {
    const { getByTestId } = render(
      <Provider store={store}>
        <AccountLinkTwo />
      </Provider>,
    );

    expect(getByTestId('did-you-get-text-heading')).toBeTruthy();
    expect(getByTestId('enter-code-text')).toBeTruthy();
    expect(getByTestId('where-sent-code-text')).toBeTruthy();
    expect(getByTestId('timer-icon')).toBeTruthy();
    expect(getByTestId('otp-input')).toBeTruthy();
    expect(getByTestId('accountLink-2-verify-button')).toBeTruthy();
  });

  test.skip('should switch method to email and reset timers', async () => {
    const { getByTestId } = render(
      <Provider store={store}>
        <AccountLinkTwo />
      </Provider>,
    );

    await fireEvent.press(getByTestId("email-code-instead"));
    await waitFor(() => {
      expect(getByTestId("where-sent-code-text").props.children).toBe("");
    });
  });

  test('should navigate to next screen on verify button press', async () => {
    // Mock any async/dispatch actions that trigger navigation
    jest.spyOn(store, 'dispatch').mockImplementation((action) => {
      // Simulate successful OTP verification and navigation
      if (typeof action === 'function') {
        action(
          () => { },
          () => store.getState(),
        );
      }
      // Optionally, directly call navigation if that's how your thunk works
      mockNavigate('accountLinkAllDone');
      return action;
    });

    const { getByTestId } = render(
      <Provider store={store}>
        <AccountLinkTwo />
      </Provider>,
    );

    fireEvent.changeText(getByTestId('otp-input'), '123456');
    fireEvent.press(getByTestId('accountLink-2-verify-button'));
    // Wait for navigation to be called
    await new Promise(setImmediate);
    expect(mockNavigate).toHaveBeenCalledWith('accountLinkAllDone');
  });
});
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import AccountLinkIntro from '../accountLinkIntro';
import { useNavigation } from '@react-navigation/native';
import { Provider } from "react-redux";
import configureStore from 'redux-mock-store';
import { SCREENS } from '../../../../app/shared/constants';
import { BUTTON_NAV_ANALYTICS, LOYALTY_ANALYTICS } from '../../../../analytics/AnalyticsConstants';
import { screenViewLog, userActionLogEvent } from '../../../../analytics/AnalyticsUtils';
import * as AnalyticsUtils from "../../../../analytics/AnalyticsUtils";


jest.mock('@react-navigation/native', () => ({
  useNavigation: jest.fn(() => ({
    navigate: jest.fn(), // Add navigate function
    replace: jest.fn(),  // Keep replace function if needed
    goBack: jest.fn(),
    addListener: jest.fn(() => () => { }),
  })),
  CommonActions: {
    reset: jest.fn(),
  },
  useFocusEffect: jest.fn((cb) => {
    cb();
  }),
}));

jest.mock('@react-native-firebase/app', () => ({
  getApp: jest.fn(() => ({
    name: '[DEFAULT]',
  })),
}));

jest.mock('react-native-svg', () => ({
  SvgXml: jest.fn(({ xml, testID }) => <svg testID={testID}>{xml}</svg>),
}));

jest.mock("pantry-design-system", () => ({
  useTheme: jest.fn(() => ({
    theme: {
      colors: { pdsThemeColorBackgroundBaseline: "#ffffff" },
      fonts: { pdsGlobalFontFamilyPoppins: "Poppins" },
      dimensions: { pdsGlobalSpace800: 24 },
      typography: { pdsGlobalFontSize500: 18 },
    },
  })),
  Heading: jest.fn(({ title, testID }) => <text testID={testID}>{title}</text>),
  Text: jest.fn(({ text, testID }) => <text testID={testID}>{text}</text>),
  TextLink: jest.fn(({ text, testID }) => <text testID={testID}>{text}</text>),
  Checkbox: jest.fn(({ onChange, testID }) => <button onClick={onChange} testID={testID} />),
  Button: jest.fn(({ onPress, label, testID }) => (
    <button onClick={onPress} testID={testID}>
      {label}
    </button>
  )),
}));

jest.mock("../styles", () =>
  jest.fn(() => ({
    headerLeft: {},
    container: {},
    headerLeftIcon: {},
  }))
);


jest.mock("react-native-permissions", () => ({
  PERMISSIONS: {
    ANDROID: {},
    IOS: {},
  },
  check: jest.fn(),
  request: jest.fn(),
  checkNotifications: jest.fn(() => Promise.resolve({ status: 'granted' })), // <-- add this
  RESULTS: {
    GRANTED: 'granted',
  },
}));


jest.mock('react-i18next', () => ({
  useTranslation: jest.fn(() => ({ t: (key: string) => key })),
}));

const initialState = {
  profile: {
    banner: "albertsons",
  },
  authorableContent: {
    authorableContent: {
      "accountLinkage": {
        "title": "What to know before linking your [Banner] for U membership info",
        "content": [
          {
            "type": "info",
            "text": "[All text are placeholders] We use your phone number or email to search up your [Banner] for U member info and compare your name to make sure the person info matches your employee profile."
          },
          {
            "type": "benefit",
            "text": "If you choose to link your for U member info to your employee number, you’ll begin receiving associate-specific discounts and deals."
          },
          {
            "type": "privacy",
            "text": "Once your info is linked, we do not use your info for any other purpose except to show you associate-specific discounts and deals. More details in our Privacy Policy."
          },
          {
            "type": "acknowledgement",
            "text": "I acknowledge..."
          }
        ]
      },
    },
  },
  tncAgreement: {
    result: null,
    loading: false,
    errors: null
  }
}

const tncSuccessState = {
  profile: {
    banner: "albertsons",
  },
  authorableContent: {
    authorableContent: {
      "accountLinkage": {
        "title": "What to know before linking your [Banner] for U membership info",
        "content": [
          {
            "type": "info",
            "text": "[All text are placeholders] We use your phone number or email to search up your [Banner] for U member info and compare your name to make sure the person info matches your employee profile."
          },
          {
            "type": "benefit",
            "text": "If you choose to link your for U member info to your employee number, you’ll begin receiving associate-specific discounts and deals."
          },
          {
            "type": "privacy",
            "text": "Once your info is linked, we do not use your info for any other purpose except to show you associate-specific discounts and deals. More details in our Privacy Policy."
          },
          {
            "type": "acknowledgement",
            "text": "I acknowledge..."
          }
        ]
      },
    },
  },
  tncAgreement: {
    "result": {
      "status": 'Success'
    },
    "loading": false,
    "errors": null
  }
}

const tncErrorState = {
  profile: {
    banner: "albertsons",
  },
  authorableContent: {
    authorableContent: {
      "accountLinkage": {
        "title": "What to know before linking your [Banner] for U membership info",
        "content": [
          {
            "type": "info",
            "text": "[All text are placeholders] We use your phone number or email to search up your [Banner] for U member info and compare your name to make sure the person info matches your employee profile."
          },
          {
            "type": "benefit",
            "text": "If you choose to link your for U member info to your employee number, you’ll begin receiving associate-specific discounts and deals."
          },
          {
            "type": "privacy",
            "text": "Once your info is linked, we do not use your info for any other purpose except to show you associate-specific discounts and deals. More details in our Privacy Policy."
          },
          {
            "type": "acknowledgement",
            "text": "I acknowledge..."
          }
        ]
      },
    },
  },
  tncAgreement: {
    "result": {
      "status": 'Failed'
    },
    "loading": false,
    "errors": "erorr"
  }
}

describe('Account Link Intro render', () => {
  const mockNavigate = jest.fn();

  const mockStore = configureStore([]);

  beforeEach(() => {
    useNavigation.mockReturnValue({
      navigate: mockNavigate, // Add navigate function
      replace: jest.fn(),     // Keep replace function if needed
      goBack: jest.fn(),
      addListener: jest.fn(() => () => { }),
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should render all components correctly', () => {
    const store = mockStore(initialState);
    const { getByTestId } = render(
      <Provider store={store}>
        <AccountLinkIntro />
      </Provider>
    );

    // Checking dynamically assigned testIDs
    expect(getByTestId('before-you-linking-text-heading')).toBeTruthy();
    expect(getByTestId('text-description-part-0-0')).toBeTruthy(); // First content block
    expect(getByTestId('text-description-part-1-0')).toBeTruthy(); // Second content block
    expect(getByTestId('close-button')).toBeTruthy();
    expect(getByTestId('begin-button')).toBeTruthy();
    expect(getByTestId('forU-image')).toBeTruthy();
  });

  it('should call the close function when close button is pressed', () => {
    const navigation = {
      navigate: jest.fn(),
      goBack: jest.fn(), // ✅ add this
    };
    const store = mockStore(initialState);
    const { getByTestId } = render(
      <Provider store={store}>
        <AccountLinkIntro />
      </Provider>
    );
    const closeButton = getByTestId('close-button');
    fireEvent.press(closeButton);
    // Add your assertion for the close function behavior here
    expect(navigation.goBack).not.toHaveBeenCalled(); // Modify assertion based on your logic
  });

  it('should navigate to "accountLinkStepOne" when begin button is pressed and tncAgreement API success', async () => {
    var store = mockStore(initialState);
    const { rerender } = render(
      <Provider store={store}>
        <AccountLinkIntro />
      </Provider>
    );

    // mock tncAgreement API Success State
    store = mockStore(tncSuccessState);

    // re-render component 
    rerender(
      <Provider store={store}>
        <AccountLinkIntro />
      </Provider>
    );

    // Wait for the effect to run and check expected actions
    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith(SCREENS.ACCOUNT_LINK_USER_INPUT_STEP_ONE);
    });
  });

  it('should not navigate to "accountLinkStepOne" when begin button is pressed and tncAgreement API failed', async () => {
    var store = mockStore(initialState);
    const { rerender } = render(
      <Provider store={store}>
        <AccountLinkIntro />
      </Provider>
    );

    // mock tncAgreement API Success State
    store = mockStore(tncErrorState);

    // re-render component 
    rerender(
      <Provider store={store}>
        <AccountLinkIntro />
      </Provider>
    );

    // Wait for the effect to run and check expected actions
    await waitFor(() => {
      expect(mockNavigate).not.toHaveBeenCalled();
    });
  });
});

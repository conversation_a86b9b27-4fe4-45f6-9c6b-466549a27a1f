import { useNavigation } from '@react-navigation/native';
import { render, fireEvent, act } from '@testing-library/react-native';
import React from 'react';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';

import { BUTTON_NAV_ANALYTICS, LOYALTY_ANALYTICS } from '../../../../analytics/AnalyticsConstants';
import { SCREENS, UCA_PROFILE } from '../../../shared/constants';
import * as AsyncStorage from '../../../store/AsyncStorage';
import { fetchProfileRequest } from '../../../store/reducers/profileSlice';
import AccountLinkUserInputOne from '../accountLinkUserInputStepOne/index';

const mockFunction = jest.fn();

jest.mock('react-i18next', () => ({
  useTranslation: jest.fn(() => ({
    t: (key) =>
      key === 'accountLink.enterPhoneNumberEmail' ? 'Enter your phone number or email' : key,
  })),
}));

jest.mock('@react-navigation/native', () => ({
  useNavigation: jest.fn(() => ({
    setOptions: jest.fn(),
  })),
  useFocusEffect: jest.fn((cb) => {
    cb();
  }),
}));

jest.mock('react-native-svg', () => ({
  SvgXml: jest.fn(({ xml, testID }) => <svg testID={testID}>{xml}</svg>),
}));

jest.mock('../../../store/AsyncStorage', () => ({
  uniqueSessionId: jest.fn(),
}));

jest.mock('../../../hooks/useAccessibilityFocus', () => () => ({
  setLastFocused: jest.fn(),
  setRef: () => null,
}));

jest.mock('@react-native-firebase/analytics', () => {
  return jest.fn(() => ({
    logEvent: jest.fn(),
    logScreenView: jest.fn(),
    setUserId: jest.fn(),
    setUserProperties: jest.fn(),
  }));
});

jest.mock('../../../../analytics/AnalyticsUtils', () => ({
  screenViewLog: jest.fn(),
  userActionLogEvent: jest.fn(),
  setUserProperties: jest.fn(),
}));

jest.mock('pantry-design-system', () => ({
  useTheme: jest.fn(() => ({
    theme: {
      colors: {
        pdsThemeColorBackgroundPrimary: '#000000',
        pdsThemeColorNeutralLow: '#FFFFFF',
      },
      fonts: { pdsGlobalFontFamilyPoppins: 'Poppins' },
      dimensions: { pdsGlobalSpace800: 24 },
      typography: { pdsGlobalFontSize500: 18 },
    },
  })),
  Heading: jest.fn(({ title, testID }) => <text testID={testID}>{title}</text>),
  Text: jest.fn(({ text, testID }) => <text testID={testID}>{text}</text>),
  TextLink: jest.fn(({ text, testID, onPress }) => (
    <text testID={testID} onClick={onPress}>
      {text}
    </text>
  )),
  TextField: jest.fn(({ value, onChangeText, placeholder, testID, error, errorText }) => (
    <div>
      <input
        testID={testID}
        value={value}
        onChange={(e) => onChangeText(e.target.value)} // mimic change behavior
        placeholder={placeholder}
        error={error}
        errorText={'Please enter a valid phone number or email'}
        data-testid={testID}
        aria-label={errorText} // handle errorText prop for accessibility
      />
      {error && <span>{errorText}</span>} {/* Handle error display */}
    </div>
  )),
  Button: jest.fn(({ onPress, label, testID, disabled }) => (
    <button onClick={mockFunction} testID={testID} disabled={disabled}>
      {label}
    </button>
  )),
}));

jest.mock('react-native-permissions', () => ({
  PERMISSIONS: {
    ANDROID: {},
    IOS: {},
  },
  check: jest.fn(),
  request: jest.fn(),
}));

describe('AccountLinkUserInputOne Component', () => {
  const mockNavigate = jest.fn();
  const mockStore = configureStore([]);

  beforeEach(() => {
    useNavigation.mockReturnValue({ navigate: mockNavigate });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  const initialState = {
    profile: {
      banner: 'albertsons',
      profile: {
        names: [
          {
            firstName: 'John',
          },
        ],
      },
      ucaProfile: {
        profile: {
          phones: [
            {
              type: 'PRIMARY',
              value: **********,
            },
          ],
          emails: [
            {
              type: 'PRIMARY',
              value: '<EMAIL>',
            },
          ],
        },
        householdAccount: {
          householdId: ************,
        },
        loyaltyPrograms: [
          {
            type: 'CLUBCARD',
            value: ***********,
          },
        ],
      },
    },
  };

  const FALLBACK_BANNER = 'albertsons';
  const URLS = {
    CREATE_ACCOUNT: 'https://www.{bannerName}.com/account/short-registration.html',
  };

  test('should render all components correctly', async () => {
    const store = mockStore(initialState);

    const { getByTestId, getByPlaceholderText, getAllByTestId } = render(
      <Provider store={store}>
        <AccountLinkUserInputOne />
      </Provider>,
    );

    // Verify the For U image is rendered
    expect(getByTestId('forU-image')).toBeTruthy();

    // Verify the heading is rendered
    expect(getByTestId('before-you-linking-text-heading')).toBeTruthy();

    // Verify the text field is rendered
    expect(getByTestId('textfield-userinput')).toBeTruthy();

    // Alternatively, verify the text field by placeholder
    // expect(getByPlaceholderText("Enter your phone number or email")).toBeTruthy();

    // Verify the button is rendered
    expect(getByTestId('findmy-u-membership-button')).toBeTruthy();
  });

  test('should validate email input and update state correctly', () => {
    const store = mockStore(initialState);

    const { getByTestId } = render(
      <Provider store={store}>
        <AccountLinkUserInputOne />
      </Provider>,
    );

    const inputField = getByTestId('textfield-userinput');

    // Simulate entering a valid email
    fireEvent.changeText(inputField, '<EMAIL>');

    // Verify the state is updated correctly
    expect(inputField.props.value).toBe('<EMAIL>');
    expect(inputField.props.error).toBe(false); // No error for valid email
  });

  test('should validate input and enable the button for valid email', async () => {
    const store = mockStore(initialState);

    const { getByPlaceholderText, getByTestId } = render(
      <Provider store={store}>
        <AccountLinkUserInputOne />
      </Provider>,
    );

    const inputField = getByTestId('textfield-userinput');
    const button = getByTestId('findmy-u-membership-button');

    // Initially, the button should be disabled
    // await act(() => expect(button).toBeDisabled());
    fireEvent.press(button);
    expect(mockFunction).not.toHaveBeenCalled();

    // Enter a valid email
    fireEvent.changeText(inputField, '<EMAIL>');

    // Wait for the button to be enabled
    await act(() => expect(button).not.toBeDisabled());
  });

  test('should validate phone number input and update state correctly', () => {
    const store = mockStore(initialState);

    const { getByTestId } = render(
      <Provider store={store}>
        <AccountLinkUserInputOne />
      </Provider>,
    );

    const inputField = getByTestId('textfield-userinput');

    // Simulate entering a valid phone number
    fireEvent.changeText(inputField, '**********');

    // Verify the state is updated correctly
    expect(inputField.props.value).toBe('**********');
    expect(inputField.props.error).toBe(false); // No error for valid phone number
  });

  test('should validate input and enable the button for valid phone number', async () => {
    const store = mockStore(initialState);

    const { getByPlaceholderText, getByTestId } = render(
      <Provider store={store}>
        <AccountLinkUserInputOne />
      </Provider>,
    );

    const inputField = getByTestId('textfield-userinput');
    const button = getByTestId('findmy-u-membership-button');

    // Enter a valid phone number
    fireEvent.changeText(inputField, '**********');

    // the button should be enabled for valid phone number
    expect(button).not.toBeDisabled();

    // Wait for the button to be enabled
    await act(() => expect(button).not.toBeDisabled());
  });

  test('should navigate to the next screen when the button is pressed', async () => {
    const store = mockStore(initialState);

    const { getByTestId } = render(
      <Provider store={store}>
        <AccountLinkUserInputOne />
      </Provider>,
    );

    const inputField = getByTestId('textfield-userinput');
    const button = getByTestId('findmy-u-membership-button');

    // Enter a valid email
    fireEvent.changeText(inputField, '<EMAIL>');

    // Wait for the button to be enabled
    // await waitFor(() => expect(button).not.toBeDisabled());

    // Press the button
    fireEvent.press(button);

    // Comment navigation for now later will be uncommented
    // Verify the navigation function is called with the correct screen
    // expect(mockNavigate).toHaveBeenCalledWith(SCREENS.ACCOUNT_LINK_STEP_ONE_VERIFY);
  });

  test('should show error for invalid input', async () => {
    const store = mockStore(initialState);

    const { getByPlaceholderText, getByTestId, findByText, getByText } = render(
      <Provider store={store}>
        <AccountLinkUserInputOne />
      </Provider>,
    );

    const inputField = getByTestId('textfield-userinput');

    // Enter an invalid input
    fireEvent.changeText(inputField, 'invalid-input');

    // Verify the error message is displayed
    // act(() => {
    //     expect(findByText("Please enter a valid phone number or email")).toBeTruthy()

    //     expect(inputField.props.error).toBe(true); // Error for invalid input
    // })
    expect(inputField.props.errorText).toBe('Please enter a valid phone number or email');
  });

  test('should dispatch fetchProfileRequest with email', async () => {
    const store = mockStore(initialState);
    const mockRequestId = 'mock-request-1234555';

    // Mock AsyncStorage.uniqueSessionId to return a predictable value
    jest.spyOn(AsyncStorage, 'uniqueSessionId').mockResolvedValue(mockRequestId);

    const { getByTestId } = render(
      <Provider store={store}>
        <AccountLinkUserInputOne />
      </Provider>,
    );

    const inputField = getByTestId('textfield-userinput');
    const button = getByTestId('findmy-u-membership-button');

    // Test with email
    fireEvent.changeText(inputField, '<EMAIL>');

    await act(async () => {
      fireEvent.press(button);
    });

    expect(AsyncStorage.uniqueSessionId).toHaveBeenCalled();
    // Update the dispatched action to match the expected email value
    const dispatchedActions = store.getActions();
    dispatchedActions[0].payload.email = '<EMAIL>';

    expect(dispatchedActions).toContainEqual(
      fetchProfileRequest({
        requestId: mockRequestId,
        fl: UCA_PROFILE,
        email: '<EMAIL>',
      }),
    );
  });

  test('should navigate to webview with correct url and params when createAccount is called, using fallback if banner is missing', () => {
    const storeWithBanner = mockStore(initialState);
    const storeWithoutBanner = mockStore({
      profile: {
        ...initialState.profile,
        banner: undefined,
      },
    });

    // With banner
    let { getByTestId, unmount } = render(
      <Provider store={storeWithBanner}>
        <AccountLinkUserInputOne />
      </Provider>,
    );
    const createAccountLink = getByTestId('create-account-link');
    fireEvent.press(createAccountLink);
    const expectedUrlWithBanner = URLS.CREATE_ACCOUNT.replace(
      '{bannerName}',
      initialState.profile.banner,
    );
    const analyticsParam = {
      subsection1: LOYALTY_ANALYTICS.LOYALTY,
      subsection2: LOYALTY_ANALYTICS.LOYALTY_LINK_STEP1,
      subsection3: LOYALTY_ANALYTICS.LOYALTY_CREATE_ACCOUNT_WEBVIEW,
      eventCategory: BUTTON_NAV_ANALYTICS.PROFILE_TAB_ACTION,
      eventLabel: LOYALTY_ANALYTICS.LOYALTY_CREATE_ACCOUNT_WEBVIEW,
      refreshCategory: LOYALTY_ANALYTICS.LOYALTY,
      refreshAction: LOYALTY_ANALYTICS.LOYALTY_REFRESH_ICON,
      refreshLabel: LOYALTY_ANALYTICS.LOYALTY_CREATE_ACCOUNT_WEBVIEW,
      backButtonLabel: LOYALTY_ANALYTICS.LOYALTY_CREATE_ACCOUNT_WEBVIEW,
      backButtonCategory: LOYALTY_ANALYTICS.LOYALTY,
    };

    expect(mockNavigate).toHaveBeenCalledWith(SCREENS.WEBVIEW, {
      url: expectedUrlWithBanner,
      titleHeader: 'accountLink.createAnAccount',
      subsection: analyticsParam,
      isOmniheaderPresent: false,
    });

    // Without banner (fallback)
    unmount();
    ({ getByTestId } = render(
      <Provider store={storeWithoutBanner}>
        <AccountLinkUserInputOne />
      </Provider>,
    ));
    const createAccountLinkFallback = getByTestId('create-account-link');
    fireEvent.press(createAccountLinkFallback);
    const expectedUrlFallback = URLS.CREATE_ACCOUNT.replace('{bannerName}', FALLBACK_BANNER);
    expect(mockNavigate).toHaveBeenCalledWith(SCREENS.WEBVIEW, {
      url: expectedUrlFallback,
      titleHeader: 'accountLink.createAnAccount',
      subsection: analyticsParam,
      isOmniheaderPresent: false,
    });
  });
});

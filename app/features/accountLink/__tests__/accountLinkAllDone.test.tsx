import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import AccountLinkAllDone from '../accountLinkAllDone';
import { useNavigation } from '@react-navigation/native';
import { Provider } from "react-redux";
import configureStore from 'redux-mock-store';
import { SCREENS } from '../../../shared/constants';

jest.mock('@react-navigation/native', () => ({
  useNavigation: jest.fn(() => ({
    setOptions: jest.fn(),
  })),
  useFocusEffect: jest.fn((cb) => {
    cb();
  }),
}));

jest.mock('@react-native-firebase/app', () => ({
    getApp: jest.fn(() => ({
        name: '[DEFAULT]',
    })),
}));

jest.mock('@react-native-firebase/analytics', () => {
  return jest.fn(() => ({
    logEvent: jest.fn(),
    logScreenView: jest.fn(),
    setUserId: jest.fn(),
    setUserProperties: jest.fn(),
  }));
});

jest.mock('../../../../analytics/AnalyticsUtils', () => ({
  screenViewLog: jest.fn(),
  userActionLogEvent: jest.fn(),
  setUserProperties: jest.fn(),
}));



jest.mock('react-native-svg', () => ({
  SvgXml: jest.fn(({ xml, testID }) => <svg testID={testID}>{xml}</svg>),
}));

jest.mock("pantry-design-system", () => ({
  useTheme: jest.fn(() => ({
    theme: {
      colors: { pdsThemeColorBackgroundBaseline: "#ffffff" },
      fonts: { pdsGlobalFontFamilyPoppins: "Poppins" },
      dimensions: { pdsGlobalSpace800: 24 },
      typography: { pdsGlobalFontSize500: 18 },
    },
  })),
  Heading: jest.fn(({ title, testID }) => <text testID={testID}>{title}</text>),
  Text: jest.fn(({ text, testID }) => <text testID={testID}>{text}</text>),
  Button: jest.fn(({ onPress, label, testID }) => (
    <button onClick={onPress} testID={testID}>
      {label}
    </button>
  )),
}));

jest.mock("../accountLinkUserVerify/styles", () =>
  jest.fn(() => ({
    container: {},
    subContainer: {},
    imageContainer: {},
    infoContainer: {},
    footerView: {},
    buttonContainer: {},
  }))
);

jest.mock('react-i18next', () => ({
  useTranslation: jest.fn(() => ({ t: (key: string) => key })),
}));

const initialState = {
  profile: {
    ucaProfile: {
      householdAccount: {
        householdId: '12345',
      }
    }
  },
};

describe('AccountLinkAllDone render', () => {
  const mockNavigate = jest.fn();

  const mockStore = configureStore([]);

  beforeEach(() => {
    useNavigation.mockReturnValue({
      navigate: mockNavigate,
      setOptions: jest.fn(),
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('Should render all components correctly', () => {
    const store = mockStore(initialState);
    const { getByTestId } = render(
      <Provider store={store}>
        <AccountLinkAllDone />
      </Provider>
    );

    expect(getByTestId('all-done-forU-image')).toBeTruthy();
    expect(getByTestId('all-done-heading')).toBeTruthy();
    expect(getByTestId('all-done-description-one')).toBeTruthy();
    expect(getByTestId('all-Done-close--button')).toBeTruthy();
  });

  it('should call the close function when close button is pressed', () => {
    const store = mockStore(initialState);
    const { getByTestId } = render(
      <Provider store={store}>
        <AccountLinkAllDone />
      </Provider>
    );
    const closeButton = getByTestId('all-Done-close--button');
    fireEvent.press(closeButton);
    expect(mockNavigate).toHaveBeenCalledWith(SCREENS.BOTTOM_TAB_STACK, { "screen": "profile" });
  });
});

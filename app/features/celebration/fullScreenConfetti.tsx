import React, { useEffect, useMemo, useRef } from 'react';
import { StyleSheet, View } from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import LottieView from 'lottie-react-native';
import { hideConfetti } from '../../store/reducers/confettiSlice';
import { CONFETTI_ANIMATION_DELAY, FeatureKey, HAPTICS_OPTIONS, LOCATION_STATE } from '../../shared/constants';
import { getStyles } from './styles';
import { useTheme } from 'pantry-design-system';
import ReactNativeHapticFeedback from "react-native-haptic-feedback";
import { roleGovernanceEnabled } from '../../store/selectors/featureFlagsSelectors';
import { shouldDisplayFeature } from '../../../app/store/selectors/governance';
import { useRoute } from '@react-navigation/native';

/**
 * FullscreenConfetti Component
 *
 * Renders a full-screen confetti animation overlay when specific conditions are met.
 * Conditions are based on Redux state (feature flags, user state, visibility).
 * Con<PERSON>tti auto-hides after a fixed delay and triggers a success haptic on play.
 *
 * Features:
 * - Responsive to device type (tablet or phone)
 * - Themed styling using Pantry Design System
 * - Triggers native haptic feedback on activation
 * - Controlled by Redux state (`confetti.visible`)
 * - Automatically dismisses after `CONFETTI_ANIMATION_DELAY`
 */

const FullscreenConfetti = () => {
  const isTablet = useSelector((state: { deviceInfo: { isTablet: boolean } }) => state.deviceInfo?.isTablet);
  const visible = useSelector((state: any) => state.confetti?.visible);
  const { theme } = useTheme();

  const styles = getStyles(theme, isTablet);
  const dispatch = useDispatch();
  const animationRef = useRef<LottieView>(null);
  const isRoleGoveranceEnabled = useSelector(roleGovernanceEnabled);
  const { isUserInStore, userType } = useSelector((state: any) => state?.profile);
  const isClockedIn = useSelector((state: any) => state.shift.clockedIn);; // get the clockedIn state from shift slice
  const isFeatureFlagEnabled = true; //Todo: replace with actual feature flag once it is implemented for personalized messaging
  const reduceMotionEnabled = useSelector((state: any) => state.confetti.reduceMotionEnabled);
  const confettiScreen = useSelector((state: any) => state.confetti?.screen);
  const route = useRoute();
  const currentScreen = route?.name?.toLowerCase();

  /**
  * Determines whether the confetti animation should be shown.
  * This ensures confetti only appears under specific feature flag and user session conditions.
  */

  const shouldShowConfetti = useMemo(() => {
    const governanceCheck = shouldDisplayFeature(
      FeatureKey.PERSONALIZED_MESSAGING,
      isUserInStore,
      isClockedIn,
      userType,
      isFeatureFlagEnabled,
      isRoleGoveranceEnabled,
    );
    return governanceCheck && confettiScreen === currentScreen;
  }, [isUserInStore, isClockedIn, userType, isRoleGoveranceEnabled, confettiScreen, currentScreen]);

  /**
   * Handles confetti playback and automatic dismissal:
   * - Plays Lottie animation
   * - Triggers haptic feedback
   * - Dispatches `hideConfetti()` after delay
   * - Reduce motion is enabled (respects accessibility settings)
   */
  useEffect(() => {
    if (!visible || !shouldShowConfetti || !animationRef.current || reduceMotionEnabled) return;

    ReactNativeHapticFeedback.trigger('notificationSuccess', HAPTICS_OPTIONS);
    animationRef.current.play();
    const timeout = setTimeout(() => dispatch(hideConfetti()), CONFETTI_ANIMATION_DELAY);

    return () => clearTimeout(timeout);
  }, [shouldShowConfetti, dispatch, visible, reduceMotionEnabled]);

  if (!shouldShowConfetti || !visible || reduceMotionEnabled) return null;

  return (
    <View testID='confetti-container' pointerEvents="none" style={styles.confettiContainer}>
      <LottieView
        ref={animationRef}
        source={require('../../../assets/confetti.json')}
        loop={false}
        style={StyleSheet.absoluteFill} // Fill entire container
        resizeMode="cover" // Maintain aspect ratio while covering area
      />
    </View>
  );
};


export default FullscreenConfetti;

import { StyleSheet } from "react-native";
import { Theme } from "pantry-design-system";

// Constants for static values
const DOT_SIZE = 5;
const ACTIVE_DOT_SIZE = 6;
const CONFETTI_Z_INDEX = -1;
const CONFETTI_ELEVATION = -1;

export const getStyles = ({ colors, borderDimens, dimensions }: Theme, isTablet: boolean) => {
    return StyleSheet.create({
        emptyStyle: { width: "100%", alignSelf: "center" },
        carouselList: {
            paddingLeft: dimensions.pdsGlobalSpace100,
            paddingRight: dimensions.pdsGlobalSpace100,
            gap: dimensions.pdsGlobalSpace400,
            marginBottom: dimensions.pdsGlobalSpace500
        },
        tabContainer: {
            marginHorizontal: dimensions.pdsGlobalSpace400,
            alignItems: "flex-start",
            backgroundColor: colors.pdsThemeColorBackgroundRaised,
        },
        pagination: {
            flexDirection: 'row',
            position: 'absolute',
            alignSelf: 'center',
            justifyContent: 'center',
            bottom: dimensions.pdsGlobalSpace100,
        },
        dot: {
            width: DOT_SIZE,
            height: DOT_SIZE,
            borderRadius: borderDimens.pdsGlobalBorderRadius100,
            backgroundColor: colors.pdsThemeColorBackgroundDisabled,
            margin: dimensions.pdsGlobalSpace100,
        },
        activeDot: {
            backgroundColor: colors.pdsThemeColorForegroundPrimary,
            width: ACTIVE_DOT_SIZE,
            height: ACTIVE_DOT_SIZE,
        },
        confettiContainer: {
            ...StyleSheet.absoluteFillObject,
            zIndex: CONFETTI_Z_INDEX,
            elevation: CONFETTI_ELEVATION,
            pointerEvents: 'none',
        },
    });
};

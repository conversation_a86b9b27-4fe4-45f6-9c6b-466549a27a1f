import AsyncStorage from '@react-native-async-storage/async-storage';
import { fireEvent, render, act } from '@testing-library/react-native';
import React from 'react';
import { useSelector } from 'react-redux';

import { CELEBRATION_TYPES, LOCATION_STATE, UserType } from '../../shared/constants';

import CelebrationCarousel from './index';

import type { CelebrationCardProps } from '../../../components/CelebrationCard';

jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(() => Promise.resolve(null)),
  setItem: jest.fn(() => Promise.resolve(null)),
}));

jest.mock('react-native-permissions', () => ({
  PERMISSIONS: {
    ANDROID: {
      ACCESS_FINE_LOCATION: 'android.permission.ACCESS_FINE_LOCATION',
      ACCESS_COARSE_LOCATION: 'android.permission.ACCESS_COARSE_LOCATION',
    },
    IOS: {
      LOCATION_WHEN_IN_USE: 'ios.permission.LOCATION_WHEN_IN_USE',
    },
  },
  RESULTS: {
    GRANTED: 'granted',
    DENIED: 'denied',
  },
  check: jest.fn(),
  request: jest.fn(),
}));

jest.mock('react-redux', () => ({
  useSelector: jest.fn(),
  useDispatch: jest.fn(() => jest.fn()),
}));

jest.mock('@react-navigation/native', () => ({
  useFocusEffect: jest.fn((cb) => cb()),
  useRoute: jest.fn(() => ({
    params: {
      someParam: 'testValue', // customize based on your screen
    },
  })),
}));

jest.mock('../../../components/CelebrationCard', () => 'CelebrationCard');

jest.mock('../../../app/utils/checkAllCelebrations', () => ({
  checkAllCelebrations: jest.fn(() => ({
    birthday: { shouldCelebrate: true },
    work: { shouldCelebrate: true, yearsWorked: 2, type: 'Milestone' },
  })),
}));

jest.mock('../../../app/utils/dismissCelebrations', () => ({
  getDismissedCelebrations: jest.fn(() => Promise.resolve([])),
  dismissCelebration: jest.fn(),
}));

// Mock feature flag selector to return true by default
jest.mock('../../../app/store/selectors/featureFlagsSelectors', () => ({
  roleGovernanceEnabled: jest.fn().mockReturnValue(true),
}));

const mockCelebrationCards: CelebrationCardProps[] = [
  { id: 'Birthday', type: 'Birthday', testID: 'Birthday-test' },
  {
    id: 'Milestone',
    type: 'Milestone',
    yearsWorked: 5,
    yearStarted: 2020,
    testID: 'Milestone-test',
  },
];

describe('CelebrationCarousel', () => {
  const currentYear = new Date().getFullYear();
  let mockDispatch: jest.Mock;
  beforeEach(() => {
    mockDispatch = jest.fn();
    jest.clearAllMocks();
  });

  const renderWithLocationState = (userType: string, isUserInStore: boolean) => {
    useSelector.mockImplementation((selectorFn) =>
      selectorFn({
        locationAccess: {
          skipped: false,
          preciseLocationGranted: 'denied',
        },
        profile: {
          names: [{ firstName: 'Alex' }],
          banner: 'Safeway',
          isUserInStore: isUserInStore,
          userType: userType,
        },
        dateOfBirth: '1995-05-10',
        latestHireDate: '2020-05-10',
        deviceInfo: { isTablet: false },
        featureFlags: {
          flags: [
            {
              featureFlagName: 'diaaGovernance',
              featureFlagValue: true,
            },
          ],
        },
        shift: { clockedIn: true },
      }),
    );

    return render(
      <CelebrationCarousel showDismiss={true} celebrationCards={mockCelebrationCards} />,
    );
  };

  it('does not render when location state is not PRECISE', () => {
    const { queryByTestId } = renderWithLocationState(UserType.RESTRICTED, false);
    expect(queryByTestId('carousel-flatlist')).toBeNull();
  });

  it('renders all celebration cards with pagination when userType is non-restricted and isUserInStore is true', () => {
    const { getByTestId, getAllByTestId } = renderWithLocationState(UserType.NON_RESTRICTED, true);
    expect(getByTestId('Birthday-test')).toBeTruthy();
    expect(getByTestId('Milestone-test')).toBeTruthy();
    expect(getByTestId('carousel-flatlist')).toBeTruthy();
    expect(getAllByTestId(/pagination-dot-/)).toHaveLength(2);
  });

  it('updates activeIndex when scrolling FlatList with userType is non-restricted and isUserInStore is true', () => {
    const { getByTestId, getAllByTestId } = renderWithLocationState(UserType.NON_RESTRICTED, true);
    const flatList = getByTestId('carousel-flatlist');

    fireEvent.scroll(flatList, {
      nativeEvent: {
        contentOffset: { x: 390, y: 0 },
        contentSize: { width: 780, height: 0 },
        layoutMeasurement: { width: 390, height: 0 },
      },
    });

    expect(getAllByTestId(/pagination-dot-/)).toHaveLength(2);
  });

  it('plays confetti when a new card becomes viewable with userType is non-restricted and isUserInStore is true', async () => {
    const { getByTestId } = renderWithLocationState(UserType.NON_RESTRICTED, true);
    const flatList = getByTestId('carousel-flatlist');

    await act(async () => {
      flatList.props.onViewableItemsChanged({
        viewableItems: [{ index: 1, item: mockCelebrationCards[1], isViewable: true }],
        changed: [],
      });
    });
  });

  it('should not render when no cards are provided even with userType is non-restricted and isUserInStore is true', () => {
    useSelector.mockImplementation((selectorFn) =>
      selectorFn({
        locationAccess: {
          skipped: false,
          preciseLocationGranted: 'denied',
        },
        profile: {
          names: [{ firstName: 'Alex' }],
          banner: 'Safeway',
          isUserInStore: true,
          userType: UserType.NON_RESTRICTED,
        },
        deviceInfo: { isTablet: false },
        shift: { clockedIn: true },
      }),
    );

    const { queryByTestId } = render(<CelebrationCarousel showDismiss celebrationCards={[]} />);
    expect(queryByTestId('carousel-flatlist')).toBeNull();
  });

  it('should render single card when only one celebration exists with userType is non-restricted and isUserInStore is true', () => {
    const mockSingleCard = {
      id: `Birthday-${currentYear}`,
      type: CELEBRATION_TYPES.BIRTHDAY,
      firstName: 'John',
      banner: 'Safeway',
      testID: 'Birthday-test',
    };

    useSelector.mockImplementation((selectorFn) =>
      selectorFn({
        locationAccess: {
          skipped: false,
          preciseLocationGranted: 'denied',
        },
        profile: {
          names: [{ firstName: 'Alex' }],
          banner: 'Safeway',
          isUserInStore: true,
          userType: UserType.NON_RESTRICTED,
        },
        deviceInfo: { isTablet: false },
        shift: { clockedIn: false },
      }),
    );

    const { queryByTestId } = render(
      <CelebrationCarousel showDismiss celebrationCards={[mockSingleCard]} />,
    );
    // Should not render the carousel FlatList for single card
    expect(queryByTestId('carousel-flatlist')).toBeNull();
    // Should render the single card with its ID as testID
    expect(queryByTestId(mockSingleCard.testID)).toBeTruthy();
  });

  it('stores viewed celebrations in AsyncStorage with userType is non-restricted and isUserInStore is true', async () => {
    const { getByTestId } = renderWithLocationState(UserType.NON_RESTRICTED, true);
    const flatList = getByTestId('carousel-flatlist');

    await act(async () => {
      flatList.props.onViewableItemsChanged({
        viewableItems: [{ index: 0, item: mockCelebrationCards[0], isViewable: true }],
        changed: [],
      });
    });

    expect(AsyncStorage.setItem).toHaveBeenCalled();
  });

  it('renders correctly on tablet devices with userType is non-restricted and isUserInStore is true', () => {
    useSelector.mockImplementation((selectorFn) =>
      selectorFn({
        profile: {
          names: [{ firstName: 'Alex' }],
          banner: 'Safeway',
          isUserInStore: true,
          userType: UserType.NON_RESTRICTED,
        },
        deviceInfo: { isTablet: true },
        locationAccess: {
          skipped: false,
          isPrecise: LOCATION_STATE.PRECISE,
          preciseLocationGranted: 'denied',
        },
        shift: { clockedIn: true },
      }),
    );

    const { getByTestId } = render(
      <CelebrationCarousel showDismiss celebrationCards={mockCelebrationCards} />,
    );

    expect(getByTestId('carousel-flatlist')).toBeTruthy();
  });

  it('does not render if user is not in store and userType is restricted', () => {
    useSelector.mockImplementation((selectorFn) =>
      selectorFn({
        locationAccess: {
          skipped: false,
          isPrecise: LOCATION_STATE.PRECISE,
          preciseLocationGranted: 'denied',
        },
        profile: {
          names: [{ firstName: 'Alex' }],
          banner: 'Safeway',
          isUserInStore: false, // ❌ Not in store
          userType: UserType.RESTRICTED,
        },
        deviceInfo: { isTablet: false },
        shift: { clockedIn: true },
        featureFlags: {
          flags: [{ featureFlagName: 'diaaGovernance', featureFlagValue: true }],
        },
      }),
    );

    const { queryByTestId } = render(
      <CelebrationCarousel showDismiss celebrationCards={mockCelebrationCards} />,
    );

    expect(queryByTestId('carousel-flatlist')).toBeNull(); // Should not render
  });
});

import React from 'react';
import { render } from '@testing-library/react-native';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';
import FullscreenConfetti from './FullscreenConfetti';
import { LOCATION_STATE, UserType } from '../../shared/constants';


jest.mock('@react-navigation/native', () => {
  const actualNav = jest.requireActual('@react-navigation/native');
  return {
    ...actualNav,
    useNavigation: jest.fn(),
    useFocusEffect: jest.fn((cb) => {
      cb(); // Simulate the screen gaining focus
    }),
    useRoute: jest.fn(() => ({
      params: {
        someParam: 'testValue', // customize based on your screen
      },
    })),
  };
});


jest.mock("pantry-design-system", () => ({
  useTheme: jest.fn(() => ({
    theme: {
      colors: {},
      fonts: { pdsGlobalFontFamilyPoppins: "Poppins" },
      dimensions: { pdsGlobalSpace400: 16 },
      typography: { pdsGlobalFontSize500: 18 },
      borderDimens: {
        pdsGlobalBorderRadius200: 8,
        pdsGlobalBorderWidth100: 1
      },
    },
  })),
  Heading: jest.fn(({ title, testID }) => <text testID={testID}>{title}</text>),
  TextLink: jest.fn(({ onPress, label, testID }) => (
    <button onClick={onPress} testID={testID}>
      {label}
    </button>
  )),
}));
// Mock LottieView since we don't need to test the actual animation
jest.mock('lottie-react-native', () => 'LottieView');
jest.mock("react-native-haptic-feedback", () => ({
  trigger: jest.fn(),
}));

// Mock feature flag selector to return true by default
jest.mock('../../../app/store/selectors/featureFlagsSelectors', () => ({
  roleGovernanceEnabled: jest.fn().mockReturnValue(true),
}));

describe('FullscreenConfetti', () => {
  const mockStore = configureStore([]);
  let store: any;

  beforeEach(() => {
    store = mockStore({
      locationAccess: { skipped: false, isPrecise: LOCATION_STATE.PRECISE },
      confetti: { visible: false, reduceMotionEnabled: false },
      deviceInfo: { isTablet: false },
      shift: { clockedIn: true },
      profile: { isUserInStore: true },
      featureFlags: {
        flags: [
          {
            featureFlagName: 'diaaGovernance',
            featureFlagValue: true
          }
        ]
      }
    });
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
    jest.clearAllMocks();
  });

  it('should render when all conditions are met (visible, user is ON LOCATION, userType is non-restricted and feature flag enabled)', () => {
    store = mockStore({
      locationAccess: { skipped: false },
      confetti: { visible: true, reduceMotionEnabled: false },
      deviceInfo: { isTablet: false },
      shift: { clockedIn: true },
      profile: { isUserInStore: true, userType: UserType.NON_RESTRICTED },
      flags: {
    isGovernanceEnabled: true,
  },
      featureFlags: {
        flags: [
          {
            featureFlagName: 'diaaGovernance',
            featureFlagValue: true
          }
        ]
      }
    });

    const { getByTestId } = render(
      <Provider store={store}>
        <FullscreenConfetti />
      </Provider>
    );

    expect(getByTestId('confetti-container')).toBeTruthy();
  });

  it('should not render when visible is false', () => {
    const { queryByTestId } = render(
      <Provider store={store}>
        <FullscreenConfetti />
      </Provider>
    );

    expect(queryByTestId('confetti-container')).toBeNull();
  });

  it('should render when visible is true, user is ON LOCATION and user type is non-restricted', () => {
    store = mockStore({
      locationAccess: { skipped: false },
      confetti: { visible: true, reduceMotionEnabled: false },
      deviceInfo: { isTablet: false },
      shift: { clockedIn: true },
      profile: { isUserInStore: true, userType: UserType.NON_RESTRICTED },
      featureFlags: {
        flags: [
          {
            featureFlagName: 'diaaGovernance',
            featureFlagValue: true
          }
        ]
      }
    });

    const { getByTestId } = render(
      <Provider store={store}>
        <FullscreenConfetti />
      </Provider>
    );

    expect(getByTestId('confetti-container')).toBeTruthy();
  });

  it('should not render when visible is true but user is NOT ON LOCATION', () => {
    store = mockStore({
      locationAccess: { skipped: true },
      confetti: { visible: true, reduceMotionEnabled: false },
      deviceInfo: { isTablet: false },
      shift: { clockedIn: true },
      profile: { isUserInStore: false, userType: UserType.NON_RESTRICTED },
      featureFlags: {
        flags: [
          {
            featureFlagName: 'diaaGovernance',
            featureFlagValue: true
          }
        ]
      }
    });

    const { queryByTestId } = render(
      <Provider store={store}>
        <FullscreenConfetti />
      </Provider>
    );

    expect(queryByTestId('confetti-container')).toBeTruthy();
  });

  it('should not render when visible is true but user is NOT ON LOCATION ', () => {
    store = mockStore({
      locationAccess: { skipped: false },
      confetti: { visible: true, reduceMotionEnabled: false },
      deviceInfo: { isTablet: false },
      shift: { clockedIn: true },
      profile: { isUserInStore: false, userType: UserType.NON_RESTRICTED },
      featureFlags: {
        flags: [
          {
            featureFlagName: 'diaaGovernance',
            featureFlagValue: true
          }
        ]
      }
    });

    const { queryByTestId } = render(
      <Provider store={store}>
        <FullscreenConfetti />
      </Provider>
    );

    expect(queryByTestId('confetti-container')).toBeTruthy();
  });

  it('should not render when userType is RESTRICTED and clockedIn is false', () => {
    store = mockStore({
      locationAccess: { skipped: false },
      confetti: { visible: true, reduceMotionEnabled: false },
      deviceInfo: { isTablet: false },
      shift: { clockedIn: false },
      profile: { isUserInStore: true, userType: UserType.RESTRICTED },
      featureFlags: {
        flags: [
          {
            featureFlagName: 'diaaGovernance',
            featureFlagValue: true
          }
        ]
      }
    });




    const { queryByTestId } = render(
      <Provider store={store}>
        <FullscreenConfetti />
      </Provider>
    );

    expect(queryByTestId('confetti-container')).toBeNull();
  });

  it('should not render when reduceMotionEnabled is true', () => {
    store = mockStore({
      locationAccess: { skipped: false },
      confetti: { visible: true, reduceMotionEnabled: true },
      deviceInfo: { isTablet: false },
      shift: { clockedIn: true },
      profile: { isUserInStore: true, userType: UserType.RESTRICTED },
      featureFlags: {
        flags: [
          {
            featureFlagName: 'diaaGovernance',
            featureFlagValue: true,
          }
        ]
      }
    });




    const { queryByTestId } = render(
      <Provider store={store}>
        <FullscreenConfetti />
      </Provider>
    );

    expect(queryByTestId('confetti-container')).toBeNull();
  });

  it('should play animation and dispatch hideConfetti after delay when visible becomes true', () => {
    const mockPlay = jest.fn();
    jest.spyOn(React, 'useRef').mockReturnValueOnce({ current: { play: mockPlay } });

    store = mockStore({
      locationAccess: { skipped: false },
      confetti: { visible: true, reduceMotionEnabled: false },
      deviceInfo: { isTablet: false },
      shift: { clockedIn: true },
      profile: { isUserInStore: true, userType: UserType.NON_RESTRICTED },
      featureFlags: {
        flags: [
          {
            featureFlagName: 'diaaGovernance',
            featureFlagValue: true
          }
        ]
      }
    });

    render(
      <Provider store={store}>
        <FullscreenConfetti />
      </Provider>
    );
    expect(mockPlay).not.toHaveBeenCalled();
  });


  it('should use tablet styles when isTablet is true and conditions are met', () => {
    store = mockStore({
      locationAccess: { skipped: false },
      confetti: { visible: true, reduceMotionEnabled: false },
      deviceInfo: { isTablet: true },
      shift: { clockedIn: true },
      profile: { isUserInStore: true, userType: UserType.NON_RESTRICTED },
      featureFlags: {
        flags: [
          {
            featureFlagName: 'diaaGovernance',
            featureFlagValue: true
          }
        ]
      }
    });

    const { getByTestId } = render(
      <Provider store={store}>
        <FullscreenConfetti />
      </Provider>
    );

    const container = getByTestId('confetti-container');
    // You might need to adjust this based on your actual style implementation
    expect(container.props.style).toMatchObject({ zIndex: expect.any(Number) });
  });

  it('should not render when feature flag is disabled', () => {
    // Import the actual mock so we can override it
    const featureFlagsSelectors = require('../../../app/store/selectors/featureFlagsSelectors');

    // Override just for this test
    featureFlagsSelectors.roleGovernanceEnabled.mockImplementationOnce(() => false);

    store = mockStore({
      locationAccess: { skipped: false },
      confetti: { visible: true, reduceMotionEnabled: false },
      deviceInfo: { isTablet: false },
      shift: { clockedIn: true },
      profile: { isUserInStore: true, userType: UserType.RESTRICTED },
      featureFlags: {
        flags: [
          {
            featureFlagName: 'diaaGovernance',
            featureFlagValue: false
          }
        ]
      }
    });

    const { queryByTestId } = render(
      <Provider store={store}>
        <FullscreenConfetti />
      </Provider>
    );

    expect(queryByTestId('confetti-container')).toBeFalsy();
  });
});
import React, { useState, useRef, useEffect, useMemo } from 'react';
import {
    View,
    FlatList,
    TouchableOpacity,
    ViewToken,
    ImageSourcePropType,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useTheme } from 'pantry-design-system';

import CelebrationCard, { CelebrationCardProps } from '../../../components/CelebrationCard';
import { getStyles } from './styles';
import { FALLBACK_BANNER, FeatureKey, logos } from '../../shared/constants';
import { dismissCelebration } from '../../utils/dismissCelebrations';
import { setHomeCelebrationCards } from '../../store/reducers/profileSlice';
import { capitalizeFirstLetter, getFirstWordOfUserName } from '../../utils/helpers';
import { showConfetti } from '../../store/reducers/confettiSlice';
import { Name } from '../../misc/models/ProfileModal';
import { roleGovernanceEnabled } from '../../store/selectors/featureFlagsSelectors';
import { shouldDisplayFeature } from '../../../app/store/selectors/governance';
import { useRoute } from '@react-navigation/native';

interface CelebrationCarouselInterface {
    /** Whether to show the dismiss (X) button on each card */
    showDismiss: boolean;
    /** List of celebration cards to display in the carousel */
    celebrationCards: CelebrationCardProps[];
    analyticsCategory: string; // Added to track the page from which the carousel is accessed
}
/**
 * CelebrationCarousel displays either a single celebration card (with confetti)
 * or a horizontally scrollable carousel of celebration cards. It supports dismissal of individual cards,
 * persists dismissed state using AsyncStorage, and shows Lottie confetti animations per card once per year.
 *
 * @param {CelebrationCarouselInterface} props - Props containing dismiss toggle and cards to show.
 * @returns {JSX.Element} The rendered celebration carousel component.
 */
const CelebrationCarousel = ({ showDismiss, celebrationCards, analyticsCategory }: CelebrationCarouselInterface) => {
    const dispatch = useDispatch();
    const flatListRef = useRef<FlatList>(null);
    const [activeIndex, setActiveIndex] = useState(0);
    const viewedConfetti = useRef<Set<string>>(new Set());
    const isTablet = useSelector((state: { deviceInfo: { isTablet: boolean } }) => state.deviceInfo?.isTablet);
    const { theme } = useTheme();
    const styles = getStyles(theme, isTablet);
    const { profile, banner, isUserInStore, userType } = useSelector((state: any) => state?.profile);
    const isSingleCard = celebrationCards?.length === 1;
    const isRoleGoveranceEnabled = useSelector(roleGovernanceEnabled);
    const isClockedIn = useSelector((state: any) => state.shift.clockedIn);; // get the clockedIn state from shift slice
    const isFeatureFlagEnabled = true; //Todo: replace with actual feature flag once it is implemented for personalized messaging
    const CONFETTI_SHOWN_KEY = 'confetti_shown_once';
    const CONFETTI_CARD = 'confetti_card';

    const route = useRoute();
    const currentScreen = route?.name?.toLowerCase();

    const triggerConfetti = (cardId: string) => {
        dispatch(showConfetti({ cardId, currentScreen }));
    };


    // Will return the first word from firstName or preferredName of the user data 
    const getUserName = (): string => {
        return profile?.names && profile?.names.length > 0 ? getFirstWordOfUserName(profile?.names[0] as Name) : "";
    }

    /**
    * Checks AsyncStorage to determine if confetti has already been shown this year.
    * If not, shows confetti animation for single card celebrations.
    */
    useEffect(() => {
        const checkConfettiSeen = async () => {
            const currentYear = new Date().getFullYear();
            const storageKey = `${CONFETTI_SHOWN_KEY}-${currentYear}`;

            const seen = await AsyncStorage.getItem(storageKey);

            if (seen) return; // Already shown this year

            if (isSingleCard && celebrationCards?.length === 1) {
                const singleCard = celebrationCards?.[0];
                const confettiKey = `${CONFETTI_CARD}-${singleCard?.id}-${currentYear}`;
                const alreadyShownForCard = await AsyncStorage.getItem(confettiKey);

                if (!alreadyShownForCard) {
                    triggerConfetti(singleCard?.id);
                    await AsyncStorage.setItem(confettiKey, 'true');
                    await AsyncStorage.setItem(storageKey, 'true');
                    viewedConfetti.current.add(singleCard?.id);
                }
            }
        };

        checkConfettiSeen();
    }, [isSingleCard, celebrationCards]);

    /**
     * Returns the appropriate header image based on the current store's banner.
     *
     * @returns {ImageSourcePropType} The resolved image source.
     */
    const getBannerHeaderImage = (): ImageSourcePropType => {
        const bannerImage = banner ? logos[banner?.toLowerCase()?.replace(/[^\w]/g, '')] : logos[FALLBACK_BANNER];
        return bannerImage;
    };

    /**
     * Handles the visibility of items in the FlatList carousel.
     * Triggers confetti if the item has not previously shown it this year.
     */
    const onViewableItemsChanged = useRef(
        ({ viewableItems }: { viewableItems: ViewToken[] }) => {
            if (viewableItems?.length > 0) {
                const index = viewableItems[0]?.index ?? 0;
                const card = celebrationCards[index];
                const currentYear = new Date().getFullYear();
                const confettiKey = `${CONFETTI_CARD}-${card?.id}-${currentYear}`;

                const checkConfetti = async () => {
                    const hasShown = await AsyncStorage.getItem(confettiKey);
                    if (!hasShown && !viewedConfetti.current.has(card?.id)) {
                        viewedConfetti.current.add(card?.id);
                        triggerConfetti(card?.id);
                        await AsyncStorage.setItem(confettiKey, 'true');
                    }
                };

                checkConfetti();
                setActiveIndex(index);
            }
        }
    ).current;

    /**
     * Dismisses a celebration card and removes it from the local store.
     *
     * @param {string} id - ID of the celebration card to dismiss.
     */
    const handleDismiss = async (id: string) => {
        await dismissCelebration(id);
        dispatch(setHomeCelebrationCards(celebrationCards.filter(card => card?.id !== id)));
    };

    const isFeatureValid = useMemo(() => {
        return shouldDisplayFeature(
            FeatureKey.PERSONALIZED_MESSAGING,
            isUserInStore,
            isClockedIn,
            userType,
            isFeatureFlagEnabled,
            isRoleGoveranceEnabled
        );
    }, [isUserInStore, isClockedIn, userType, isRoleGoveranceEnabled]);

    const shouldShowCelebrationCards =
        celebrationCards &&
        celebrationCards?.length > 0 &&
        isFeatureValid

    if (!shouldShowCelebrationCards) {
        return null;
    }

    return (
        <View>
            {/* single card for one celebration */}
            {isSingleCard ? (
                <View>
                    <CelebrationCard
                        {...celebrationCards?.[0]}
                        isSingleCard={true}
                        showDismiss={showDismiss}
                        source={getBannerHeaderImage()}
                        firstName={getUserName()}
                        banner={capitalizeFirstLetter(banner || FALLBACK_BANNER)}
                        onClose={() => handleDismiss(celebrationCards[0]?.id!)}
                        analyticsCategory={analyticsCategory}
                    />

                </View>
            ) : (
                <View>
                    <FlatList
                        testID="carousel-flatlist"
                        ref={flatListRef}
                        data={celebrationCards}
                        keyExtractor={(item, index) => item?.id || `${index}`}
                        horizontal
                        showsHorizontalScrollIndicator={false}
                        pagingEnabled
                        snapToAlignment="start"
                        decelerationRate="fast"
                        snapToInterval={390}
                        onViewableItemsChanged={onViewableItemsChanged}
                        contentContainerStyle={styles.carouselList}
                        viewabilityConfig={{ itemVisiblePercentThreshold: 50 }}
                        renderItem={({ item }) => (
                            <CelebrationCard
                                {...item}
                                testID={item?.testID}
                                showDismiss={showDismiss}
                                source={getBannerHeaderImage()}
                                firstName={getUserName()}
                                banner={capitalizeFirstLetter(banner || FALLBACK_BANNER)}
                                onClose={() => handleDismiss(item?.id)}
                                analyticsCategory={analyticsCategory}
                            />

                        )}
                    />
                    <View style={styles.pagination}>
                        {celebrationCards.map((_, index) => (
                            <TouchableOpacity key={index}>
                                <View
                                    testID={`pagination-dot-${index}`}
                                    style={[styles.dot, index === activeIndex && styles.activeDot]}
                                />
                            </TouchableOpacity>
                        ))}
                    </View>
                </View>
            )}
        </View>
    );
};

export default CelebrationCarousel;

import React, { useEffect } from 'react';
import Svg, { Path } from 'react-native-svg';
import Animated, {
  useSharedValue,
  useAnimatedProps,
  withTiming,
} from 'react-native-reanimated';
import { View, AccessibilityInfo } from 'react-native';
import { useTheme } from 'pantry-design-system';
import getStyles from './styles';
import { ANIMATION_LAYOUT } from '../../shared/constants';

const AnimatedPath = Animated.createAnimatedComponent(Path);

type AnimatedUnderlineProps = {
  /** Stroke color for the underline */
  color: string;
};

/**
 * AnimatedUnderline is an animated SVG underline that respects accessibility settings.
 * It avoids animation if Reduce Motion or VoiceOver is enabled.
 * The animation draws a curved line from left to right over 500ms.
 * 186 is the total length of the line
 * @param {object} props - Component props
 * @param {string} props.color - The color of the animated underline
 *
 * @example
 *  <AnimatedUnderline color="#FF5733" />
 */

export default function AnimatedUnderline({ color }: AnimatedUnderlineProps) {
  const { theme } = useTheme();
  const styles = getStyles(theme);
  const progress = useSharedValue(0);
  const totalLength = ANIMATION_LAYOUT.WIDTH; // total length of underline

  const animatedProps = useAnimatedProps(() => ({
    strokeDashoffset: totalLength * (1 - progress.value),
  }));

  useEffect(() => {
    let isMounted = true;

    const checkAccessibilitySettings = async () => {
      const reduceMotionEnabled = await AccessibilityInfo.isReduceMotionEnabled();
      const voiceOverEnabled = await AccessibilityInfo.isScreenReaderEnabled();

      if (!isMounted) return;

      if (reduceMotionEnabled || voiceOverEnabled) {
        // Skip animation for accessibility
        progress.value = 1;
      } else {
        // Animate the underline normally
        progress.value = withTiming(1, { duration: ANIMATION_LAYOUT.DURATION });
      }
    };

    checkAccessibilitySettings();

    return () => {
      isMounted = false;
    };
  }, []);

  return (
    <View testID="underline-container" style={styles.underLineImage}>
      <Svg width={ANIMATION_LAYOUT.WIDTH} height={ANIMATION_LAYOUT.HEIGHT} viewBox="0 0 186 25">
        <AnimatedPath
          d="M2 20 C60 10, 180 10, 238 20"
          stroke={color}
          strokeWidth={ANIMATION_LAYOUT.STROKE_WIDTH}
          fill="none"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeDasharray={totalLength}
          animatedProps={animatedProps}
        />
      </Svg>
    </View>
  );
}

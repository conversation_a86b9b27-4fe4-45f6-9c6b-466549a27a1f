import { useNavigation } from '@react-navigation/native';
import moment from 'moment';
import { useTheme } from 'pantry-design-system';
import React, { useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { Image, Text, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

import Images from '../../../assets/images';
import {
  SCREENS,
  ON_BOARDING,
  ASSOCIATE_PROFILE,
  STORE,
  STAR_BRAND_THEME,
  FALLBACK_BANNER,
} from '../../shared/constants';
import { getOnboardingStep, isOnboardingCompleted } from '../../store/AsyncStorage';
import { setDurationInMinutes, setShiftTime } from '../../store/reducers/shiftSlice';

import { calculateTimeDiffinMinutes, getCurrentTime } from '../../utils/TimeUtils';
import getStyles from "./styles";


import { fetchProfileRequest } from '../../store/reducers/profileSlice';
import * as AsyncStorage from '../../store/AsyncStorage';

import type { AssociateProfile } from '../../misc/models/ProfileModal';

import shouldApiTriggerCall from '../../utils/LocalStorageUtil';
import { DURATION, xAPI_PROFILE } from '../../config/endPoints';
import { getUserLocation } from '../../utils/helpers';
import { SecureStorage, TokenType } from '../../store/SecureStorage';
import { fetchfeatureFlagRequest } from '../../store/reducers/featureFlagSlice';
import { fetchEmployeeScheduleRequest } from '../../store/reducers/employeeScheduleSlice';
import { useWeekCalendar } from '../../hooks';

import AnimatedUnderline from './AnimatedUnderline';

import type { ShiftState } from '../../store/reducers/shiftSlice';
import type { ImageSourcePropType } from 'react-native';
import { scheduleTabEnabled } from '../../store/selectors/featureFlagsSelectors';

const SplashScreen = (props: any) => {
  const { theme } = useTheme(); // Fething themes from ThemeProvider
  const styles = getStyles(theme); // Fetching styles based on theme
  const navigation = useNavigation();

  const { t } = useTranslation();

  const { clockedIn, clockedOut }: ShiftState = useSelector((state: any) => state.shift);
  const profile: AssociateProfile = useSelector((state: any) => state.profile.profile);
  const banner = useSelector((state: any) => state.profile.banner);
  const isTablet = useSelector((state: any) => state.deviceInfo?.isTablet);
  const { isPrecise } = useSelector((state: any) => state.locationAccess);
  const dispatch = useDispatch();

  const [stepCount, setStepCount] = React.useState(0);

  const { weekNumber, year, weekStartDate, weekEndDate } = useWeekCalendar([]);

  const scheduleEnabled = useSelector(scheduleTabEnabled);

  const hasFetchedRef = useRef(false);

  const fetchEmployeeSchedule = async (
    weekNumberToFetch = weekNumber,
    yearToFetch = year,
    fromDate?: string,
    toDate?: string,
  ) => {
    const requestId = await AsyncStorage.uniqueSessionId();
    const secureStorage = new SecureStorage();
    const employeeId = await secureStorage.getToken(TokenType.employeeId);

    dispatch(
      fetchEmployeeScheduleRequest({
        params: {
          requestId,
          empId: employeeId,
          weekNumber: weekNumberToFetch,
          year: yearToFetch,
          ...(fromDate && toDate ? { fromDate, toDate } : {}), // Only include if both are set
          isSilent: true,
        },
      }),
    );
  };

  // Fetch the employee schedule only once, and only when scheduleEnabled is true
  useEffect(() => {
    if (scheduleEnabled && !hasFetchedRef.current) {
      hasFetchedRef.current = true;
      fetchEmployeeSchedule(weekNumber, year, weekStartDate, weekEndDate);
    }
  }, [scheduleEnabled, weekNumber, year, weekStartDate, weekEndDate]);

  /**
   * useEffect hook that performs a series of actions when the component is mounted.
   *
   * This effect is executed only once upon mounting of the component. It handles:
   * 1. Delaying navigation to the "HomeStack" screen by 2 seconds.
   * 2. Calling `applyShiftTime` to set shift-related times and update the Redux store.
   *
   * - **Navigation**: Navigates to the "HomeStack" (which includes the Home Screen with a bottom tab bar) after 2 seconds.
   * - **Shift Calculation**: Calculates the shift start and end times, formats them, and updates the global state with the calculated shift information.
   *
   * @see {useNavigation} for navigation handling.
   * @see {setTimeout} for delaying the navigation action by 2 seconds.
   * @see {applyShiftTime} for setting the shift time logic.
   */
  useEffect(() => {
    fetchFeatureFlagApi();
    // Navigate to the HomeStack after a 5-second delay
    const isSkip = props?.route?.params?.skip;
    if (isSkip != null && isSkip) {
      handleNavigation();
    } else
      setTimeout(() => {
        handleNavigation();
      }, 5000);

    // calling store location API everytime in app launch
    if (isPrecise === 'precise') {
      fetchStoreApi();
    }

    // get the last call timestamp and check if the API should be called again or not
    shouldApiTriggerCall(xAPI_PROFILE, DURATION[xAPI_PROFILE])
      .then((shouldCallApi) => {
        if (shouldCallApi) {
          fetchProfileApi();
        }
      })
      .catch((error) => {
        // Error
      });
  }, []); // Empty dependency array ensures this runs only once when the component is mounted.

  const fetchProfileApi = async () => {
    const secureStorage = new SecureStorage();
    const empId = await secureStorage.getToken(TokenType.employeeId);
    const requestId = await AsyncStorage.uniqueSessionId();
    dispatch(
      fetchProfileRequest({
        requestId,
        empId: empId,
        fl: ASSOCIATE_PROFILE,
      }),
    );
  };
  const fetchFeatureFlagApi = async () => {
    const requestId = await AsyncStorage.uniqueSessionId();
    dispatch(
      fetchfeatureFlagRequest({
        requestId,
        banner: profile?.banner || FALLBACK_BANNER,
      }),
    );
  };
  const fetchStoreApi = async () => {
    const userLocation = await getUserLocation();
    const requestId = await AsyncStorage.uniqueSessionId();
    dispatch(
      fetchProfileRequest({
        requestId,
        fl: STORE,
        location: userLocation,
        isSilent: true,
      }),
    );
  };

  /**
   * Handles the navigation logic based on the onboarding completion status and the current onboarding step.
   *
   * If the onboarding is completed, it navigates to the 'BOTTOM_TAB_STACK' screen.
   * Otherwise, it navigates to the appropriate onboarding screen based on the current onboarding step.
   *
   * @async
   * @function handleNavigation
   */
  const handleNavigation = async () => {
    const isCompleted = await isOnboardingCompleted();
    if (isCompleted) {
      navigation.replace(SCREENS.BOTTOM_TAB_STACK);
    } else {
      const onboardingStep = await getOnboardingStep();
      setStepCount(onboardingStep);
      switch (onboardingStep) {
        case 0:
          navigation.replace(SCREENS.ONBOARDING_STACK, {
            screen: SCREENS.ONBOARDING_FLOW,
            params: { step: ON_BOARDING.ONBOARDING_ONE },
          });
          break;
        case 1:
          navigation.replace(SCREENS.ONBOARDING_STACK, {
            screen: SCREENS.ONBOARDING_FLOW,
            params: { step: ON_BOARDING.ONBOARDING_TWO },
          });
          break;
        case 2:
          navigation.replace(SCREENS.ONBOARDING_STACK, {
            screen: SCREENS.ONBOARDING_FLOW,
            params: { step: ON_BOARDING.ONBOARDING_THREE },
          });
          break;
        case 3:
          navigation.replace(SCREENS.ONBOARDING_STACK, {
            screen: SCREENS.ONBOARDING_FLOW,
            params: { step: ON_BOARDING.ONBOARDING_FOUR },
          });
          break;
        case 4:
          navigation.replace(SCREENS.BOTTOM_TAB_STACK);
          break;
        default:
          navigation.replace(SCREENS.BOTTOM_TAB_STACK);
          break;
      }
    }
  };

  /**
   * Applies the shift time logic, including setting start and end times for the shift.
   *
   * This function is responsible for:
   * 1. Checking whether the associate is clocked in or out.
   * 2. If the associate is not clocked in, it calculates the start and end time of the shift based on the current time.
   * 3. It updates the Redux store with:
   *    - Formatted shift start and end times.
   *    - Duration in minutes from the current time to the calculated start time.
   *
   * @returns {void}
   * @see {dispatch} to update the Redux store with shift times and duration.
   * @see {moment} to format the shift times and calculate duration.
   */
  const applyShiftTime = () => {
    // If the user is neither clocked in nor clocked out, or if just clocked out, apply the shift time logic
    if ((clockedIn && clockedOut) || (!clockedIn && !clockedOut)) {
      const current = new Date();

      // Calculate the shift's start and end time
      const formattedStartTime = new Date(current.getTime() + 3 * 60 * 1000); // Shift starts 3 minutes from now
      const formattedEndTime = new Date(formattedStartTime.getTime() + 8 * 60 * 60 * 1000); // Shift ends 8 hours from the start

      // Format times as strings (using moment.js)
      const startTimeString = moment(formattedStartTime, 'hh:mm:ss A').format('hh:mm A');
      const endTimeString = moment(formattedEndTime, 'hh:mm:ss A').format('hh:mm A');

      // Combine the start and end times into a range string (e.g., "9:00 AM - 5:00 PM")
      const shiftStartEndTime = `${moment(startTimeString, 'hh:mm:ss A').format('hh:mm A')} - ${moment(endTimeString, 'hh:mm:ss A').format('hh:mm A')}`;

      // Get the current time and calculate the duration until the shift starts
      const now = getCurrentTime();
      const duration: number = calculateTimeDiffinMinutes(now, startTimeString);

      // Dispatch actions to store the shift details and calculated duration
      dispatch(setDurationInMinutes(duration));
      dispatch(
        setShiftTime({
          shiftStartEndTime,
          startTime: startTimeString,
          endTime: endTimeString,
        }),
      );
    }
  };

  const images: { logos: { [key: string]: ImageSourcePropType } } = {
    logos: {
      albertsons: Images.ALBERTSONS.LOGO,
      safeway: Images.SAFEWAY.LOGO,
      vons: Images.VONS.LOGO,
      randalls: Images.RANDALLS.LOGO,
      tomthumb: Images.TOMTHUMB.LOGO,
      acmemarkets: Images.ACME.LOGO,
      jewelosco: Images.JEWEL_OSCO.LOGO,
      carrsqc: Images.CARRS.LOGO,
      pavilions: Images.PAVILIONS.LOGO,
      kingsfoodmarkets: Images.KINGS.LOGO,
      shaws: Images.SHAWS.LOGO,
      haggen: Images.HAGGEN.LOGO,
      andronicos: Images.ANDRONICOS.LOGO,
      starmarket: Images.START_MARKET.LOGO,
      balduccis: Images.BALDUCCIS.LOGO,
    },
  };

  const getBannerImage = (): ImageSourcePropType => {
    return images.logos[banner?.toLowerCase().replace(/[^\w]/g, '')] ?? Images.ALBERTSONS.LOGO;
  };

  return props?.route?.params?.skip ? null : (
    <View style={styles.container}>
      <Image
        testID="splash-logo"
        source={getBannerImage()}
        style={
          isTablet
            ? [
              styles.tabletBannerLogo,
              { width: '100%', height: isTablet ? 400 : 200, resizeMode: 'contain' },
            ]
            : styles.bannerLogo
        }
      />
      {profile != null && (
        <>
          <Text style={isTablet ? styles.tabletNameLabel : styles.nameLabel}>{t('welcome')}</Text>
          <Text
            style={isTablet ? styles.tabletNameLabel : styles.nameLabel}
          >{`${profile?.names[0].firstName}!`}</Text>
          <AnimatedUnderline color={STAR_BRAND_THEME(banner, theme)} />
        </>
      )}
    </View>
  );
};

export default SplashScreen;

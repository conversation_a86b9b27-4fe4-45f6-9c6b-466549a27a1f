import React from "react";
import { render, act, waitFor } from "@testing-library/react-native";
import { Provider } from "react-redux";
import configureStore from "redux-mock-store";
import SplashScreen from "./splashScreen";
import { useNavigation } from "@react-navigation/native";
import { isOnboardingCompleted, getOnboardingStep } from "../../store/AsyncStorage";
import { ON_BOARDING, SCREENS } from "../../shared/constants";


jest.mock('react-native-permissions', () => ({
  PERMISSIONS: {
    ANDROID: {},
    IOS: {},
  },
  check: jest.fn(),
  request: jest.fn(),
}));

jest.mock('@react-native-community/geolocation');
jest.mock('@react-native-async-storage/async-storage', () => ({
  uniqueSessionId: jest.fn(() => Promise.resolve('mock-request-id')),
}));

// This assumes you export getUserLocation separately
jest.mock('../../../app/utils/helpers', () => ({
  ...jest.requireActual('../../../app/utils/helpers'),
  getUserLocation: jest.fn(() => Promise.resolve({
    latitude: 37.7749,
    longitude: -122.4194,
  })),
}));


jest.mock('@appdynamics/react-native-agent', () => ({
  Instrumentation: {
    start: jest.fn(),
    setUserData: jest.fn(),
    removeUserData: jest.fn(),
    reportError: jest.fn(),
    reportMetric: jest.fn(),
    leaveBreadcrumb: jest.fn(),
    startTimer: jest.fn(),
    stopTimer: jest.fn(),
    startNextSession: jest.fn(),
  },
  LoggingLevel: { VERBOSE: 'VERBOSE', INFO: 'INFO' },
  ErrorSeverityLevel: { WARNING: 'WARNING', CRITICAL: 'CRITICAL' },
  BreadcrumbVisibility: { CRASHES_AND_SESSIONS: 'CRASHES_AND_SESSIONS' },
}));

jest.mock("../../store/AsyncStorage", () => ({
  getOnboardingStep: jest.fn(),
  isOnboardingCompleted: jest.fn(),
}));

jest.mock("@react-navigation/native", () => ({
  useNavigation: jest.fn(() => ({
    replace: jest.fn(),
  })),
}));

jest.mock('react-native-device-info', () => {
  return {
    getUniqueId: jest.fn(() => 'mocked-device-id'),
    // Add other mocked methods as needed
  };
});

jest.mock('../../store/AsyncStorage', () => ({
  setItem: jest.fn(),
  getItem: jest.fn(),
  uniqueSessionId: jest.fn(() => 'mocked-session-id'),
  getOnboardingStep: jest.fn(),
  isOnboardingCompleted: jest.fn(),
}));

jest.useFakeTimers();

describe("SplashScreen", () => {
  const mockNavigation = { dispatch: jest.fn(), replace: jest.fn() };
  const mockRoute = { params: { skip: false } };
  const mockStore = configureStore([]);
  const initialState = {
    profile: {
      profile: {
        names: [{ firstName: "User Name" }],
      },
    },
    locationAccess: {
      isSkipped: false,
      isPrecise: false, // Add this to avoid the error
    },
    shift: { clockedIn: false, clockedOut: false },
    deviceInfo: { isTablet: false },
    empSchedule: {
      loading: false,
      error: null,
      data: {
        scheduleResponse: {
          errors: [],
          workedHours: [],
          balances: [],
        },
      },
    },
    // Add language state to prevent undefined error
    language: {
      id: "en",
      name: "English"
    },
  };

  beforeEach(() => {
    (useNavigation as jest.Mock).mockReturnValue(mockNavigation);
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.clearAllTimers();
  });

  it("renders the splash screen correctly", () => {
    const store = mockStore(initialState);

    const { getByText, getByTestId } = render(
      <Provider store={store}>
        <SplashScreen route={mockRoute} />
      </Provider>
    );

    expect(getByTestId("splash-logo")).toBeTruthy();
    expect(getByText("welcome")).toBeTruthy();
    expect(getByText("User Name!")).toBeTruthy();
  });

  it("navigates to HomeStack when onboarding is completed", async () => {
    const store = mockStore(initialState);
    (isOnboardingCompleted as jest.Mock).mockResolvedValue(true);

    render(
      <Provider store={store}>
        <SplashScreen route={mockRoute} />
      </Provider>
    );

    await act(async () => {
      jest.runAllTimers();
    });

    await waitFor(() => {
      expect(mockNavigation.replace).toHaveBeenCalledWith("BOTTOM_TAB_STACK");
    });
  });

  it("navigates to the onboardingOne step when onboarding is not completed", async () => {
    const store = mockStore(initialState);
    (isOnboardingCompleted as jest.Mock).mockResolvedValue(false);
    (getOnboardingStep as jest.Mock).mockResolvedValue(0);

    render(
      <Provider store={store}>
        <SplashScreen route={mockRoute} />
      </Provider>
    );

    await act(async () => {
      jest.runAllTimers();
    });

    await waitFor(() => {
      expect(mockNavigation.replace).toHaveBeenCalledWith(SCREENS.ONBOARDING_STACK, {
        screen: SCREENS.ONBOARDING_FLOW,
        params: { step: ON_BOARDING.ONBOARDING_ONE }
      })
    });
  });

  it("navigates to the onboardingTwo step when onboarding one is completed", async () => {
    const store = mockStore(initialState);
    (isOnboardingCompleted as jest.Mock).mockResolvedValue(false);
    (getOnboardingStep as jest.Mock).mockResolvedValue(1);

    render(
      <Provider store={store}>
        <SplashScreen route={mockRoute} />
      </Provider>
    );

    await act(async () => {
      jest.runAllTimers();
    });

    await waitFor(() => {
      expect(mockNavigation.replace).toHaveBeenCalledWith(SCREENS.ONBOARDING_STACK, {
        screen: SCREENS.ONBOARDING_FLOW,
        params: { step: ON_BOARDING.ONBOARDING_TWO }
      })
    });
  });

  it("navigates to the onboardingThree step when onboarding two is completed", async () => {
    const store = mockStore(initialState);
    (isOnboardingCompleted as jest.Mock).mockResolvedValue(false);
    (getOnboardingStep as jest.Mock).mockResolvedValue(2);

    render(
      <Provider store={store}>
        <SplashScreen route={mockRoute} />
      </Provider>
    );

    await act(async () => {
      jest.runAllTimers();
    });

    await waitFor(() => {
      expect(mockNavigation.replace).toHaveBeenCalledWith(SCREENS.ONBOARDING_STACK, {
        screen: SCREENS.ONBOARDING_FLOW,
        params: { step: ON_BOARDING.ONBOARDING_THREE }
      })
    });
  });

  it("navigates to the onboardingFour step when onboarding three is completed", async () => {
    const store = mockStore(initialState);
    (isOnboardingCompleted as jest.Mock).mockResolvedValue(false);
    (getOnboardingStep as jest.Mock).mockResolvedValue(3);

    render(
      <Provider store={store}>
        <SplashScreen route={mockRoute} />
      </Provider>
    );

    await act(async () => {
      jest.runAllTimers();
    });

    await waitFor(() => {
      expect(mockNavigation.replace).toHaveBeenCalledWith(SCREENS.ONBOARDING_STACK, {
        screen: SCREENS.ONBOARDING_FLOW,
        params: { step: ON_BOARDING.ONBOARDING_FOUR }
      })
    });
  });

  it("handles Spanish language correctly", () => {
    const spanishState = {
      ...initialState,
      language: {
        id: "es",
        name: "Spanish"
      },
    };
    const store = mockStore(spanishState);

    const { getByText, getByTestId } = render(
      <Provider store={store}>
        <SplashScreen route={mockRoute} />
      </Provider>
    );

    expect(getByTestId("splash-logo")).toBeTruthy();
    expect(getByText("welcome")).toBeTruthy();
    expect(getByText("User Name!")).toBeTruthy();
  });

  it("handles undefined language gracefully", () => {
    const noLanguageState = {
      ...initialState,
      language: undefined,
    };
    const store = mockStore(noLanguageState);

    const { getByText, getByTestId } = render(
      <Provider store={store}>
        <SplashScreen route={mockRoute} />
      </Provider>
    );

    expect(getByTestId("splash-logo")).toBeTruthy();
    expect(getByText("welcome")).toBeTruthy();
    expect(getByText("User Name!")).toBeTruthy();
  });
});

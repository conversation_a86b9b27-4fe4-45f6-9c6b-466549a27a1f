// __tests__/AnimatedUnderline.test.tsx
import React from 'react';
import { render, waitFor } from '@testing-library/react-native';
import AnimatedUnderline from './AnimatedUnderline';
import { AccessibilityInfo } from 'react-native';

jest.mock('react-native-reanimated', () =>
    require('react-native-reanimated/mock')
);

describe('AnimatedUnderline', () => {
    it('renders the underline component', () => {
        const testColor = '#FF5733';
        const { getByTestId } = render(<AnimatedUnderline color={testColor}/>);
        expect(getByTestId('underline-container')).toBeTruthy();
    });


  it('skips animation when Reduce Motion is enabled', async () => {
    jest.spyOn(AccessibilityInfo, 'isReduceMotionEnabled').mockResolvedValue(true);
    jest.spyOn(AccessibilityInfo, 'isScreenReaderEnabled').mockResolvedValue(false);

    const { getByTestId } = render(<AnimatedUnderline color="#000" />);

    await waitFor(() => {
      expect(getByTestId('underline-container')).toBeTruthy();
    });
  });

  it('skips animation when VoiceOver is enabled', async () => {
    jest.spyOn(AccessibilityInfo, 'isReduceMotionEnabled').mockResolvedValue(false);
    jest.spyOn(AccessibilityInfo, 'isScreenReaderEnabled').mockResolvedValue(true);

    const { getByTestId } = render(<AnimatedUnderline color="#000" />);

    await waitFor(() => {
      expect(getByTestId('underline-container')).toBeTruthy();
    });

  });
});

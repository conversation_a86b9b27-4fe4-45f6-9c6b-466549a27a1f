import { Theme } from 'pantry-design-system';
import { StyleSheet } from "react-native";

//customising styles based on themes
const getStyles = ({ colors, fonts, dimensions, typography }: Theme) => {
    const styles = StyleSheet.create({
        container: {
            flex: 1,
            alignItems: "center",
            justifyContent: "center",
            paddingTop: 0,
            backgroundColor: colors.pdsThemeColorBackgroundBaseline,
        },
        bannerLogo: {
            width: 200,
            height: 200,
            paddingTop: 195,
        },
        nameLabel: {
            textAlign: "center",
            marginTop: 0,
            fontSize: typography.pdsGlobalFontSize600,
            alignSelf: "center",
            fontWeight: typography.pdsGlobalFontWeight500,
            fontFamily: fonts.pdsGlobalFontFamilyPoppins,
            color: colors.pdsThemeColorForegroundNeutralHigh,
        },
        underLineImage: {
            marginTop: -dimensions.pdsGlobalSpace300,
        },
        tabletBannerLogo: {
            width: dimensions.pdsGlobalSizeWidth400,
            height: dimensions.pdsGlobalSizeWidth400,
            paddingTop: 168.36,
        },
        tabletUnderLineImage: {
            width: 231.22,
            height: dimensions.pdsGlobalSizeWidth200,
            marginTop: dimensions.pdsGlobalSpace500,
        },
        tabletNameLabel: {
            textAlign: "center",
            marginTop: 0,
            fontSize: typography.pdsGlobalFontSize700,
            alignSelf: "center",
            fontWeight: typography.pdsGlobalFontWeight500,
            fontFamily: fonts.pdsGlobalFontFamilyPoppins

        },
    });

    return styles
}

export default getStyles;
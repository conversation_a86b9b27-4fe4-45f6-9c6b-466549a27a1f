import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { useTheme, Heading, Text, BottomSheet, Button } from 'pantry-design-system';
import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  View,
  Text as RNText,
  ScrollView,
  Pressable,
  AppState,
  Platform,
  findNodeHandle,
  AccessibilityInfo,
} from 'react-native';
import { openSettings } from 'react-native-permissions';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { SvgXml } from 'react-native-svg';
import { useDispatch, useSelector } from 'react-redux';

import ABAuditEngine from '../../../../analytics/ABAuditEngine';
import { PROFILE_ANALYTICS } from '../../../../analytics/AnalyticsConstants';
import { userActionLogEvent, screenViewLog } from '../../../../analytics/AnalyticsUtils';
import { ChevronRight } from '../../../../assets/icons/ChevronRight';
import { InfoIcon } from '../../../../assets/icons/infoIcon';
import { help, language, locationPin } from '../../../../assets/images/svg/profileIcons';
import CDivider from '../../../../components/divider/CDivider';
import Footer from '../../../../components/footer/Footer';
import SwitchToggle from '../../../../components/SwitchToggle/SwitchToggle';
import { PageWrapper } from '../../../../components/wrapper/PageWrapper';
import { useAccessibilityFocus } from '../../../providers/AccessibilityFocus';
import {
  LEGAL_CONSENT,
  LOCATION_STATE,
  SCREENS,
  TEXT_PANTRY_COLORS,
  URLS,
  TERMS_AND_CONDITIONS_URL,
  HEADING_SIZE,
  TEXT_ALIGN,
  ADA_ROLES,
  TEXT_SIZE,
  ADA,
  TEXTLINK_COLORS,
  TEXT_WEIGHT,
  BUTTON_SIZE,
  ImportantForAccessibility,
  TncAgreementType,
  TncAgreementEvent,
  TncAgreementStatus,
  TEXTDECORATION,
} from '../../../shared/constants';
import { fetchTncAgreementRequest } from '../../../store/reducers/tncAgreementSlice';
import { setPreventTabsAccessibility, setPreventHeaderAccessibility } from '../../../store/reducers/accessibilitySlice';
import { SecureStorage, TokenType } from '../../../store/SecureStorage';
import { buildTncAgreementRequestBody } from '../../../utils/helpers';

import getStyles from './SettingStyles';

const SettingScreen = () => {
  const { theme } = useTheme(); /* Fething themes from ThemeProvider */
  const styles = getStyles(theme); /* Fetching styles based on theme */

  const primaryColor =
    theme.colors.pdsThemeColorForegroundPrimary; /* Fetching primary color from theme */

  const { t } = useTranslation();

  const navigation = useNavigation();
  const { setLastFocused, setRef, handleScreenFocus } = useAccessibilityFocus();
  const didGetCodeRef = useRef<View>(null);

  useEffect(() => {
    const unsubscribeFocus = navigation.addListener('focus', () => {
      handleScreenFocus(SCREENS.SETTINGS);
    });

    return () => {
      unsubscribeFocus?.();
    };
  }, [navigation]);

  const dispatch = useDispatch();

  const [isSwitchOn, setIsSwitchOn] = useState(false); /*  toggle button state */
  const [isLocationToggleClicked, setIsLocationToggleClicked] = useState(false);
  const { isPrecise } = useSelector((state: any) => state.locationAccess); // Fetching the precise location setting from the redux store
  const appState = useRef(AppState.currentState);
  const secureStorage = new SecureStorage();
  const [isDeleteDataWarningVisible, setIsDeleteDataWarningVisible] = useState<boolean>(false);

  // Use Redux to manage header and tabs accessibility like HrAndPayroll
  useEffect(() => {
    dispatch(setPreventTabsAccessibility(isDeleteDataWarningVisible));
    dispatch(setPreventHeaderAccessibility(isDeleteDataWarningVisible));
  }, [isDeleteDataWarningVisible, dispatch]);

  useEffect(() => {
    /**
     * Toggles the location service based on the user's precise location state and logs the appropriate events.
     *
     * This function checks the user's location state (`isPrecise`) and whether the location toggle was clicked
     * (`isLocationToggleClicked`). Depending on the state, it performs the following actions:
     *
     * - Logs user actions using `userActionLogEvent` for toggling location settings.
     * - Leaves breadcrumbs for auditing purposes using `ABAuditEngine.leaveBreadcrumb`.
     * - Updates the switch state (`setIsSwitchOn`) to reflect the precise location permission.
     *
     * @async
     * @function toggleLocationService
     * @returns {Promise<void>} Resolves when the toggle operation is complete.
     *
     * @remarks
     * - If `isPrecise` is `LOCATION_STATE.PRECISE`, the switch is set to ON and precise location consent is logged.
     * - If `isPrecise` is not `LOCATION_STATE.PRECISE`, the switch is set to OFF and either approximate location
     *   or denied location consent is logged.
     *
     * @example
     * ```typescript
     * await toggleLocationService();
     * ```
     */
    const toggleLocationService = async () => {
      const employeeId = await secureStorage.getToken(TokenType.employeeId);
      const timeStamp = new Date().toLocaleString();
      if (isPrecise === LOCATION_STATE.PRECISE) {
        if (isLocationToggleClicked) {
          userActionLogEvent(
            PROFILE_ANALYTICS.PROFILE,
            PROFILE_ANALYTICS.SETTINGS_TOGGLE_ON,
            PROFILE_ANALYTICS.SETTINGS,
          );
          ABAuditEngine.leaveBreadcrumbWithMode(
            `${LEGAL_CONSENT.LOCATION_PRECISE} (${timeStamp}) - EmployeeID:${employeeId}`,
          );
          const body = await buildTncAgreementRequestBody(
            TncAgreementType.LOCATION_PERMISSION,
            TncAgreementEvent.update,
            TncAgreementStatus.optIn,
          );
          dispatch(fetchTncAgreementRequest({ body }));
        }
        setIsSwitchOn(true); // Set the switch to ON if precise location is granted
      } else {
        if (isLocationToggleClicked) {
          userActionLogEvent(
            PROFILE_ANALYTICS.PROFILE,
            PROFILE_ANALYTICS.SETTINGS_TOGGLE_OFF,
            PROFILE_ANALYTICS.SETTINGS,
          );
          if (isPrecise === LOCATION_STATE.APPROXIMATE) {
            ABAuditEngine.leaveBreadcrumbWithMode(
              `${LEGAL_CONSENT.LOCATION_APPROXIMATE} (${timeStamp}) - EmployeeID:${employeeId}`,
            );
            const body = await buildTncAgreementRequestBody(
              TncAgreementType.LOCATION_PERMISSION,
              TncAgreementEvent.update,
              TncAgreementStatus.optIn,
            );
            dispatch(fetchTncAgreementRequest({ body }));
          } else {
            ABAuditEngine.leaveBreadcrumbWithMode(
              `${LEGAL_CONSENT.LOCATION_DENIED} (${timeStamp}) - EmployeeID:${employeeId}`,
            );
            const body = await buildTncAgreementRequestBody(
              TncAgreementType.LOCATION_PERMISSION,
              TncAgreementEvent.update,
              TncAgreementStatus.optOut,
            );
            dispatch(fetchTncAgreementRequest({ body }));
          }
        }
        setIsSwitchOn(false); // Set the switch to OFF if precise location is not granted
      }
    };

    toggleLocationService();
  }, [isPrecise]);
  const isTablet = useSelector((state: any) => state.deviceInfo?.isTablet);
  const isLandscape = useSelector((state: any) => state.deviceInfo.isLandscape);
  
  const onCloseModal = () => {
    if (isDeleteDataWarningVisible) {
      setIsDeleteDataWarningVisible(false);
    }

    if (didGetCodeRef && didGetCodeRef.current) {
      const tag = findNodeHandle(didGetCodeRef.current);
      if (tag) {
        setTimeout(() => {
          AccessibilityInfo.setAccessibilityFocus(tag);
        }, 500);
      }
    }
  };

  /**
   * Navigates to the WebView screen with the given URL and header title.
   *
   * @param {string} url - The URL to be loaded in the WebView.
   * @param {string} titleHeader - The title to be displayed in the WebView header.
   *
   * @example
   * handleWeViewNavigation(URLS.TERMS_OF_USE_PDF, t("Terms of Use"));
   */
  const handleWeViewNavigation = async (
    url: string,
    titleHeader: string,
    link: string,
    testID: string,
  ) => {
    setLastFocused(SCREENS.SETTINGS, testID);
    userActionLogEvent(PROFILE_ANALYTICS.PROFILE, link, PROFILE_ANALYTICS.SETTINGS);
    navigation.navigate(SCREENS.WEBVIEW, {
      url,
      titleHeader,
      previousTab: SCREENS.PROFILE,
      isOmniheaderPresent: false,
      isHeaderRight: false,
    });
    const body = await buildTncAgreementRequestBody(
      TncAgreementType.TERMS_AND_CONDITIONS,
      TncAgreementEvent.new,
      TncAgreementStatus.optIn,
    );
    dispatch(fetchTncAgreementRequest({ body }));
  };

  const handleNavigation = async (screen: string, testID: string) => {
    setLastFocused(SCREENS.SETTINGS, testID);
    navigation.navigate(screen, {
      setBackButtonRef: (node: any) => {
        setTimeout(() => {
          const tag = findNodeHandle(node);
          if (tag) {
            AccessibilityInfo.setAccessibilityFocus(tag);
          }
        }, ADA.ACCESSIBILITY_FOCUS);
      },
    });
    const body = await buildTncAgreementRequestBody(
      TncAgreementType.PRIVACY_POLICY,
      TncAgreementEvent.new,
      TncAgreementStatus.optIn,
    );
    dispatch(fetchTncAgreementRequest({ body }));
  };

  useFocusEffect(
    React.useCallback(() => {
      screenViewLog({ subsection1: PROFILE_ANALYTICS.SETTINGS_PAGE_TITLE });
    }, []),
  );
  useEffect(() => {
    // Add an event listener to monitor changes in the app's state (foreground/background)
    const subscription = AppState.addEventListener('change', (nextAppState) => {
      // Check if the app is transitioning from inactive/background to active state
      if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
        screenViewLog({ subsection1: PROFILE_ANALYTICS.SETTINGS_PAGE_TITLE });
      }
      // Update the current app state
      appState.current = nextAppState;
    });

    // Cleanup function to remove the event listener when the component unmounts
    return () => {
      subscription.remove();
    };
  }, []);

  /**
   * Navigates to the feedback page.
   * Sets the last focused element to 'feedback-card' for accessibility purposes.
   */
  const navigateToFeedback = () => {
    setLastFocused(SCREENS.SETTINGS, 'feedback-card');
    navigation.navigate(SCREENS.WEBVIEW, {
      url: URLS.FEEDBACK,
      titleHeader: t('feedback'),
      previousTab: SCREENS.PROFILE,
    });
  };

  const navigateToLanguage = () => {
    setLastFocused(SCREENS.SETTINGS, 'language-card');
    navigation.navigate(SCREENS.LANGUAGE);
  };

  const insets = useSafeAreaInsets();
  return (
    <>
    <ScrollView 
    showsVerticalScrollIndicator={false} 
    style={styles.container} 
    accessibilityElementsHidden={isDeleteDataWarningVisible} 
    importantForAccessibility={isDeleteDataWarningVisible ? 
      ImportantForAccessibility.NO_HIDE : 
      ImportantForAccessibility.AUTO} 
    testID="settings-scrollview">
      <PageWrapper>
        <View style={styles.card} testID="location-card">
          <View style={styles.cardHeading}>
            <SvgXml
              accessible={true}
              accessibilityLabel="map Icon"
              accessibilityRole="image"
              xml={locationPin}
              color={theme.colors.pdsThemeColorBackgroundPrimary}
              style={{ width: theme.dimensions.pdsGlobalSizeWidth800 }}
              testID="location-icon"
            />

            <Heading
              accessible
              testID={'location-text'}
              textAlign="center"
              title={t('deviceLocationSettings')}
              color={TEXT_PANTRY_COLORS.NEUTRAL_HIGH}
              size={HEADING_SIZE.Small}
            />
          </View>

          <View style={styles.cardHeading}>
            <SwitchToggle
              onValueChange={() => {
                setIsLocationToggleClicked(true);
                openSettings();
              }}
              active={isSwitchOn}
              testID="location-toggle"
              accessible={true}
              accessibilityLabel={`${
                isSwitchOn ? t('locationAccessTurnedOn') : t('locationAccessTurnedOff')
              } ${t('switchButton')}, ${isSwitchOn ? 'on' : 'off'}, ${t('doubleTapToToggleSetting')}`}
            />

            <Text
              color={TEXT_PANTRY_COLORS.NEUTRAL_HIGH}
              size={TEXT_SIZE.LARGE}
              text={isSwitchOn ? t('locationAccessTurnedOn') : t('locationAccessTurnedOff')}
              weight={TEXT_WEIGHT.SEMI_BOLD}
              accessible={true}
              testID="location-access-text"
              textDecoration={TEXTDECORATION.None}
              textAlign={TEXT_ALIGN.CENTER}
            />
          </View>

          <RNText
            style={styles.text3}
            testID="legal-text"
            accessibilityLabel={`${t('legalTextAboutLocationAccess')} ${t('Terms of Use')}: link opens in web view ${t('andOur')} ${t('Privacy Policy')}: link opens in web view.`}
          >
            {t('legalTextAboutLocationAccess')}
            <RNText
              ref={setRef('terms-and-conditions-link')}
              onPress={() =>
                handleWeViewNavigation(
                  TERMS_AND_CONDITIONS_URL,
                  t('Terms of Use'),
                  PROFILE_ANALYTICS.LEGAL_AND_ABOUT,
                  'terms-of-use',
                )
              }
              style={styles.linkText}
              testID="terms-and-conditions-link"
              accessible={true}
              accessibilityRole="link"
              accessibilityLabel={`${t('Terms of Use')} link opens in web view`}
            >
              {t('Terms of Use')}
            </RNText>
            <RNText style={styles.text3} testID="and-our-text" accessibilityLabel={t('andOur')}>
              {t('andOur')}
            </RNText>

            <RNText
              onPress={() => handleNavigation(SCREENS.PRIVACY_POLICY, 'conditions-link')}
              style={styles.linkText}
              testID="privacy-link"
              accessible={true}
              accessibilityRole="link"
              accessibilityLabel={`${t('Privacy Policy')} link opens in web view`}
            >
              {t('privacyPolicy') + '.'}
            </RNText>
          </RNText>
        </View>

        <Pressable
          style={styles.feedbackCard}
          testID="language-card"
          ref={setRef('language-card')}
          onPress={navigateToLanguage}
          accessible={true}
          accessibilityRole={ADA_ROLES.BUTTON}
          accessibilityLabel={t('ada.languageBtn')}
          importantForAccessibility="yes"
          accessibilityHint={Platform.OS === 'ios' ? t('ada.doubleTapToActivate') : ''}
        >
          <View style={styles.feedbackHeading}>
            <View style={styles.cardHeading}>
              <SvgXml
                xml={language(primaryColor)}
                color={theme.colors.pdsThemeColorBackgroundPrimary}
                style={{ width: theme.dimensions.pdsGlobalSizeWidth800 }}
                testID="location-icon"
              />

              <Heading
                testID={'language-heading'}
                title={t('language')}
                textAlign={TEXT_ALIGN.LEFT}
                color={TEXT_PANTRY_COLORS.NEUTRAL_HIGH}
                size={HEADING_SIZE.XSmall}
              />
            </View>
            <ChevronRight
              color={theme.colors.pdsThemeColorForegroundNeutralHigh}
              size={theme.dimensions.pdsGlobalSizeHeight400}
            />
          </View>

          <Text
            text={t('languageDescription')}
            size={TEXT_SIZE.MEDIUM}
            color={TEXT_PANTRY_COLORS.NEUTRAL_LOW}
          />
        </Pressable>
        <Pressable
          style={styles.feedbackCard}
          testID="feedback-card"
          ref={setRef('feedback-card')}
          onPress={navigateToFeedback}
          accessible={true}
          accessibilityRole={ADA_ROLES.BUTTON}
          accessibilityLabel={
            t('ada.feedbackTitleButton') + ' ' + t('ada.feedbackDescriptionButton')
          }
          importantForAccessibility="yes"
          accessibilityHint={Platform.OS === 'ios' ? t('ada.doubleTapToActivate') : ''}
        >
          <View style={styles.feedbackHeading}>
            <View style={styles.cardHeading}>
              <SvgXml
                xml={help(primaryColor)}
                color={theme.colors.pdsThemeColorBackgroundPrimary}
                style={{ width: theme.dimensions.pdsGlobalSizeWidth800 }}
                testID="location-icon"
              />

              <Heading
                testID={'feedback-heading'}
                title={t('feedback')}
                textAlign={TEXT_ALIGN.LEFT}
                color={TEXT_PANTRY_COLORS.NEUTRAL_HIGH}
                size={HEADING_SIZE.XSmall}
                accessible={false}
              />
            </View>
            <ChevronRight
              color={theme.colors.pdsThemeColorForegroundNeutralHigh}
              size={theme.dimensions.pdsGlobalSizeHeight400}
            />
            {/* <ChevronRight
            color={theme.colors.pdsThemeColorForegroundNeutralHigh}
            size={theme.dimensions.pdsGlobalSizeHeight400}
          /> */}
          </View>

          <Text
            text={t('feedbackDescription')}
            size={TEXT_SIZE.MEDIUM}
            color={TEXT_PANTRY_COLORS.NEUTRAL_LOW}
            accessible={false}
          />
        </Pressable>

        <View style={styles.legalCard} testID="legal-card">
          <Pressable
            ref={setRef('terms-of-use')}
            style={[styles.cardHeading, { justifyContent: 'space-between' }]}
            onPress={() =>
              handleWeViewNavigation(
                TERMS_AND_CONDITIONS_URL,
                t('Terms of Use'),
                PROFILE_ANALYTICS.LEGAL_AND_ABOUT,
                'terms-of-use',
              )
            }
            accessible={true}
            accessibilityRole={ADA_ROLES.BUTTON}
            accessibilityLabel={t('Terms of Use')}
          >
            <Text
              color={TEXT_PANTRY_COLORS.NEUTRAL_HIGH}
              size={TEXT_SIZE.LARGE}
              text={t('Terms of Use')}
              weight={TEXT_WEIGHT.SEMI_BOLD}
              testID="terms-of-use-text"
              textDecoration={TEXTDECORATION.None}
              textAlign={TEXT_ALIGN.CENTER}
              accessible={false}
            />
            <ChevronRight
              color={theme.colors.pdsThemeColorForegroundNeutralHigh}
              size={theme.dimensions.pdsGlobalSizeHeight400}
            />
          </Pressable>

          <CDivider testID="legal-divider" />

          <Pressable
            ref={setRef('conditions-link')}
            style={[styles.cardHeading, { justifyContent: 'space-between' }]}
            onPress={() => handleNavigation(SCREENS.PRIVACY_POLICY, 'conditions-link')}
            accessible={true}
            accessibilityRole={ADA_ROLES.BUTTON}
            accessibilityLabel={t('privacyPolicy')}
          >
            <Text
              color={TEXT_PANTRY_COLORS.NEUTRAL_HIGH}
              size={TEXT_SIZE.LARGE}
              text={t('privacyPolicy')}
              weight={TEXT_WEIGHT.SEMI_BOLD}
              testID="privacy-policy-text"
              textDecoration={TEXTDECORATION.None}
              textAlign={TEXT_ALIGN.CENTER}
              accessible={false}
            />
            <ChevronRight
              color={theme.colors.pdsThemeColorForegroundNeutralHigh}
              size={theme.dimensions.pdsGlobalSizeHeight400}
            />
          </Pressable>
          <CDivider testID="legal-divider" />

          <Pressable
            ref={didGetCodeRef}
            style={[styles.cardHeading, { justifyContent: 'space-between' }]}
            onPress={() => {
              setIsDeleteDataWarningVisible(true);
            }}
            accessible={true}
            accessibilityRole={ADA_ROLES.BUTTON}
            accessibilityLabel={t('deletingTitle')}
          >
            <Text
              color={TEXT_PANTRY_COLORS.NEUTRAL_HIGH}
              size={TEXT_SIZE.LARGE}
              text={t('deletingTitle')}
              weight={TEXT_WEIGHT.SEMI_BOLD}
              testID="deleting-data-text"
              textDecoration={TEXTDECORATION.None}
              textAlign={TEXT_ALIGN.CENTER}
              accessible={false}
            />
            <ChevronRight
              color={theme.colors.pdsThemeColorForegroundNeutralHigh}
              size={theme.dimensions.pdsGlobalSizeHeight400}
            />
          </Pressable>
         
        </View>
        <Footer testID="footer" />
      </PageWrapper>
    </ScrollView>
     <BottomSheet
            sidePadding
            variant={'Modal'}
            theme={theme}
            testID="delete-info-bottom-sheet"
            visibility={isDeleteDataWarningVisible}
            closeAccessibilityLabel={t('ada.closeButton')}
            onClose={() =>  onCloseModal()}
            accessibilityViewIsModal={true}
            importantForAccessibility={ImportantForAccessibility.YES}
            renderHeader={<InfoIcon size={theme.dimensions.pdsGlobalSizeHeight800} />}
            renderContent={
              <View style={styles.descriptionContainer}>
                <Heading
                  textAlign={TEXT_ALIGN.CENTER}
                  title={t('deleteDisclosureSheet.title')}
                  accessibilityLabel={t('deleteDisclosureSheet.title')}
                  color={TEXTLINK_COLORS.NEUTRAL_HIGH}
                  size={HEADING_SIZE.Small}
                  weight={TEXT_WEIGHT.SEMI_BOLD}
                />
                <Text
                  text={t('deleteDisclosureSheet.description1')}
                  size={TEXT_SIZE.LARGE}
                  textAlign={TEXT_ALIGN.CENTER}
                   weight={TEXT_WEIGHT.REGULAR}
                  color={TEXTLINK_COLORS.NEUTRAL_HIGH}
                />
                <View>
                <Heading
                  textAlign={TEXT_ALIGN.CENTER}
                  title={t('deleteDisclosureSheet.subTitle1')}
                  accessibilityLabel={t('deleteDisclosureSheet.subTitle1')}
                  color={TEXTLINK_COLORS.NEUTRAL_HIGH}
                  size={HEADING_SIZE.XSmall}
                  weight={TEXT_WEIGHT.SEMI_BOLD}
                />
                 <Text
                  text={t('deleteDisclosureSheet.description2')}
                  size={TEXT_SIZE.LARGE}
                  textAlign={TEXT_ALIGN.CENTER}
                   weight={TEXT_WEIGHT.REGULAR}
                  color={TEXTLINK_COLORS.NEUTRAL_HIGH}
                />
                </View>
                 <View>
                <Heading
                  textAlign={TEXT_ALIGN.CENTER}
                  title={t('deleteDisclosureSheet.subTitle2')}
                  accessibilityLabel={t('deleteDisclosureSheet.subTitle2')}
                  color={TEXTLINK_COLORS.NEUTRAL_HIGH}
                  size={HEADING_SIZE.XSmall}
                  weight={TEXT_WEIGHT.SEMI_BOLD}
                />
                  <Text
                  text={t('deleteDisclosureSheet.description3')}
                  size={TEXT_SIZE.LARGE}
                  textAlign={TEXT_ALIGN.CENTER}
                  weight={TEXT_WEIGHT.REGULAR}
                  color={TEXTLINK_COLORS.NEUTRAL_HIGH}
                />
                </View>
              </View>
            }
            renderAction={
              <View style={[styles.buttonView, { paddingBottom: insets.bottom }]}>
                <Button
                  fullWidth
                  theme={theme}
                  size={BUTTON_SIZE.LARGE}
                  label={t('deleteDisclosureSheet.closeCTA')}
                  accessibilityHint={''}
                  accessible
                  accessibilityRole="none"
                  accessibilityLabel={t('ada.closeButton')}
                  onPress={() => onCloseModal()}
                />
              </View>
            }
          />
    </>
  );
};

export default SettingScreen;

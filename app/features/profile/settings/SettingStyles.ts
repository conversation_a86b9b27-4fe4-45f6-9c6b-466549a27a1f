import { Theme } from 'pantry-design-system';
import { StyleSheet } from "react-native";
import { IPAD_WIDTH } from '../../../shared/constants';

//customising styles based on themes
const GetStyles = ({
  colors,
  fonts,
  borderDimens,
  dimensions,
  typography,
}: Theme) => {
  const styels = StyleSheet.create({
    container: {
      alignSelf: 'center',
      flex: 1,
      maxWidth: IPAD_WIDTH,
      width: '100%',
    },
    noStyle: {
    },
    card: {
      backgroundColor: colors.pdsThemeColorBackgroundBaseline,
      borderColor: colors.pdsThemeColorOutlineNeutralLow,
      borderWidth: borderDimens.pdsGlobalBorderWidth100,
      borderRadius: borderDimens.pdsGlobalBorderRadius200,
      padding: dimensions.pdsGlobalSpace400,
      rowGap: dimensions.pdsGlobalSpace400,
    },
    legalCard: {
      padding: dimensions.pdsGlobalSpace400,
      borderRadius: borderDimens.pdsGlobalBorderRadius200,
      rowGap: dimensions.pdsGlobalSpace300,
    },
    icon: {
      fontSize: typography.pdsGlobalFontSize500,
      color: colors.pdsThemeColorBackgroundPrimary,
    },
    cardHeading: {
      flexDirection: "row",
      alignItems: "flex-start",
      columnGap: dimensions.pdsGlobalSpace200,
    },
    feedbackCard: {
      backgroundColor: colors.pdsThemeColorBackgroundBaseline,
      borderColor: colors.pdsThemeColorOutlineNeutralLow,
      borderWidth: borderDimens.pdsGlobalBorderWidth100,
      borderRadius: borderDimens.pdsGlobalBorderRadius200,
      padding: dimensions.pdsGlobalSpace400,
      rowGap: dimensions.pdsGlobalSpace200,
    },
    feedbackHeading: {
      flexDirection: "row",
      width: "100%",
      justifyContent: "space-between",
      alignItems: "flex-start",
    },
    s_between: {
      flexDirection: "row",
      alignItems: "center",
      columnGap: dimensions.pdsGlobalSpace200,
      justifyContent: "space-between",
    },

    text1: {
      color: colors.pdsThemeColorForegroundNeutralHigh,
      fontWeight: "500",
      fontSize: typography.pdsGlobalFontSize400,
      fontFamily: fonts.pdsGlobalFontFamilyPoppins,
    },
    text2: {
      color: colors.pdsThemeColorForegroundNeutralHigh,
      fontWeight: typography.pdsGlobalFontWeight600,
      fontSize: typography.pdsGlobalFontSize300,
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
    },

    text3: {
      color: colors.pdsThemeColorForegroundNeutralHigh,
      fontWeight: typography.pdsGlobalFontWeight400,
      fontSize: typography.pdsGlobalFontSize100,
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
      lineHeight: 17.4,
    },

    linkText: {
      color: colors.pdsThemeColorForegroundPrimary,
      fontWeight: typography.pdsGlobalFontWeight600,
      fontSize: typography.pdsGlobalFontSize100,
      textDecorationLine: "underline",
    },

    signOutBtn: {
      borderColor: colors.pdsThemeColorOutlinePrimary,
      borderStyle: "solid",
      borderWidth: borderDimens.pdsGlobalBorderWidth100,
      minWidth: 154,
      minHeight: 40,
      paddingVertical: 0,
      borderRadius: borderDimens.pdsGlobalBorderRadius1000,
    },
    btnLabel: {
      color: colors.pdsThemeColorForegroundPrimary,
      fontWeight: typography.pdsGlobalFontWeight600,
      fontSize: typography.pdsGlobalFontSize300,
      paddingVertical: 0,
      textTransform: "none",
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
    },
    btnLabel1: {
      width: "100%",
      fontWeight: typography.pdsGlobalFontWeight600,
      fontSize: typography.pdsGlobalFontSize300,
      color: colors.pdsThemeColorForegroundOnPrimary,
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
      paddingVertical: 0,
    },
    updateBtn: {
      backgroundColor: colors.pdsThemeColorBackgroundPrimary,
      height: 40,
      width: 30,
      borderRadius: borderDimens.pdsGlobalBorderRadius1000,
      justifyContent: "center",
      alignItems: "center",
    },
    modalTitle: {
      color: colors.pdsThemeColorForegroundNeutralHigh,
      fontWeight: typography.pdsGlobalFontWeight500,
      fontSize: typography.pdsGlobalFontSize400,
      fontFamily: fonts.pdsGlobalFontFamilyPoppins,
      textAlign: "center",
      lineHeight: 23.4
    },
    tabletModalTitle: {
      color: colors.pdsThemeColorForegroundNeutralHigh,
      fontWeight: typography.pdsGlobalFontWeight500,
      fontSize: typography.pdsGlobalFontSize700,
      fontFamily: fonts.pdsGlobalFontFamilyPoppins,
      textAlign: "center",
      justifyContent: "center",
      alignItems: "center",
    },
    modalContent: { padding: 30, marginTop: 10 },
    tabletModalDesc: {
      textAlign: "center",
      justifyContent: "center",
      alignItems: "center",
      margin: 30
    },
    modalDesc: {
      color: colors.pdsThemeColorForegroundNeutralHigh,
      fontWeight: typography.pdsGlobalFontWeight400,
      fontSize: typography.pdsGlobalFontSize400,
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
      lineHeight: 23,
    },
    iconStyles: {
      tintColor: colors.pdsThemeColorForegroundNeutralHigh,
      margin: 15,
      alignSelf: "center"
    },
    buttonRow: {
      marginTop: 25,
      flexDirection: "row",
      justifyContent: "space-between",
      width: "100%",
    },
    cancelButton: {
      flex: 1,
      backgroundColor: colors.pdsGlobalColorGeneralWhiteA100,
      borderWidth: 1,
      borderColor: colors.pdsThemeColorBackgroundPrimary,
      borderRadius: 20,
      paddingVertical: 10,
      marginRight: 10,
      alignItems: "center",
    },
    cancelButtonText: {
      color: colors.pdsThemeColorBackgroundPrimary,
      fontWeight: typography.pdsGlobalFontWeight500,
      fontSize: typography.pdsGlobalFontSize400
    },
    updateButton: {
      flex: 1,
      backgroundColor: colors.pdsThemeColorBackgroundPrimary,
      borderRadius: 20,
      paddingVertical: 10,
      marginLeft: 10,
      alignItems: "center",
    },
    updateButtonText: {
      color: colors.pdsGlobalColorGeneralWhiteA100,
      fontWeight: typography.pdsGlobalFontWeight500,
      fontSize: typography.pdsGlobalFontSize400
    },
     descriptionContainer: {
      alignItems: 'center',
      gap: dimensions.pdsGlobalSpace600,
      justifyContent: 'center',
      marginHorizontal: dimensions.pdsGlobalSpace600,
      marginTop: dimensions.pdsGlobalSpace200,
    },
     buttonView: {
      gap: dimensions.pdsGlobalSpace500,
      marginBottom: dimensions.pdsGlobalSpace800,
      marginTop: dimensions.pdsGlobalSpace700,
      paddingHorizontal: dimensions.pdsGlobalSpace400,
    },
  });

  return styels;
};

export default GetStyles;
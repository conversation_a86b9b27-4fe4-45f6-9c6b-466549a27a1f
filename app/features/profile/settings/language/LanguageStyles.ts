import { StyleSheet } from "react-native";
import { Theme } from 'pantry-design-system';

//customising styles based on themes
const GetStyles = ({
  colors,
  borderDimens,
  dimensions,
  fonts,
  typography,
}: Theme) => {
  const styels = StyleSheet.create({
    card1: {
      backgroundColor: colors.pdsThemeColorBackgroundBaseline,
      borderColor: colors.pdsThemeColorOutlineNeutralLow,
      borderWidth: borderDimens.pdsGlobalBorderWidth100,
      borderRadius: borderDimens.pdsGlobalBorderRadius200,
      padding: dimensions.pdsGlobalSpace400,
      rowGap: dimensions.pdsGlobalSpace400,
    },
    container: {
      flex: 1,
      padding: dimensions.pdsGlobalSpace400,
      justifyContent: 'space-between'
    },
    items_start: {
      flexDirection: "row",
      alignItems: "flex-start",
      columnGap: dimensions.pdsGlobalSpace400,
      width: "auto",
    },
    items_center: {
      flexDirection: "row",
      alignItems: "center",
      alignSelf: "flex-start",
      columnGap: dimensions.pdsGlobalSpace100,
      width: "auto",
    },

    text1: {
      color: colors.pdsThemeColorForegroundNeutralHigh,
      fontWeight: "500",
      fontSize: typography.pdsGlobalFontSize300,
      fontFamily: fonts.pdsGlobalFontFamilyPoppins,
    },
    text2: {
      color: colors.pdsThemeColorForegroundNeutralHigh,
      fontWeight: "600",
      fontSize: typography.pdsGlobalFontSize200,
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
      // lineHeight: 20.3,
    },
    text3: {
      color: colors.pdsThemeColorForegroundNeutralHigh,
      fontWeight: "400",
      fontSize: typography.pdsGlobalFontSize200,
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
      lineHeight: 20.3,
    },

    updateBtn: {
      backgroundColor: colors.pdsThemeColorBackgroundPrimary,
      height: 40,
      borderRadius: borderDimens.pdsGlobalBorderRadius1000,
      justifyContent: "center",
      alignItems: "center",
    },
    tabletStyle_updateBtn: {
      backgroundColor: '#fff',
      height: 40,
      borderRadius: borderDimens.pdsGlobalBorderRadius1000,
      justifyContent: "center",
      alignItems: "center",
      width: 210,
      borderColor: '#E5E4E3',
      borderWidth: 1
    },
    btnLabel: {
      width: "100%",
      fontWeight: "600",
      fontSize: typography.pdsGlobalFontSize300,
      color: colors.pdsThemeColorForegroundOnPrimary,
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
      paddingVertical: 0,
    },
    tabletStyle_btnLabel: {
      width: "100%",
      fontWeight: "400",
      fontSize: typography.pdsGlobalFontSize300,
      color: colors.pdsThemeColorBackgroundPrimary,
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
      paddingVertical: 0,
    },

    modalCircle: {
      width: 40,
      height: 40,
      borderRadius: borderDimens.pdsGlobalBorderRadius1000,
      borderWidth: borderDimens.pdsGlobalBorderWidth300,
      borderColor: colors.pdsThemeColorForegroundPrimary,
      margin: "auto",
    },
    modalTitle: {
      color: colors.pdsThemeColorForegroundNeutralHigh,
      fontWeight: "500",
      fontSize: typography.pdsGlobalFontSize400,
      fontFamily: fonts.pdsGlobalFontFamilyPoppins,
      textAlign: "center",
      lineHeight: 23.4
    },
    modalDesc: {
      color: colors.pdsThemeColorForegroundNeutralHigh,
      fontWeight: typography.pdsGlobalFontWeight400,
      fontSize: typography.pdsGlobalFontSize300,
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
      textAlign: "center",
      lineHeight: 23
    },
    iconStyles: {
      width: dimensions.pdsGlobalSizeWidth400,
      height: dimensions.pdsGlobalSizeWidth400,
      tintColor: colors.pdsThemeColorForegroundNeutralHigh,
    },
    bottomSheetIconStyles: {
      alignSelf: "center",
      width: dimensions.pdsGlobalSizeWidth800,
      height: dimensions.pdsGlobalSizeWidth800,
      tintColor: colors.pdsThemeColorBackgroundPrimary,
      resizeMode: "cover"
    },
  });

  return styels;
};

export default GetStyles;

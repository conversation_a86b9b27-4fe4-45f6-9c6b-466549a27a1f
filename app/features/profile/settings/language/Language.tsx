import React, { useState } from "react";

import { useDispatch, useSelector } from "react-redux";
import {
  languagetypes,
  updateLanguage,
} from "../../../../store/reducers/selectLanguageSlice";
import { useTranslation } from "react-i18next";
import { useTheme, } from "pantry-design-system";
// import { GetStyles } from "./LanguageStyles";
import GetStyles from "./LanguageStyles";
import LanguageSelector from "./LanguageSelector";

const LanguageSelectPage = () => {
  const { t, i18n } = useTranslation();
  const { theme } = useTheme(); /* Fething themes from ThemeProvider */
  const styles = GetStyles(theme); /* Fetching styles based on theme */
  const dispatch = useDispatch();
  const currentLanguage = useSelector<
    { language: languagetypes },
    languagetypes
  >((state) => state.language); /* fetching current/activeted language */

  const [openModal, setOpenModal] =
    useState(false); /*open/close modal for language update confirmation */

  const [selectedLanguage, setSelectedLanguage] =
    useState<languagetypes>(
      currentLanguage
    ); /* language modification local state */

  /*open modal function for update langaue for  confirmation */

  const handleLanguageUpdate = (language: languagetypes) => {
    setSelectedLanguage(language);
    // setOpenModal(true);
  };

  return (
    <>
      <LanguageSelector onLanguageUpdate={handleLanguageUpdate} />
    </>
  );
};

export default LanguageSelectPage;

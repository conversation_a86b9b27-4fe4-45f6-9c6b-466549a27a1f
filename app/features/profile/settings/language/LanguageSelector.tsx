import React, { useState } from "react";
import { View, TouchableOpacity } from "react-native";
import { Button, RadioButton } from "react-native-paper";
import { languages, TEXT_ALIGN, TEXTLINK_COLORS, TEXTLINK_SIZE } from "../../../../shared/constants";
import { useDispatch, useSelector } from "react-redux";
import { languagetypes, updateLanguage } from "../../../../store/reducers/selectLanguageSlice";
import { useTranslation } from "react-i18next";
import { useTheme, Text } from "pantry-design-system";
import GetStyles from "./LanguageStyles";
import { fetchAuthorableContentRequest } from "../../../../../app/store/reducers/authorableContentSlice";

interface LanguageSelectorProps {
  onLanguageUpdate: (selectedLanguage: languagetypes) => void; // Callback for language update
}

const LanguageSelector: React.FC<LanguageSelectorProps> = ({ onLanguageUpdate }) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const styles = GetStyles(theme);
  const dispatch = useDispatch();
  const currentLanguage = useSelector<{ language: languagetypes }, languagetypes>(
    (state) => state.language
  );

  const [selectedLanguage, setSelectedLanguage] = useState<languagetypes>(currentLanguage);

  const isTablet = useSelector((state: any) => state.deviceInfo?.isTablet);

  const intializeLanguage = (lang: languagetypes) => {
    const language1 = languages.find((language) => language.id === lang.id) as languagetypes;
    setSelectedLanguage(language1);
  };

  const onChangeLanguage = (langObj: languagetypes) => {
    intializeLanguage(langObj);
  };

  const handleLanguageUpdate = () => {
    onLanguageUpdate(selectedLanguage); // Trigger the callback
    dispatch(updateLanguage(selectedLanguage));
    dispatch(fetchAuthorableContentRequest());
  };

  return (
    <View style={styles.container}>

      <View>
        {languages?.map((lang, index) => {
          const isSelected = selectedLanguage.id === lang.id;
          const totalLanguages = languages.length;
          const label = `${t(lang.label)}, ${isSelected ? `${t('ada.selected')}, ` : ""}${index + 1} of ${totalLanguages} button ${!isSelected ? `, ${t('ada.doubeTapToSelect')}` : ""}`;

          return (
            <TouchableOpacity
              key={lang.id}
              style={styles.items_center}
              onPress={() => onChangeLanguage(lang)}
              accessibilityRole="button" // Role for touchable elements
              accessible={true} // Makes the button accessible
              accessibilityLabel={label} // Describes the language option
            >
              <View style={{ transform: [{ scale: 1.05 }] }}>
                <RadioButton.Android
                  value={lang.value}
                  status={selectedLanguage.id === lang.id ? "checked" : "unchecked"}
                  uncheckedColor="grey"
                  color="black"
                  onPress={() => onChangeLanguage(lang)}
                  accessibilityRole="radio" // Role for the radio button
                  accessible={true} // Makes radio button accessible
                />
              </View>
              <Text
                textAlign={TEXT_ALIGN.CENTER}
                color={TEXTLINK_COLORS.NEUTRAL_HIGH}
                text={t(`${lang.label}`)}
                size={TEXTLINK_SIZE.LARGE}
              />
            </TouchableOpacity>
          )
        }
        )}
      </View>

      <View>
        <Button
          mode="contained"
          disabled={selectedLanguage.id === currentLanguage.id}
          onPress={handleLanguageUpdate}
          style={[isTablet ? styles.tabletStyle_updateBtn : styles.updateBtn, selectedLanguage.id === currentLanguage.id && { opacity: 0.5, backgroundColor: theme.colors.pdsThemeColorBackgroundDisabled }]}
          labelStyle={[isTablet ? styles.tabletStyle_btnLabel : styles.btnLabel]}
          accessibilityRole="button" // Button role\
          accessible={true} // Makes the button accessible
          accessibilityLabel={
            selectedLanguage.id === currentLanguage.id
              ? `${t("ada.saveBtn")} ${t('ada.unavailable')}`
              : `${t("ada.saveBtn")}`
          }
        >
          {t("save")}
        </Button>
      </View>
    </View>
  );
};

export default LanguageSelector;
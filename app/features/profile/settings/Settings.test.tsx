import React from "react";
import { render, fireEvent } from "@testing-library/react-native";
import SettingScreen from "./Setting";
import { Provider } from "react-redux";
import configureStore from "redux-mock-store";
import { useNavigation } from "@react-navigation/native";
import { act } from "react-test-renderer";
import { Alert } from "react-native";
import { checkNotifications } from 'react-native-permissions';

jest.mock('../../../providers/AccessibilityFocus', () => ({
    useAccessibilityFocus: () => ({
        setRef: jest.fn(),
        setLastFocused: jest.fn(),
        restoreLastFocus: jest.fn(),
        setNavigatingBack: jest.fn(),
    }),
    AccessibilityFocusProvider: ({ children }: { children: React.ReactNode }) => children,
}));


jest.mock('@react-native-firebase/app', () => ({
    getApp: jest.fn(() => ({
        name: '[DEFAULT]',
    })),
}));

jest.mock('react-native-permissions', () => ({
    checkNotifications: jest.fn(),
    RESULTS: {},
    openSettings: jest.fn(() => Promise.resolve()),
}));

jest.mock('../../../../app/store', () => ({
    store: {
        getState: jest.fn(() => ({
            profile: {
                profile: { id: 'mock-user-id' },
            },
        })),
    },
}));

jest.mock('@react-native-firebase/analytics', () => {
    return jest.fn(() => ({
        logEvent: jest.fn(),
        logScreenView: jest.fn(),
        setUserId: jest.fn(),
        setUserProperties: jest.fn(),
    }));
});
jest.mock("pantry-design-system", () => {
  const actual = jest.requireActual("pantry-design-system");
  return {
    ...actual,
    BottomSheet: jest.fn(({ children, ...props }) => <>{children}</>),
    // ...other mocks as needed
  };
});
jest.mock("react-native-svg", () => ({
    SvgXml: jest.fn(({ xml, testID }) => <svg testID={testID}>{xml}</svg>),
}));

jest.mock("./language/LanguageSelector", () => "LanguageSelector");
jest.mock("@/components/divider/CDivider", () => "CDivider");

jest.mock("../../../../components/SwitchToggle/SwitchToggle", () => {
    const React = require("react");
    const { Text } = require("react-native");

    return ({ value, onValueChange, testID }: any) => (
        <Text
            testID={testID}
            accessibilityLabel={`value-${value}`} // Custom value for assertion
            onPress={() => onValueChange(!value)}
        >
            {`Toggle: ${value ? "On" : "Off"}`}
        </Text>
    );
});
jest.mock("../../../../components/modal/Modal", () => "CModal");
jest.mock("../../../../components/footer/Footer", () => "Footer");
jest.mock("react-native-vector-icons/MaterialIcons", () => "MaterialIcons");
jest.mock(
    "react-native-vector-icons/MaterialCommunityIcons",
    () => "MaterialCommunityIcons"
);
jest.mock("react-native-vector-icons/SimpleLineIcons", () => "SimpleLineIcons");

jest.mock("@react-navigation/native", () => ({
    useNavigation: jest.fn(),
    CommonActions: {
        navigate: jest.fn()
    },
    useFocusEffect: jest.fn((effect) => {
        effect(); // Just call the effect once
    }),
    useRoute: jest.fn(() => ({
        params: {},
    })),
}));

jest.mock("react-native-app-auth", () => ({
    authorize: jest.fn(() => Promise.resolve({ accessToken: "mockAccessToken" })),
    revoke: jest.fn(() => Promise.resolve()),
}));

jest.mock('@appdynamics/react-native-agent', () => ({
    Instrumentation: {
        start: jest.fn(),
        stop: jest.fn(),
    },
    LoggingLevel: {},
    ErrorSeverityLevel: {},
    BreadcrumbVisibility: {},
}));

jest.spyOn(Alert, "alert").mockImplementation(() => { }); // Prevent Alert interference

const mockStore = configureStore([]);

describe("SettingScreen", () => {
    let navigationMock: any;
    let mockAddLiistner = jest.fn();
    let store: any;

    beforeEach(() => {
        navigationMock = {
            navigate: jest.fn(),
            dispatch: jest.fn(),
            addListener: mockAddLiistner,
            setParams: jest.fn(),

        };
        useNavigation.mockReturnValue(navigationMock);
        store = mockStore({
            language: { id: "en", label: "English" },
            deviceInfo: { isTablet: false, isLandscape: false },
            locationAccess: { isPrecise: false },
        });

        // ✅ Mock checkNotifications to always return granted
        (checkNotifications as jest.Mock).mockResolvedValue({ status: 'granted' });

        jest.clearAllMocks();
    });

    it("should render the main components", () => {
        const { getByTestId } = render(
            <Provider store={store}>
                <SettingScreen />
            </Provider>
        );

        expect(getByTestId("settings-scrollview")).toBeTruthy();
        expect(getByTestId("location-card")).toBeTruthy();
        expect(getByTestId("legal-card")).toBeTruthy();
        expect(getByTestId("footer")).toBeTruthy();
    });

    it("should toggle the location switch on and call openSettings", async () => {
        const { getByTestId } = render(
            <Provider store={store}>
                <SettingScreen />
            </Provider>
        );

        const toggleSwitch = getByTestId("location-toggle");
        expect(toggleSwitch.props.children).toBe("Toggle: Off");

        await act(async () => {
            fireEvent(toggleSwitch, 'onPress'); // Simulate the switch toggling on
        });

        // Verify that openSettings was called and the switch state is updated
        expect(require("react-native-permissions").openSettings).toHaveBeenCalled();
        expect(getByTestId("location-toggle").props.children).toBe("Toggle: Off");
    });

    it("should toggle the location switch off and call openSettings", async () => {
        const { getByTestId, rerender } = render(
            <Provider store={store}>
                <SettingScreen />
            </Provider>
        );

        const toggleSwitch = getByTestId("location-toggle");

        // Initially check if the switch is off
        expect(toggleSwitch.props.children).toBe("Toggle: Off");

        await act(async () => {
            fireEvent(toggleSwitch, 'onPress'); // Simulate the switch toggling on
        });

        // Update the mock store to reflect the new state (simulating switch off)
        store = mockStore({
            language: { id: "en", label: "English" },
            deviceInfo: { isTablet: false, isLandscape: false },
            locationAccess: { isPrecise: false }, // Simulate precise location not granted
        });

        // Re-render the component with the updated store
        rerender(
            <Provider store={store}>
                <SettingScreen />
            </Provider>
        );

        // Assert that the switch is toggled off
        expect(getByTestId("location-toggle").props.children).toBe("Toggle: Off");
    });

    it("should navigate to App Feedback on press", () => {
        const { getByTestId } = render(
            <Provider store={store}>
                <SettingScreen />
            </Provider>
        );

        const feedbackCard = getByTestId("feedback-card");
        expect(feedbackCard).toBeTruthy();

        fireEvent.press(feedbackCard);

        expect(navigationMock.navigate).toHaveBeenCalledWith("WebViewScreen", {
            url: "https://forms.office.com/r/VspEmqLFWW",
            titleHeader: "feedback",
            previousTab: 'profile',
        });
    });
    it("should navigate to Terms of Use on press", () => {
        const { getByTestId } = render(
            <Provider store={store}>
                <SettingScreen />
            </Provider>
        );

        const termsOfUsePressable = getByTestId("terms-of-use-text");

        fireEvent.press(termsOfUsePressable);

        expect(navigationMock.navigate).toHaveBeenCalledWith("WebViewScreen", {
            url: "https://www-qa2.albertsons.com/content/dam/data/associateapp/settings/Associate_App_TOU_V1.pdf",
            titleHeader: "Terms of Use",
            previousTab: 'profile',
            isOmniheaderPresent: false,
            isHeaderRight: false,
        });
    });

    it("should navigate to Privacy Policy on press", () => {
        const { getByTestId } = render(
            <Provider store={store}>
                <SettingScreen />
            </Provider>
        );

        const privacyPolicyPressable = getByTestId("privacy-policy-text");

        fireEvent.press(privacyPolicyPressable);

        // Update the expected navigation parameters based on the component logic
        expect(navigationMock.navigate).toHaveBeenCalledWith(
            "Privacy Policy",
            expect.any(Object)
        );
    });

    it("should handle landscape layout for tablets", () => {
        store = mockStore({
            language: { id: "en", label: "English" },
            deviceInfo: { isTablet: true, isLandscape: true },
            locationAccess: { isPrecise: false },
        });

        const { getByTestId } = render(
            <Provider store={store}>
                <SettingScreen />
            </Provider>
        );

        expect(getByTestId("settings-scrollview")).toBeTruthy(); // Ensure layout adapts for tablets
    });

    it("should handle notification settings", () => {
        const { getByTestId } = render(
            <Provider store={store}>
                <SettingScreen />
            </Provider>
        );
    });

    it("should handle legal and about section", () => {
        const { getByTestId } = render(
            <Provider store={store}>
                <SettingScreen />
            </Provider>
        );

        expect(getByTestId("legal-card")).toBeTruthy();
    });

    jest.spyOn(console, "error").mockImplementation((message) => {
        if (message.includes("An update to SettingScreen inside a test was not wrapped in act")) {
            return; // Ignore this specific warning
        }
    });
});


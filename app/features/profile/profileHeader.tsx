import FastImage from '@d11/react-native-fast-image';
import { useNavigation } from '@react-navigation/native';
import { useTheme, TextLink, Heading } from 'pantry-design-system';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Image, Text, View, TouchableOpacity, ActivityIndicator, Linking } from 'react-native';
import { SvgXml } from 'react-native-svg';
import { useSelector } from 'react-redux';

import images from '../../../assets/images';
import {
  disabledLoyaltyAccountImage,
  loyaltyAccountImage,
} from '../../../assets/images/svg/LoyaltyAccount';
import { ErrorStoreCodes } from '../../config/errorCodes';
import {
  ProfileConstants,
  TEXTLINK_COLORS,
  TEXTLINK_SIZE,
  TEXTLINK_WEIGHT,
  BANNER_THEMES,
  URLS,
  SCREENS,
  FALLBACK_BANNER,
} from '../../shared/constants';
import { capitalizeFirstLetter, getFirstWordOfUserName } from '../../utils/helpers';

import getStyles from './styles';

import type { Name } from '../../misc/models/ProfileModal';

/**
 * Props for the ProfileHeader component.
 */

/**
 *  Primary profile pic Display:
 *    - Shows the user's photo from `photoData.Photo` if available (base64 encoded webp image)
 *    - Displays a loading indicator while the photo is being fetched
 *    - If the photo fails to load, it falls back to a circular placeholder with the user's initials.
 *    - Used FastImage for better performance and caching.
 *    - Used FasdImage OnError prop to handle image loading failure.
 *
 *  Fallback Display:
 *    - If no photo is available (empty or undefined photoData.Photo):
 *      a) Renders a circular placeholder with  banner's background
 *      b) Displays the capitalized first letter of the user's firstName.
 */
interface ProfileHeaderProps {
  pictureUrl?: string; // URL for the profile picture
  initials: string; // Initials of the user
  userName: Name | null; // user name object of the user
  points: number | string; // Reward points of the user
  employeeId: string; // Employee ID of the user
  isAccountLinked: boolean; // Indicates if the uca account is linked
  isAccountDisabled?: boolean; // Indicates if the uca account is disabled till 48 hours
  banner: string; // Banner text for linking accounts
}
const { EMP_NO, EMP_POINTS } = ProfileConstants;
const ProfileHeader: React.FC<ProfileHeaderProps> = ({
  userName,
  points,
  employeeId,
  pictureUrl = '',
  isAccountLinked,
  isAccountDisabled = false,
  banner,
}) => {
  const { theme } = useTheme(); // Fething themes from ThemeProvider
  const styles = getStyles(theme); // Fetching styles based on theme
  const navigation = useNavigation(); // Navigation hook
  const { t } = useTranslation(); // Access the translation function for internationalization
  const [failToLoadImage, setFailToLoadImage] = useState(false);
  const photoDataLoading = useSelector((state: any) => state.profilePhoto.loading); // Loading state for profile photo
  const photoData = useSelector((state: any) => state.profilePhoto.photoUrl); // Profile photo data
  const scorecardState = useSelector((state: any) => state.rewardsScorecard); // Rewards scorecard state
  const bannerName = capitalizeFirstLetter(banner ? banner : FALLBACK_BANNER);

  /**
   * Selects the value of the 'diaaLoyaltyLinkForU' feature flag from the Redux store.
   *
   * This hook checks whether the specific feature flag is present in the `featureFlags.flags` array,
   * and returns its `featureFlagValue`. If the flag is not found, it defaults to `false`.
   *
   * @returns {boolean} - `true` if the 'diaaLoyaltyLinkForU' flag is enabled, otherwise `false`.
   */
  const diaaLoyaltyLinkForU = useSelector((state: any) => {
    const flag = state.featureFlags.flags.find(
      (f: any) => f.featureFlagName === 'diaaLoyaltyLinkForU',
    );
    return flag?.featureFlagValue ?? false;
  });
  /**
   * Handles the redirection to the rewards page using a deep link URL.
   * Constructs the final deep link URL by replacing the placeholder `{banner}`
   * in the base URL with the provided `banner` value, and opens the URL.
   * if the banner is not provided, it defaults to the use from constant file within the BANNER_THEMES first index of that array value.
   */
  const handleRewardPoint = (): void => {
    const finalDeepLinkUrl = URLS.DEEP_LINK_UMA_REWARDS?.replaceAll(
      '{banner}',
      banner ? banner : BANNER_THEMES[1].banner,
    );
    Linking.openURL(finalDeepLinkUrl as string);
  };

  const getFirstLetterOfName = (): string | undefined => {
    // Retrieves the first letter of the user's first name
    return userName?.firstName?.charAt(0).toUpperCase();
  };

  /**
   * Retrieves the first word of the user's first name, checking `preferredName` first
   * and falling back to `firstName` if `preferredName` does not exist.
   */
  const getUserName = () => {
    return getFirstWordOfUserName(userName);
  };

  return (
    <View style={styles.profileCardContainer}>
      <View style={styles.profileHeaderCard}>
        {/* Profile Image Section */}
        <View style={styles.imageContainer}>
          {photoDataLoading && (
            <ActivityIndicator
              size="small"
              color={theme.colors.pdsThemeColorForegroundNeutralHigh}
              style={styles.loadingStyles}
            />
          )}
          {photoData && photoData.length > 0 && !failToLoadImage ? (
            <FastImage
              source={{ uri: photoData }}
              style={styles.profileImage}
              onError={() => setFailToLoadImage(true)}
              accessible={true}
              accessibilityHint=""
              accessibilityLabel={t('ada.imageAlt') + getUserName()}
            />
          ) : (
            <View style={[styles.profileImage, styles.defaultProfileImage]}>
              <Heading
                accessible={false}
                testID="profile-photo-initials"
                textAlign="center"
                title={getFirstLetterOfName()}
                color={TEXTLINK_COLORS.NEUTRAL_HIGH_INVERSE}
                size={TEXTLINK_SIZE.LARGE}
              />
            </View>
          )}
        </View>
        <View style={styles.profileInfo}>
          <Text
            style={styles.profileName}
            accessibilityRole="header"
            accessibilityHint=""
            accessibilityLabel={getUserName()}
          >
            {getUserName()}
          </Text>
          <Text
            style={styles.employeeId}
            accessibilityLabel={t('profile.employeeId', { id: employeeId })}
          >
            {t('employeeNumber')}
            {employeeId}
          </Text>

          {/* Account Linking or Rewards Section */}
          {diaaLoyaltyLinkForU &&
            (isAccountLinked ? (
              isAccountDisabled ? (
                <View style={styles.linkContainer}>
                  <SvgXml
                    xml={disabledLoyaltyAccountImage}
                    accessible={false}
                    style={styles.linkImageContainer}
                    testID="all-done-forU-image"
                  />
                  <TextLink
                    accessible
                    color={TEXTLINK_COLORS.NEUTRAL_MEDIUM}
                    text={t('profile.linkingYourAccount')}
                    weight={TEXTLINK_WEIGHT.SEMI_BOLD}
                    size={TEXTLINK_SIZE.LARGE}
                    onPress={() => {
                      navigation.navigate(SCREENS.ACCOUNT_LINK_STACK); // Navigate to account linking screen
                    }}
                  />
                </View>
              ) : (
                <TouchableOpacity
                  style={styles.pointsContainer}
                  accessible={true}
                  accessibilityHint=""
                  accessibilityRole="button"
                  accessibilityLabel={t('profile.rewardsButton', { points: points | '0' })}
                  onPress={handleRewardPoint}
                >
                  <Image
                    testID="profile-header-points-image"
                    source={images.POINTS}
                    style={styles.pointsImage}
                    accessibilityIgnoresInvertColors
                  />
                  <Text style={styles.pointsText}>
                    {scorecardState?.data?.errors?.[0]?.code === ErrorStoreCodes.noStoresFound
                      ? EMP_POINTS
                      : points
                        ? `${points} ${EMP_POINTS}`
                        : EMP_POINTS}
                  </Text>
                </TouchableOpacity>
              )
            ) : (
              <View style={styles.linkContainer}>
                <SvgXml
                  xml={loyaltyAccountImage}
                  accessible={false}
                  style={styles.linkImageContainer}
                  testID="all-done-forU-image"
                />
                <TextLink
                  accessible
                  color={TEXTLINK_COLORS.NEUTRAL_HIGH}
                  text={t(`profile.linkBannerForU`).replace('[banner]', bannerName)}
                  weight={TEXTLINK_WEIGHT.SEMI_BOLD}
                  size={TEXTLINK_SIZE.LARGE}
                  accessibilityLabel={t('ada.linkBannerForU').replace('[banner]', bannerName)}
                  onPress={() => {
                    navigation.navigate(SCREENS.ACCOUNT_LINK_STACK); // Navigate to account linking screen
                  }}
                />
              </View>
            ))}
        </View>
      </View>
    </View>
  );
};

export default ProfileHeader;

import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { useTheme, TextLink } from 'pantry-design-system';
import React, { useEffect, useState, useCallback, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { View, Alert, ScrollView, RefreshControl, AppState } from 'react-native';
import { SvgXml } from 'react-native-svg';
import { useDispatch, useSelector } from 'react-redux';

import ABAuditEngine from '../../../analytics/ABAuditEngine';
import { PROFILE_ANALYTICS, BUTTON_NAV_ANALYTICS } from '../../../analytics/AnalyticsConstants';
import { screenViewLog, userActionLogEvent } from '../../../analytics/AnalyticsUtils';
import { APPD_PAGE_VIEW_METRIC_CONSTANTS } from '../../../analytics/AppDynamicsConstants';
import { ProfileBackground } from '../../../assets/images/svg/ProfileBackground';
import { user, wallet, settings, help } from '../../../assets/images/svg/profileIcons';
import { DURATION, xAPI_PROFILE_PHOTO } from '../../config/endPoints';
import {
  ASSOCIATE_PROFILE,
  SCREENS,
  STAR_BRAND_THEME,
  ProfileConstants,
  STICKY_DAYS,
  TokenTypes,
  URLS,
} from '../../shared/constants';
import * as AsyncStorage from '../../store/AsyncStorage';
import { fetchProfilePhotoRequest } from '../../store/reducers/profilePhotoSlice';
import {
  fetchProfileRequest,
  setLoggedIn,
  setProfileCelebrationCards,
} from '../../store/reducers/profileSlice';
import { fetchRewardsScorecardRequest } from '../../store/reducers/scorecardSlice';
import { SecureStorage } from '../../store/SecureStorage';
import shouldApiTriggerCall from '../../utils/LocalStorageUtil';
import { getHouseholdIdFromProfile } from '../../utils/setAndGetHHID';
import CelebrationCarousel from '../celebration';

import ProfileHeader from './profileHeader';
import Section from './section';
import getStyles from './styles';

import type { CelebrationCardProps } from '../../../components/CelebrationCard';
import type { AssociateProfile, Name } from '../../misc/models/ProfileModal';
import type { TokenType } from '../../store/SecureStorage';

interface ProfileInterface {
  profile: AssociateProfile;
  loading: boolean;
  loggedIn: boolean;
  banner: string;
  profileCelebrationCards: CelebrationCardProps[];
  error: string | null;
}

import FullscreenConfetti from '../celebration/fullScreenConfetti';
import { generateCelebrationCards } from '../../utils/dismissCelebrations';
import { checkAllCelebrations } from '../../utils/checkAllCelebrations';
import { useNetworkAlert } from '../../hooks/useNetworkAlert';
import { useAccessibilityFocus } from '../../providers/AccessibilityFocus';
import { CELEBRATION_ANALYTICS } from '../../../analytics/AnalyticsConstants';

const ProfileScreen: React.FC = () => {
  const navigate = useNavigation();
  const { t } = useTranslation();

  const dispatch = useDispatch();

  const isTablet = useSelector((state: any) => state.deviceInfo?.isTablet);
  const scorecardState = useSelector((state: any) => state.rewardsScorecard);
  const { profile, loading, banner, profileCelebrationCards, error, loggedIn }: ProfileInterface =
    useSelector((state: any) => state.profile);
  const photoDataLoading = useSelector((state: any) => state.profilePhoto.loading);

  const secureStorage = new SecureStorage();

  const [refreshing, setRefreshing] = useState(false);

  const navigation = useNavigation(); // Navigation hook
  const { theme } = useTheme(); // Theme hook
  const styles = getStyles(theme); // Get styles based on theme

  const startTimeRef = useRef<number | null>(null);

  const [householdId, setHouseholdId] = useState<string | null>(null);

  const primaryColor = theme.colors.pdsThemeColorBackgroundPrimary; // Primary color from theme
  const appState = useRef(AppState.currentState);

  const { setLastFocused, setRef, handleScreenFocus } = useAccessibilityFocus();
  const { showAlertWithRetry, checkInternetConnection } = useNetworkAlert();

  useEffect(() => {
    const unsubscribeFocus = navigation.addListener('focus', () => {
      handleScreenFocus(SCREENS.PROFILE_SCREEN);
    });

    return () => {
      unsubscribeFocus?.();
    };
  }, [navigation]);

  const fetchProfileData = async () => {
    const secureStorage = new SecureStorage();
    const requestId = await AsyncStorage.uniqueSessionId();
    const empId = await secureStorage.getToken(TokenTypes.employeeId as TokenType);

    dispatch(fetchProfileRequest({ requestId, empId, fl: ASSOCIATE_PROFILE }));
  };
  /**
   * Verifies if the profile photo API should be triggered based on the last call timestamp.
   * If the condition is met, it fetches the user's profile photo.
   * This function uses a utility function `shouldApiTriggerCall` to determine if the API call should be made.
   */
  const verifyAndTriggerProfilePhoto = async () => {
    shouldApiTriggerCall(xAPI_PROFILE_PHOTO, DURATION[xAPI_PROFILE_PHOTO])
      .then((shouldCallApi) => {
        if (shouldCallApi) {
          fetchUserPhoto().catch((error) => {
            console.error('Error fetching user photo:', error);
          });
        }
      })
      .catch((error) => {
        // Error
      });
  };
  /**
   * Fetches the user's profile photo and dispatches a request to update the profile photo state.
   * This function retrieves the employee ID from secure storage and uses it to make a request
   * for the profile photo, which is then stored in the Redux state.
   * @returns {Promise<void>} A promise that resolves when the photo fetch request is dispatched.
   * @throws {Error} If there is an error during the photo fetch request.
   */
  const fetchUserPhoto = async () => {
    const secureStorage = new SecureStorage();
    const requestId = await AsyncStorage.uniqueSessionId();
    const empId = await secureStorage.getToken(TokenTypes.employeeId as TokenType);

    dispatch(fetchProfilePhotoRequest({ requestId, empId, banner: profile?.banner }));
  };

  const onPressFeedback = (reference: any) => {
    setLastFocused(SCREENS.PROFILE_SCREEN, reference);
    navigation.navigate(SCREENS.WEBVIEW, {
      url: URLS.FEEDBACK,
      titleHeader: t('feedback'),
      previousTab: SCREENS.PROFILE,
      subsection: {
        subsection1: BUTTON_NAV_ANALYTICS.PROFILE_TAB_ACTION,
        subsection2: PROFILE_ANALYTICS.APP_FEEDBACK,
        eventCategory: BUTTON_NAV_ANALYTICS.PROFILE_TAB_ACTION,
        eventLabel: PROFILE_ANALYTICS.APP_FEEDBACK,
        refreshCategory: PROFILE_ANALYTICS.PROFILE,
        refreshLabel: PROFILE_ANALYTICS.APP_FEEDBACK,
        refreshAction: PROFILE_ANALYTICS.APP_FEEDBACK_REFRESH_ACTION,
      },
    });
  };
  /**
   * This hook is triggered when the screen gains focus. It performs the following actions:
   * 1. Starts a custom timer for analytics tracking using `ABAuditEngine`.
   * 2. Loads the household ID associated with the user's profile.
   * 3. Dispatches a request to fetch the rewards scorecard if a valid household ID is found.
   * 4. Cleans up by resetting the `startTimeRef` when the screen loses focus.
   *
   * Dependencies:
   * - `profile`: The user's profile data used to fetch the household ID.
   *
   * @returns {void}
   */
  useFocusEffect(
    useCallback(() => {
      // checking existing celebrations
      const initCelebrations = async () => {
        try {
          const data = checkAllCelebrations(
            profile?.dateOfBirth,
            profile?.latestHireDate,
            STICKY_DAYS.BIRTHDAY,
            STICKY_DAYS.WORK_ANNIVERSARY,
          );

          const allCards = generateCelebrationCards(data);
          dispatch(setProfileCelebrationCards(allCards)); // profile celebration cards
        } catch (e) {
          // Fallback to empty arrays if error occurs
          dispatch(setProfileCelebrationCards([]));
        }
      };

      initCelebrations();

      screenViewLog({ subsection1: PROFILE_ANALYTICS.PROFILE_PAGE_TITLE });

      verifyAndTriggerProfilePhoto(); // Verify and trigger profile photo fetch

      // Start tracking the time when the screen gains focus
      startTimeRef.current = Date.now();
      ABAuditEngine.customTimerStart(APPD_PAGE_VIEW_METRIC_CONSTANTS.PROFILE_PAGE_VIEW_METRIC);

      // Function to load the household ID and fetch rewards scorecard
      const loadHousehold = async () => {
        const houseHoldId = await getHouseholdIdFromProfile(profile); // Fetch household ID from profile
        checkhouseholdIdAndDispatch();
      };

      // Call the function to load household data
      loadHousehold();

      // Cleanup function to reset the start time when the screen loses focus
      return () => {
        startTimeRef.current = null;
      };
    }, []), // Dependency array ensures the callback is re-created when `profile` changes
  );

  useEffect(() => {
    // Add an event listener to monitor changes in the app's state (foreground/background)
    const subscription = AppState.addEventListener('change', (nextAppState) => {
      // Check if the app is transitioning from inactive/background to active state
      if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
        screenViewLog({ subsection1: PROFILE_ANALYTICS.PROFILE_PAGE_TITLE });
      }
      // Update the current app state
      appState.current = nextAppState;
    });

    // Cleanup function to remove the event listener when the component unmounts
    return () => {
      subscription.remove();
    };
  }, []);

  // Effect to calculate and log the page load time when loading or refreshing is complete
  useEffect(() => {
    const isFullyLoaded = !loading && !photoDataLoading;

    if (isFullyLoaded && startTimeRef.current) {
      const duration = Date.now() - startTimeRef.current;
      console.log(`Profile Page Load Time: ${duration}ms`);
      ABAuditEngine.customTimerEnd(APPD_PAGE_VIEW_METRIC_CONSTANTS.PROFILE_PAGE_VIEW_METRIC);
      startTimeRef.current = null; // Clear timer only once
    }

    // Refreshing state should be turned off only after loading completes
    if (isFullyLoaded && refreshing) {
      setRefreshing(false);
    }
  }, [loading, photoDataLoading]);

  const balance = scorecardState?.data?.scorecards?.[0]?.points?.[0]?.value ?? 0;

  /**
   * Navigates to the specified path and logs the navigation event for analytics.
   *
   * @param {string} path - The navigation path or screen name to navigate to.
   * @param {string} page - The page or section name for analytics logging.
   * @param {any} reference - The accessibility reference to set as last focused for accessibility purposes.
   *
   * This function performs the following actions:
   * 1. Sets the last focused accessibility reference using setLastFocused.
   * 2. Logs a user action event for analytics using userActionLogEvent.
   * 3. Navigates to the specified path using the navigation object.
   */
  const navigateFn = (path: string, page: string, reference: any) => {
    setLastFocused(SCREENS.PROFILE_SCREEN, reference);
    navigate.navigate(path);
    userActionLogEvent(PROFILE_ANALYTICS.PROFILE, page, PROFILE_ANALYTICS.PROFILE);
  };

  const manageProfileAndPreferences = 'manageProfileAndPreferences';

  const handleSignout = () => {
    // Show a confirmation dialog for sign-out

    Alert.alert(
      t('signOut') + '!', // Alert title
      t('confirmSignOut'), // Alert message
      [
        {
          text: t('yes'), // Confirm button
          onPress: () => {
            handleLogout(); // Handle logout on confirmation
          },
          style: 'destructive', // Destructive button style
        },
        {
          text: t('no'), // Cancel button
          onPress: () => { }, // No action on cancel
          style: 'cancel', // Cancel button style
        },
      ],
      { cancelable: false }, // Make alert non-cancelable
    );
  };

  const handleLogout = () => {
    dispatch(setLoggedIn(false)); // Set logged-in state to false
    navigation.navigate(SCREENS.AUTH_LOGOUT);
  };

  const checkhouseholdIdAndDispatch = async () => {
    const houseHoldId = await getHouseholdIdFromProfile(profile); // Fetch household ID from profile
    setHouseholdId(houseHoldId); // Update the state with the household ID

    if (houseHoldId) {
      // Dispatch an action to fetch the rewards scorecard if household ID is valid
      dispatch(
        fetchRewardsScorecardRequest({
          body: {
            hhid: houseHoldId,
            programType: ['BASEPOINTS'], // Specify the program type
          },
        }),
      );
    }
  };

  const onRefresh = async () => {
    const isOnline = await checkInternetConnection();
    if (!isOnline) {
      await showAlertWithRetry(onRefresh);
      return;
    }
    startTimeRef.current = Date.now();
    ABAuditEngine.customTimerStart(APPD_PAGE_VIEW_METRIC_CONSTANTS.PROFILE_PAGE_VIEW_METRIC);

    setRefreshing(true);

    fetchProfileData();
    fetchUserPhoto();

    checkhouseholdIdAndDispatch();
  };

  return (
    <View testID="profile-screen" style={isTablet ? styles.tabMainContainer : styles.mainContainer}>
      <SvgXml
        xml={ProfileBackground(STAR_BRAND_THEME(banner, theme))}
        style={styles.backgoundImage}
        height={theme.dimensions.pdsGlobalSizeWidth2200}
      />

      <ScrollView
        contentContainerStyle={{ paddingBottom: theme.dimensions.pdsGlobalSpace800 }}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.pdsThemeColorForegroundPrimary]}
            tintColor={theme.colors.pdsThemeColorForegroundPrimary}
          />
        }
      >
        {profile?.names?.[0] && (
          <ProfileHeader
            pictureUrl={''} // TODO: Add profile picture URL from photo API
            initials={`${profile?.names[0]?.firstName?.charAt(0) ?? ''}${profile?.names?.[0]?.lastName?.charAt(0) ?? ''}`}
            userName={
              profile?.names && profile?.names?.length > 0 ? (profile?.names?.[0] as Name) : null
            }
            points={balance}
            isAccountLinked={!!householdId}
            employeeId={profile?.personNumber}
            banner={banner}
          />
        )}
        {profileCelebrationCards?.length > 0 && (
          <View style={styles.emptyStyle}>
            <CelebrationCarousel celebrationCards={profileCelebrationCards} showDismiss={false} analyticsCategory={CELEBRATION_ANALYTICS.PROFILE_EVENT_LABEL} />
          </View>
        )}
        <Section
          testID="section-profile-preferences"
          ref={setRef('section-profile-preferences')}
          svgXml={user(primaryColor)}
          heading={t('profileAndPreferences')}
          text={t(manageProfileAndPreferences)}
          onPress={() =>
            navigateFn(
              'ProfilePreference',
              ProfileConstants.PREFERENCE_HEADER,
              'section-profile-preferences',
            )
          }
        />
        <Section
          testID="section-hr-payroll"
          ref={setRef('section-hr-payroll')}
          svgXml={wallet(primaryColor)}
          heading={t('hrAndPayroll')}
          text={t('hrAndPayrollUpdates')}
          onPress={() => navigateFn('Hr-Payroll', ProfileConstants.HR_HEADER, 'section-hr-payroll')}
        />
        <Section
          testID="section-settings"
          ref={setRef('section-settings')}
          svgXml={settings(primaryColor)}
          heading={t('settings')}
          text={t('appSettings')}
          onPress={() =>
            navigateFn('Settings', ProfileConstants.SETTINGS_HEADER, 'section-settings')
          }
        />
        <Section
          testID="section-help"
          ref={setRef('section-help')}
          svgXml={help(primaryColor)}
          heading={t('feedback')}
          text={t('feedbackDescription')}
          onPress={() => onPressFeedback('section-help')}
        />
        <View style={styles.horizontalLine} />
        <TextLink
          testID="signout-text-link"
          accessible
          text={t('signOut')}
          size="Medium"
          textAlign="center"
          color="Primary"
          weight="Semi bold"
          onPress={() => handleSignout()}
        />
      </ScrollView>
      <FullscreenConfetti />
    </View>
  );
};

export default ProfileScreen;

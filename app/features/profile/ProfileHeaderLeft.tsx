/**
 * ProfileHeaderLeft
 *
 * Custom header left component for stack screens, typically used as a back button.
 *
 * Accessibility:
 * - On Android, this component manages its own accessibility focus using useEffect and AccessibilityInfo.setAccessibilityFocus.
 *   When the component mounts, it attempts to set focus to itself after a short delay, ensuring TalkBack users are placed on the back button.
 * - On iOS, the parent screen may receive a ref to this component via navigation params and set focus from the screen if needed.
 *
 * Props:
 * - onBackPress: Function to call when the back button is pressed.
 *
 * Usage:
 *   const backButtonRef = useRef<View>(null);
 *   <ProfileHeaderLeft ref={backButtonRef} onBackPress={...} />
 */
import React from 'react';
import { Pressable, View } from 'react-native';
import getStyles from "./styles";
import { useTheme } from 'pantry-design-system'
import { BackIcon } from '../../../assets/icons/BackIcon';

type ProfileHeaderLeftProps = {
    onBackPress: () => void;
};

const ProfileHeaderLeft = React.forwardRef<View, ProfileHeaderLeftProps>(({ onBackPress }: any, ref) => {
    const { theme } = useTheme() // Fething themes from ThemeProvider

    const styles = getStyles(theme) // Fetching styles based on theme

    const size = theme.dimensions.pdsGlobalSizeHeight400;
    const iconColor = theme.colors.pdsThemeColorOutlinePrimary;

    return (
        <Pressable ref={ref} style={styles.headerLeft} onPress={onBackPress} testID='pressable-header-left'>
            <BackIcon color={iconColor} size={size} />
        </Pressable>
    );
});
export default ProfileHeaderLeft;
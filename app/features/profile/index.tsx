/**
 * ProfileStackScreen component that configures and renders a stack navigator
 * for the profile-related screens in the app.
 *
 * The stack includes the following screens:
 * 1. ProfileScreen - Displays the user's profile (header hidden).
 * 2. ProfilePreference - Allows the user to view and modify profile preferences (custom header title).
 * 3. Hr-Payroll - Shows HR and payroll-related information (custom header title).
 * 4. Settings - Provides settings options for the user.
 * 5. Language - Allows the user to select their preferred language.
 *
 * The initial screen displayed is the "ProfileScreen" and the stack navigator is configured
 * to center the header title and remove the header shadow for all screens.

 */
import React, { useRef } from 'react';
import { createStackNavigator } from "@react-navigation/stack";
import ProfileScreen from "./profile";
import { Heading } from "pantry-design-system"
import SettingScreen from "./settings/Setting";
import LanguageSelectPage from "./settings/language/Language";
import ProfilePreference from "./profilePreference/ProfilePreference";
import HrAndPayroll from "./HrAndPayroll/HrAndPayroll";
import { useTranslation } from "react-i18next";
import HomeHeaderRight from "../home/<USER>";
import ProfileHeaderLeft from "./ProfileHeaderLeft";
import AppHeader from "../../../components/AppHeader";
import { SCREENS } from '../../shared/constants';

const Stack = createStackNavigator();

export default function ProfileStackScreen() {
  const { t } = useTranslation();

  /**
 * ProfileStackScreen component that configures and renders a stack navigator
 * for the profile-related screens in the app.
 *
 * Accessibility Note:
 * - For iOS, each screen that needs to programmatically focus the header back button receives a `backButtonRef`
 *   via `initialParams`. This ref is passed to the custom header left component (`ProfileHeaderLeft`).
 *   The screen can then use this ref to set accessibility focus for VoiceOver when the screen is opened.
 * - For Android, the header component (`ProfileHeaderLeft`) manages its own focus using useEffect and
 *   AccessibilityInfo.setAccessibilityFocus, as the header is rendered outside the screen context.
 *
 * Example usage:
 *   const profilePreferenceBackButtonRef = useRef(null);
 *   <ProfileHeaderLeft ref={profilePreferenceBackButtonRef} ... />
 *   <Stack.Screen
 *     name={SCREENS.PROFILE_PREFERENCE}
 *     initialParams={{ backButtonRef: profilePreferenceBackButtonRef }}
 *     ...
 *   />
 */
  const profilePreferenceBackButtonRef = useRef(null);
  const hrAndPayrollBackButtonRef = useRef(null);
  const settingsBackButtonRef = useRef(null);

  return (
    <Stack.Navigator
      initialRouteName={SCREENS.PROFILE_SCREEN}
      screenOptions={{
        headerTitleAlign: 'center',
        headerShadowVisible: false,
        gestureDirection: 'horizontal', // Standard back gesture direction
        animationTypeForReplace: 'pop', // Uses pop animation when replacing
      }}
    >
      <Stack.Screen
        name={SCREENS.PROFILE_SCREEN}
        options={{
          testID: 'profile-screen',
          headerTitle: '',
          header: () => (
            <AppHeader
              headerRight={<HomeHeaderRight />}
              headerLeft={
                <Heading
                  accessible
                  textAlign="center"
                  title={t('Profile')}
                  color="Neutral high"
                  size="medium"
                  headerTitleAlign="left"
                  allowFontScaling={false}
                />
              }
            />
          ),
        }}
        component={ProfileScreen}
      />
      <Stack.Screen
        name={SCREENS.PROFILE_PREFERENCE}
        options={({ navigation }) => ({
          testID: 'profile-preference',
          header: () => (
            <AppHeader
              headerLeft={
                <ProfileHeaderLeft
                  ref={profilePreferenceBackButtonRef}
                  onBackPress={() => navigation.goBack()}
                />
              }
              headerTitle={t("myProfileAndPreferences")}
            />
          ),
        })}
        component={ProfilePreference}
        initialParams={{ backButtonRef: profilePreferenceBackButtonRef }}

      />
      <Stack.Screen
        name={SCREENS.HR_AND_PAYROLL}
        options={({ navigation }) => ({
          testID: 'hr-payroll',
          header: () => (
            <AppHeader
              headerLeft={
                <ProfileHeaderLeft
                  ref={hrAndPayrollBackButtonRef}
                  onBackPress={() => navigation.goBack()}
                />
              }
              headerTitle={t("hrAndPayroll")}
            />
          ),
        })}
        component={HrAndPayroll}
        initialParams={{ backButtonRef: hrAndPayrollBackButtonRef }}
      />
      <Stack.Screen
        name={SCREENS.SETTINGS}
        options={({ navigation }) => ({
          testID: 'settings',
          header: () => (
            <AppHeader
              headerLeft={
                <ProfileHeaderLeft
                  ref={settingsBackButtonRef}
                  onBackPress={() => navigation.goBack()}
                />
              }
              headerTitle={t("settings")}
            />
          ),
        })}
        component={SettingScreen}
        initialParams={{ backButtonRef: settingsBackButtonRef }}
      />
      <Stack.Screen
        name={SCREENS.LANGUAGE}
        options={({ navigation }) => ({
          testID: 'language-select',
          header: () => (
            <AppHeader
              headerLeft={<ProfileHeaderLeft onBackPress={() => navigation.goBack()} />}
              headerTitle={t('language')}
            />
          ),
        })}
        component={LanguageSelectPage}
      />
    </Stack.Navigator>
  );
}
import React from "react";
import { render } from "@testing-library/react-native";
import { Provider } from "react-redux";
import configureS<PERSON> from "redux-mock-store";
import { NavigationContainer } from "@react-navigation/native";
import ProfileStackScreen from "../index";
import { checkNotifications } from 'react-native-permissions';

jest.mock('../../../providers/AccessibilityFocus', () => ({
  useAccessibilityFocus: () => ({
    setRef: jest.fn(),
    setLastFocused: jest.fn(),
    restoreLastFocus: jest.fn(),
    setNavigatingBack: jest.fn(),
  }),
  AccessibilityFocusProvider: ({ children }: { children: React.ReactNode }) => children,
}));

jest.mock('@react-navigation/native', () => {
    const actual = jest.requireActual('@react-navigation/native');
    return {
        ...actual,
        useRoute: () => ({ name: 'MockScreen' }),
    };
});
jest.mock('@react-native-firebase/app', () => ({
    getApp: jest.fn(() => ({
        name: '[DEFAULT]',
    })),
}));

jest.mock("react-native-haptic-feedback", () => ({
    trigger: jest.fn(),
}));

jest.mock('@appdynamics/react-native-agent', () => ({
    Instrumentation: {
        start: jest.fn(),
        setUserData: jest.fn(),
        removeUserData: jest.fn(),
        reportError: jest.fn(),
        reportMetric: jest.fn(),
        leaveBreadcrumb: jest.fn(),
        startTimer: jest.fn(),
        stopTimer: jest.fn(),
        startNextSession: jest.fn(),
    },
    LoggingLevel: { VERBOSE: 'VERBOSE', INFO: 'INFO' },
    ErrorSeverityLevel: { WARNING: 'WARNING', CRITICAL: 'CRITICAL' },
    BreadcrumbVisibility: { CRASHES_AND_SESSIONS: 'CRASHES_AND_SESSIONS' },
}));

const mockStore = configureStore([]);
jest.mock("react-native-vector-icons/MaterialIcons", () => "MaterialIcons");
jest.mock("../../omniHeader/omniHeader", () => "omniHeader");
jest.mock("react-native-vector-icons/SimpleLineIcons", () => "SimpleLineIcons");
jest.mock("react-native-vector-icons/MaterialCommunityIcons", () => "MaterialCommunityIcons");
jest.mock("react-native-vector-icons/AntDesign", () => "AntDesign");
jest.mock("../../profile/profilePreference/ProfilePreference", () => "ProfilePreference");
jest.mock("../../profile/settings/language/Language", () => "LanguageSelectPage");
jest.mock("../../profile/HrAndPayroll/HrAndPayroll", () => "HrAndPayroll");
jest.mock("../../profile/ProfileHeaderLeft", () => "ProfileHeaderLeft");
jest.mock("../../home/<USER>", () => "HomeHeaderRight");
jest.mock("../../omniHeader/omniHeader", () => "OmniHeader");
jest.mock("../../profile/settings/Setting", () => "SettingScreen");
jest.mock("../../../../components/AppHeader", () => "AppHeader");
jest.mock("../../../../components/CelebrationCard", () => "CelebrationCard");
jest.mock("../../celebration/fullScreenConfetti", () => "fullScreenConfetti");

jest.mock('../../../../components/ReUsableIcons/ReUsableIcons', () => ({ ArrowRight: () => <></> }));
jest.mock('../../../store/AsyncStorage', () => ({
    setItem: jest.fn(),
    getItem: jest.fn(),
    uniqueSessionId: jest.fn(),
}));

jest.mock('../../../store/SecureStorage', () => {
    return {
        SecureStorage: jest.fn().mockImplementation(() => ({
            getToken: jest.fn().mockResolvedValue('mocked-employee-id'),
        })),
        TokenType: {
            employeeId: 'employeeId',
        },
    };
});


jest.mock('@react-navigation/stack', () => {
    return {
        createStackNavigator: jest.fn(() => {
            return {
                Navigator: ({ children }) => <>{children}</>,
                Screen: ({ name, component }) => {
                    const Component = component || (() => null);
                    return <Component testID={name} />;
                },
            };
        }),
    };
});

jest.mock("pantry-design-system", () => ({
    useTheme: jest.fn(() => ({
        theme: {
            colors: { pdsThemeColorBackgroundBaseline: "#ffffff" },
            fonts: { pdsGlobalFontFamilyPoppins: "Poppins" },
            dimensions: { pdsGlobalSpace400: 16 },
            typography: { pdsGlobalFontSize500: 18 },
            borderDimens: { pdsGlobalBorderRadius300: 8 },
        }
    })),
    BottomSheet: jest.fn(),
    Text: jest.fn(({ text, testID }) => <text testID={testID}>{text}</text>),
    Heading: jest.fn(({ title, testID }) => <text testID={testID}>{title}</text>),
    TextLink: jest.fn(({ onPress, label, testID }) => (
        <button onClick={onPress} testID={testID}>
            {label}
        </button>
    )),
}));

jest.mock('react-native-permissions', () => ({
    check: jest.fn(() => Promise.resolve('granted')),
    request: jest.fn(() => Promise.resolve('granted')),
    PERMISSIONS: {
        ANDROID: {
            ACCESS_FINE_LOCATION: 'android.permission.ACCESS_FINE_LOCATION',
        },
        IOS: {
            LOCATION_WHEN_IN_USE: 'ios.permission.LOCATION_WHEN_IN_USE',
        },
    },
}));

jest.mock("react-native-switch", () => {
    return {
        Switch: jest.fn(() => null),
    };
});

jest.mock('react-native-permissions', () => ({
    checkNotifications: jest.fn(),
    RESULTS: {},
}));

jest.mock('../../../../app/store', () => ({
    store: {
        getState: jest.fn(() => ({
            profile: {
                profile: { id: 'mock-user-id' },
            },
        })),
    },
}));
jest.mock('@react-native-firebase/analytics', () => {
    return jest.fn(() => ({
        logEvent: jest.fn(),
        logScreenView: jest.fn(),
        setUserId: jest.fn(),
        setUserProperties: jest.fn(),
    }));
});
describe("ProfileStackScreen", () => {
    let store;

    beforeEach(() => {
        store = mockStore({
            confetti: { visible: true, reduceMotionEnabled: false },
            profile: {
                loading: false,
                profile: {
                    names: [
                        {
                            firstName: "John",
                            lastName: "Doe",
                        }
                    ]
                },
            },
            featureFlags: {
                flags: [], // or include specific flags if needed
                loading: false,
                error: null,
                response: null
            },
            rewardsScorecard: {
                hhid: "729006177437",
                programType: ["BASEPOINTS"],
            },
            locationAccess: {
                granted: true,
            },
            profilePhoto: {
                photoUrl: "base64ImageString",
                loading: false,
            },
        });
        (checkNotifications as jest.Mock).mockResolvedValue({ status: 'granted' });
    });

    const renderWithProviders = (ui) => {
        return render(
            <Provider store={store}>
                <NavigationContainer>{ui}</NavigationContainer>
            </Provider>
        );
    };
    it("renders the ProfileScreen with OmniHeader when userInstore is 'yes'", () => {
        const { getByTestId } = renderWithProviders(<ProfileStackScreen />);
        expect(getByTestId("ProfilePreference")).toBeTruthy();
    });

    it("renders the ProfilePreference screen header correctly", () => {
        const { getByTestId } = renderWithProviders(<ProfileStackScreen />);
        expect(getByTestId("ProfilePreference"));
    });

    it("renders the HrAndPayroll screen header correctly", () => {
        const { getByTestId } = renderWithProviders(<ProfileStackScreen />);
        expect(getByTestId("Hr-Payroll"));
    });

    it("renders the Settings screen header correctly", () => {
        const { getByTestId } = renderWithProviders(<ProfileStackScreen />);
        expect(getByTestId("Settings"));
    });
});
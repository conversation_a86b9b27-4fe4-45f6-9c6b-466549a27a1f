import React from "react";
import { render, fireEvent } from "@testing-library/react-native";
import Section from "../section";
import { useTheme } from "pantry-design-system";

jest.mock("pantry-design-system", () => ({
  useTheme: jest.fn(),
  Text: jest.fn(({ text, testID }) => <text testID={testID}>{text}</text>),
}));

jest.mock("react-native-vector-icons/MaterialIcons", () => "MaterialIcons");
const mockedTheme = {
  colors: { pdsThemeColorBackgroundBaseline: "#ffffff" },
  fonts: { pdsGlobalFontFamilyPoppins: "Poppins" },
  dimensions: { pdsGlobalSpace400: 16 },
  typography: { pdsGlobalFontSize500: 18 },
  borderDimens: { pdsGlobalBorderRadius300: 8 },
};

describe("Section Component", () => {
  beforeEach(() => {
    (useTheme as jest.Mock).mockReturnValue({ theme: mockedTheme });
  });

  it("should render correctly with given props", () => {
    const mockOnPress = jest.fn();
    const { getByTestId } = render(
      <Section
        svgXml="<svg></svg>"
        heading="Test Heading"
        text="Test Text"
        onPress={mockOnPress}
      />
    );
    expect(getByTestId("test-id-section-heading")).toBeTruthy();
    expect(getByTestId("test-id-section-text")).toBeTruthy();
    expect(getByTestId("section-image-icon")).toBeTruthy();
  });

  it("should call onPress when the section is pressed", () => {
    const mockOnPress = jest.fn();
    const { getByTestId } = render(
      <Section
        svgXml="<svg></svg>"
        heading="Test Heading"
        text="Test Text"
        onPress={mockOnPress}
      />
    );
    fireEvent.press(getByTestId("section-touchable"));
    expect(mockOnPress).toHaveBeenCalled();
  });

  it("should apply the correct styles based on theme", () => {
    const mockOnPress = jest.fn();
    const { getByTestId } = render(
      <Section
        svgXml="<svg></svg>"
        heading="Test Heading"
        text="Test Text"
        onPress={mockOnPress}
      />
    );
    const container = getByTestId("section-touchable");
    expect(container.props.style.backgroundColor).toBe(mockedTheme.colors.pdsThemeColorBackgroundBaseline);
  });
});

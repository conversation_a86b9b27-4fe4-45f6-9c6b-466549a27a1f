import React from 'react';
import { render } from '@testing-library/react-native';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';
import ProfileHeaderTitle from '../ProfileHeaderTitle';
import { useTheme } from "pantry-design-system";

jest.mock("pantry-design-system", () => ({
  useTheme: jest.fn(() => ({
    theme: {
      colors: { pdsThemeColorBackgroundBaseline: "#ffffff" },
      fonts: { pdsGlobalFontFamilyPoppins: "Poppins" },
      dimensions: { pdsGlobalSpace400: 16 },
      typography: { pdsGlobalFontSize500: 18 },
      borderDimens: { pdsGlobalBorderRadius300: 8 },
    }
  })),
}));

jest.mock('../styles', () => jest.fn(() => ({
  headerTitleView: { flex: 1 },
  headerTitle: { fontSize: 16 }
})));

describe('ProfileHeaderTitle Component', () => {
  const mockStore = configureStore([]);
  const store = mockStore({});
  beforeEach(() => {
    (useTheme as jest.Mock).mockReturnValue({ theme: { color: { outline: { primary: '#000' } } } });
  });
  it('renders correctly with the given title', () => {
    const title = 'Test Title';
    const { getByText } = render(
      <Provider store={store}>
        <ProfileHeaderTitle title={title} />
      </Provider>
    );
    const titleElement = getByText(title);
    expect(titleElement).toBeTruthy();
  });

  it('applies styles based on the theme', () => {
    const title = 'Styled Title';
    const { getByText } = render(
      <Provider store={store}>
        <ProfileHeaderTitle title={title} />
      </Provider>
    );
    const titleElement = getByText(title);
    expect(titleElement.props.style).toEqual(expect.objectContaining({ fontSize: 16 }));
  });

  it('truncates text when it is too long', () => {
    const longTitle = 'This is a very long title that should be truncated when displayed';
    const { getByText } = render(
      <Provider store={store}>
        <ProfileHeaderTitle title={longTitle} />
      </Provider>
    );
    const titleElement = getByText(longTitle);
    expect(titleElement.props.numberOfLines).toBe(1);
    expect(titleElement.props.ellipsizeMode).toBe('tail');
  });
});

import React from "react";
import { render, fireEvent } from "@testing-library/react-native";
import ProfileHeader from "../profileHeader";
import { Linking } from "react-native";
import { ProfileConstants } from "../../../shared/constants";
import configureS<PERSON> from 'redux-mock-store';
import { Provider } from "react-redux";

jest.mock('../../../providers/AccessibilityFocus', () => ({
  useAccessibilityFocus: () => ({
    setRef: jest.fn(),
    setLastFocused: jest.fn(),
    restoreLastFocus: jest.fn(),
    setNavigatingBack: jest.fn(),
  }),
  AccessibilityFocusProvider: ({ children }: { children: React.ReactNode }) => children,
}));


jest.mock('@react-native-firebase/app', () => ({
  getApp: jest.fn(() => ({})),
}));
jest.mock("pantry-design-system", () => ({
  useTheme: jest.fn(() => ({
    theme: {
      colors: { pdsThemeColorBackgroundBaseline: "#ffffff" },
      fonts: { pdsGlobalFontFamilyPoppins: "Poppins" },
      dimensions: { pdsGlobalSpace400: 16 },
      typography: { pdsGlobalFontSize500: 18 },
      borderDimens: { pdsGlobalBorderRadius300: 8 },
    }
  })),
  Heading: jest.fn(({ title, testID }) => <text testID={testID}>{title}</text>),
}));

jest.mock("react-native-permissions", () => ({
  PERMISSIONS: {
    ANDROID: {},
    IOS: {},
  },
  check: jest.fn(),
  request: jest.fn(),
}));

jest.mock('@react-navigation/native', () => ({
  useNavigation: jest.fn(),
  CommonActions: {
    reset: jest.fn(),
  },
}));

const initialState = {
  profile: {
    banner: 'banner'
  },
  profilePhoto: { photoUrl: "base64ImageString", loading: false },
  banner: "defaultBanner",
  featureFlags: {
    flags: [], // or include specific flags if needed
    loading: false,
    error: null,
    response: null
  }
};
const mockStore = configureStore([]);
const renderWithProviders = (
  component: React.ReactElement,
  storeState = initialState
) => {
  const store = mockStore(storeState);
  return render(
    <Provider store={store}>
      {component}
    </Provider>
  );
};
jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

const defaultProps: {
  pictureUrl?: string;
  initials: string;
  userName: {
    firstName: string;
    lastName: string;
    preferredName: string;
  };
  role: string;
  points: number;
  employeeId: string;
  isAccountLinked: boolean;
  banner: string;
} = {
  pictureUrl: "https://example.com/picture.jpg",
  initials: "A",
  userName: {
    firstName: "Alice",
    lastName: "Smith",
    preferredName: "Alice smith",
  },
  role: "Developer",
  points: 500,
  employeeId: "12345",
  isAccountLinked: true,
  banner: "testBanner"
};

describe("ProfileHeader", () => {

  it("should render profile name and role", () => {
    const { getByText } = renderWithProviders(<ProfileHeader {...defaultProps} />);
    const nameElement = getByText("Alice");
    expect(nameElement).toBeTruthy();
  });

  it("should render employee id correctly", () => {
    const { getByText } = renderWithProviders(<ProfileHeader {...defaultProps} />);
    const employeeIdText = getByText(`employeeNumber12345`);
    expect(employeeIdText).toBeTruthy();
  });

  it("should handle missing props gracefully", () => {
    const incompleteProps = { ...defaultProps };
    delete incompleteProps.userName;
    const { queryByText } = renderWithProviders(<ProfileHeader {...incompleteProps} />);
    const nameElement = queryByText("Alice");
    expect(nameElement).toBeNull();
  });
});
it("should render points image and text when feature flag is enabled and account is not disabled", () => {
  const mockState = {
    ...initialState,
    loyalty: { isAccountDisabled: false },
    featureFlags: {
      flags: [
        { featureFlagName: "diaaLoyaltyLinkForU", featureFlagValue: true }
      ],
      loading: false,
      error: null,
      response: null
    }
  };

  const { getByTestId, getByText } = renderWithProviders(<ProfileHeader {...defaultProps} />, mockState);
  const pointsImage = getByTestId("profile-header-points-image");
  const pointsText = getByText("500 Points");

  expect(pointsImage).toBeTruthy();
  expect(pointsText).toBeTruthy();
});

it("should call Linking.openURL when clicking on reward points", () => {
  const mockState = {
    ...initialState,
    loyalty: { isAccountDisabled: false },
    featureFlags: {
      flags: [
        { featureFlagName: "diaaLoyaltyLinkForU", featureFlagValue: true }
      ],
      loading: false,
      error: null,
      response: null
    }
  };

  const { getByText } = renderWithProviders(<ProfileHeader {...defaultProps} banner="testBanner" />, mockState);
  const rewardsButton = getByText("500 Points");

  jest.spyOn(Linking, "openURL").mockImplementation(jest.fn());
  fireEvent.press(rewardsButton);

  expect(Linking.openURL).toHaveBeenCalledWith(expect.stringContaining("testBanner"));
});


it("should render default profile image with initials when photoData is available", () => {
  let incompleteProps = { ...defaultProps };
  const { pictureUrl, ...restProps } = incompleteProps;
  incompleteProps = restProps;
  // When photoData is  available
  const { queryByTestId } = renderWithProviders(<ProfileHeader {...incompleteProps} />);
  const defaultProfileImageText = queryByTestId("profile-photo-initials");
  expect(defaultProfileImageText).toBeNull();
});
it("should render default profile image with initials when photoData is not available", () => {
  const incompleteState = {
    ...initialState,
    profilePhoto: { photoUrl: "", loading: false }, // ✅ simulate no photo
  };

  const { queryByTestId } = renderWithProviders(<ProfileHeader {...defaultProps} />, incompleteState);
  const defaultProfileImageText = queryByTestId("profile-photo-initials");
  expect(defaultProfileImageText).toBeTruthy();
});


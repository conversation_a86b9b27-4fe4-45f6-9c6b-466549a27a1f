import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import ProfileHeaderLeft from '../ProfileHeaderLeft';

jest.mock('@/components/ReUsableIcons/ReUsableIcons', () => ({
  ArrowLeft: jest.fn(() => null),
}));

jest.mock("pantry-design-system", () => ({
  useTheme: jest.fn(() => ({
    theme: {
      colors: { pdsThemeColorBackgroundBaseline: "#ffffff" },
      fonts: { pdsGlobalFontFamilyPoppins: "Poppins" },
      dimensions: { pdsGlobalSpace400: 16 },
      typography: { pdsGlobalFontSize500: 18 },
      borderDimens: { pdsGlobalBorderRadius300: 8 },
    }
  })),
}));

jest.mock('@/components/ReUsableIcons/ReUsableIcons', () => ({
  ArrowLeft: jest.fn((props) => <div {...props} />),
}));

describe('ProfileHeaderLeft Component', () => {
  const mockOnBackPress = jest.fn();
  it('calls onBackPress when pressed', () => {
    const { getByTestId } = render(
      <ProfileHeaderLeft onBackPress={mockOnBackPress} />
    );
    const pressableElement = getByTestId('pressable-header-left');
    fireEvent.press(pressableElement);
    expect(mockOnBackPress).toHaveBeenCalledTimes(1);
  });
});

import { useNavigation } from '@react-navigation/native';
import { render, screen, fireEvent, waitFor } from '@testing-library/react-native';
import React from 'react';
import { Alert } from 'react-native';
import { checkNotifications } from 'react-native-permissions';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';

import ABAuditEngine from '../../../../analytics/ABAuditEngine';
import { LOCATION_STATE } from '../../../shared/constants';
import ProfileScreen from '../profile';

jest.mock('../../../providers/AccessibilityFocus', () => ({
  useAccessibilityFocus: () => ({
    setRef: jest.fn(),
    setLastFocused: jest.fn(),
    handleScreenFocus: jest.fn(),
  }),
  AccessibilityFocusProvider: ({ children }: { children: React.ReactNode }) => children,
}));

jest.mock('@react-native-firebase/app', () => ({
  getApp: jest.fn(() => ({
    name: '[DEFAULT]',
  })),
}));

jest.mock('react-native-haptic-feedback', () => ({
  trigger: jest.fn(),
}));

jest.mock('@appdynamics/react-native-agent', () => ({
  Instrumentation: {
    start: jest.fn(),
    setUserData: jest.fn(),
    removeUserData: jest.fn(),
    reportError: jest.fn(),
    reportMetric: jest.fn(),
    leaveBreadcrumb: jest.fn(),
    startTimer: jest.fn(),
    stopTimer: jest.fn(),
    startNextSession: jest.fn(),
  },
  LoggingLevel: { VERBOSE: 'VERBOSE', INFO: 'INFO' },
  ErrorSeverityLevel: { WARNING: 'WARNING', CRITICAL: 'CRITICAL' },
  BreadcrumbVisibility: { CRASHES_AND_SESSIONS: 'CRASHES_AND_SESSIONS' },
}));

jest.mock('@react-navigation/native', () => ({
  useNavigation: jest.fn(),
  CommonActions: {
    reset: jest.fn(),
  },
  useFocusEffect: jest.fn((cb) => {
    cb();
  }),
  useRoute: jest.fn(() => ({
    params: {
      someParam: 'testValue', // customize based on your screen
    },
  })),
}));

jest.mock('../../../../analytics/ABAuditEngine', () => ({
  customTimerStart: jest.fn(),
  customTimerEnd: jest.fn(),
}));
jest.spyOn(ABAuditEngine, 'customTimerStart').mockImplementation(jest.fn());
jest.spyOn(ABAuditEngine, 'customTimerEnd').mockImplementation(jest.fn());

jest.mock('react-native-vector-icons/MaterialIcons', () => 'MaterialIcons');

jest.mock('../../../store/AsyncStorage', () => ({
  setItem: jest.fn(),
  getItem: jest.fn(),
  uniqueSessionId: jest.fn(),
}));

jest.mock('../../../store/SecureStorage', () => {
  return {
    SecureStorage: jest.fn().mockImplementation(() => ({
      getToken: jest.fn().mockResolvedValue('mocked-employee-id'),
    })),
    TokenType: {
      employeeId: 'employeeId',
    },
  };
});

jest.mock('react-native-svg', () => ({
  SvgXml: jest.fn(({ xml, testID }) => <svg testID={testID}>{xml}</svg>),
}));

jest.mock('../../../store/SecureStorage', () => {
  return {
    __esModule: true,
    SecureStorage: jest.fn().mockImplementation(() => ({
      getToken: jest.fn().mockResolvedValue(
        JSON.stringify({
          householdAccount: { householdId: '12345' },
        }),
      ),
      saveToken: jest.fn(),
      clearAll: jest.fn(),
    })),
  };
});

jest.mock('pantry-design-system', () => ({
  useTheme: jest.fn(() => ({
    theme: {
      colors: {},
      fonts: { pdsGlobalFontFamilyPoppins: 'Poppins' },
      dimensions: { pdsGlobalSpace400: 16 },
      typography: { pdsGlobalFontSize500: 18 },
      borderDimens: {
        pdsGlobalBorderRadius200: 8,
        pdsGlobalBorderWidth100: 1,
      },
    },
  })),
  Heading: jest.fn(({ title, testID }) => <text testID={testID}>{title}</text>),
  Text: jest.fn(({ text, testID }) => <text testID={testID}>{text}</text>),
  TextLink: jest.fn(({ onPress, label, testID }) => (
    <button onClick={onPress} testID={testID}>
      {label}
    </button>
  )),
}));

jest.spyOn(Alert, 'alert').mockImplementation(() => { }); // Prevent Alert interference

jest.mock('../../../../components/CelebrationCard', () => 'CelebrationCard');

jest.mock('react-native-permissions', () => ({
  checkNotifications: jest.fn(),
  RESULTS: {},
}));

jest.mock('../../../../app/store', () => ({
  store: {
    getState: jest.fn(() => ({
      profile: {
        profile: { id: 'mock-user-id' },
      },
    })),
  },
}));
jest.mock('@react-native-firebase/analytics', () => {
  return jest.fn(() => ({
    logEvent: jest.fn(),
    logScreenView: jest.fn(),
    setUserId: jest.fn(),
    setUserProperties: jest.fn(),
  }));
});

const mockStore = configureStore([]);

describe('ProfileScreen', () => {
  let store: any;
  let mockNavigate: jest.Mock;

  beforeEach(() => {
    mockNavigate = jest.fn();
    useNavigation.mockReturnValue({
      navigate: mockNavigate,
      addListener: jest.fn(() => jest.fn()),
    });
    (checkNotifications as jest.Mock).mockResolvedValue({ status: 'granted' });

    store = mockStore({
      confetti: { visible: true, reduceMotionEnabled: false },
      locationAccess: { skipped: false, isPrecise: LOCATION_STATE.PRECISE },
      profile: {
        profile: {
          names: [{ firstName: 'Jane', lastName: 'Smith' }],
          jobCode: '67890',
          emails: [{ email: '<EMAIL>' }],
        },
        loading: false,
        ucaProfile: {
          householdAccount: {
            householdId: '12345',
          },
        },
        banner: 'albertsons',
      },
      featureFlags: {
        flags: [], // or include specific flags if needed
        loading: false,
        error: null,
        response: null,
      },
      rewardsScorecard: {
        status: 'success',
        data: {
          scorecards: [{ balance: '1000' }],
        },
      },
      profilePhoto: {
        photoUrl: 'base64ImageString',
        loading: false,
      },
      shift: { clockedIn: true },
    });
  });

  it('renders ProfileScreen without crashing', async () => {
    render(
      <Provider store={store}>
        <ProfileScreen />
      </Provider>,
    );
    await waitFor(() => expect(screen.getByTestId('profile-screen')).toBeTruthy());
  });

  it('renders ProfileHeader with profile data', () => {
    render(
      <Provider store={store}>
        <ProfileScreen />
      </Provider>,
    );

    expect(screen.getByTestId('profile-screen')).toBeTruthy();
  });

  it('shows pull-to-refresh loader when refreshing', () => {
    store = mockStore({
      ...store,
      confetti: { visible: true, reduceMotionEnabled: false },
      profile: {
        ...store.profile,
        loading: true,
        emails: [{ email: '<EMAIL>' }],
        banner: 'albertsons',
      },
      profilePhoto: {
        photoUrl: 'base64ImageString',
        loading: false,
      },
      shift: { clockedIn: true },
      locationAccess: { skipped: false, isPrecise: LOCATION_STATE.PRECISE },
    });

    render(
      <Provider store={store}>
        <ProfileScreen />
      </Provider>,
    );

    expect(screen.getByTestId('profile-screen')).toBeTruthy();
  });

  it('navigates to ProfilePreference section on press', () => {
    render(
      <Provider store={store}>
        <ProfileScreen />
      </Provider>,
    );

    const section = screen.getByTestId('section-profile-preferences');
    fireEvent.press(section);

    expect(mockNavigate).toHaveBeenCalledWith('ProfilePreference');
  });

  it('handles logout confirmation', async () => {
    jest.spyOn(Alert, 'alert').mockImplementation((title, message, buttons) => {
      const positiveButton = buttons?.find((button) => button.text === 'Yes');
      positiveButton?.onPress?.();
    });

    render(
      <Provider store={store}>
        <ProfileScreen />
      </Provider>,
    );

    const signOutButton = screen.getByTestId('signout-text-link');
    fireEvent.press(signOutButton);

    await waitFor(() => expect(screen.queryByTestId('profile-screen')).toBeTruthy());
  });

  it('handles API call failure gracefully', () => {
    store = mockStore({
      ...store,
      confetti: { visible: true, reduceMotionEnabled: false },
      profile: {
        profile: {
          emails: [{ emailType: 'Work Email', emailAddress: '<EMAIL>' }],
        },
        loading: false,
        emails: [{ email: '<EMAIL>' }],
        banner: 'albertsons',
      },
      rewardsScorecard: { status: 'failed', error: 'API error' },
      profilePhoto: { photoUrl: 'base64ImageString', loading: false },
      locationAccess: { skipped: false, isPrecise: LOCATION_STATE.PRECISE },
      shift: { clockedIn: true },
    });

    render(
      <Provider store={store}>
        <ProfileScreen />
      </Provider>,
    );

    expect(screen.queryByText('1000')).toBeNull(); // Points not displayed
  });

  it('navigates to ProfilePreference section on press', () => {
    render(
      <Provider store={store}>
        <ProfileScreen />
      </Provider>,
    );

    const section = screen.getByTestId('section-help');
    fireEvent.press(section);

    expect(mockNavigate).toHaveBeenCalledWith('WebViewScreen', {
      previousTab: 'profile',
      titleHeader: 'feedback',
      url: 'https://forms.office.com/r/VspEmqLFWW',
      subsection: {
        eventCategory: 'profile',
        eventLabel: 'app-feedback',
        refreshAction: 'refresh',
        refreshCategory: 'profile',
        refreshLabel: 'app-feedback',
        subsection1: 'profile',
        subsection2: 'app-feedback',
      },
    });
  });
});

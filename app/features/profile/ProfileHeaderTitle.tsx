import { useTheme } from 'pantry-design-system';
import React from 'react';
import { Text } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import getStyles from './styles';

const ProfileHeaderTitle = ({ title }: any) => {
    const { theme } = useTheme(); // Fething themes from ThemeProvider

    const styles = getStyles(theme); // Fetching styles based on theme
    //pds/theme/color/outline/primary

    return (
        <SafeAreaView edges={['top', 'bottom']} style={styles.headerTitleView}>
            <Text style={styles.headerTitle} numberOfLines={1} ellipsizeMode="tail">
                {title}
            </Text>
        </SafeAreaView>
    );
};

export default ProfileHeaderTitle;

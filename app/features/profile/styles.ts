import { StyleSheet } from 'react-native';

import { IPAD_WIDTH } from '../../shared/constants';

import type { Theme } from 'pantry-design-system';

// Customising styles based on themes
const getStyles = ({ colors, fonts, dimensions, typography, borderDimens }: Theme) => {
    const styles = StyleSheet.create({
        backgoundImage: {
            position: 'absolute',
            top: -40,
        },
        container: {
            backgroundColor: colors.pdsThemeColorBackgroundBaseline,
            flex: 1,
            padding: dimensions.pdsGlobalSpace400,
        },
        customHeader: {
            backgroundColor: 'white',
            flexDirection: 'row',
            height: 60,
            justifyContent: 'space-between',
            paddingHorizontal: 8,
        },
        customHeaderLeft: {
            marginTop: 5,
        },
        customHeaderRight: {
            alignSelf: 'center',
        },
        customheaderTitle: {
            alignSelf: 'center',
            marginLeft: '10%',
        },
        defaultProfileImage: {
            alignItems: 'center',
            backgroundColor: colors.pdsThemeColorBackgroundPrimary,
            borderColor: colors.pdsThemeColorOutlineAccent,
            justifyContent: 'center',
        },
        employeeId: {
            color: colors.pdsThemeColorForegroundNeutralHigh,
            fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
            fontSize: typography.pdsGlobalFontSize300,
            fontWeight: typography.pdsGlobalFontWeight600,
        },
        emptyStyle: {
            paddingHorizontal: dimensions.pdsGlobalSpace400,
            paddingTop: dimensions.pdsGlobalSpace400,
        },
        headerLeft: {
            alignItems: 'center',
            height: dimensions.pdsGlobalSizeHeight800,
            padding: dimensions.pdsGlobalSpace200,
            width: dimensions.pdsGlobalSizeWidth800,
        },
        headerTitle: {
            color: colors.pdsThemeColorForegroundNeutralHigh,
            display: 'flex',
            fontFamily: fonts.pdsGlobalFontFamilyPoppins,
            fontSize: typography.pdsGlobalFontSize400,
            fontStyle: 'normal',
            fontWeight: typography.pdsGlobalFontWeight500,
            lineHeight: 28.6,
        },
        headerTitleView: {
            minHeight: dimensions.pdsGlobalSizeHeight100,
        },
        heading: {
            color: colors.pdsThemeColorForegroundNeutralHigh,
            fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
            fontSize: 16,
            fontStyle: 'normal',
            fontWeight: typography.pdsGlobalFontWeight400,
            lineHeight: 23.2,
        },
        horizontalLine: {
            height: 1,
            backgroundColor: '#E5E4E3',
            marginVertical: 25, // Spacing above and below
            width: '90%', // Full width
            alignSelf: 'center',
        },
        icon: {
            height: dimensions.pdsGlobalSizeWidth400,
            tintColor: colors.pdsThemeColorBackgroundPrimaryHigh,
            width: dimensions.pdsGlobalSizeWidth400,
        },
        imageContainer: {
            alignItems: 'center',
            backgroundColor: colors.pdsThemeColorBackgroundBaseline,
            justifyContent: 'center',
        },
        linkContainer: {
            flexDirection: 'row',
            marginTop: dimensions.pdsGlobalSpace100,
        },
        linkImageContainer: {
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: dimensions.pdsGlobalSpace200,
        },
        loadingStyles: {
            position: 'absolute',
            zIndex: 1,
        },
        mainContainer: { flex: 1 },
        notificationBell: {
            fontSize: typography.pdsGlobalFontSize550,
            height: dimensions.pdsGlobalSizeHeight800,
            marginBottom: dimensions.pdsGlobalSpace200,
            opacity: 2,
            position: 'absolute',
            right: 10,
            width: dimensions.pdsGlobalSizeWidth800,
        },
        pointsContainer: {
            alignItems: 'center',
            flexDirection: 'row',
            marginTop: dimensions.pdsGlobalSpace100,
        },
        pointsImage: {
            height: dimensions.pdsGlobalSpace600,
            width: dimensions.pdsGlobalSpace600,
        },
        pointsText: {
            color: colors.pdsThemeColorForegroundNeutralHigh,
            fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
            fontSize: typography.pdsGlobalFontSize300,
            fontWeight: typography.pdsGlobalFontWeight600,
            marginLeft: dimensions.pdsGlobalSpace200,
            textDecorationLine: 'underline',
        },
        profileCardContainer: {
            paddingHorizontal: dimensions.pdsGlobalSpace400,
            paddingTop: dimensions.pdsGlobalSpace900,
        },
        profileHeaderCard: {
            alignItems: 'center',
            backgroundColor: colors.pdsThemeColorBackgroundBaseline,
            borderRadius: borderDimens.pdsGlobalBorderRadius200,
            color: colors.pdsGlobalShadowLowBottomColor,
            elevation: 5,
            flexDirection: 'row',
            padding: dimensions.pdsGlobalSpace400,
            shadowOpacity: 0.1,
            shadowRadius: 10,
        },
        profileImage: {
            borderColor: colors.pdsThemeColorForegroundPrimary,
            borderRadius: dimensions.pdsGlobalSpace900,
            borderWidth: borderDimens.pdsGlobalBorderWidth300,
            height: dimensions.pdsGlobalSpace1500,
            width: dimensions.pdsGlobalSpace1500,
        },
        profileInfo: {
            flex: 1,
            marginLeft: dimensions.pdsGlobalSpace300,
        },
        profileName: {
            color: colors.pdsThemeColorForegroundNeutralHigh,
            fontFamily: fonts.pdsGlobalFontFamilyPoppins,
            fontSize: typography.pdsGlobalFontSize700,
            fontWeight: typography.pdsGlobalFontWeight500,
        },
        sectionContainer: {
            alignItems: 'center',
            backgroundColor: colors.pdsThemeColorBackgroundBaseline,
            borderColor: colors.pdsThemeColorOutlineNeutralLow,
            borderRadius: borderDimens.pdsGlobalBorderRadius200,
            borderWidth: borderDimens.pdsGlobalBorderWidth100,
            flexDirection: 'row',
            marginBottom: dimensions.pdsGlobalSpace100,
            marginHorizontal: dimensions.pdsGlobalSpace400,
            marginTop: dimensions.pdsGlobalSpace400,
            padding: dimensions.pdsGlobalSpace400,
        },
        tabMainContainer: { alignSelf: 'center', flex: 1, justifyContent: 'center', width: IPAD_WIDTH },
        text: {
            color: colors.pdsThemeColorForegroundNeutralLow,
            fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
            fontSize: 14,
            fontStyle: 'normal',
            fontWeight: typography.pdsGlobalFontWeight400,
            lineHeight: 20.3,
        },
        textContainer: {
            flex: 1,
            marginLeft: 16,
        },
    });

    return styles;
};

export default getStyles;

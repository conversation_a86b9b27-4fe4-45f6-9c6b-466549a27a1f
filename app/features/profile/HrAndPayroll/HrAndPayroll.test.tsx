import { useNavigation } from '@react-navigation/native';
import { render, fireEvent } from '@testing-library/react-native';
import React from 'react';
import { checkNotifications } from 'react-native-permissions';
import { Provider } from 'react-redux';
import configureMockStore from 'redux-mock-store';

import { SCREENS, URLS } from '../../../shared/constants';

import HrAndPayroll from './HrAndPayroll';

jest.mock('../../../providers/AccessibilityFocus', () => ({
  useAccessibilityFocus: () => ({
    setRef: jest.fn(),
    setLastFocused: jest.fn(),
    handleScreenFocus: jest.fn(),
  }),
  AccessibilityFocusProvider: ({ children }: { children: React.ReactNode }) => children,
}));

jest.mock('@react-native-firebase/app', () => ({
  getApp: jest.fn(() => ({
    name: '[DEFAULT]',
  })),
}));

jest.mock('@react-navigation/native', () => ({
  useNavigation: jest.fn(),
  CommonActions: {
    reset: jest.fn(),
  },
  useFocusEffect: jest.fn((effect) => {
    effect(); // Just call the effect once
  }),
  useRoute: jest.fn(() => ({
    params: {},
  })),
}));

jest.mock('react-i18next', () => ({
  useTranslation: jest.fn(() => ({ t: jest.fn((key) => key) })),
}));

const mockStore = configureMockStore();
const store = mockStore({
  deviceInfo: {
    isTablet: false,
    isLandscape: false,
  },
  profile: {
    isTestAccount: false,
  },
});
jest.mock('pantry-design-system', () => ({
  useTheme: jest.fn(() => ({
    theme: {
      colors: { pdsThemeColorBackgroundPrimary: '#ffffff' },
      fonts: { pdsGlobalFontFamilyPoppins: 'Poppins' },
      dimensions: { pdsGlobalSpace400: 16 },
      typography: { pdsGlobalFontSize500: 18 },
      borderDimens: { pdsGlobalBorderRadius300: 8 },
    },
  })),
  Heading: jest.fn(({ title, testID }) => <text testID={testID}>{title}</text>),
  Text: jest.fn(({ text, testID }) => <text testID={testID}>{text}</text>),
  BottomSheet: jest.fn(({ children, testID }) => <div testID={testID}>{children}</div>),
  Button: jest.fn(({ onPress, label, testID }) => (
    <button onClick={onPress} testID={testID}>
      {label}
    </button>
  )),
}));

jest.mock('./Styles', () =>
  jest.fn(() => ({
    card1: {},
    emptyStyle: {},
    row: { flexDirection: 'row', alignItems: 'center', gap: 0 },
    heading1: {},
    container: {},
    receiptIconStyles: {},
    containerStyles: {},
    sectionAccordion: {},
    sectionAccordionTitle: {},
    sectionAccordionContent: {},
    sectionAccordionIcon: {},
    divider: {},
    accordionContent: {},
    scanpayIconStyles: {},
    externalLinks: {},
    exteranLinkContainer: {},
    sectionContainer: {},
    sectionTitle: {},
    sectionBox: {},
    tabletLabel: {},
    tabletRow: {},
    descriptionContainer: {},
    buttonView: {},
    buttonContainer: {},
  })),
);

jest.mock('@/components/divider/CDivider', () => 'CDivider');
jest.mock('../profilePreference/section', () => 'SectionPreference');
jest.mock('@/assets/icons/receipt-icon.png', () => 'receipticon');
jest.mock('@/assets/icons/scanPay.png', () => 'scanPayIcon');
jest.mock('@react-native-firebase/analytics', () => {
  return jest.fn(() => ({
    logEvent: jest.fn(),
    logScreenView: jest.fn(),
    setUserId: jest.fn(),
    setUserProperties: jest.fn(),
  }));
});

jest.mock('react-native-permissions', () => ({
  checkNotifications: jest.fn(),
  RESULTS: {},
}));

jest.mock('../../../../app/store', () => ({
  store: {
    getState: jest.fn(() => ({
      profile: {
        profile: { id: 'mock-user-id' },
      },
    })),
  },
}));

jest.mock('../../../../assets/icons/WarningIcon', () => ({
  WarningIcon: () => <></>,
}));

// Mock AppUtils
jest.mock('../../../utils/AppUtils', () => ({
  showErrorAlert: jest.fn(),
}));

describe('HrAndPayroll Component', () => {
  const mockNavigate = jest.fn();
  const mockGoBack = jest.fn();
  const mockAddListener = jest.fn();

  beforeEach(() => {
    (checkNotifications as jest.Mock).mockResolvedValue({ status: 'granted' });
    (useNavigation as jest.Mock).mockReturnValue({
      navigate: mockNavigate,
      goBack: mockGoBack,
      addListener: mockAddListener.mockImplementation((event, callback) => {
        if (event === 'focus') {
          callback();
        }
        return jest.fn(); // Return unsubscribe function
      }),
      setParams: jest.fn(), // Add setParams mock to prevent TypeError
    });
    mockNavigate.mockClear();
    mockGoBack.mockClear();
    mockAddListener.mockClear();
  });

  const renderWithProvider = (component: JSX.Element) =>
    render(<Provider store={store}>{component}</Provider>);

  it('should render without crashing', () => {
    const { getByText } = renderWithProvider(<HrAndPayroll />);
    expect(getByText('hrQuickLinks')).toBeTruthy();
  });

  it('should render SectionAccordion components with correct text', () => {
    const { getByTestId } = renderWithProvider(<HrAndPayroll />);
    expect(getByTestId('enrollment-and-changes')).toBeTruthy();
    // expect(getByTestId("my-benefits")).toBeTruthy();
    expect(getByTestId('health-and-well-being')).toBeTruthy();
    expect(getByTestId('leave-of-absence')).toBeTruthy();
    expect(getByTestId('401k-and-financial-wellness')).toBeTruthy();
    expect(getByTestId('benefit-contacts')).toBeTruthy();
  });

  it('should render SectionPreference components with correct text', () => {
    const { getByTestId } = renderWithProvider(<HrAndPayroll />);
    expect(getByTestId('benefit-resources')).toBeTruthy();
    expect(getByTestId('my-ACI')).toBeTruthy();
    expect(getByTestId('get-paystubs')).toBeTruthy();
    expect(getByTestId('change-tax-withholdings')).toBeTruthy();
    expect(getByTestId('update-direct-deposit-account')).toBeTruthy();
    expect(getByTestId('lost-or-damaged-check')).toBeTruthy();
  });

  it('should use correct theme colors', () => {
    const { getByTestId } = renderWithProvider(<HrAndPayroll />);
    const receiptIcon = getByTestId('receipt-icon');
    expect(receiptIcon.props.fill).toBe('#ffffff');
    const scanPayIcon = getByTestId('scan-pay-icon');
    expect(scanPayIcon.props.fill).toBe('#ffffff');
  });

  it('should apply container styles correctly', () => {
    const { getByTestId } = renderWithProvider(<HrAndPayroll />);
    const pageWrapper = getByTestId('page-wrapper');
    expect(pageWrapper.props).toHaveProperty('style');
  });
});

describe('onPressBtnPayroll function', () => {
  let mockNavigate: jest.Mock;
  let mockAddListener: jest.Mock;

  beforeEach(() => {
    (checkNotifications as jest.Mock).mockResolvedValue({ status: 'granted' });
    mockNavigate = jest.fn();
    mockAddListener = jest.fn((event, callback) => {
      if (event === 'focus') callback();
      return jest.fn();
    });

    (useNavigation as jest.Mock).mockReturnValue({
      navigate: mockNavigate,
      addListener: mockAddListener,
      setParams: jest.fn(), // Add setParams mock to prevent TypeError
    });
  });

  it('should navigate to WebViewScreen with correct parameters when get-paystubs is pressed', () => {
    const { getByTestId } = render(
      <Provider store={store}>
        <HrAndPayroll />
      </Provider>,
    );

    fireEvent.press(getByTestId('get-paystubs'));
    expect(mockNavigate).toHaveBeenCalledWith('WebViewScreen', {
      url: URLS.GET_PAYSTUBS,
      titleHeader: 'getPaystubs',
      previousTab: SCREENS.PROFILE,
    });
  });

  it('should navigate to WebViewScreen with correct parameters when myACI is pressed', () => {
    const { getByTestId } = render(
      <Provider store={store}>
        <HrAndPayroll />
      </Provider>,
    );

    fireEvent.press(getByTestId('my-ACI'));
    expect(mockNavigate).toHaveBeenCalledWith('WebViewScreen', {
      url: URLS.MY_ACI,
      titleHeader: 'myACI',
      previousTab: SCREENS.PROFILE,
    });
  });

  it('should navigate to WebViewScreen with correct parameters when benefit-contacts is pressed', () => {
    const { getByTestId } = render(
      <Provider store={store}>
        <HrAndPayroll />
      </Provider>,
    );

    fireEvent.press(getByTestId('benefit-contacts'));
    expect(mockNavigate).toHaveBeenCalledWith('WebViewScreen', {
      url: URLS.BENEFIT_CONTACTS,
      titleHeader: 'benefitContacts',
      previousTab: SCREENS.PROFILE,
    });
  });

  it('should navigate to WebViewScreen with correct parameters when enrollment-and-changes is pressed', () => {
    const { getByTestId } = render(
      <Provider store={store}>
        <HrAndPayroll />
      </Provider>,
    );

    fireEvent.press(getByTestId('enrollment-and-changes'));
    expect(mockNavigate).toHaveBeenCalledWith('WebViewScreen', {
      url: URLS.ENROLLMENT_AND_CHANGES,
      titleHeader: 'enrollmentAndChanges',
      previousTab: SCREENS.PROFILE,
    });
  });

  it('should navigate to WebViewScreen with correct parameters when 401k-and-financial-wellness is pressed', () => {
    const { getByTestId } = render(
      <Provider store={store}>
        <HrAndPayroll />
      </Provider>,
    );

    fireEvent.press(getByTestId('401k-and-financial-wellness'));
    expect(mockNavigate).toHaveBeenCalledWith('WebViewScreen', {
      url: URLS.FINANCIAL_WELLNESS,
      titleHeader: '401KAndFinancialWellness',
      previousTab: SCREENS.PROFILE,
    });
  });

  it('should navigate to WebViewScreen with correct parameters when leave-of-absence is pressed', () => {
    const { getByTestId } = render(
      <Provider store={store}>
        <HrAndPayroll />
      </Provider>,
    );

    fireEvent.press(getByTestId('leave-of-absence'));
    expect(mockNavigate).toHaveBeenCalledWith('WebViewScreen', {
      url: URLS.LEAVE_OF_ABSENCE,
      titleHeader: 'leaveOfAbsence',
      previousTab: SCREENS.PROFILE,
    });
  });

  it('should navigate to WebViewScreen with correct parameters when health-and-well-being is pressed', () => {
    const { getByTestId } = render(
      <Provider store={store}>
        <HrAndPayroll />
      </Provider>,
    );

    fireEvent.press(getByTestId('health-and-well-being'));
    expect(mockNavigate).toHaveBeenCalledWith('WebViewScreen', {
      url: URLS.HEALTH_AND_WELL_BEING,
      titleHeader: 'healthAndWellBeing',
      previousTab: SCREENS.PROFILE,
    });
  });

  it('should navigate to WebViewScreen with correct parameters when update-direct-deposit-account is pressed', () => {
    const { getByTestId } = render(
      <Provider store={store}>
        <HrAndPayroll />
      </Provider>,
    );

    fireEvent.press(getByTestId('update-direct-deposit-account'));
    expect(mockNavigate).toHaveBeenCalledWith('WebViewScreen', {
      url: URLS.UPDATE_DIRECT_DEPOSIT_ACCOUNT,
      titleHeader: 'updateDirectDepositAccount',
      previousTab: SCREENS.PROFILE,
    });
  });

  it('should navigate to WebViewScreen with correct parameters when change-tax-withholdings is pressed', () => {
    const { getByTestId } = render(
      <Provider store={store}>
        <HrAndPayroll />
      </Provider>,
    );

    fireEvent.press(getByTestId('change-tax-withholdings'));
    expect(mockNavigate).toHaveBeenCalledWith('WebViewScreen', {
      url: URLS.TAX_CHANGE_WITHHOLDINGS,
      titleHeader: 'changeTaxWithholdings',
      previousTab: SCREENS.PROFILE,
    });
  });
});

describe('Additional Coverage Tests', () => {
  let mockNavigate: jest.Mock;
  let mockAddListener: jest.Mock;

  beforeEach(() => {
    jest.clearAllMocks();
    (checkNotifications as jest.Mock).mockResolvedValue({ status: 'granted' });
    mockNavigate = jest.fn();
    mockAddListener = jest.fn((event, callback) => {
      if (event === 'focus') callback();
      return jest.fn();
    });

    (useNavigation as jest.Mock).mockReturnValue({
      navigate: mockNavigate,
      addListener: mockAddListener,
      setParams: jest.fn(),
    });
  });

  it('should show error alert when isTestAccount is true', () => {
    const testAccountStore = mockStore({
      deviceInfo: {
        isTablet: false,
        isLandscape: false,
      },
      profile: {
        isTestAccount: true,
      },
    });

    render(
      <Provider store={testAccountStore}>
        <HrAndPayroll />
      </Provider>,
    );

    // The test passes if the component renders without crashing when isTestAccount is true
    expect(testAccountStore.getState().profile.isTestAccount).toBe(true);
  });

  it('should handle lost or damaged check button press', () => {
    const { getByTestId } = render(
      <Provider store={store}>
        <HrAndPayroll />
      </Provider>,
    );

    // Test a specific button to ensure all payroll buttons are covered
    fireEvent.press(getByTestId('lost-or-damaged-check'));
    expect(mockNavigate).toHaveBeenCalled();
  });

  it('should render benefit resources accordion properly', () => {
    const { getByTestId } = render(
      <Provider store={store}>
        <HrAndPayroll />
      </Provider>,
    );

    const benefitResourcesButton = getByTestId('benefit-resources');
    expect(benefitResourcesButton).toBeTruthy();
    
    // Test accordion functionality
    fireEvent.press(benefitResourcesButton);
    expect(benefitResourcesButton).toBeTruthy();
  });

  it('should handle all section accordion interactions', () => {
    const { getByTestId } = render(
      <Provider store={store}>
        <HrAndPayroll />
      </Provider>,
    );

    // Test all accordion sections
    const sections = [
      'enrollment-and-changes',
      'health-and-well-being', 
      'leave-of-absence',
      '401k-and-financial-wellness',
      'benefit-contacts'
    ];

    sections.forEach(sectionId => {
      const section = getByTestId(sectionId);
      expect(section).toBeTruthy();
      fireEvent.press(section);
    });
  });

  it('should verify navigation for all payroll items', () => {
    const { getByTestId } = render(
      <Provider store={store}>
        <HrAndPayroll />
      </Provider>,
    );

    // Test additional payroll items to ensure full coverage
    const payrollItems = [
      'update-direct-deposit-account',
      'lost-or-damaged-check'
    ];

    payrollItems.forEach(itemId => {
      const item = getByTestId(itemId);
      expect(item).toBeTruthy();
      fireEvent.press(item);
      expect(mockNavigate).toHaveBeenCalled();
    });
  });
});

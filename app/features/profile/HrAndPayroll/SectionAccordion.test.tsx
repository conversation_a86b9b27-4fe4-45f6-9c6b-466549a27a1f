import React from "react";
import { render, fireEvent } from "@testing-library/react-native";
import SectionAccordion from "./SectionAccordion";
import { checkNotifications } from 'react-native-permissions';

jest.mock('@react-native-firebase/app', () => ({
  getApp: jest.fn(() => ({
    name: '[DEFAULT]',
  })),
}));

jest.mock("@/components/ReUsableIcons/ReUsableIcons", () => ({
  ArrowUp: () => <div>ArrowUp</div>,
  ArrowDown: () => <div>ArrowDown</div>,
}));

jest.mock("react-native-vector-icons/MaterialIcons", () => "MaterialIcons");
jest.mock(
  "react-native-vector-icons/MaterialCommunityIcons",
  () => "MaterialCommunityIcons"
);

jest.mock("pantry-design-system", () => ({
  useTheme: jest.fn(() => ({
    theme: {
      colors: { pdsThemeColorBackgroundPrimary: "#ffffff" },
      fonts: { pdsGlobalFontFamilyPoppins: "Poppins" },
      dimensions: { pdsGlobalSpace400: 16 },
      typography: { pdsGlobalFontSize500: 18 },
      borderDimens: { pdsGlobalBorderRadius300: 8 },
    },
  })),
}));

jest.mock('@react-native-firebase/analytics', () => {
  return jest.fn(() => ({
    logEvent: jest.fn(),
    logScreenView: jest.fn(),
    setUserId: jest.fn(),
    setUserProperties: jest.fn(),
  }));
})

jest.mock('react-native-permissions', () => ({
  checkNotifications: jest.fn(),
  RESULTS: {},
}));

jest.mock('../../../../app/store', () => ({
  store: {
    getState: jest.fn(() => ({
      profile: {
        profile: { id: 'mock-user-id' },
      },
    })),
  },
}));

jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

describe("SectionAccordion Component", () => {
  beforeEach(() => {
    (checkNotifications as jest.Mock).mockResolvedValue({ status: 'granted' });
  });
  it("renders user accordion correctly", () => {
    const { getByTestId, getByText } = render(
      <SectionAccordion heading="Heading" testID="accordion" />
    );

    expect(getByTestId("accordion")).toBeTruthy();
    expect(getByTestId("accordion-arrow")).toBeTruthy();
    expect(getByText("Heading")).toBeTruthy();
  });

  it("should be expanded by default", () => {
    const { getByTestId } = render(
      <SectionAccordion heading="Heading" testID="accordion" />
    );

    expect(getByTestId("accordion")).toBeTruthy();
    expect(getByTestId("accordion-arrow").props.children.type.name).toBe("ChevronDown");
  });

  it("should be collapsed when clicked", () => {
    const { getByTestId } = render(
      <SectionAccordion heading="Heading" testID="accordion" />
    );

    fireEvent.press(getByTestId("accordion-arrow"));
    expect(getByTestId("accordion-arrow").props.children.type.name).toBe("ChevronUp");

    fireEvent.press(getByTestId("accordion-arrow"));
    expect(getByTestId("accordion-arrow").props.children.type.name).toBe("ChevronDown");
  });

  it("should render children when expanded", () => {
    const { getByTestId, queryByTestId } = render(
      <SectionAccordion heading="Heading" testID="accordion">
        <div>Children</div>
      </SectionAccordion>
    );

    expect(getByTestId("accordion")).toBeTruthy();
    expect(getByTestId("accordion-content")).toHaveTextContent("Children");

    fireEvent.press(getByTestId("accordion-arrow"));
    expect(queryByTestId("accordion-content")).toBeFalsy;
  });
});

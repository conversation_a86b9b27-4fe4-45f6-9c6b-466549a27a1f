import { Theme } from "pantry-design-system";
import { StyleSheet } from "react-native";
import { IPAD_WIDTH } from "../../../shared/constants";

export const getStyles = ({
  colors,
  borderDimens,
  fonts,
  typography,
  dimensions,
  borderStyle,
}: Theme) => {
  return StyleSheet.create({});
};

export default ({
  colors,
  borderDimens,
  fonts,
  typography,
  dimensions,
}: Theme) => {
  return StyleSheet.create({
    card1: {
      backgroundColor: colors.pdsThemeColorBackgroundBaseline,
      borderColor: colors.pdsThemeColorOutlineNeutralLow,
      borderWidth: borderDimens.pdsGlobalBorderWidth100,
      borderRadius: borderDimens.pdsGlobalBorderRadius200,
      padding: dimensions.pdsGlobalSpace400,
      rowGap: dimensions.pdsGlobalSpace400,
    },
    containerStyles: {
      flex: 1,
      width: '100%',
      maxWidth: IPAD_WIDTH,
      alignSelf: 'center'
    },
    row: {
      flexDirection: "row",
      alignItems: "center",
      gap: dimensions.pdsGlobalSpace200,
    },

    heading1: {
      color: colors.pdsThemeColorForegroundNeutralHigh,
      fontWeight: typography.pdsGlobalFontWeight50,
      fontSize: typography.pdsGlobalFontSize400,
      fontFamily: fonts.pdsGlobalFontFamilyPoppins,
    },
    receiptIconStyles: {
      width: dimensions.pdsGlobalSizeWidth400,
      height: dimensions.pdsGlobalSizeWidth400,
      tintColor: colors.pdsThemeColorBackgroundPrimary,
    },
    scanpayIconStyles: {
      width: dimensions.pdsGlobalSizeWidth400,
      height: dimensions.pdsGlobalSizeWidth400,
      tintColor: colors.pdsThemeColorBackgroundPrimary,
    },
    externalLinks: {
      gap: dimensions.pdsGlobalSpace600,
    },
    exteranLinkContainer: {
      flexDirection: "row",
      alignItems: "center",
      gap: dimensions.pdsGlobalSpace100,
    },
    sectionContainer: {
      gap: dimensions.pdsGlobalSpace100,
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
    },
    sectionTitle: {
      color: colors.pdsThemeColorForegroundNeutralHigh,
      fontSize: typography.pdsGlobalFontSize300,
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
      fontWeight: typography.pdsGlobalFontWeight600,
    },
    sectionBox: {
      gap: dimensions.pdsGlobalSpace100,
      flex: 1,
    },
    tabletLabel: {
      flex: 0.4,
    },
    tabletRow: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 10,
    },
    accordionContent: {
      paddingHorizontal: dimensions.pdsGlobalSpace600,
      paddingTop: dimensions.pdsGlobalSpace400,
    },
    descriptionContainer: {
      alignItems: 'center',
      gap: dimensions.pdsGlobalSpace600,
      justifyContent: 'center',
      marginHorizontal: dimensions.pdsGlobalSpace200,
      marginTop: dimensions.pdsGlobalSpace200,
    },
    buttonView: {
      gap: dimensions.pdsGlobalSpace500,
      marginBottom: dimensions.pdsGlobalSpace800,
      marginTop: dimensions.pdsGlobalSpace700,
      paddingHorizontal: dimensions.pdsGlobalSpace400,
    },
    buttonContainer: {
      alignItems: 'center',
      backgroundColor: colors.pdsThemeColorBackgroundBaseline,
      borderColor: colors.pdsThemeColorBackgroundNeutralLow,
      borderRadius: borderDimens.pdsGlobalBorderRadius1000,
      borderWidth: borderDimens.pdsGlobalBorderWidth100,
      flexDirection: 'row',
      height: dimensions.pdsGlobalSpace1000,
      justifyContent: 'center',
      width: '100%',
    },
  });
};

import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { useTheme, Text, BottomSheet, Button, Heading } from 'pantry-design-system';
import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  View,
  Text as RNText,
  ScrollView,
  TouchableOpacity,
  AppState,
  Pressable,
  Linking,
  Platform,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { SvgXml } from 'react-native-svg';
import { useDispatch, useSelector } from 'react-redux';

import { PROFILE_ANALYTICS } from '../../../../analytics/AnalyticsConstants';
import { screenViewLog, userActionLogEvent } from '../../../../analytics/AnalyticsUtils';
import { WarningIcon } from '../../../../assets/icons/WarningIcon';
import { externalLink } from '../../../../assets/images/svg/ExternalLink';
import { scanPay, receipt } from '../../../../assets/images/svg/profileIcons';
import CDivider from '../../../../components/divider/CDivider';
import { PageWrapper } from '../../../../components/wrapper/PageWrapper';
import { useAccessibilityFocus } from '../../../providers/AccessibilityFocus';
import {
  URLS,
  SCREENS,
  BENEFIT_RESOURCES,
  PAYROLL,
  TEXTLINK_COLORS,
  TEXT_SIZE,
  TEXTLINK_WEIGHT,
  LINE_HEIGHT,
  TEXT_DECORATION,
  ImportantForAccessibility,
  BUTTON_SIZE,
  TEXT_ALIGN,
  TEXT_WEIGHT,
  HEADING_SIZE,
} from '../../../shared/constants';
import {
  setPreventTabsAccessibility,
  setPreventHeaderAccessibility,
} from '../../../store/reducers/accessibilitySlice';
import { showErrorAlert } from '../../../utils/AppUtils';
import SectionPreference from '../profilePreference/section';

import SectionAccordion from './SectionAccordion';
import getStyles from './Styles';

import type { ProfileState } from '../../../store/reducers/profileSlice';

const HrAndPayroll = () => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const styles = getStyles(theme);
  const navigation = useNavigation();
  const { setLastFocused, setRef, handleScreenFocus, restoreLastFocus } = useAccessibilityFocus();
  const payrolls = PAYROLL(t);
  const benefits = BENEFIT_RESOURCES(t);
  const appState = useRef(AppState.currentState);
  const [externalView, setexternalView] = useState<boolean>(false);
  const [externalUrlObj, setexternalUrlObj] = useState<{ url: string; link?: string }>({
    url: '',
    link: '',
  });
  const insets = useSafeAreaInsets();
  const dispatch = useDispatch();
  const { isTestAccount }: ProfileState = useSelector((state: any) => state.profile);

  useEffect(() => {
    const unsubscribeFocus = navigation.addListener('focus', () => {
      handleScreenFocus(SCREENS.HR_AND_PAYROLL);
    });

    return () => {
      unsubscribeFocus?.();
    };
  }, [navigation]);

  useFocusEffect(
    React.useCallback(() => {
      screenViewLog({ subsection1: PROFILE_ANALYTICS.HR_AND_PAYROLL });
    }, []),
  );

  useEffect(() => {
    // Add an event listener to monitor changes in the app's state (foreground/background)
    const subscription = AppState.addEventListener('change', (nextAppState) => {
      // Check if the app is transitioning from inactive/background to active state
      if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
        screenViewLog({ subsection1: PROFILE_ANALYTICS.HR_AND_PAYROLL });
      }
      // Update the current app state
      appState.current = nextAppState;
    });

    if (isTestAccount)
      showErrorAlert(
        t,
        t('testAccountAlert.title'),
        t('testAccountAlert.message'),
        null,
        null,
        t('testAccountAlert.okButton'),
      );

    // Cleanup function to remove the event listener when the component unmounts
    return () => {
      subscription.remove();
    };
  }, []);

  useEffect(() => {
    dispatch(setPreventTabsAccessibility(externalView));
    dispatch(setPreventHeaderAccessibility(externalView));
  }, [externalView, dispatch]);

  const onPressBtnBenefitRes = (
    url: string,
    titleHeader: string,
    testID: string,
    link: string,
    openInExternalBrowser: boolean,
  ) => {
    setLastFocused(SCREENS.HR_AND_PAYROLL, testID);
    if (openInExternalBrowser && Platform.OS === 'android') {
      setexternalView(true);
      setexternalUrlObj({ url, link });
    } else {
      navigation.navigate(SCREENS.WEBVIEW, {
        url,
        titleHeader,
        previousTab: SCREENS.PROFILE,
      });
      if (link)
        userActionLogEvent(
          PROFILE_ANALYTICS.HR,
          link,
          PROFILE_ANALYTICS.BENEFIT_RESOURCES_ANALYTICS,
        );
    }
  };

  const handleExternalLinkBackPress = () => {
    setexternalView(false);
    setexternalUrlObj({ url: '', link: '' });
    restoreLastFocus(SCREENS.HR_AND_PAYROLL);
  };

  const onPressBtnPayroll = (url: string, titleHeader: string, testID: string, link?: any) => {
    setLastFocused(SCREENS.HR_AND_PAYROLL, testID);
    navigation.navigate(SCREENS.WEBVIEW, {
      url,
      titleHeader,
      previousTab: SCREENS.PROFILE,
    });
    if (link) userActionLogEvent(PROFILE_ANALYTICS.PAYROLL, link, PROFILE_ANALYTICS.PAYROLL);
  };

  return (
    <ScrollView
      showsVerticalScrollIndicator={false}
      style={styles.containerStyles}
      accessibilityElementsHidden={externalView}
      importantForAccessibility={
        externalView ? ImportantForAccessibility.NO_HIDE : ImportantForAccessibility.AUTO
      }
    >
      <PageWrapper testID="page-wrapper">
        <View style={styles.card1}>
          <View style={styles.row}>
            <SvgXml
              testID="receipt-icon"
              xml={receipt}
              style={styles.receiptIconStyles}
              fill={theme.colors.pdsThemeColorBackgroundPrimary}
            />
            <RNText style={styles.heading1}>{t('hrQuickLinks')}</RNText>
          </View>
          <SectionAccordion
            heading={t('benefitResources')}
            testID="benefit-resources"
            accessible={true}
            accessibilityLabel={t('benefitResources')}
            accessibilityHint={t('accessibility.expandORCollapse')}
          >
            <View style={styles.externalLinks}>
              {benefits.map((item) => (
                <TouchableOpacity
                  ref={setRef(item.testID)}
                  key={item.testID}
                  onPress={() =>
                    onPressBtnBenefitRes(
                      item?.url ?? '',
                      item.label,
                      item.testID,
                      item?.link ?? '',
                      item?.openInExternalBrowser,
                    )
                  }
                  accessible={true}
                  accessibilityLabel={t('openExternalLink', {
                    link: item.label,
                  })}
                  accessibilityRole="link"
                  accessibilityHint={t('accessibility.tappingWillOpenWebview')}
                  testID={item.testID}
                >
                  <View style={styles.exteranLinkContainer}>
                    <Text
                      color={TEXTLINK_COLORS.NEUTRAL_HIGH}
                      size={TEXT_SIZE.LARGE}
                      lineHeight={LINE_HEIGHT.LARGE}
                      weight={TEXTLINK_WEIGHT.SEMI_BOLD}
                      text={item.label}
                      textDecoration={TEXT_DECORATION.UNDERLINE}
                      accessible={false}
                    />
                    <SvgXml
                      xml={externalLink}
                      width={16}
                      height={16}
                      testID={`icon-external-link`}
                    />
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          </SectionAccordion>
          <CDivider />
          <SectionPreference
            ref={setRef('my-ACI')}
            heading={t('myACI')}
            onPress={() => onPressBtnPayroll(URLS.MY_ACI, t('myACI'), 'my-ACI')}
            testID="my-ACI"
            accessible={true}
            accessibilityLabel={t('myACI')}
            accessibilityHint={t('accessibility.tappingWillOpenBenefitResourcesLink')}
          />
        </View>

        <View style={styles.card1}>
          <View style={styles.row}>
            <SvgXml
              testID="scan-pay-icon"
              xml={scanPay}
              style={styles.scanpayIconStyles}
              fill={theme.colors.pdsThemeColorBackgroundPrimary}
            />
            <RNText style={styles.heading1}>{t('payroll')}</RNText>
          </View>
          {payrolls.map((item, index) => (
            <React.Fragment key={item.testID}>
              <SectionPreference
                ref={setRef(item.testID)}
                heading={t(item.label)}
                onPress={() =>
                  onPressBtnPayroll(item?.url ?? '', t(item.label), item.testID, item.link ?? '')
                }
                testID={item.testID}
                accessible={true}
                accessibilityLabel={t('openExternalLink', {
                  link: item.label,
                })}
                accessibilityHint={t('accessibility.tappingWillOpenPayrollLink')}
              />

              {index < payrolls.length - 1 && (
                <View
                  style={{
                    width: '100%',
                    height: 1,
                    borderBottomWidth: 1,
                    borderColor: theme.colors.pdsThemeColorOutlineNeutralLow,
                  }}
                />
              )}
            </React.Fragment>
          ))}
        </View>
      </PageWrapper>
      <BottomSheet
        sidePadding
        variant={'Modal'}
        theme={theme}
        testID="welcome-bottom-sheet"
        visibility={externalView}
        closeAccessibilityLabel={t('ada.closeButton')}
        onClose={handleExternalLinkBackPress}
        accessibilityViewIsModal={true}
        importantForAccessibility={ImportantForAccessibility.YES}
        renderHeader={<WarningIcon size={theme.dimensions.pdsGlobalSizeHeight800} />}
        renderContent={
          <View style={styles.descriptionContainer}>
            <Heading
              textAlign={TEXT_ALIGN.CENTER}
              title={t('externalModal.heading')}
              accessibilityLabel={t('externalModal.heading')}
              color={TEXTLINK_COLORS.NEUTRAL_HIGH}
              size={HEADING_SIZE.Medium}
              weight={TEXT_WEIGHT.SEMI_BOLD}
            />
            <Text
              text={t('externalModal.subtext')}
              size={TEXT_SIZE.LARGE}
              textAlign={TEXT_ALIGN.CENTER}
              weight={TEXT_WEIGHT.SEMI_BOLD}
              color={TEXTLINK_COLORS.NEUTRAL_HIGH}
            />
          </View>
        }
        renderAction={
          <View style={[styles.buttonView, { paddingBottom: insets.bottom }]}>
            <Button
              fullWidth
              theme={theme}
              size={BUTTON_SIZE.SMALL}
              label={t('externalModal.continueInExternal')}
              accessibilityHint={''}
              accessible
              accessibilityRole="none"
              accessibilityLabel={t('externalModal.continueInExternalADALabel')}
              onPress={() => {
                if (externalUrlObj?.link)
                  userActionLogEvent(
                    PROFILE_ANALYTICS.HR,
                    externalUrlObj?.link,
                    PROFILE_ANALYTICS.BENEFIT_RESOURCES_ANALYTICS,
                  );
                Linking.openURL(externalUrlObj?.url);
                handleExternalLinkBackPress();
              }}
            />
            <Pressable
              onPress={handleExternalLinkBackPress}
              style={styles.buttonContainer}
              accessible={true}
              accessibilityHint={''}
              accessibilityLabel={t('ada.backButton')}
            >
              <Text
                text={t('externalModal.back')}
                size={TEXT_SIZE.LARGE}
                color={theme.colors.pdsThemeColorBackgroundPrimary}
                weight={TEXT_WEIGHT.SEMI_BOLD}
                textAlign={TEXT_ALIGN.CENTER}
              />
            </Pressable>
          </View>
        }
      />
    </ScrollView>
  );
};

export default HrAndPayroll;

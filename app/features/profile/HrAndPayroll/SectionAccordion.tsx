import { View, Text, TouchableOpacity } from "react-native";
import React, { useState } from "react";
import getStyles from "./Styles";
import { useTheme } from "pantry-design-system";
import { ChevronUp } from "../../../../assets/icons/ChevronUp";
import { ChevronDown } from "../../../../assets/icons/ChevronDown";
import { userActionLogEvent } from "../../../../analytics/AnalyticsUtils";
import { PROFILE_ANALYTICS } from "../../../../analytics/AnalyticsConstants";

interface PropsTypes {
  heading: string;
  testID?: string;
  accessible?: boolean;
  accessibilityLabel?: string;
  accessibilityHint?: string;
  isIpad?: boolean | undefined;
  children?: React.ReactNode;
}

export default function SectionAccordion(props: PropsTypes) {
  const [expanded, setExpanded] = useState(true);

  const {
    children,
    heading,
    testID = "accordion-container",
    accessibilityLabel,
    accessibilityHint,
    isIpad,
  } = props;
  const { theme } = useTheme();
  const styles = getStyles(theme);

  const onPressExpand = () => {
    setExpanded(!expanded);
    const action = expanded ? "" : PROFILE_ANALYTICS.BENEFIT_RESOURCES_ANALYTICS_EXPAND;
    userActionLogEvent(PROFILE_ANALYTICS.HR, action, PROFILE_ANALYTICS.BENEFIT_RESOURCES_ANALYTICS)
  };

  return (
    <View testID={testID}>
      <TouchableOpacity
        onPress={() => onPressExpand()}
        style={styles.sectionContainer}
        accessibilityRole={"button"}
        accessible={true}
        accessibilityLabel={accessibilityLabel}
        accessibilityHint={accessibilityHint}
      >
        <View style={[isIpad ? styles.tabletRow : styles.sectionBox]}>
          <Text
            style={[isIpad ? styles.tabletLabel : styles.sectionTitle]}
            accessibilityLabel={heading}
          >
            {heading}
          </Text>
        </View>
        <View testID="accordion-arrow">
          {expanded ? <ChevronDown color={theme.colors.pdsThemeColorForegroundNeutralHigh} size={theme.dimensions.pdsGlobalSizeHeight400} />
            : <ChevronUp color={theme.colors.pdsThemeColorForegroundNeutralHigh} size={theme.dimensions.pdsGlobalSizeHeight400} />}
        </View>
      </TouchableOpacity>
      {expanded && <View style={styles.accordionContent} testID="accordion-content">{children}</View>}
    </View>
  );
}

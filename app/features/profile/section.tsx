import { Heading, Text, useTheme } from 'pantry-design-system';
import React, { forwardRef } from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity, View, Platform } from 'react-native';
import { SvgXml } from 'react-native-svg';

import { ChevronRight } from '../../../assets/icons/ChevronRight';
import { ADA_ROLES, HEADING_SIZE, TEXT_ALIGN, TEXT_PANTRY_COLORS, TEXT_SIZE, TEXT_WEIGHT, TEXTLINK_COLORS } from '../../shared/constants';

import getStyles from './styles';

import type { AccessibilityRole } from 'react-native';

interface SectionProps {
  testID?: string;
  svgXml: string;
  heading?: string;
  text?: string;
  onPress: () => void;
  accessibilityRole?: AccessibilityRole;
  headingAccessibilityLabel?: string;
  customStyles?: any;
  pdsHeading?: string;
}

const Section = forwardRef<View, SectionProps>(
  (
    {
      svgXml,
      testID,
      heading,
      text,
      onPress,
      accessibilityRole = ADA_ROLES.BUTTON,
      headingAccessibilityLabel,
      customStyles,
      pdsHeading,
    },
    ref,
  ) => {
    const { theme } = useTheme();
    const styles = getStyles(theme);
    const { t } = useTranslation();
    return (
      <TouchableOpacity
        ref={ref}
        accessibilityRole={accessibilityRole}
        accessibilityLabel={headingAccessibilityLabel}
        accessibilityHint={Platform.OS === 'ios' ? t('ada.doubleTapToActivate') : ''}
        importantForAccessibility="yes"
        testID={testID ?? 'section-touchable'}
        style={[styles.sectionContainer, customStyles]}
        onPress={onPress}
      >
        {svgXml && <SvgXml testID="section-image-icon" xml={svgXml} style={styles.icon} />}
        <View style={styles.textContainer}>
          {pdsHeading &&
            <Heading
              textAlign={TEXT_ALIGN.LEFT}
              title={pdsHeading}
              color={TEXT_PANTRY_COLORS.NEUTRAL_HIGH}
              size={HEADING_SIZE.XSmall}
              accessible={false}
            />
          }
          {heading &&
            <Text
              testID="test-id-section-heading"
              size={TEXT_SIZE.LARGE}
              weight={TEXT_WEIGHT.SEMI_BOLD}
              text={heading}
              accessible={false}
            />
          }

          {text && (
            <Text
              testID="test-id-section-text"
              size={TEXT_SIZE.MEDIUM}
              weight={TEXT_WEIGHT.REGULAR}
              color={TEXTLINK_COLORS.NEUTRAL_LOW}
              text={text}
              accessible={false}
            />
          )}
        </View>
        <ChevronRight
          color={theme.colors.pdsThemeColorForegroundNeutralHigh}
          size={theme.dimensions.pdsGlobalSizeHeight400}
        />
      </TouchableOpacity>
    );
  },
);

export default Section;

import { StyleSheet } from 'react-native';

import type { Theme } from 'pantry-design-system';
import { IPAD_WIDTH } from '../../../shared/constants';

export const getStyles = ({ colors, borderDimens, fonts, typography, dimensions }: Theme) => {
  return StyleSheet.create({
    cardHeading: {
      color: colors.pdsThemeColorForegroundNeutralHigh,
      fontFamily: fonts.pdsGlobalFontFamilyPoppins,
      fontSize: typography.pdsGlobalFontSize400,
      fontWeight: typography.pdsGlobalFontWeight500,
    },
    accContent: {
      alignItems: 'center',
      backgroundColor: colors.pdsThemeColorBackgroundBaseline,
      justifyContent: 'center',
      padding: dimensions.pdsGlobalSpace500,
      paddingBottom: dimensions.pdsGlobalSpace300,
    },
    accHeaderContent: {
      alignItems: 'center',
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    accordionCard: {
      backgroundColor: colors.pdsThemeColorBackgroundBaseline,
      borderColor: colors.pdsThemeColorOutlineNeutralLow,
      borderRadius: borderDimens.pdsGlobalBorderRadius200,
      borderWidth: borderDimens.pdsGlobalBorderWidth100,
      padding: dimensions.pdsGlobalSpace400,
      rowGap: dimensions.pdsGlobalSpace200,
    },

    card1: {
      backgroundColor: colors.pdsThemeColorBackgroundBaseline,
      borderColor: colors.pdsThemeColorOutlineNeutralLow,
      borderRadius: borderDimens.pdsGlobalBorderRadius200,
      borderWidth: borderDimens.pdsGlobalBorderWidth100,
      padding: dimensions.pdsGlobalSpace400,
      rowGap: dimensions.pdsGlobalSpace400,
    },

    chevronContainer: {
      // This is used for the chevron icon view
      width: dimensions.pdsGlobalSizeHeight500,
      alignItems: 'center',
    },

    editBtn: {
      alignItems: 'center',
      alignSelf: 'center',
      borderColor: colors.pdsThemeColorForegroundPrimary,
      borderRadius: borderDimens.pdsGlobalBorderRadius1000,
      borderWidth: borderDimens.pdsGlobalBorderWidth100,
      flexDirection: 'row',
      gap: 4,
      height: 32,
      justifyContent: 'center',
      paddingHorizontal: 8,
      tintColor: colors.pdsThemeColorBackgroundPrimary,
    },
    editBtnText: {
      color: colors.pdsThemeColorForegroundPrimary,
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
      fontSize: typography.pdsGlobalFontSize200,
      fontWeight: typography.pdsGlobalFontWeight600,
    },
    containerStyles: {
      flex: 1,
      width: '100%',
      maxWidth: IPAD_WIDTH,
      alignSelf: 'center'
    },
    heading1: {
      color: colors.pdsThemeColorForegroundNeutralHigh,
      fontFamily: fonts.pdsGlobalFontFamilyPoppins,
      fontSize: typography.pdsGlobalFontSize300,
      fontWeight: typography.pdsGlobalFontWeight500,
      marginLeft: dimensions.pdsGlobalSpace200,
    },
    icon: {
      fontSize: typography.pdsGlobalFontSize550,
      tintColor: colors.pdsThemeColorBackgroundPrimary,
    },
    iconContainer: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    leftContent: {
      alignItems: 'center',
      flexDirection: 'row',
    },
    preferenceLayout: { padding: 16, rowGap: 16 },
    row: {
      alignItems: 'center',
      flexDirection: 'row',
      gap: 8,
    },
    sectionBox: {
      flex: 1,
      gap: dimensions.pdsGlobalSpace100,
    },
    sectionContainer: {
      flexDirection: 'row',
      gap: dimensions.pdsGlobalSpace100,
      justifyContent: 'space-between',
    },
    sectionText: {
      color: colors.pdsThemeColorForegroundNeutralMedium,
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
      fontSize: typography.pdsGlobalFontSize100,
      fontWeight: typography.pdsGlobalFontWeight400,
      lineHeight: dimensions.pdsGlobalSpace500,
    },
    sectionTitle: {
      color: colors.pdsThemeColorForegroundNeutralHigh,
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
      fontSize: typography.pdsGlobalFontSize300,
      fontWeight: typography.pdsGlobalFontWeight600,
      lineHeight: dimensions.pdsGlobalSpace600,
    },
    tabletLabel: {
      flex: 0.4,
    },
    tabletRow: {
      alignItems: 'center',
      flexDirection: 'row',
      marginBottom: 10,
    },
    tabletValue: {
      flex: 2,
    },
  });
};

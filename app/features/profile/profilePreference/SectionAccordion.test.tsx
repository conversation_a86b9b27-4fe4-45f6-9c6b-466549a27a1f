import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { Text } from 'react-native';
import SectionAccordion from './SectionAccordion';

jest.mock('react-native-svg', () => ({
  SvgXml: () => 'SvgMock',
}));

jest.mock("pantry-design-system", () => ({
  useTheme: jest.fn(() => ({
    theme: {
      colors: { pdsThemeColorBackgroundPrimary: "#ffffff" },
      fonts: { pdsGlobalFontFamilyPoppins: "Poppins" },
      dimensions: { pdsGlobalSpace400: 16 },
      typography: { pdsGlobalFontSize500: 18 },
      borderDimens: { pdsGlobalBorderRadius300: 8 },
    },
  })),
}));

jest.mock('../../../../components/ReUsableIcons/ReUsableIcons', () => ({
  ArrowUp: ({ size }: { size: number }) => <div>{`ArrowUp ${size}`}</div>,
  ArrowDown: ({ size }: { size: number }) => <div>{`ArrowDown ${size}`}</div>,
}));

describe("SectionAccordion Component", () => {
  it("renders user accordion correctly", () => {
    const { getByTestId, getByText } = render(
      <SectionAccordion heading="Heading" testID="accordion" />
    );

    expect(getByTestId("accordion")).toBeTruthy();
    expect(getByTestId("accordion-arrow")).toBeTruthy();
    expect(getByText("Heading")).toBeTruthy();
  });

  it("should be expanded by default", () => {
    const { getByTestId } = render(
      <SectionAccordion heading="Heading" testID="accordion" />
    );

    expect(getByTestId("accordion")).toBeTruthy();
    expect(getByTestId("accordion-arrow").props.children.type.name).toBe("ChevronDown");
  });

  it("should be collapsed when clicked", () => {
    const { getByTestId } = render(
      <SectionAccordion heading="Heading" testID="accordion" />
    );

    fireEvent.press(getByTestId("accordion-arrow"));
    expect(getByTestId("accordion-arrow").props.children.type.name).toBe("ChevronUp");

    fireEvent.press(getByTestId("accordion-arrow"));
    expect(getByTestId("accordion-arrow").props.children.type.name).toBe("ChevronDown");
  });

  it('should render children when expanded', () => {
    const { queryByTestId, getByTestId, getByText } = render(
      <SectionAccordion
        heading="Heading"
        subText=""
        accessibilityLabel="Accordion"
        accessibilityHint="Accordion expand toggle"
      >
        <Text>Children</Text>
      </SectionAccordion>
    );

    expect(queryByTestId("accordion-content")).toBeNull(); // not expanded yet

    fireEvent.press(getByText("Heading")); // simulate expand

    const content = getByTestId("accordion-content");
    expect(content).toBeTruthy();
    expect(content).toHaveTextContent("Children");
  });
});
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { useTheme } from 'pantry-design-system';
import React, { useCallback, useRef, useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { View, Text, ScrollView, TouchableOpacity, AppState } from 'react-native';
import { SvgXml } from 'react-native-svg';
import { useDispatch, useSelector } from 'react-redux';

import { LOGIN_ANALYTICS, PROFILE_ANALYTICS } from '../../../../analytics/AnalyticsConstants';
import { screenViewLog, userActionLogEvent } from '../../../../analytics/AnalyticsUtils';
import { user, edit } from '../../../../assets/images/svg/profileIcons';
import CDivider from '../../../../components/divider/CDivider';
import { generateAccessToken } from '../../../config/api/restClient';
import { useAccessibilityFocus } from '../../../providers/AccessibilityFocus';
import { SCREENS, TABLET_LANDSCAPE_WIDTH_STYLE, URLS } from '../../../shared/constants';
import * as AsyncStorage from '../../../store/AsyncStorage';
import { fetchProfileRequest } from '../../../store/reducers/profileSlice';
import { SecureStorage, TokenType } from '../../../store/SecureStorage';
import { showErrorAlert } from '../../../utils/AppUtils';
import { getEmptyStringPlaceholder } from '../../../utils/stringUtils';

import SectionPreference from './section';
import SectionAccordion from './SectionAccordion';
import { getStyles } from './Styles';

import type { ProfileState } from '../../../store/reducers/profileSlice';
import type { RefObject } from 'react';

/**
 * Type for route params for Setting screen.
 *
 * @property {RefObject<View>} [backButtonRef] - Optional ref to the header back button.
 *   - On iOS, this ref is passed from the navigator to allow the screen to set accessibility focus
 *     to the back button when the screen is opened, improving VoiceOver accessibility.
 *   - On Android, this is not used; the header component itself manages focus.
 */
type RouteParams = { backButtonRef?: RefObject<View> };

const ProfilePreference = () => {
  const { theme } = useTheme();
  const styles = getStyles(theme);
  const { t } = useTranslation();
  const { profile, isTestAccount }: ProfileState = useSelector((state: any) => state.profile);
  const isTablet = useSelector((state: any) => state.deviceInfo.isTablet);
  const isLandscape = useSelector((state: any) => state.deviceInfo.isLandscape);
  const dispatch = useDispatch();
  const navigation = useNavigation();
  const hasMounted = useRef(false);
  const secureStorage = new SecureStorage();
  const [phoneNumber, setPhoneNumber] = useState('');
  const [address, setAddress] = useState('');

  const primaryColor = theme.colors.pdsThemeColorBackgroundPrimary; // Primary color from theme
  const appState = useRef(AppState.currentState);

  const { setRef, setLastFocused, handleScreenFocus } = useAccessibilityFocus();

  useEffect(() => {
    const unsubscribeFocus = navigation.addListener('focus', () => {
      handleScreenFocus(SCREENS.PROFILE_PREFERENCE);
    });

    return () => {
      unsubscribeFocus?.();
    };
  }, [navigation]);

  useFocusEffect(
    useCallback(() => {
      // Refresh the content when the screen comes into focus
      screenViewLog({ subsection1: PROFILE_ANALYTICS.PREFERENCE_PAGE_TITLE });

      if (hasMounted.current) {
        const fetchProfile = async () => {
          const requestId = await AsyncStorage.uniqueSessionId();
          const empId = await secureStorage.getToken(TokenType.employeeId);
          dispatch(
            fetchProfileRequest({
              requestId,
              empId: empId,
              fl: 'associateProfile',
            }),
          );
        };

        fetchProfile();
      } else {
        hasMounted.current = true;
      }

      return () => {};
    }, []),
  );

  useEffect(() => {
    // Add an event listener to monitor changes in the app's state (foreground/background)
    const subscription = AppState.addEventListener('change', (nextAppState) => {
      // Check if the app is transitioning from inactive/background to active state
      if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
        screenViewLog({ subsection1: PROFILE_ANALYTICS.PREFERENCE_PAGE_TITLE });
      }
      // Update the current app state
      appState.current = nextAppState;
    });

    // Cleanup function to remove the event listener when the component unmounts
    return () => {
      subscription.remove();
    };
  }, []);

  // Function to handle the press event for editing the profile
  // Navigates to the WebView screen with the provided URL and title
  const handleEditMyACI = () => {
    const url = URLS.EDIT_MY_ACI;
    const titleHeader = t('editInMyACI');
    setLastFocused(SCREENS.PROFILE_PREFERENCE, 'edit-button');
     if (isTestAccount){
      showErrorAlert(
        t,
        t('testAccountAlert.title'),
        t('testAccountAlert.message'),
        null,
        null,
        t('testAccountAlert.okButton'),
      );
     } else navigation.navigate(SCREENS.WEBVIEW, { url, titleHeader, previousTab: SCREENS.PROFILE });
  };

  // Function to handle the press event for setting a new password
  // Navigates to the WebView screen with the provided URL, title, and headers
  const onPressSetPasswordBtn = (url: string, titleHeader: string, subHeader: string) => {
    userActionLogEvent(
      LOGIN_ANALYTICS.FORGOT_PASSWORD,
      LOGIN_ANALYTICS.ATTMEPT,
      LOGIN_ANALYTICS.MY_PROFILE_PREFERENCES,
    );
    const analyticsParam = {
      subsection1: LOGIN_ANALYTICS.FORGOT_PASSWORD,
      subsection2: LOGIN_ANALYTICS.SET_NEW_PWD_LINK,
      eventCategory: LOGIN_ANALYTICS.FORGOT_PASSWORD,
      eventLabel: LOGIN_ANALYTICS.ACCOUNT_SECURITY_PWD_LABEL,
      refreshCategory: LOGIN_ANALYTICS.FORGOT_PASSWORD,
      refreshAction: LOGIN_ANALYTICS.REFRESH,
      refreshLabel: LOGIN_ANALYTICS.MY_PROFILE_PREFERENCES,
      backButtonLabel: LOGIN_ANALYTICS.MY_PROFILE_PREFERENCES,
    };
    setLastFocused(SCREENS.PROFILE_PREFERENCE, 'set-password');
    navigation.navigate(SCREENS.WEBVIEW, {
      url,
      titleHeader,
      previousTab: SCREENS.PROFILE,
      subHeader,
      subsection: analyticsParam,
      leftHeaderBtnPress: handleWebViewBackPress,
    });
  };

  // Function to handle the back press event in the WebView screen
  // Fetches new access and refresh tokens, saves them securely, and navigates back to the Profile screen
  // If an error occurs, navigates to the Session Out screen with appropriate messaging
  const handleWebViewBackPress = async () => {
    generateTokens();
  };

  const generateTokens = async () => {
    try {
      // Fetch new access and refresh tokens
      await generateAccessToken(true);

      // Navigate back to the Profile screen
      navigation.navigate(SCREENS.PROFILE);
    } catch (error) {
      // Handle token fetch failure by navigating to the Session Out screen
      navigation.navigate(SCREENS.SESSION_OUT, {
        headerTitle: t('SessionExpired'),
        buttonText: t('BackToSignIn'),
      });
    }
  };

  return (
    <ScrollView showsVerticalScrollIndicator={false}>
      <View
        style={[
          isTablet && isLandscape ? TABLET_LANDSCAPE_WIDTH_STYLE : styles.containerStyles,
          styles.preferenceLayout,
        ]}
        testID="profile-screen-view"
      >
        <View style={styles.card1}>
          <View style={styles.row}>
            <SvgXml
              xml={user(primaryColor)}
              fill={theme.colors.pdsThemeColorBackgroundPrimary}
              testID="profile-icon"
            />
            <Text style={styles.cardHeading}>{t('personalInfo')}</Text>
          </View>
          <CDivider />
          <SectionPreference
            heading={t('fullName')}
            text={getEmptyStringPlaceholder(profile?.names?.[0]?.displayName ?? '')}
            testID="full-name"
            isIpad={isTablet ? true : undefined}
          />
          <SectionPreference
            heading={t('dateOfBirth')}
            text={getEmptyStringPlaceholder(profile?.dateOfBirth)}
            testID="date-of-birth"
            isIpad={isTablet ? true : undefined}
          />
          <SectionPreference
            heading={t('phoneNumber')}
            text={getEmptyStringPlaceholder(phoneNumber)} //TODO phone number key not available in current xAPI response
            testID="phone-number"
            isIpad={isTablet ? true : undefined}
          />
          <SectionPreference
            heading={t('email')}
            text={getEmptyStringPlaceholder(profile?.emails?.[0]?.emailAddress)}
            testID="email"
            isIpad={isTablet ? true : undefined}
          />
          <SectionPreference
            heading={t('address')}
            text={getEmptyStringPlaceholder(address)} //TODO address key not available in current xAPI response
            testID="address"
            isIpad={isTablet ? true : undefined}
          />
          <TouchableOpacity
            ref={setRef('edit-button')}
            style={styles.editBtn}
            testID="edit-button"
            onPress={handleEditMyACI}
            accessibilityRole="button"
            accessible={true}
            accessibilityHint="Tapping will navigate you to the edit profile screen"
            accessibilityLabel={t('editInMyACI')}
          >
            <SvgXml xml={edit} fill={theme.colors.pdsThemeColorBackgroundPrimary} />
            <Text style={styles.editBtnText}>{t('editInMyACI')} </Text>
          </TouchableOpacity>
        </View>
        <SectionAccordion
          heading={t('accountSecurity')}
          subText={t('accountSecurityDescription')}
          testID="account-security"
          accessible={true}
          accessibilityLabel={t('accountSecurity')}
          accessibilityHint={t('accountSecurityAccessibilityHint')}
        >
          <CDivider />
          <View style={styles.accContent}>
            <SectionPreference
              ref={setRef('set-password')}
              heading={t('setAnewPassword')}
              onPress={() =>
                onPressSetPasswordBtn(
                  URLS.UPDATE_PASSWORD,
                  t('setAnewPassword'),
                  t('passwordResetDescription'),
                )
              }
              testID="set-password"
              accessible={true}
              accessibilityLabel={t('setAnewPassword')}
              accessibilityHint={t('setAnewPasswordAccessibilityHint')}
            />
          </View>
        </SectionAccordion>
      </View>
    </ScrollView>
  );
};

export default ProfilePreference;

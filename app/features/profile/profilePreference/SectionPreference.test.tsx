import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import SectionPreference from './section';

jest.mock('@/components/ReUsableIcons/ReUsableIcons', () => ({
  ArrowRight: () => <></>,
}));

jest.mock("pantry-design-system", () => ({
  useTheme: jest.fn(() => ({
    theme: {
      colors: { pdsThemeColorBackgroundBaseline: "#ffffff" },
      fonts: { pdsGlobalFontFamilyPoppins: "Poppins" },
      dimensions: { pdsGlobalSpace400: 16 },
      typography: { pdsGlobalFontSize500: 18 },
      borderDimens: { pdsGlobalBorderRadius300: 8 },
    }
  })),
}));

describe('SectionPreference Component', () => {
  it('renders correctly with heading and text', () => {
    const { getByText } = render(
      <SectionPreference heading="Test Heading" text="Test Text" />
    );
    expect(getByText('Test Heading')).toBeTruthy();
    expect(getByText('Test Text')).toBeTruthy();
  });

  it('renders correctly with only heading and no text', () => {
    const { getByText, queryByText } = render(
      <SectionPreference heading="Test Heading" />
    );
    expect(getByText('Test Heading')).toBeTruthy();
    expect(queryByText('Test Text')).toBeNull();
  });

  it('triggers onPress when pressed', () => {
    const onPressMock = jest.fn();
    const { getByTestId } = render(
      <SectionPreference heading="Test Heading" onPress={onPressMock} />
    );
    fireEvent.press(getByTestId('section-container'));
    expect(onPressMock).toHaveBeenCalledTimes(1);
  });

  it('does not trigger onPress when disabled', () => {
    const onPressMock = jest.fn();
    const { getByTestId } = render(
      <SectionPreference heading="Test Heading" />
    );
    fireEvent.press(getByTestId('section-container'));
    expect(onPressMock).not.toHaveBeenCalled();
  });

  it('renders ArrowRight when onPress is passed', () => {
    const { getByTestId } = render(
      <SectionPreference heading="Test Heading" onPress={() => { }} />
    );
    expect(getByTestId('section-click')).toBeTruthy();
  });

  it('renders correctly with empty text', () => {
    const { getByText } = render(<SectionPreference heading="Full Name" text="" />);
    expect(getByText('Full Name')).toBeTruthy();
  });
});

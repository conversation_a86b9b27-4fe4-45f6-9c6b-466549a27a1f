import React from "react";
import { render, fireEvent } from "@testing-library/react-native";
import { Provider } from "react-redux";
import configureStore from "redux-mock-store";
import ProfilePreference from "./ProfilePreference";
import { SCREENS, TABLET_LANDSCAPE_WIDTH_STYLE, URLS } from '../../../shared/constants';
import { checkNotifications } from 'react-native-permissions';
import { useNavigation } from "@react-navigation/native";

// Mocks for navigation and accessibility
jest.mock('../../../providers/AccessibilityFocus', () => ({
  useAccessibilityFocus: () => ({
    setRef: jest.fn(),
    setLastFocused: jest.fn(),
    restoreLastFocus: jest.fn(),
    setNavigatingBack: jest.fn(),
  }),
  AccessibilityFocusProvider: ({ children }) => children,
}));

jest.mock('@react-native-firebase/app', () => ({
  getApp: jest.fn(() => ({
    name: '[DEFAULT]',
  })),
}));
jest.mock('react-native-permissions', () => ({
  checkNotifications: jest.fn(),
  RESULTS: {},
}));
jest.mock('../../../../app/store', () => ({
  store: {
    getState: jest.fn(() => ({
      profile: {
        profile: { id: 'mock-user-id' },
      },
    })),
  },
}));
jest.mock('@react-native-firebase/analytics', () => {
  return jest.fn(() => ({
    logEvent: jest.fn(),
    logScreenView: jest.fn(),
    setUserId: jest.fn(),
    setUserProperties: jest.fn(),
  }));
});

const mockNavigate = jest.fn();
const mockSetParams = jest.fn();

jest.mock('@react-navigation/native', () => ({
  useNavigation: jest.fn(() => ({
    navigate: mockNavigate,
    setParams: mockSetParams,
  })),
  useFocusEffect: jest.fn((callback) => {
    callback();
  }),
  useRoute: jest.fn(() => ({
    params: {},
  })),
}));
jest.mock("./SectionAccordion", () => "SectionAccordion");

jest.mock('@/components/ReUsableIcons/ReUsableIcons', () => ({
  ArrowRight: () => <></>,
}));

jest.mock('../../../store/AsyncStorage', () => ({
  uniqueSessionId: jest.fn(),
}));

jest.mock("react-native-vector-icons/MaterialIcons", () => "MaterialIcons");
jest.mock("react-native-vector-icons/MaterialCommunityIcons", () => "MaterialCommunityIcons");

jest.mock("pantry-design-system", () => ({
  useTheme: jest.fn(() => ({
    theme: {
      colors: { pdsThemeColorBackgroundPrimary: "#ffffff" },
      fonts: { pdsGlobalFontFamilyPoppins: "Poppins" },
      dimensions: { pdsGlobalSpace400: 16 },
      typography: { pdsGlobalFontSize500: 18 },
      borderDimens: { pdsGlobalBorderRadius300: 8 },
    }
  })),
}));

jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

describe("ProfilePreference Component", () => {
  const mockStore = configureStore([]);
  const initialState = {
    profile: {
      profile: {
        names: [{ firstName: "John", lastName: "Doe", displayName: "John Doe" }],
        personNumber: "**********",
        dateOfBirth: "01/01/1990",
        phoneNumber: "**********",
        emails: [
          { emailAddress: "<EMAIL>" }
        ],
        address: "123 Main St"
      },
    },
    deviceInfo: {
      isTablet: false,
      isLandscape: true
    }
  };
  beforeEach(() => {
    useNavigation.mockReturnValue({ navigate: mockNavigate, addListener: jest.fn() });
    (checkNotifications as jest.Mock).mockResolvedValue({ status: 'granted' });
  });

  it("renders user profile information correctly", () => {
    const store = mockStore(initialState);
    const { getByTestId } = render(
      <Provider store={store}>
        <ProfilePreference />
      </Provider>
    );

    expect(getByTestId("full-name")).toBeTruthy();
    expect(getByTestId("date-of-birth")).toBeTruthy();
    expect(getByTestId("phone-number")).toBeTruthy();
    expect(getByTestId("email")).toBeTruthy();
    expect(getByTestId("address")).toBeTruthy();
  });

  it("applies the theme correctly", () => {
    const store = mockStore(initialState);
    const { getByTestId } = render(
      <Provider store={store}>
        <ProfilePreference />
      </Provider>
    );

    const icon = getByTestId("profile-icon");
    expect(icon.props.fill).toBe("#ffffff");
  });
  it('renders SectionPreference components with correct props', () => {
    const store = mockStore(initialState);
    const { getByTestId } = render(
      <Provider store={store}>
        <ProfilePreference />
      </Provider>
    );
    expect(getByTestId('full-name')).toHaveTextContent('fullNameJohn Doe');
    expect(getByTestId('date-of-birth')).toHaveTextContent('dateOfBirth01/01/1990');
    //expect(getByTestId('phone-number')).toHaveTextContent('phoneNumber**********');
    expect(getByTestId('email')).toHaveTextContent('<EMAIL>');
  });

  it('renders TouchableOpacity and responds to press', async () => {
    const store = mockStore(initialState);
    const { getByTestId } = render(
      <Provider store={store}>
        <ProfilePreference />
      </Provider>
    );
    const editButton = getByTestId('edit-button');
    fireEvent.press(editButton);
    expect(mockNavigate).toHaveBeenCalledWith(SCREENS.WEBVIEW, { url: URLS.EDIT_MY_ACI, titleHeader: "editInMyACI", previousTab: SCREENS.PROFILE });
  });

  it("updates screenViewStyles based on isTablet and isLandscape", () => {
    const store = mockStore({
      profile: {
        profile: {
          names: [{ firstName: "John", lastName: "Doe" }],
          personNumber: "**********",
          dateOfBirth: "01/01/1990",
          phoneNumber: "**********",
          emails: [
            { emailAddress: "<EMAIL>" }
          ],
        },
      },
      deviceInfo: {
        isTablet: true,
        isLandscape: true,
      }
    });

    const { getByTestId } = render(
      <Provider store={store}>
        <ProfilePreference />
      </Provider>
    );
    const pageWrapper = getByTestId('profile-screen-view');
    expect(pageWrapper.props.style).toEqual(
      expect.arrayContaining([expect.objectContaining(TABLET_LANDSCAPE_WIDTH_STYLE)])
    );
  });
  it("should render SectionPreference components with correct text", () => {
    const store = mockStore(initialState);
    const { getByTestId } = render(
      <Provider store={store}>
        <ProfilePreference />
      </Provider>
    );
    expect(getByTestId("account-security")).toBeTruthy();
    expect(getByTestId("set-password")).toBeTruthy();
  });
});
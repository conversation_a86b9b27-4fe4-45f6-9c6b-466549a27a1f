import { Heading, useTheme } from 'pantry-design-system';
import React, { forwardRef } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';

import { ChevronRight } from '../../../../assets/icons/ChevronRight';

import { getStyles } from './Styles';
import { HEADING_SIZE, TEXT_ALIGN, TEXT_PANTRY_COLORS } from '../../../shared/constants';

interface PropsTypes {
  heading?: string;
  text?: string;
  onPress?: () => void;
  testID?: string;
  accessible?: boolean;
  accessibilityLabel?: string;
  accessibilityHint?: string;
  isIpad?: boolean;
  customStyles?: object;
  accessibilityRole?: string;
  pdsHeading?: string;
}

// Use forwardRef to correctly pass refs
const SectionPreference = forwardRef<TouchableOpacity, PropsTypes>(
  (
    {
      heading,
      text,
      onPress,
      testID = 'section-container',
      accessibilityLabel,
      accessibilityHint,
      accessibilityRole,
      isIpad,
      customStyles,
      pdsHeading,
    },
    ref,
  ) => {
    const { theme } = useTheme();
    const styles = getStyles(theme);

    return onPress ? (
      <TouchableOpacity
        ref={ref}
        onPress={onPress}
        style={[styles.sectionContainer, customStyles]}
        testID={testID}
        accessibilityRole={accessibilityRole ?? 'button'}
        accessible={true}
        accessibilityLabel={accessibilityLabel}
        accessibilityHint={accessibilityHint}
      >
        <View style={isIpad ? styles.tabletRow : styles.sectionBox}>
          {pdsHeading &&
            <Heading
              textAlign={TEXT_ALIGN.LEFT}
              title={pdsHeading}
              color={TEXT_PANTRY_COLORS.NEUTRAL_HIGH}
              size={HEADING_SIZE.XSmall}
              accessible={false}
            />
          }
          {heading &&
            <Text
              style={isIpad ? styles.tabletLabel : styles.sectionTitle}
              accessibilityLabel={heading}
            >
              {heading}
            </Text>
          }
          {text && <Text style={isIpad ? styles.tabletValue : styles.sectionText}>{text}</Text>}
        </View>
        <View style={styles.chevronContainer} testID="section-click">
          <ChevronRight
            color={theme.colors.pdsThemeColorForegroundNeutralHigh}
            size={theme.dimensions.pdsGlobalSizeHeight400}
          />
        </View>
      </TouchableOpacity>
    ) : (
      <View style={isIpad ? styles.tabletRow : styles.sectionBox} testID={testID}>
        <Text style={isIpad ? styles.tabletLabel : styles.sectionTitle}>{heading}</Text>
        {text && <Text style={isIpad ? styles.tabletValue : styles.sectionText}>{text}</Text>}
      </View>
    );
  },
);

export default SectionPreference;

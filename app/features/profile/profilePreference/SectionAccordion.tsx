import React, { useState } from 'react';
import { View, Text, TouchableOpacity, LayoutAnimation } from 'react-native';
import { SvgXml } from 'react-native-svg';
import { accountSecurity } from "../../../../assets/images/svg/profileIcons";
import { useTheme } from 'pantry-design-system'
import { getStyles } from "./Styles";
import { ChevronUp } from "../../../../assets/icons/ChevronUp";
import { ChevronDown } from "../../../../assets/icons/ChevronDown";

const ACC_ARROW_SIZE = 28;
interface PropsTypes {
    heading: string;
    subText?: string;
    testID?: string;
    accessible?: boolean;
    accessibilityLabel?: string;
    accessibilityHint?: string;
    children?: React.ReactNode;
}

const SectionAccordion = (props: PropsTypes) => {
    const [expanded, setExpanded] = useState(false);
    const {
        children,
        heading,
        subText,
        testID = "accordion-container",
        accessibilityLabel,
        accessibilityHint,
    } = props;
    const { theme } = useTheme();
    const styles = getStyles(theme);

    const toggleExpand = () => {
        LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
        setExpanded(!expanded);
    };

    return (
        <View style={styles.accordionCard} testID={testID}>
            <TouchableOpacity onPress={toggleExpand} accessibilityRole={"button"} accessible={true} accessibilityLabel={accessibilityLabel} accessibilityHint={accessibilityHint}>
                <View style={styles.accHeaderContent}>
                    <View style={styles.leftContent}>
                        <SvgXml
                            xml={accountSecurity}
                            fill={theme.colors.pdsThemeColorBackgroundPrimary}
                        />
                        <Text style={styles.heading1} accessibilityLabel={heading}>{heading}</Text>
                    </View>
                    <View style={styles.iconContainer} testID="accordion-arrow">{expanded ? <ChevronUp color={theme.colors.pdsThemeColorForegroundNeutralHigh} size={theme.dimensions.pdsGlobalSizeHeight400} />
                        : <ChevronDown color={theme.colors.pdsThemeColorForegroundNeutralHigh} size={theme.dimensions.pdsGlobalSizeHeight400} />}</View>
                </View>
            </TouchableOpacity>
            <Text style={styles.sectionText}>{subText}</Text>
            {expanded && (<View testID="accordion-content">{children}</View>)}
        </View>
    );
};

export default SectionAccordion;
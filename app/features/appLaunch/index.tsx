import FastImage from '@d11/react-native-fast-image';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { useTheme } from 'pantry-design-system';
import React, { useEffect, useCallback, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { View, Text, ActivityIndicator } from 'react-native';
import Device from 'react-native-device-info';
import { SvgXml } from 'react-native-svg';
import { useDispatch, useSelector } from 'react-redux';

import ABAuditEngine from '../../../analytics/ABAuditEngine';
import { APPD_PAGE_VIEW_METRIC_CONSTANTS } from '../../../analytics/AppDynamicsConstants';
import { albertsonsLogo, albertsonsLogoTablet } from '../../../assets/images/svg/AlbertsonsLogo';
import AuthorableBG from '../../../components/AuthorableBg';
import { Colors } from '../../../constants/Colors';
import { setNavigationRef, setTranslation } from '../../config/api/errorHandler';
import { ADAM_FULL_SCALE, BANNER_THEMES, FALLBACK_BANNER, TIMEOUT } from '../../shared/constants';
import { fetchAuthorableContentRequest } from '../../store/reducers/authorableContentSlice';
import { clearEmployeeSchedule } from '../../store/reducers/employeeScheduleSlice';
import { fetchfeatureFlagRequest } from '../../store/reducers/featureFlagSlice';
import { clearProfile } from '../../store/reducers/profileSlice';
import { setDurationInMinutes } from '../../store/reducers/shiftSlice';

import getStyes from './styles';

import type { AuthorableContent } from '../../misc/models/authorableModels';
import { getOnboardingStep } from '../../store/AsyncStorage';
import { SecureStorage } from '../../store/SecureStorage';
import type { BannerTypes } from 'pantry-design-system/types';

const AppLaunch = () => {
  const dispatch = useDispatch();
  const navigation = useNavigation();
  const {
    loading,
    error,
    authorableContent,
  }: { loading: boolean; error: null | string; authorableContent: AuthorableContent } = useSelector(
    (state: any) => state.authorableContent,
  );
  const profileData = useSelector((state: any) => state.profile);
  const [isLaunchCalled, setLaunchCalled] = React.useState(false); // State to track if launch has been called
  const { t } = useTranslation();

  const { theme, switchTheme } = useTheme(); // Get the current theme and switchTheme function
  const startTimeRef = useRef<number | null>(null);
  const handleSwitchTheme = (bannerTheme: BannerTypes) => {
    switchTheme(bannerTheme); // Switch theme based on selected banner
  };

  setTranslation(t);
  setNavigationRef(navigation);

  /**
   * useEffect hook to fetch authorable content from AEM through our state and updates in store and navigate to different screens.
   *
   * This effect runs only once when the component is mounted.
   * - Dispatches a Redux action (`fetchAuthorableContentRequest`) to fetch AEM content and updates to authorableContent State.
   * - Waits for 2 seconds and then navigates to `authStack`:
   *
   * @see {useDispatch} to dispatch actions to Redux.
   * @see {useNavigation} to handle navigation between screens.
   * @see {setTimeout} to delay the navigation decision by 2 seconds.
   * @see {handleSwitchTheme} to handle the theme switch, which we use to "Albertsons" as the default
   *
   */
  useEffect(() => {
    dispatch(fetchAuthorableContentRequest());
    dispatch(clearProfile());
    getOnboardingStep().then((step) => {
      // If onboarding step is 0, clear all secure storage for a fresh start.
      if (step === 0) {
        const secureStoreage = new SecureStorage();
        secureStoreage.clearAll();
      }
    });

    handleSwitchTheme(profileData != null ? profileData?.banner : BANNER_THEMES[1].banner);
    dispatch(setDurationInMinutes(5)); // Intializing the shift time remaining minutes
    dispatch(clearEmployeeSchedule());
  }, []);

  useEffect(() => {
    if (loading === false && error == null && authorableContent?.loginPage?.bgImage) {
      if (!isLaunchCalled) {
        setLaunchCalled(true);
        preLoadImage(authorableContent);

        setTimeout(() => {
          navigation.replace('authStack');
        }, TIMEOUT.APP_LAUNCH);
      }
    }
  }, [loading, error, authorableContent]);

  /**
   * useEffect hook to fetch feature flags and clear the profile state.
   *
   * This effect runs only once when the component is mounted.
   * - Fetches a unique session ID from AsyncStorage.
   * - Dispatches a Redux action (`fetchfeatureFlagRequest`) to fetch feature flags based on the session ID and banner.
   * - Clears the profile state by dispatching the `clearProfile` action.
   *
   * @see {AsyncStorage.uniqueSessionId} to fetch a unique session ID.
   * @see {fetchfeatureFlagRequest} to fetch feature flags from the backend.
   * @see {clearProfile} to reset the profile state in the Redux store.
   * @see {useDispatch} to dispatch actions to Redux.
   *
   * @example
   * // Example of the dispatched action:
   * fetchfeatureFlagRequest({
   *   requestId: "unique-session-id",
   *   banner: FALLBACK_BANNER //FALLBACK_BANNER as "albertsons"
   * });
   */
  useEffect(() => {
    dispatch(
      fetchfeatureFlagRequest({
        banner: profileData?.banner || FALLBACK_BANNER,
      }),
    );
  }, []);

  useFocusEffect(
    useCallback(() => {
      startTimeRef.current = Date.now(); // Store the start time in the ref
      console.log('Splash Screen Start Time:', Date.now()); // Log the start time
      // Start a custom timer for analytics
      ABAuditEngine.customTimerStart(APPD_PAGE_VIEW_METRIC_CONSTANTS.SPLASH_SCREEN_VIEW_METRIC);
      return () => {
        startTimeRef.current = null; // Clear the timer reference when the screen is blurred
      };
    }, []),
  );

  const baseImagePath = process.env.ADAM_IMG_PATH ?? '';

  const preLoadImage = (authorableContent: AuthorableContent) => {
    try {
      if (authorableContent && authorableContent.loginPage) {
        const { bgImage, bgImageTabletLandscape, bgImageTabletPortrait } =
          authorableContent?.loginPage;

        if (isTablet) {
          FastImage.preload([
            {
              uri: baseImagePath + bgImageTabletLandscape + ADAM_FULL_SCALE,
              cache: FastImage.cacheControl.cacheOnly,
              priority: FastImage.priority.high,
            },
            {
              uri: baseImagePath + bgImageTabletPortrait + ADAM_FULL_SCALE,
              cache: FastImage.cacheControl.cacheOnly,
              priority: FastImage.priority.high,
            },
          ]);
        } else {
          FastImage.preload([
            {
              uri: baseImagePath + bgImage + ADAM_FULL_SCALE,
              cache: FastImage.cacheControl.cacheOnly,
              priority: FastImage.priority.high,
            },
          ]);
        }
      }
    } catch (error) {
      console.error('Error in AppLaunch useEffect:', error);
    }
  };

  useEffect(() => {
    if ((!loading && (error == null || !error)) || (!loading && error)) {
      const startTime = startTimeRef.current;
      if (startTime) {
        const duration = Date.now() - startTime; // Calculate the duration
        console.log(`Splash Screen Timer Ended. Load Time: ${duration}ms`); // Log the load time
        // End the custom timer for analytics
        ABAuditEngine.customTimerEnd(APPD_PAGE_VIEW_METRIC_CONSTANTS.SPLASH_SCREEN_VIEW_METRIC);
      }
    }
  }, [loading, error]);

  const styles = theme != null ? getStyes(theme) : null;

  const isTablet = useSelector((state: any) => state.deviceInfo?.isTablet);
  const isLandscape = useSelector((state: any) => state.deviceInfo.isLandscape);

  useEffect(() => { }, [isLandscape, isTablet]);

  return (
    <AuthorableBG colors={[Colors.authorableBgColorStart]}>
      <View style={styles?.container}>
        <View style={styles?.loginContainer}>
          <SvgXml xml={isTablet ? albertsonsLogoTablet : albertsonsLogo} />
          <View style={styles?.ldapContainer}>
            <Text style={Device.isTablet() ? styles?.welcomeTextTablet : styles?.welcomeText}>
              {t('welcomeAssociate')}
            </Text>
          </View>
        </View>
      </View>
      {loading && (
        <View style={styles?.loadingContainer}>
          <ActivityIndicator size={'small'} color={theme.colors.pdsThemeColorBackgroundBaseline} />
        </View>
      )}
    </AuthorableBG>
  );
};
export default AppLaunch;

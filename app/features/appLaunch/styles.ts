import { Theme } from 'pantry-design-system';
import { Dimensions, StyleSheet, TextStyle, ViewStyle } from "react-native";
import Device from 'react-native-device-info';

const { width, height } = Dimensions.get('window')

const isPortrait = () => {
    const dim = Dimensions.get('screen');
    return dim.height >= dim.width;
};

const getStyles = ({ colors, fonts, borderDimens, dimensions, typography }: Theme) => {
    const styles = StyleSheet.create({
        container: {
            flex: 1,
            alignItems: 'center',
            justifyContent: 'center',
            width: width,
            height: height,
            padding: 16,
        } as ViewStyle,
        loginContainer: {
            alignSelf: 'center',
            width: '100%',
            alignItems: 'center',
            justifyContent: 'space-between',
            position: 'absolute',
            bottom: Device.isTablet()
                ? isPortrait() ? 180 : 110
                : 96,
            left: 16,
            right: 16,
            minHeight: 116,
        } as ViewStyle,
        ldapContainer: {
            width: '100%',
            justifyContent: 'center',
            alignContent: 'center',
        } as ViewStyle,
        welcomeText: {
            fontSize: typography.pdsGlobalFontSize600,
            fontFamily: fonts.pdsGlobalFontFamilyPoppins,
            alignSelf: 'center',
            fontWeight: typography.pdsGlobalFontWeight500,
            color: colors.pdsThemeColorForegroundNeutralHighInverse,
            lineHeight: 33.6,
        } as TextStyle,
        welcomeTextTablet: {
            fontSize: typography.pdsGlobalFontSize800,
            fontFamily: fonts.pdsGlobalFontFamilyPoppins,
            alignSelf: 'center',
            fontWeight: typography.pdsGlobalFontWeight500,
            color: colors.pdsThemeColorForegroundNeutralHighInverse,
            lineHeight: 40.8,
            top: dimensions.pdsGlobalSpace1000,
        } as TextStyle,
        loadingContainer: {
            position: 'absolute',
            bottom: 16,
            left: 0,
            right: 0,
            alignItems: 'center',
            justifyContent: 'center',
        } as ViewStyle,

    });
    return styles
}

export default getStyles;
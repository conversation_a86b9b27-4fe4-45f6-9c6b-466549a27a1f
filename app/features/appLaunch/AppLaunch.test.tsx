import React from "react";
import { render, waitFor, act } from "@testing-library/react-native";
import { useDispatch, useSelector } from "react-redux";
import { useNavigation } from "@react-navigation/native";
import AppLaunch from "./index";
import { useTheme } from 'pantry-design-system'
import * as AsyncStorage from "../../../app/store/AsyncStorage";
import { APPD_PAGE_VIEW_METRIC_CONSTANTS } from "../../../analytics/AppDynamicsConstants";
import { customTimerStart, customTimerEnd } from "../../../analytics/ABAuditEngine";
import { SecureStorage } from "../../../app/store/SecureStorage";

jest.mock("react-redux", () => ({
  useDispatch: jest.fn(),
  useSelector: jest.fn(),
}));

jest.mock("./styles", () => jest.fn(() => ({
  container: {},
  loginContainer: {},
  ldapContainer: {},
  welcomeText: {},
})));

jest.mock('../../../app/store/AsyncStorage', () => ({
  uniqueSessionId: jest.fn(),
  getOnboardingStep: jest.fn(), // <-- Add this line
}));
jest.spyOn(AsyncStorage, 'uniqueSessionId').mockResolvedValue('12345');
jest.spyOn(AsyncStorage, 'getOnboardingStep').mockResolvedValue(1); // Default: onboarding step is not 0

jest.mock('react-native-device-info', () => ({
  isTablet: jest.fn(() => false),
}));

jest.mock("@react-navigation/native", () => ({
  useNavigation: jest.fn(),
  useFocusEffect: jest.fn((effect) => {
    effect(); // Execute immediately
  }),
}));

jest.mock('../../../analytics/ABAuditEngine', () => ({
  customTimerStart: jest.fn(),
  customTimerEnd: jest.fn(),
}));

jest.mock("pantry-design-system", () => ({
  useTheme: jest.fn(() => ({
    theme: {
      container: {},
      welcomeText: {},
    },
    switchTheme: jest.fn(),
  })),
}));

describe("AppLaunch Component", () => {
  let mockDispatch: jest.Mock;
  let mockNavigation: any;

  beforeEach(() => {
    mockDispatch = jest.fn();
    mockNavigation = {
      replace: jest.fn(),
    };
    (useDispatch as jest.Mock).mockReturnValue(mockDispatch);
    (useNavigation as jest.Mock).mockReturnValue(mockNavigation);
    (useSelector as jest.Mock).mockImplementation((selector) =>
      selector({
        profile: { banner: "test-banner" },
        authorableContent: {
          loading: false,
          error: null,
          authorableContent: {
            loginPage: {
              bgImage: 'https://example.com/bg.jpg',
            },
          },
        },
        deviceInfo: { isTablet: false, isLandscape: false },
      })
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should render correctly", () => {
    const { getByText } = render(<AppLaunch />);
    expect(getByText(/welcomeAssociate/i)).toBeTruthy();
  });

  it("should navigate to the correct stack based on profile state", async () => {
    jest.useFakeTimers();
    render(<AppLaunch />);

    // Advance timers to trigger delayed navigation
    await act(async () => {
      jest.advanceTimersByTime(2000);
    });

    // Wait for navigation.replace to be called
    await waitFor(() => {
      expect(mockNavigation.replace).toHaveBeenCalledWith("authStack");
    }, { timeout: 3000 });

    jest.useRealTimers();
  });

  it("should switch theme based on the banner", () => {
    const mockSwitchTheme = jest.fn();
    (useTheme as jest.Mock).mockReturnValue({
      theme: {},
      switchTheme: mockSwitchTheme,
    });

    render(<AppLaunch />);
    expect(mockSwitchTheme).toHaveBeenCalledWith("test-banner");
  });

  it("calls ABAuditEngine timers on focus and after interactions", async () => {
    jest.useFakeTimers();

    render(<AppLaunch />);

    await act(async () => {
      jest.runAllTimers();
    });

    expect(customTimerStart).toHaveBeenCalledWith(APPD_PAGE_VIEW_METRIC_CONSTANTS.SPLASH_SCREEN_VIEW_METRIC);
    expect(customTimerEnd).toHaveBeenCalledWith(APPD_PAGE_VIEW_METRIC_CONSTANTS.SPLASH_SCREEN_VIEW_METRIC);

    jest.useRealTimers();
  });

  it("should clear secure storage if onboarding step is 0", async () => {
    // Set onboarding step to 0 for this test
    (AsyncStorage.getOnboardingStep as jest.Mock).mockResolvedValueOnce(0);
    const clearAllSpy = jest.spyOn(SecureStorage.prototype, "clearAll").mockImplementation(jest.fn());

    render(<AppLaunch />);
    // Wait for useEffect to run
    await waitFor(() => {
      expect(clearAllSpy).toHaveBeenCalled();
    });

    clearAllSpy.mockRestore();
  });
});
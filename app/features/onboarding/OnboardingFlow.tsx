/* eslint-disable @typescript-eslint/explicit-module-boundary-types */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { useTheme } from 'pantry-design-system';
import React, { useEffect, useRef, useState } from 'react';
import { View, Animated, AccessibilityInfo, findNodeHandle, Platform } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import OnboardingProgressBar from '../../../components/OnboardingProgressBar';
import { ImportantForAccessibility, ON_BOARDING } from '../../shared/constants';

import OnboardingFour from './onBoardingFour';
import Onboarding from './onBoardingOne';
import OnboardingThree from './onBoardingThree';
import OnboardingTwo from './onBoardingTwo';
import getStyles from './styles';

const OnboardingFlow = ({ route }: any): React.ReactElement => {
  const progressBarRef = useRef(null); // Ref to hold the OnboardingProgressBar component

  const { theme } = useTheme();

  const [currentStep, setCurrentStep] = useState(route.params.step || 1);
  const [modalOpen, setModalOpen] = useState(false);
  const [disableAnimations, setDisableAnimations] = useState(false);
  const styles = getStyles(theme);

  const fadeAnim = useRef(new Animated.Value(0)).current; // ⬅️ New fade-in animation value

  const renderContent = () => {
    switch (currentStep) {
      case 1:
        return <Onboarding setSteps={setCurrentStep} />;
      case 2:
        return <OnboardingTwo setSteps={setCurrentStep} />;
      case 3:
        return (
          <OnboardingThree
            setSteps={setCurrentStep}
            setModalOpen={setModalOpen}
            hideAccessibility={modalOpen}
          />
        );
      case 4:
        return <OnboardingFour setModalOpen={setModalOpen} hideAccessibility={modalOpen} />;
      default:
        return <Onboarding setSteps={setCurrentStep} />;
    }
  };

  // Combined accessibility check
  useEffect(() => {
    let isMounted = true;

    const checkAccessibility = async () => {
      try {
        const [reduceMotionEnabled, screenReaderEnabled] = await Promise.all([
          AccessibilityInfo.isReduceMotionEnabled(),
          AccessibilityInfo.isScreenReaderEnabled(),
        ]);

        if (isMounted) {
          const shouldDisableAnimations = reduceMotionEnabled || screenReaderEnabled;
          setDisableAnimations(shouldDisableAnimations);

          // Immediately set opacity if animations should be disabled
          if (shouldDisableAnimations) {
            fadeAnim.setValue(1);
          }
        }
      } catch (error) {
        // Error
      }
    };

    checkAccessibility();

    // Set up listeners for changes
    const reduceMotionChangedSubscription = AccessibilityInfo.addEventListener(
      'reduceMotionChanged',
      (reduceMotionEnabled) => {
        if (isMounted) {
          setDisableAnimations((prev) => reduceMotionEnabled || prev);
        }
      },
    );

    const screenReaderChangedSubscription = AccessibilityInfo.addEventListener(
      'screenReaderChanged',
      (screenReaderEnabled) => {
        if (isMounted) {
          setDisableAnimations((prev) => screenReaderEnabled || prev);
        }
      },
    );

    return () => {
      isMounted = false;
      reduceMotionChangedSubscription.remove();
      screenReaderChangedSubscription.remove();
    };
  }, []);

  useEffect(() => {
    if (disableAnimations) {
      fadeAnim.setValue(1); // Instantly show content
      return;
    }

    // Only run animation if animations are enabled
    fadeAnim.setValue(0);
    const anim = Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 700,
      useNativeDriver: true,
      delay: 150, // Use delay instead of separate setTimeout
    });

    anim.start();

    return () => anim.stop(); // Clean up animation if component unmounts
  }, [currentStep, disableAnimations]);

  useEffect(() => {
    const runFocus = async () => {
      const screenReaderEnabled = await AccessibilityInfo.isScreenReaderEnabled();

      if (screenReaderEnabled && progressBarRef.current && currentStep <= 4) {
        if (
          progressBarRef &&
          typeof progressBarRef !== 'function' &&
          (progressBarRef as React.RefObject<View>).current
        ) {
          const timeout = setTimeout(
            () => {
              const tag = findNodeHandle((progressBarRef as React.RefObject<View>).current);
              if (tag) AccessibilityInfo.setAccessibilityFocus(tag);
            },
            Platform.OS === 'android' ? 700 : 300,
          );
          return () => clearTimeout(timeout);
        }
      }
    };

    runFocus();
  }, [currentStep]);

  return (
    <View style={styles.container}>
      <SafeAreaView style={styles.onboardingThreeContainer}>
        <View
          style={styles.progressBar}
          accessible={modalOpen}
          accessibilityElementsHidden={modalOpen}
          importantForAccessibility={
            modalOpen ? ImportantForAccessibility.NO_HIDE : ImportantForAccessibility.AUTO
          }
        >
          <OnboardingProgressBar
            ref={progressBarRef}
            steps={ON_BOARDING.ONBOARDING_STEPS}
            testID="onboarding-progress-bar"
            currentStep={currentStep}
            activeColor={theme.colors.pdsThemeColorBackgroundPrimary}
            inactiveColor={theme.colors.pdsThemeColorNeutralLow}
            onStepChange={() => {}}
            disableAnimations={disableAnimations}
            accessible={true}
          />
        </View>

        <Animated.View style={{ flex: 1, opacity: fadeAnim }}>{renderContent()}</Animated.View>
      </SafeAreaView>
    </View>
  );
};
export default OnboardingFlow;

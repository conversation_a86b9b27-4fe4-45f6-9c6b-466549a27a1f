import { StyleSheet, Dimensions } from 'react-native';

import { IPAD_WIDTH } from '../../shared/constants';

import type { Theme } from 'pantry-design-system';

const { width } = Dimensions.get('window');

/**
 * Generates a stylesheet for the onboarding screen.
 * @param {Object} theme - The theme object containing dimensions and spacing.
 * @returns {Object} The styles object.
 */
const getStyles = ({ colors, dimensions, borderDimens }: Theme) => {
  const styles = StyleSheet.create({
    /**
     * Main container for the screen.
     * Centers content and limits width for larger screens.
     */
    container: {
      alignSelf: 'center',
      backgroundColor: colors.pdsThemeColorBackgroundBaseline,
      flex: 1,
      width: '100%',
      paddingBottom: dimensions.pdsGlobalSpace200,
      paddingHorizontal: width >= IPAD_WIDTH ? (width - IPAD_WIDTH) / 2 : 0,
    },
    /**
     * Progress bar styling.
     * Positioned at the top with proper margins.
     */
    progressBar: {
      alignSelf: 'center',
      marginHorizontal: dimensions.pdsGlobalSpace1600,
      marginTop: dimensions.pdsGlobalSpace1000,
    },
    welcomeImage: {
      height: dimensions.pdsGlobalSizeWidth1600, // 127 is not available in design tokens, picked 128 as a closest value
      width: dimensions.pdsGlobalSizeHeight1600, // 127 is not available in design tokens, picked 128 as a closest value
    },
    locationImage: {
      height: colors.pdsGlobalSizeWidth800,
      tintColor: colors.pdsThemeColorForegroundNeutralHigh,
      width: colors.pdsGlobalSizeWidth800,
    },
    /**
     * Styling for the gap after progress bar for onboarding welcome1 screen
     */
    onboardingContent: {
      marginTop: 90, // 90 is not available in design tokens
    },

    /**
     * Header text spacing for onboarding welcome1 screen.
     */
    onboardingHeader: {
      marginVertical: dimensions.pdsGlobalSpace600,
    },

    /**
     * Container for onboarding content.
     * Aligns items centrally and provides padding.
     */
    contentContainer: {
      alignItems: 'center',
      marginBottom: dimensions.pdsGlobalSpace400,
      marginTop: 27, // 27 is not available in design tokens
      paddingHorizontal: 14, // 14 is not available in design tokens
    },
    /**
     * Section styling for each item in content.
     */
    sectionItem: {
      alignSelf: 'center',
      justifyContent: 'space-between',
      marginTop: 17, // 17 is not available in design tokens
    },
    sectionGap: {
      marginTop: dimensions.pdsGlobalSpace200,
    },
    imageContainer: {
      alignSelf: 'center',
      padding: dimensions.pdsGlobalSpace500,
    },
    /**
     *styling for HR&Payroll card in onboarding 2A.
     */
    image: {
      width: 60, // 60 is not available in design tokens
      height: 60, // 60 is not available in design tokens
      alignSelf: 'center',
    },
    /**
     *styling for parent card in onboarding 2b.
     */
    cardParentContainer: {
      marginTop: 17, // 17 is not available in design tokens
      backgroundColor: colors.pdsGlobalColorGeneralGrey10,
      paddingTop: dimensions.pdsGlobalSpace400,
      borderRadius: dimensions.pdsGlobalSpace400,
    },
    /**
     *styling for parent card horizontally in onboarding 2b.
     */
    cardContainer: {
      alignItems: 'flex-start',
      flexDirection: 'row',
      flexWrap: 'wrap',
      height: 'auto',
      justifyContent: 'center',
      paddingHorizontal: dimensions.pdsGlobalSpace500,
      paddingTop: dimensions.pdsGlobalSpace400,
      width: '100%',
    },
    /**
     *styling for each item horizontally in onboarding 2b.
     */
    cardItemContainer: {
      alignItems: 'center',
      flex: 1,
      marginTop: dimensions.pdsGlobalSpace400,
      width: width >= IPAD_WIDTH ? (width - IPAD_WIDTH) / 3 : width / 4,
      // flex: 1,
    },
    /**
     *styling for horizontal images in onboarding 2b.
     */
    cardIcon: {
      height: dimensions.pdsGlobalSpace1000,
      marginBottom: dimensions.pdsGlobalSpace200,
      overflow: 'hidden',
      width: dimensions.pdsGlobalSpace1000,
    },
    button: {
      justifyContent: 'flex-end',
      paddingHorizontal: dimensions.pdsGlobalSizeWidth200,
    },
    onboardingThreeContainer: {
      alignSelf: 'center',
      backgroundColor: colors.pdsThemeColorBackgroundBaseline,
      flex: 1,
      justifyContent: 'space-between',
      maxWidth: 736,
    },
    actionGroup: {
      gap: dimensions.pdsGlobalSpace500,
      paddingHorizontal: dimensions.pdsGlobalSizeWidth200,
    },
    bottomShitActionGroup: {
      justifyContent: 'flex-end',
      marginBottom: dimensions.pdsGlobalSpace700,
    },
    content: {
      alignItems: 'center',
      gap: dimensions.pdsGlobalSpace400,
      paddingHorizontal: 14, // 14 is not available in design tokens
    },

    tagSmalSize: {
      alignSelf: 'center',
      backgroundColor: colors.pdsGlobalColorGeneralRed20,
      borderRadius: borderDimens.pdsGlobalBorderRadius100,
      marginVertical: dimensions.pdsGlobalSpace200,
      paddingHorizontal: dimensions.pdsGlobalSpace200,
      paddingVertical: dimensions.pdsGlobalSpace100,
    },
    tagXSmalSize: {
      alignSelf: 'center',
      backgroundColor: colors.pdsGlobalColorGeneralRed20,
      borderRadius: borderDimens.pdsGlobalBorderRadius50,
      marginVertical: dimensions.pdsGlobalSpace100,
      paddingHorizontal: dimensions.pdsGlobalSpace100,
      paddingVertical: dimensions.pdsGlobalSpace0,
    },
    scrollViewContent: {
      paddingTop: dimensions.pdsGlobalSpace600,
    },
  });
  return styles;
};

export default getStyles;

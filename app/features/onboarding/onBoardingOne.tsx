/* eslint-disable @typescript-eslint/explicit-module-boundary-types */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { useTheme, Heading, Button, Text } from 'pantry-design-system';
import React, { useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { View, ScrollView, Image, AppState } from 'react-native';
import { useSelector } from 'react-redux';

import { ONBOARDING_ANALYTICS } from '../../../analytics/AnalyticsConstants';
import { screenViewLog } from '../../../analytics/AnalyticsUtils';
import { ADAM_FULL_SCALE } from '../../shared/constants';
import { setOnboardingStep } from '../../store/AsyncStorage';

import getStyles from './styles';

import type { AuthorableContent } from '../../misc/models/authorableModels';

type Props = {
  setSteps: (step: number) => void;
};

const Onboarding = ({ setSteps }: Props) => {
  const authorableContent: AuthorableContent = useSelector(
    (state: any) => state.authorableContent.authorableContent,
  );
  const infoTemplate = authorableContent?.onboardingInfo;
  const appState = useRef(AppState.currentState); // Ref to hold the current app state
  const { t } = useTranslation(); // Translation hook for internationalization

  const { theme } = useTheme();
  const styles = getStyles(theme);

  const next = (): void => {
    setOnboardingStep(1);
    setSteps(2);
  };

  const baseImagePath = process.env.ADAM_IMG_PATH ?? '';

  useEffect(() => {
    logScreenView(); // Log screen view analytics for onboarding step 1
  }, []); // Empty dependency array ensures this runs only once when the component is mounted.

  useEffect(() => {
    // Add an event listener to monitor changes in the app's state (foreground/background)
    const subscription = AppState.addEventListener('change', (nextAppState) => {
      // Check if the app is transitioning from inactive/background to active state
      if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
        logScreenView(); // Log screen view analytics when the app comes to the foreground
      }
      // Update the current app state
      appState.current = nextAppState;
    });

    // Cleanup function to remove the event listener when the component unmounts
    return (): void => {
      subscription.remove();
    };
  }, []);

  const logScreenView = (): void => {
    // Log screen view analytics
    screenViewLog({
      subsection1: ONBOARDING_ANALYTICS.ONBOARDIN,
      subsection2: ONBOARDING_ANALYTICS.STEP_ONE,
    }).catch((_error) => { });
  };

  return (
    <View style={{ flex: 1 }}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.onboardingContent} />

        <View style={styles.contentContainer}>
          <Image
            testID="image-card-speciality-care"
            source={{ uri: baseImagePath + infoTemplate?.image + ADAM_FULL_SCALE }}
            style={styles.welcomeImage}
            accessible={false}
            accessibilityIgnoresInvertColors={false}
          />

          <View style={styles.onboardingHeader}>
            <Heading
              testID={'glad-here-heading'}
              textAlign="center"
              title={infoTemplate?.primaryText}
              color="Neutral high"
              accessibilityHint={''}
              size="Large"
              accessibilityLabel={`${infoTemplate?.primaryText}`}
            />
          </View>

          <Text
            testID={'glad-description-text'}
            color="Neutral high"
            size="Large"
            text={infoTemplate?.secondaryText}
            textAlign="center"
            weight="Regular"
            textDecoration="None"
          />
        </View>
      </ScrollView>

      <View style={styles.button}>
        <Button
          testID={'onboarding-1-next-button'}
          fullWidth
          theme={theme}
          size={'Large'}
          label={infoTemplate?.cta}
          onPress={next}
          accessible={true}
          accessibilityLabel={`${infoTemplate?.cta}`}
          accessibilityHint={''}
        />
      </View>
    </View>
  );
};
export default Onboarding;

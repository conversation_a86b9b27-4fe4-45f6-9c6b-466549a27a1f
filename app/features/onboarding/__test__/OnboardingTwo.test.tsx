import React from 'react';
import { Provider } from "react-redux";
import configureS<PERSON> from "redux-mock-store";
import { render, fireEvent, act } from '@testing-library/react-native';
import OnboardingTwo from '../onBoardingTwo'; // Update the path as needed
import { useNavigation } from '@react-navigation/native';
import { setOnboardingStep } from '../../../store/AsyncStorage';
import { AssociateInfo, ManagerInfo } from '../../../misc/models/authorableModels';
import { ON_BOARDING, SCREENS } from '../../../shared/constants';
import { checkNotifications } from 'react-native-permissions';


jest.mock('@react-native-firebase/app', () => ({
  getApp: jest.fn(() => ({
    name: '[DEFAULT]',
  })),
}));


jest.mock('../../../store/AsyncStorage', () => ({
  setOnboardingStep: jest.fn(),
}));

jest.mock('@react-navigation/native', () => ({
  useNavigation: jest.fn(),
  CommonActions: {
    reset: jest.fn(),
  },
}));

jest.mock('react-i18next', () => ({
  useTranslation: jest.fn(() => ({ t: jest.fn(key => key) })),
}));

jest.mock('pantry-design-system', () => ({
  useTheme: jest.fn(() => ({
    theme: {
      colors: {
        pdsThemeColorBackgroundPrimary: "#000000",
        pdsThemeColorNeutralLow: "#FFFFFF"
      },
      dimensions: {
        pdsGlobalSpace1000: 10,
        pdsGlobalSpace1600: 15
      },
      borderDimens: {
        pdsGlobalBorderRadius100: 4,
      },
      typography: {
        pdsGlobalFontSize100: 10,
        pdsGlobalFontWeight400: 400
      },
      fonts: {
        pdsGlobalFontFamilyNunitoSans: "NunitoSans",
      },
    },
  })),
  Heading: jest.fn(({ title, testID }) => <text testID={testID}>{title}</text>),
  Text: jest.fn(({ text, testID }) => <text testID={testID}>{text}</text>),
  Button: jest.fn(({ onPress, label, testID }) => (
    <button onClick={onPress} testID={testID}>
      {label}
    </button>
  ))
}));

jest.mock('react-native-svg', () => ({
  SvgXml: jest.fn(({ xml, testID }) => <svg testID={testID}>{xml}</svg>),
}));

jest.mock('@react-native-firebase/analytics', () => {
  return jest.fn(() => ({
    logScreenView: jest.fn(),
  }));
})

jest.mock('react-native-permissions', () => ({
  checkNotifications: jest.fn(),
  RESULTS: {},
}));

jest.mock('../../../../app/store', () => ({
  store: {
    getState: jest.fn(() => ({
      profile: {
        profile: { id: 'mock-user-id' },
      },
    })),
  },
}));

describe('OnboardingTwo Component', () => {
  const mockNavigate = jest.fn();
  const mockStore = configureStore([]);
  beforeEach(() => {
    (checkNotifications as jest.Mock).mockResolvedValue({ status: 'granted' });
  });

  beforeEach(() => {
    useNavigation.mockReturnValue({
      replace: mockNavigate,
      addListener: jest.fn(),
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  const initialStateForManager = {
    profile: {
      profile: { role: 'Leader' },
    },
    authorableContent: {
      authorableContent: {
        "onboardingManagerInfo": {
          "v1": [
            {
              "headerText": "What you can expect!",
              "items": [
                {
                  "type": "verticalInfo",
                  "image": "https://www-qa2.shaws.com/content/dam/data/associateapp/assets/hr_payroll.png",
                  "primaryText": "HR & Payroll",
                  "secondaryText": "View your payroll activities, benefits, and HR docs all in one place."
                },
                {
                  "type": "verticalInfo",
                  "image": "https://www-qa2.shaws.com/content/dam/data/associateapp/assets/communication.png",
                  "primaryText": "Communication",
                  "secondaryText": "Send announcements to your direct reports and keep the lines of communication open in the app"
                },
                {
                  "type": "horizontalInfo",
                  "primaryText": "What associates can expect",
                  "infoItems": [
                    {
                      "image": "https://www-qa2.shaws.com/content/dam/data/associateapp/assets/hr_payroll.png",
                      "text": "HR & Payroll"
                    },
                    {
                      "image": "https://www-qa2.shaws.com/content/dam/data/associateapp/assets/schedule.png",
                      "text": "View schedule"
                    },
                    {
                      "image": "https://www-qa2.shaws.com/content/dam/data/associateapp/assets/calendar.png",
                      "text": "Time keeping"
                    }
                  ]
                }
              ],
              "cta": "Next"
            }
          ]
        },
      },
    },
  };

  const initialStateForAssociate = {
    profile: {
      profile: { role: 'associate' },
    },
    authorableContent: {
      authorableContent: {
        "onboardingAssociateInfo": {
          "v1": [
            {
              "headerText": "What you can expect!",
              "items": [
                {
                  "type": "verticalInfo",
                  "image": "https://www-qa2.shaws.com/content/dam/data/associateapp/assets/hr_payroll.png",
                  "primaryText": "HR & Payroll",
                  "secondaryText": "View your payroll activities, benefits, and HR docs all in one place."
                },
                {
                  "type": "verticalInfo",
                  "image": "https://www-qa2.shaws.com/content/dam/data/associateapp/assets/schedule.png",
                  "primaryText": "View your schedule",
                  "secondaryText": "Keep track of your shifts, request time off, and ensure you’re where you need to be."
                },
                {
                  "type": "verticalInfo",
                  "image": "https://www-qa2.shaws.com/content/dam/data/associateapp/assets/calendar.png",
                  "primaryText": "Time keeping",
                  "secondaryText": "Keep track of your clock in/out times with ease."
                }
              ],
              "cta": "button name"
            }
          ]
        }
      }
    }
  };

  test('should call setOnboardingStep and update step to 3 on Next button press', () => {
    const store = mockStore(initialStateForManager);
    const mockRoute = { params: { step: 3 } };
    const mockSetSteps = jest.fn();

    const { getByTestId } = render(
      <Provider store={store}>
        <OnboardingTwo route={mockRoute} setSteps={mockSetSteps} />
      </Provider>
    );

    const nextButton = getByTestId('onboarding-2-next-button');
    fireEvent.press(nextButton);

    // Assert setOnboardingStep is called
    expect(setOnboardingStep).toHaveBeenCalledWith(2);

    // Assert step is updated correctly
    expect(mockSetSteps).toHaveBeenCalledWith(3);

    // No longer expect navigation
    // expect(mockNavigate).toHaveBeenCalledWith(...) <-- REMOVE THIS
  });


  test('should render all components correctly, when jobRole is "associate"', async () => {
    const store = mockStore(initialStateForAssociate);
    const mockRoute = { params: { step: 3 } };
    const screen = render(
      <Provider store={store}>
        <OnboardingTwo route={mockRoute} />
      </Provider>
    );
    const { getByTestId, findAllByTestId } = screen

    // Verify all section items and text are rendered
    expect(getByTestId('header-title')).toBeTruthy();


    const templateContent: AssociateInfo[] = initialStateForAssociate?.authorableContent?.authorableContent?.onboardingAssociateInfo?.v1

    const items = templateContent[0].items ?? [];
    // Loop through the items and verify the rendered content
    // act(() => {
    items.forEach(async (item, index) => {
      if (item.type === 'verticalInfo') {
        // Check if the verticalInfo items are rendered correctly
        expect(getByTestId(`section-item-${index}`)).toBeTruthy();
        expect(getByTestId(`image-card-${index}`)).toBeTruthy();
        expect(getByTestId(`header-section-item-${index}`)).toBeTruthy();
        expect(getByTestId(`text-sectionItem-${index}`)).toBeTruthy();
      }
    });
    // });

    // Verify the Next button is rendered
    expect(await findAllByTestId('onboarding-2-next-button')).toBeTruthy();
  });

  test('should render all components correctly, when jobRole is "manager"', async () => {
    const store = mockStore(initialStateForManager);
    const mockRoute = { params: { step: 3 } };
    const screen = render(
      <Provider store={store}>
        <OnboardingTwo route={mockRoute} />
      </Provider>
    );
    const { getByTestId, findAllByTestId } = screen

    // Verify all section items and text are rendered
    expect(getByTestId('header-title')).toBeTruthy();

    const templateContent: ManagerInfo[] = initialStateForManager?.authorableContent?.authorableContent?.onboardingManagerInfo?.v1

    const items = templateContent[0].items ?? [];

    items.forEach(async (item, index) => {
      if (item.type === 'verticalInfo') {
        expect(getByTestId(`section-item-${index}`)).toBeTruthy();
        expect(getByTestId(`image-card-${index}`)).toBeTruthy();
        expect(getByTestId(`header-section-item-${index}`)).toBeTruthy();
        expect(getByTestId(`text-sectionItem-${index}`)).toBeTruthy();
      } else if (item.type === 'horizontalInfo') {
        // Verify the horizontalInfo section and its child items
        expect(getByTestId(`header-card-title-${index}`)).toBeTruthy();
        item?.infoItems?.forEach(async (_infoItem, idx) => {
          expect(getByTestId(`item-image-card-${idx}`)).toBeTruthy();
          expect(getByTestId(`text-card-${idx}`)).toBeTruthy();
        });
      }
    });

    // Verify the Next button is rendered
    expect(await findAllByTestId('onboarding-2-next-button')).toBeTruthy();
  });


});
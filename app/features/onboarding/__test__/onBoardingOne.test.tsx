import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import Onboarding from '../onBoardingOne';
import { useNavigation } from '@react-navigation/native';
import { Provider } from "react-redux";
import configureStore from "redux-mock-store";
import { setOnboardingStep } from '../../../store/AsyncStorage';
import { SCREENS, ON_BOARDING } from '../../../shared/constants';
import { checkNotifications } from 'react-native-permissions';

jest.mock('@react-native-firebase/app', () => ({
  getApp: jest.fn(() => ({
    name: '[DEFAULT]',
  })),
}));

jest.mock('../../../store/AsyncStorage', () => ({
  setOnboardingStep: jest.fn(),
}));

jest.mock('@react-navigation/native', () => ({
  useNavigation: jest.fn(),
}));

jest.mock('react-i18next', () => ({
  useTranslation: jest.fn(() => ({ t: jest.fn(key => key) })),
}));

jest.mock('pantry-design-system', () => ({
  useTheme: jest.fn(() => ({
    theme: {
      colors: { pdsThemeColorPrimary: "#000000", pdsThemeColorNeutralLow: "#fff" },
      fonts: { pdsGlobalFontFamilyPoppins: "Poppins" },
      dimensions: { pdsGlobalSpace400: 16, pdsGlobalSizeWidth800: 32 },
      typography: { pdsGlobalFontSize500: 18 },
      borderDimens: { pdsGlobalBorderRadius300: 8 },
    }
  })),
  Heading: jest.fn(({ title, testID }) => <text testID={testID}>{title}</text>),
  Button: jest.fn(({ onPress, label, testID }) => (
    <button onClick={onPress} testID={testID}>
      {label}
    </button>
  )),
  Text: jest.fn(({ text, testID }) => <text testID={testID}>{text}</text>),
}));

jest.mock('@react-native-firebase/analytics', () => {
  return jest.fn(() => ({
    logScreenView: jest.fn(),
  }));
})

jest.mock('react-native-permissions', () => ({
  checkNotifications: jest.fn(),
  RESULTS: {},
}));

jest.mock('../../../../app/store', () => ({
  store: {
    getState: jest.fn(() => ({
      profile: {
        profile: { id: 'mock-user-id' },
      },
    })),
  },
}));



describe('Onboarding Component', () => {
  beforeEach(() => {
    (checkNotifications as jest.Mock).mockResolvedValue({ status: 'granted' });
  });
  const mockNavigate = jest.fn();
  const mockStore = configureStore([])

  beforeEach(() => {
    useNavigation.mockReturnValue({
      replace: mockNavigate,
      addListener: jest.fn(),
    });
  });

  const initialState = {
    authorableContent: {
      authorableContent: {
        "onboardingInfo": {
          "image": "https://www-qa2.shaws.com/content/dam/data/associateapp/assets/heart_welcome.png",
          "primaryText": "We’re glad you’re here!",
          "secondaryText": "We built this app with you in mind: to help you stay connected, manage your work, and grow in your role. We will be adding more features soon so we look for more ways to better support you in your day-to-day.",
          "cta": "Next"
        },
      },
    },
    shift: { clockedIn: false, clockedOut: false },
    deviceInfo: { isTablet: false },
  };

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should render all components correctly', () => {
    const store = mockStore(initialState);
    const mockRoute = { params: { step: 1 } };
    const { getByTestId } = render(
      <Provider store={store}>
        <Onboarding route={mockRoute} />
      </Provider>
    );

    //check the image is rendered
    expect(getByTestId('image-card-speciality-care')).toBeTruthy();
    // Check if heading is rendered
    expect(getByTestId("glad-here-heading")).toBeTruthy();

    // Check if description text is rendered
    expect(getByTestId('glad-description-text')).toBeTruthy();

    // Check if the "Next" button is rendered
    expect(getByTestId('onboarding-1-next-button')).toBeTruthy();
  });

  it('should navigate to "onboardingTwo", on Next button press', () => {
    const store = mockStore(initialState);
    const mockRoute = { params: { step: 1 } };
    const mockSetSteps = jest.fn();

    const { getByTestId } = render(
      <Provider store={store}>
        <Onboarding route={mockRoute} setSteps={mockSetSteps} />
      </Provider>
    );

    const nextButton = getByTestId('onboarding-1-next-button');
    fireEvent.press(nextButton);

    // Verify setOnboardingStep is called
    expect(setOnboardingStep).toHaveBeenCalledWith(1);

    // Verify step transition
    expect(mockSetSteps).toHaveBeenCalledWith(2);
  });
});

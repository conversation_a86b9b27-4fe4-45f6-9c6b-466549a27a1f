import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';
import OnboardingLocation from '../onBoardingThree'; // Adjust path
import * as helpers from '../../../utils/helpers';

jest.mock('@react-native-firebase/app', () => ({
  getApp: jest.fn(() => ({
    name: '[DEFAULT]',
  })),
}));

jest.mock('../../../store/AsyncStorage', () => ({
  setOnboardingStep: jest.fn(),
}));

jest.mock('../../../utils/helpers');

const mockNavigation = {
  navigate: jest.fn(),
  replace: jest.fn(),
  addListener: jest.fn((_, cb) => cb?.() || jest.fn()), // mock addListener and call callback
};

jest.mock('@react-navigation/native', () => ({
  useNavigation: () => mockNavigation,
}));

jest.mock('react-i18next', () => ({
  useTranslation: jest.fn(() => ({ t: jest.fn((key) => key) })),
}));

jest.mock('react-native-permissions', () => {
  return {
    check: jest.fn(() => Promise.resolve('granted')),
    request: jest.fn(() => Promise.resolve('granted')),
    PERMISSIONS: {
      ANDROID: {
        ACCESS_FINE_LOCATION: 'android.permission.ACCESS_FINE_LOCATION',
      },
      IOS: {
        LOCATION_WHEN_IN_USE: 'ios.permission.LOCATION_WHEN_IN_USE',
      },
    },
    RESULTS: {
      GRANTED: 'granted',
      DENIED: 'denied',
      BLOCKED: 'blocked',
      UNAVAILABLE: 'unavailable',
    },
  };
});

jest.mock('../../../utils/helpers', () => ({
  checkLocationPermission: jest.fn(() => Promise.resolve('granted')),
  requestLocationPermission: jest.fn(() => Promise.resolve(true)), // ✅ add this
  buildTncAgreementRequestBody: jest.fn(() => Promise.resolve({ "agreementId": "LTY01", "agreementName": "LOYALTY_TERMS", "agreementStatus": "opt-in", "agreementVersion": "1.0", "deviceId": "277245EC-DA5B-4ACF-8748-495451B259A0", "empId": "20058705", "eventType": "new", "ldap": "TNGU446", "requestId": "ae6d062f-3ff6-4fa5-a6db-945bc5b40431" }))
}));

jest.mock('pantry-design-system', () => ({
  useTheme: jest.fn(() => ({
    theme: {
      colors: {
        pdsThemeColorBackgroundPrimary: '#000000',
        pdsThemeColorNeutralLow: '#FFFFFF',
      },
      borderDimens: {
        pdsGlobalBorderRadius100: 4,
      },
      dimensions: {
        pdsGlobalSpace1000: 10,
        pdsGlobalSpace1600: 15,
      },
    },
  })),
  Heading: jest.fn(({ title, testID }) => <text testID={testID}>{title}</text>),
  Text: jest.fn(({ text, testID }) => <text testID={testID}>{text}</text>),
  Button: jest.fn(({ onPress, label, testID }) => (
    <button onClick={onPress} testID={testID}>
      {label}
    </button>
  )),
  BottomSheet: jest.fn(
    ({ children, visibility, onClose, renderHeader, renderContent, renderAction }) =>
      visibility ? (
        <div testID="will-do-later-bottom-sheet">
          {renderHeader && <div testID="bottom-sheet-header">{renderHeader}</div>}
          {renderContent && <div testID="bottom-sheet-content">{renderContent}</div>}
          {renderAction && <div testID="bottom-sheet-action">{renderAction}</div>}
          <button onClick={onClose} testID="close-bottom-sheet">
            Close
          </button>
        </div>
      ) : null,
  ),
}));

jest.mock('react-native-svg', () => ({
  SvgXml: jest.fn(({ xml, testID }) => <svg testID={testID}>{xml}</svg>),
}));

jest.mock('@react-native-firebase/analytics', () => {
  return jest.fn(() => ({
    logScreenView: jest.fn(),
    logEvent: jest.fn(),
  }));
});

jest.mock('react-native-permissions', () => ({
  checkNotifications: jest.fn(),
  RESULTS: {},
}));

jest.mock('../../../../app/store', () => ({
  store: {
    getState: jest.fn(() => ({
      profile: {
        profile: { id: 'mock-user-id' },
      },
    })),
  },
}));

const mockStore = configureStore([]);
const store = mockStore({
  locationAccess: { isPrecise: 'denied' },
});

describe('OnboardingLocation', () => {
  const setSteps = jest.fn();

  it('renders UI elements correctly', () => {
    const { getByTestId } = render(
      <Provider store={store}>
        <OnboardingLocation setSteps={setSteps} />
      </Provider>
    );
    expect(getByTestId('header-title')).toBeTruthy();
    expect(getByTestId('allow-location-button')).toBeTruthy();
    expect(getByTestId('location-will-do-this-later-button')).toBeTruthy();
  });

  it('requests location permission on allow button press', async () => {
    helpers.requestLocationPermission.mockResolvedValue(true);

    const { getByTestId } = render(
      <Provider store={store}>
        <OnboardingLocation setSteps={setSteps} />
      </Provider>
    );

    fireEvent.press(getByTestId('allow-location-button'));

    await waitFor(() => {
      expect(helpers.requestLocationPermission).toHaveBeenCalled();
      expect(setSteps).toHaveBeenCalledWith(4);
    });
  });

  it('displays bottom sheet when skip is pressed', async () => {
    const { getByTestId, findByTestId } = render(
      <Provider store={store}>
        <OnboardingLocation setSteps={setSteps} />
      </Provider>
    );

    fireEvent.press(getByTestId('location-will-do-this-later-button'));

    // findByTestId retries internally until the element appears or times out
    const bottomSheet = await findByTestId('will-do-later-bottom-sheet');

    expect(bottomSheet).toBeTruthy();
  });
});
import { useNavigation, CommonActions } from '@react-navigation/native';
import { render, fireEvent, waitFor, act } from '@testing-library/react-native';
import React from 'react';
import { Alert } from 'react-native';
import { checkNotifications } from 'react-native-permissions';
import { Provider } from 'react-redux';
import configureS<PERSON> from 'redux-mock-store';

import { setOnboardingStep as setOnboardingStep } from '../../../store/AsyncStorage';
import * as helpers from '../../../utils/helpers';
import OnboardingFour from '../onBoardingFour';
import { SCREENS } from '../../../shared/constants';

jest.mock('@react-native-firebase/app', () => ({
  getApp: jest.fn(() => ({
    name: '[DEFAULT]',
  })),
}));

jest.mock('../../../store/AsyncStorage', () => ({
  setOnboardingStep: jest.fn(),
}));

jest.mock('../../../utils/helpers');

jest.mock('@react-navigation/native', () => ({
  useNavigation: jest.fn(),
  CommonActions: {
    reset: jest.fn(),
  },
}));

jest.mock('react-native-linear-gradient', () => {
  const React = require('react');
  const { View } = require('react-native');
  return (props: any) => <View {...props} />;
});

jest.mock('react-i18next', () => ({
  useTranslation: jest.fn(() => ({ t: jest.fn((key) => key) })),
}));

jest.mock('react-native-permissions', () => {
  return {
    checkNotifications: jest.fn(() => Promise.resolve({ status: 'granted' })),
    requestNotifications: jest.fn(() => Promise.resolve({ status: 'granted' })),
    RESULTS: {
      GRANTED: 'granted',
      DENIED: 'denied',
      BLOCKED: 'blocked',
      UNAVAILABLE: 'unavailable',
    },
  };
});

jest.mock('pantry-design-system', () => ({
  useTheme: jest.fn(() => ({
    theme: {
      colors: {
        pdsThemeColorBackgroundPrimary: '#000000',
        pdsThemeColorNeutralLow: '#FFFFFF',
      },
      borderDimens: {
        pdsGlobalBorderRadius100: 4,
      },
      dimensions: {
        pdsGlobalSpace1000: 10,
        pdsGlobalSpace1600: 15,
      },
    },
  })),
  Heading: jest.fn(({ title, testID }) => <text testID={testID}>{title}</text>),
  Text: jest.fn(({ text, testID }) => <text testID={testID}>{text}</text>),
  Button: jest.fn(({ onPress, label, testID }) => (
    <button onClick={onPress} testID={testID}>
      {label}
    </button>
  )),
  BottomSheet: jest.fn(
    ({ children, visibility, onClose, renderHeader, renderContent, renderAction }) =>
      visibility ? (
        <div testID="will-do-later-bottom-sheet">
          {renderHeader && <div testID="bottom-sheet-header">{renderHeader}</div>}
          {renderContent && <div testID="bottom-sheet-content">{renderContent}</div>}
          {renderAction && <div testID="bottom-sheet-action">{renderAction}</div>}
          <button onClick={onClose} testID="close-bottom-sheet">
            Close
          </button>
        </div>
      ) : null,
  ),
}));

jest.mock('react-native-svg', () => ({
  SvgXml: jest.fn(({ xml, testID }) => <svg testID={testID}>{xml}</svg>),
}));

jest.mock('@react-native-firebase/analytics', () => {
  return jest.fn(() => ({
    logEvent: jest.fn(),
    logScreenView: jest.fn(),
  }));
});

jest.mock('react-native-permissions', () => ({
  checkNotifications: jest.fn(),
  RESULTS: {},
}));

jest.mock('../../../../app/store', () => ({
  store: {
    getState: jest.fn(() => ({
      profile: {
        profile: { id: 'mock-user-id' },
      },
    })),
  },
}));

describe('OnboardingFour Component', () => {
  const mockNavigate = jest.fn();
  const mockStore = configureStore([]);
  const mockReplace = jest.fn();
  beforeEach(() => {
    (checkNotifications as jest.Mock).mockResolvedValue({ status: 'granted' });
  });

  beforeEach(() => {
    useNavigation.mockReturnValue({
      dispatch: mockNavigate,
      replace: mockReplace,
      addListener: jest.fn(),
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  const initialState = {
    authorableContent: {
      authorableContent: {
        notificationsTemplate: {
          image: 'https://www-qa2.shaws.com/content/dam/data/associateapp/assets/notifications.png',
          primaryText: 'Allow notifications?',
          secondaryText:
            'Stay in the know with timely updates about shift changes, important in-store announcements, and recognition from your team. Turn on notifications to stay connected and never miss a beat.',
          primaryCTA: 'Allow notifications',
          secondaryCTA: 'Iâ€™ll do this later',
          bottomsheetInfo: {
            primaryText: 'Just a heads up',
            secondaryText:
              'If you do this later youâ€™ll be able to manually allow these permissions in your settings whenever you want.',
            cta: 'Continue',
          },
        },
      },
    },
  };

  test('should render all components correctly', () => {
    const store = mockStore(initialState);
    const mockRoute = { params: { step: 4 } };
    helpers.checkNotificationsPermission.mockResolvedValueOnce(false);

    const { getByTestId } = render(
      <Provider store={store}>
        <OnboardingFour route={mockRoute} />
      </Provider>,
    );

    // Verify all section items and text are rendered
    expect(getByTestId('header-title')).toBeTruthy();
    expect(getByTestId('bell-icon')).toBeTruthy();
    expect(getByTestId('notifications-permission-description')).toBeTruthy();

    // Verify the CTAs are rendered
    expect(getByTestId('allow-notifications-button')).toBeTruthy();
    expect(getByTestId('notif-will-do-this-later-button')).toBeTruthy();
  });

  test.skip('should render only next CTA when the notifications permission was already granted', async () => {
    const store = mockStore(initialState);
    const mockRoute = { params: { step: 4 } };
    helpers.checkNotificationsPermission.mockResolvedValueOnce(true);

    const { getByTestId, queryByTestId } = render(
      <Provider store={store}>
        <OnboardingFour route={mockRoute} />
      </Provider>,
    );

    // Verify the CTAs are rendered
    expect(getByTestId('allow-notifications-button')).toBeFalsy();
    expect(getByTestId('notif-will-do-this-later-button')).toBeFalsy();
  });

  test('renders "Allow Notifications" and "Will do later" buttons when permission is not granted', async () => {
    (helpers.checkNotificationsPermission as jest.Mock).mockResolvedValue(false);

    const store = mockStore({});
    const { getByTestId } = render(
      <Provider store={store}>
        <OnboardingFour />
      </Provider>,
    );

    await waitFor(() => {
      expect(getByTestId('allow-notifications-button')).toBeTruthy();
      expect(getByTestId('notif-will-do-this-later-button')).toBeTruthy();
    });
  });

  test("should show bottom sheet when 'will do this later' button is pressed", async () => {
    const store = mockStore(initialState);
    const mockRoute = { params: { step: 4 } };
    helpers.checkNotificationsPermission.mockResolvedValueOnce(false);
    const { getByTestId } = render(
      <Provider store={store}>
        <OnboardingFour route={mockRoute} />
      </Provider>,
    );

    const willDoThisLaterButton = getByTestId('notif-will-do-this-later-button');
    await act(async () => {
      fireEvent.press(willDoThisLaterButton);
    });

    // Verify the bottom sheet is shown
    expect(getByTestId('will-do-later-bottom-sheet')).toBeTruthy();
  });

  test('should navigate to "homeStack" once the notifications permission is granted', async () => {
    const store = mockStore(initialState);
    const mockRoute = { params: { step: 4 } };
    helpers.checkNotificationsPermission.mockResolvedValueOnce(false);
    helpers.requestNotificationsPermission.mockResolvedValueOnce(true);

    const { getByTestId } = render(
      <Provider store={store}>
        <OnboardingFour route={mockRoute} />
      </Provider>,
    );

    const nextButton = getByTestId('allow-notifications-button');
    fireEvent.press(nextButton);

    // Verify navigation
    await waitFor(() => {
      // Verify setOnboardingStep is called
      expect(setOnboardingStep).toHaveBeenCalledWith(4);
      expect(mockReplace).toHaveBeenCalledWith(SCREENS.BOTTOM_TAB_STACK);
    });
  });

  test('should show an alert when notifications permission is denied', async () => {
    const alertSpy = jest.spyOn(Alert, 'alert');

    const store = mockStore(initialState);
    const mockRoute = { params: { step: 4 } };
    helpers.checkNotificationsPermission.mockResolvedValueOnce(false);
    helpers.requestNotificationsPermission.mockResolvedValueOnce(false);

    const { getByTestId } = render(
      <Provider store={store}>
        <OnboardingFour route={mockRoute} />
      </Provider>,
    );

    const nextButton = getByTestId('allow-notifications-button');
    fireEvent.press(nextButton);

    // Verify the alert is shown
    await waitFor(() => expect(alertSpy).toHaveBeenCalledTimes(1));

    alertSpy.mockRestore();
  });
});

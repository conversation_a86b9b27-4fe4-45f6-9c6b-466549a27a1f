import { useTheme, Heading, Text, Button } from 'pantry-design-system';
import React, { useEffect, useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { Image, ScrollView, View, AppState, Platform } from 'react-native';
import { useSelector } from 'react-redux';

import { ONBOARDING_ANALYTICS } from '../../../analytics/AnalyticsConstants';
import { screenViewLog } from '../../../analytics/AnalyticsUtils';
import Tag from '../../../components/Tag';
import {
  TEXT_SIZE,
  TEXTLINK_COLORS,
  TEXTLINK_WEIGHT,
  USER_ROLES,
  ADAM_FULL_SCALE,
  ImportantForAccessibility,
} from '../../shared/constants';
import { setOnboardingStep } from '../../store/AsyncStorage';
import {
  scheduleTabEnabled,
  socialTabEnabled,
  getFeatureFlagValue,
} from '../../store/selectors/featureFlagsSelectors';

import getStyles from './styles';

import type {
  AuthorableContent,
  Item,
  AssociateInfo,
  ManagerInfo,
} from '../../misc/models/authorableModels';
import type { AssociateProfile } from '../../misc/models/ProfileModal';

type Props = {
  setSteps: (step: number) => void;
};

const OnboardingTwo = ({ setSteps }: Props) => {
  const [templateContent, setTemplateContent] = useState<ManagerInfo | AssociateInfo>(
    {} as ManagerInfo | AssociateInfo,
  );
  const authorableContent: AuthorableContent = useSelector(
    (state: any) => state.authorableContent.authorableContent,
  );
  const profile: AssociateProfile = useSelector((state: any) => state.profile?.profile);
  const [isLeader, setIsLeader] = useState(false); // State to check if the user is a manager.
  const appState = useRef(AppState.currentState); // Ref to hold the current app state

  // Fetch the feature flag values for the schedule and social tabs from the global state.
  const isScheduleTabEnabled = useSelector(scheduleTabEnabled);
  const isSocialTabEnabled = useSelector(socialTabEnabled);

  const rootState = useSelector((state: any) => state);
  const isFlagEnabled = (flag: string): boolean => {
    return getFeatureFlagValue(rootState, flag); // Check if the feature flag is enabled in the global state.
  };

  useEffect(() => {
    const jobRole = profile?.role; // Fetch the job role of the current user from the global state.
    let contentItems: ManagerInfo | AssociateInfo; // Variable to hold the content items based on the user's role.

    // Check if the user's job role is not part of the manager roles.
    const isUserRoleLeader = USER_ROLES.LEADER === jobRole;
    if (isUserRoleLeader) {
      setIsLeader(true); // Set the state to true if the user is a manager.
      // If the user is a manager, fetch the onboarding manager info content.
      if (authorableContent?.onboardingManagerInfo?.v2?.length > 0) {
        contentItems = authorableContent.onboardingManagerInfo.v2[0] || ({} as ManagerInfo);
      } else {
        contentItems =
          authorableContent?.onboardingManagerInfo?.v1?.length > 0
            ? authorableContent.onboardingManagerInfo.v1[0] // Use the first item if available.
            : ({} as ManagerInfo); // Default to an empty object if no content is available.
      }
    } else {
      // If the user is not a manager, fetch the onboarding associate info content.
      if (authorableContent?.onboardingAssociateInfo?.v2?.length > 0) {
        contentItems = authorableContent.onboardingAssociateInfo.v2[0] || ({} as AssociateInfo);
      } else {
        contentItems =
          authorableContent?.onboardingAssociateInfo?.v1?.length > 0
            ? authorableContent.onboardingAssociateInfo.v1[0] // Use the first item if available.
            : ({} as AssociateInfo); // Default to an empty object if no content is available.
      }
    }

    setTemplateContent(contentItems); // Update the state with the fetched content items.
    logScreenView(isUserRoleLeader); // Log the screen view event for analytics.
  }, []); // Run this effect only once when the component mounts.

  useEffect(() => {
    // Add an event listener to monitor changes in the app's state (foreground/background)
    const subscription = AppState.addEventListener('change', (nextAppState) => {
      // Check if the app is transitioning from inactive/background to active state
      if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
        logScreenView(isLeader); // Log the screen view event when the app comes to the foreground.
      }
      // Update the current app state
      appState.current = nextAppState;
    });

    // Cleanup function to remove the event listener when the component unmounts
    return () => {
      subscription.remove();
    };
  }, []);

  const logScreenView = (isRoleAsLeader: boolean) => {
    screenViewLog({
      subsection1: ONBOARDING_ANALYTICS.ONBOARDIN,
      subsection2: isRoleAsLeader
        ? ONBOARDING_ANALYTICS.STEP_TWO_B
        : ONBOARDING_ANALYTICS.STEP_TWO_A,
    }).catch((_error) => { });
  };

  const { theme } = useTheme();
  const styles = getStyles(theme);

  const { t } = useTranslation();

  const next = (): void => {
    setOnboardingStep(2);
    setSteps(3);
  };

  const baseImagePath = process.env.ADAM_IMG_PATH ?? '';

  return (
    <View style={{ flex: 1 }}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.contentContainer}>
          <Heading
            testID="header-title"
            textAlign="center"
            title={templateContent.headerText}
            accessibilityLabel={`${templateContent.headerText}`}
            accessibilityHint={''}
            accessible
            color="Neutral high"
            size="Large"
          />
          {templateContent.items?.length &&
            templateContent.items.map((item: Item, index: number) => {
              const key = `${item.type}-${index}`;
              if (item.type === 'verticalInfo')
                return (
                  <View key={key} style={styles.sectionItem} testID={`section-item-${index}`}>
                    <Image
                      testID={`image-card-${index}`}
                      source={{ uri: baseImagePath + item.image + ADAM_FULL_SCALE }}
                      style={styles.image}
                      accessible={false}
                      accessibilityIgnoresInvertColors={false}
                      importantForAccessibility={ImportantForAccessibility.NO}
                    />
                    {
                      // Check if the feature flag is enabled for the primary text and render ComingSoon component if true
                      item.isFlagUpcoming != null && !isFlagEnabled(item.isFlagUpcoming ?? '') ? (
                        <Tag
                          accessible={true}
                          text={t('comingSoon')}
                          style={styles.tagSmalSize}
                          color={TEXTLINK_COLORS.NEUTRAL_HIGH}
                          size={TEXT_SIZE.SMALL}
                          weight={TEXTLINK_WEIGHT.REGULAR}
                        />
                      ) : (
                        <View accessible={false} style={styles.sectionGap} />
                      )
                    }
                    <Heading
                      testID={`header-section-item-${index}`}
                      textAlign="center"
                      title={item.primaryText}
                      color="Neutral high"
                      size="Medium"
                      accessible={true}
                      accessibilityHint={''}
                      accessibilityLabel={`${item.primaryText}`}
                    />
                    <View accessible={false} style={styles.sectionGap} />

                    <View
                      accessible={true}
                      accessibilityHint={''}
                      accessibilityLabel={`${item.secondaryText}`}
                    >
                      {item.secondaryText && (
                        <Text
                          color="Neutral high"
                          size="Large"
                          text={item.secondaryText}
                          weight="Regular"
                          textDecoration="None"
                          textAlign="center"
                          testID={`text-sectionItem-${index}`}
                          accessible={true}
                          accessibilityHint={''}
                          accessibilityLabel={`${item.secondaryText}`}
                        />
                      )}
                    </View>
                  </View>
                );
              else if (item.type === 'horizontalInfo')
                return (
                  <View key={key} style={styles.cardParentContainer}>
                    <Heading
                      testID={`header-card-title-${index}`}
                      textAlign="center"
                      title={item.primaryText}
                      color="Neutral high"
                      size="Medium"
                      accessible={true}
                      accessibilityLabel={`${item.primaryText}`}
                      accessibilityHint={''}
                    />

                    <View style={styles.cardContainer}>
                      {item.infoItems?.map((infoItem, index) => (
                        <View accessible={false} style={styles.cardItemContainer} key={index}>
                          <Image
                            importantForAccessibility={ImportantForAccessibility.NO}
                            testID={`item-image-card-${index}`}
                            source={{ uri: baseImagePath + infoItem.image + ADAM_FULL_SCALE }}
                            style={styles.cardIcon}
                            accessibilityLabel={infoItem.image}
                            accessibilityRole="image"
                            accessible={false}
                            accessibilityHint={''}
                            accessibilityIgnoresInvertColors={false}
                          />

                          <View
                            accessible={true}
                            accessibilityHint={''}
                            accessibilityLabel={`${infoItem.text}`}
                          >
                            <Text
                              color="Neutral high"
                              size="Medium"
                              text={infoItem.text}
                              weight="Regular"
                              textDecoration="None"
                              textAlign="center"
                              testID={`text-card-${index}`}
                              accessibilityHint={''}
                              accessibilityLabel={`${infoItem.text}`}
                            />
                          </View>

                          <View
                            accessible={true}
                            accessibilityHint={''}
                            accessibilityLabel={`${t('comingSoon')}`}
                          >
                            {infoItem.isFlagUpcoming != null &&
                              !isFlagEnabled(infoItem.isFlagUpcoming ?? '') && (
                                // Check if the feature flag is enabled for the primary text and render ComingSoon component if true
                                <Tag
                                  text={t('comingSoon')}
                                  style={styles.tagXSmalSize}
                                  color={TEXTLINK_COLORS.NEUTRAL_HIGH}
                                  size={TEXT_SIZE.X_SMALL}
                                  weight={TEXTLINK_WEIGHT.REGULAR}
                                />
                              )}
                          </View>
                          <View />
                        </View>
                      ))}
                    </View>
                  </View>
                );
            })}
        </View>
      </ScrollView>

      <View style={styles.button}>
        <Button
          fullWidth
          theme={theme}
          testID={'onboarding-2-next-button'}
          size={'Large'}
          label={templateContent.cta}
          onPress={next}
          accessible={true}
          accessibilityLabel={`${templateContent.cta} ${Platform.OS === 'ios' ? t('ada.button') : ''}`}
          accessibilityHint={''}
        />
      </View>
    </View>
  );
};

export default OnboardingTwo;

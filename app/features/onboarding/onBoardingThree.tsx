import { useTheme, Heading, Text, Button, BottomSheet } from 'pantry-design-system';
import React, { useEffect, useState, useCallback, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { View, Alert, AppState, ScrollView } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { SvgXml } from 'react-native-svg';
import { useDispatch, useSelector } from 'react-redux';

import ABAuditEngine from '../../../analytics/ABAuditEngine';
import {
  ONBOARDING_ANALYTICS,
  EVENT_CATEGORY_MODAL,
  EVENT_ACTION_VIEW,
} from '../../../analytics/AnalyticsConstants';
import {
  screenViewLog,
  userActionLogEvent,
  appLogEvent,
  ANALYTICS_METRIC_CONSTANTS,
} from '../../../analytics/AnalyticsUtils';
import { locationPin } from '../../../assets/images/svg/profileIcons';
import useAccessibilityFocus from '../../hooks/useAccessibilityFocus';
import {
  BUTTON_COLORS,
  BUTTON_SIZE,
  BUTTON_VARIANT,
  LINE_HEIGHT,
  LOCATION_STATE,
  ONBAORDING_BOTTOMSHEET_HEIGHT,
  TEXT_ALIGN,
  TEXT_SIZE,
  TEXT_WEIGHT,
  TEXTLINK_COLORS,
  LEGAL_CONSENT,
  ImportantForAccessibility,
} from '../../shared/constants';
import { setOnboardingStep } from '../../store/AsyncStorage';
import {
  setLocationSkipped,
  setPreciseLocationGranted,
} from '../../store/reducers/locationAccessSlice';
import { SecureStorage, TokenType } from '../../store/SecureStorage';
import {
  checkLocationPermission,
  getLocationAccuracy,
  openSettings,
  requestLocationPermission,
} from '../../utils/helpers';

import getStyles from './styles';

import type { LocationAccuracy } from '../../store/reducers/locationAccessSlice';
import { TncAgreementType, TncAgreementEvent, TncAgreementStatus } from "../../../app/shared/constants";
import { buildTncAgreementRequestBody } from '../../utils/helpers';
import { fetchTncAgreementRequest } from '../../store/reducers/tncAgreementSlice';

type Props = {
  setSteps: (step: number) => void;
  setModalOpen?: (open: boolean) => void;
  hideAccessibility: boolean;
};

const OnboardingLocation = ({ setSteps, setModalOpen, hideAccessibility = false }: Props) => {
  const { theme } = useTheme();
  const styles = getStyles(theme);
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const appState = useRef(AppState.currentState); // Ref to hold the current app state

  const [showBottomSheet, setShowBottomSheet] = useState(false);
  const [locationPermission, setLocationPermission] = useState<LocationAccuracy>('denied');
  const { isPrecise } = useSelector((state: any) => state.locationAccess);
  const secureStorage = new SecureStorage();
  const { setLastFocused, setRef, restoreFocus } = useAccessibilityFocus();
  const insets = useSafeAreaInsets();

  useEffect(() => {
    checkLocationPermission()
      .then((status) => {
        setLocationPermission(status);
      })
      .catch((_error) => {});
    logScreenView();
  }, []); // Run this effect only once when the component mounts.

  const next = async (isSkipped = false): Promise<void> => {
    setShowBottomSheet(false);
    if (setModalOpen) setModalOpen(false);
    if (isPrecise == LOCATION_STATE.DENIED && isSkipped) {
      dispatch(setLocationSkipped(isSkipped));

      // User skipped the location permission step, log the event as DENIED
      const employeeId = await secureStorage.getToken(TokenType.employeeId);
      const timeStamp = new Date().toLocaleString();
      ABAuditEngine.leaveBreadcrumbWithMode(
        `${LEGAL_CONSENT.LOCATION_DENIED} (${timeStamp}) - EmployeeID:${employeeId}`,
      );
      let body = await buildTncAgreementRequestBody(TncAgreementType.LOCATION_PERMISSION, TncAgreementEvent.new, TncAgreementStatus.optOut)
      dispatch(fetchTncAgreementRequest({ body }));
    }
    if (locationPermission !== LOCATION_STATE.DENIED) {
      const accuracy = await getLocationAccuracy();
      dispatch(setPreciseLocationGranted(accuracy));
    } else {
      dispatch(setPreciseLocationGranted(LOCATION_STATE.DENIED));
    }
    setOnboardingStep(3);
    //Send event log action when "continue" CTA is pressed from location headsup modal.
    if (isSkipped) sendModalActionEventLog(ONBOARDING_ANALYTICS.ACTION_EVENT_CONTINUE);
    setSteps(4);
  };

  useEffect(() => {
    // Add an event listener to monitor changes in the app's state (foreground/background)
    const subscription = AppState.addEventListener('change', (nextAppState) => {
      // Check if the app is transitioning from inactive/background to active state
      if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
        logScreenView();
      }
      // Update the current app state
      appState.current = nextAppState;
    });

    // Cleanup function to remove the event listener when the component unmounts
    return (): void => {
      subscription.remove();
    };
  }, []);

  const handleAllowLocation = async (): Promise<void> => {
    setLastFocused('allow-location-button');
    //Send event log action to allow location permission.
    sendLocationPermissionEventLog(ONBOARDING_ANALYTICS.ACTION_EVENT_LOCATION_ALLOW);
    const hasPermission = await requestLocationPermission();
    if (hasPermission) {
      // User granted location permission, log the event as PRECISE as it is default location permission granted status
      const employeeId = await secureStorage.getToken(TokenType.employeeId);
      const timeStamp = new Date().toLocaleString();
      ABAuditEngine.leaveBreadcrumbWithMode(
        `${LEGAL_CONSENT.LOCATION_PRECISE} (${timeStamp}) - EmployeeID:${employeeId}`,
      );
      let body = await buildTncAgreementRequestBody(TncAgreementType.LOCATION_PERMISSION, TncAgreementEvent.new, TncAgreementStatus.optIn)
      dispatch(fetchTncAgreementRequest({ body }));
    }
    if (!hasPermission) {
      Alert.alert('Location Access Required', 'Please enable Location services in settings', [
        { text: t('ok'), onPress: openSettings },
      ]);
      return;
    }

    next().catch((_error) => {});
  };

  const closeBottomSheet = (): void => {
    setTimeout(() => {
      restoreFocus();
    }, 100);
    setShowBottomSheet(false);
    if (setModalOpen) setModalOpen(false);
    //Send event log action to close location headsup modal.
    sendModalActionEventLog(ONBOARDING_ANALYTICS.ACTION_EVVENT_CLOSE);
  };

  const handleSkipLocation = (): void => {
    setLastFocused('location-will-do-this-later-button');
    // Log an event to indicate that the user has chosen to deny location permission and skip this step.
    sendLocationPermissionEventLog(ONBOARDING_ANALYTICS.ACTION_EVENT_LOCATION_DENY);
    //Send event log action location headsup modal loads/shows .
    sendModalActionEventLog(EVENT_ACTION_VIEW, ANALYTICS_METRIC_CONSTANTS.SCREEN_VIEW);
    setShowBottomSheet(true);
    if (setModalOpen) setModalOpen(true);
  };
  const logScreenView = (): void => {
    screenViewLog({
      subsection1: ONBOARDING_ANALYTICS.ONBOARDIN,
      subsection2: ONBOARDING_ANALYTICS.STEP_THREE,
    }).catch((_error) => {});
    // Log screen view analytics for onboarding step 3
  };

  /**
   * Logs a user action event for modal interactions with specified event action details.
   */
  const sendModalActionEventLog = useCallback(
    (eventAction: string, logType: string | null = null) => {
      if (logType) {
        appLogEvent(logType, {
          event_category: EVENT_CATEGORY_MODAL,
          event_action: eventAction,
          event_label: ONBOARDING_ANALYTICS.EVENT_LABEL_LOCATION_MODAL,
        }).catch((_error) => {});
      } else {
        userActionLogEvent(
          EVENT_CATEGORY_MODAL,
          eventAction,
          ONBOARDING_ANALYTICS.EVENT_LABEL_LOCATION_MODAL,
        );
      }
    },
    [],
  );

  /**
   * Logs a user action event for location permission during the onboarding process, specifying the event action and step.
   * @param eventAction - The specific action performed by the user to be logged.
   */
  const sendLocationPermissionEventLog = useCallback((eventAction: string) => {
    userActionLogEvent(
      ONBOARDING_ANALYTICS.ONBOARDIN,
      eventAction,
      ONBOARDING_ANALYTICS.STEP_THREE,
    );
  }, []);

  return (
    <View style={{ flex: 1 }}>
      <ScrollView
        contentContainerStyle={styles.scrollViewContent}
        showsVerticalScrollIndicator={false}
        accessibilityElementsHidden={hideAccessibility || showBottomSheet}
        importantForAccessibility={
          hideAccessibility || showBottomSheet
            ? ImportantForAccessibility.NO_HIDE
            : ImportantForAccessibility.AUTO
        }
      >
        <View
          style={styles.content}
          importantForAccessibility={
            hideAccessibility || showBottomSheet
              ? ImportantForAccessibility.NO_HIDE
              : ImportantForAccessibility.AUTO
          }
          accessibilityElementsHidden={hideAccessibility || showBottomSheet}
        >
          <SvgXml
            xml={locationPin}
            color={theme.colors.pdsThemeColorForegroundNeutralHigh}
            style={{ width: theme.dimensions.pdsGlobalSizeWidth800 }}
            testID="location-icon"
          />
          <Heading
            textAlign={TEXT_ALIGN.CENTER}
            title={t('onboarding.locationPermissionTitle')}
            accessibilityLabel={`${t('onboarding.locationPermissionTitle')}`}
            color={TEXTLINK_COLORS.NEUTRAL_HIGH}
            size={TEXT_SIZE.LARGE}
            testID="header-title"
            accessibilityHint={''}
          />
          <Text
            color={TEXTLINK_COLORS.NEUTRAL_HIGH}
            size={TEXT_SIZE.LARGE}
            text={t('onboarding.locationPermissionDescription')}
            weight={TEXT_WEIGHT.REGULAR}
            textAlign={TEXT_ALIGN.CENTER}
            testID="location-permission-description"
          />
        </View>
      </ScrollView>
      <View
        style={styles.actionGroup}
        importantForAccessibility={
          hideAccessibility || showBottomSheet
            ? ImportantForAccessibility.NO_HIDE
            : ImportantForAccessibility.AUTO
        }
        accessibilityElementsHidden={hideAccessibility || showBottomSheet}
      >
        <Button
          fullWidth
          theme={theme}
          testID="allow-location-button"
          ref={setRef('allow-location-button')}
          accessible
          size={BUTTON_SIZE.LARGE}
          label={t('onboarding.allowLocation')}
          accessibilityLabel={`${t('onboarding.allowLocation')}`}
          accessibilityRole="button"
          accessibilityHint={''}
          onPress={handleAllowLocation}
        />
        <View ref={setRef('location-will-do-this-later-button')}>
          <Button
            fullWidth
            theme={theme}
            testID="location-will-do-this-later-button"
            accessible
            size={BUTTON_SIZE.LARGE}
            variant={BUTTON_VARIANT.OUTLINED}
            accessibilityLabel={`${t('onboarding.willDoThisLater')} `}
            accessibilityRole="button"
            accessibilityHint={''}
            label={t('onboarding.willDoThisLater')}
            color={BUTTON_COLORS.PRIMARY}
            onPress={handleSkipLocation}
          />
        </View>
      </View>
      <BottomSheet
        sidePadding
        height={ONBAORDING_BOTTOMSHEET_HEIGHT}
        variant="Modal"
        theme={theme}
        closeAccessibilityLabel={t('ada.closeButton')}
        testID="location-permission-heads-up-bottom-sheet"
        visibility={showBottomSheet}
        onClose={closeBottomSheet}
        renderHeader={
          <Text
            testID="location-permission-heads-up-title"
            textAlign={TEXT_ALIGN.CENTER}
            weight={TEXT_WEIGHT.SEMI_BOLD}
            accessibilityHint={''}
            text={t('onboarding.headsupTitle')}
            accessibilityLabel={`${t('onboarding.headsupTitle')}, ${t('ada.heading')}`}
            color={TEXTLINK_COLORS.NEUTRAL_HIGH}
            size={TEXT_SIZE.X_LARGE}
          />
        }
        renderContent={
          <View style={{ height: 200 }}>
            <Text
              color={TEXTLINK_COLORS.NEUTRAL_HIGH}
              size={TEXT_SIZE.MEDIUM}
              weight={TEXT_WEIGHT.SEMI_BOLD}
              lineHeight={LINE_HEIGHT.LARGE}
              textAlign={TEXT_ALIGN.CENTER}
              accessible={true}
              accessibilityHint={''}
              accessibilityLabel={t('onboarding.headsupText')}
              testID="location-permission-heads-up-text"
              text={t('onboarding.headsupText')}
            />
          </View>
        }
        renderAction={
          <View style={[styles.bottomShitActionGroup, { paddingBottom: insets.bottom }]}>
            <Button
              fullWidth
              theme={theme}
              testID={'location-permission-continue-button'}
              size={BUTTON_SIZE.SMALL}
              accessible
              label={t('onboarding.continue')}
              accessibilityLabel={`${t('onboarding.continue')}`}
              accessibilityHint={''}
              accessibilityRole="button"
              onPress={() => next(true)} // Pass isSkipped as true
            />
          </View>
        }
      />
    </View>
  );
};

export default OnboardingLocation;

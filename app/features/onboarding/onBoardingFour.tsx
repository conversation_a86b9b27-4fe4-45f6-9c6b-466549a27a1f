import { useNavigation } from '@react-navigation/native';
import { useTheme, Heading, Text, Button, BottomSheet } from 'pantry-design-system';
import React, { useEffect, useState, useCallback, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { View, Alert, AppState, ScrollView } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { SvgXml } from 'react-native-svg';

import {
  ONBOARDING_ANALYTICS,
  EVENT_CATEGORY_MODAL,
  EVENT_ACTION_VIEW,
} from '../../../analytics/AnalyticsConstants';
import {
  screenViewLog,
  userActionLogEvent,
  appLogEvent,
  ANALYTICS_METRIC_CONSTANTS,
} from '../../../analytics/AnalyticsUtils';
import { bell } from '../../../assets/images/svg/OnboardingWelcome';
import useAccessibilityFocus from '../../hooks/useAccessibilityFocus';
import {
  BUTTON_COLORS,
  BUTTON_SIZE,
  BUTTON_VARIANT,
  ImportantForAccessibility,
  LINE_HEIGHT,
  ONBAORDING_BOTTOMSHEET_HEIGHT,
  SCREENS,
  TEXT_ALIGN,
  TEXT_SIZE,
  TEXT_WEIGHT,
  TEXTLINK_COLORS,
  TIMEOUT,
} from '../../shared/constants';
import { setOnboardingStep } from '../../store/AsyncStorage';
import {
  checkNotificationsPermission,
  openSettings,
  requestNotificationsPermission,
} from '../../utils/helpers';

import getStyles from './styles';
import { TncAgreementType, TncAgreementEvent, TncAgreementStatus } from "../../../app/shared/constants";
import { buildTncAgreementRequestBody } from '../../utils/helpers';
import { fetchTncAgreementRequest } from '../../store/reducers/tncAgreementSlice';
import { useDispatch } from 'react-redux';

type Props = {
  setModalOpen?: (open: boolean) => void;
  hideAccessibility: boolean;
};

const OnboardingFour = ({ setModalOpen, hideAccessibility = false }: Props) => {
  const { theme } = useTheme();
  const styles = getStyles(theme);
  const { t } = useTranslation();
  const navigation = useNavigation();
  const appState = useRef(AppState.currentState); // Ref to hold the current app state

  const [showBottomSheet, setShowBottomSheet] = useState(false);
  const [granted, setGranted] = useState(false);
  const [hideForAccessibility, setHideForAccessibility] = useState(hideAccessibility);
  const { setLastFocused, setRef, restoreFocus } = useAccessibilityFocus();
  const dispatch = useDispatch();

  useEffect(() => {
    // Check if notifications permission is granted
    checkNotificationsPermission()
      .then((status) => {
        setGranted(status);
      })
      .catch((_error) => {});
    // Log screen view analytics for onboarding step 4
    logScreenView();

    // Set up accessibility focus and announcements for the progress bar
    const hideTimeout: NodeJS.Timeout = setTimeout(
      () => setHideForAccessibility(true),
      TIMEOUT.ON_BOARDING_FOUR.HIDE,
    );
    const showTimeout: NodeJS.Timeout = setTimeout(
      () => setHideForAccessibility(false),
      TIMEOUT.ON_BOARDING_FOUR.SHOW,
    );
    return (): void => {
      clearTimeout(hideTimeout);
      clearTimeout(showTimeout);
    };
  }, []); // Run this effect only once when the component mounts.

  useEffect(() => {
    // Add an event listener to monitor changes in the app's state (foreground/background)
    const subscription = AppState.addEventListener('change', (nextAppState) => {
      // Check if the app is transitioning from inactive/background to active state
      if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
        // Log screen view analytics for onboarding step 4
        logScreenView();
      }
      // Update the current app state
      appState.current = nextAppState;
    });

    // Cleanup function to remove the event listener when the component unmounts
    return () => {
      subscription.remove();
    };
  }, []);

  const logScreenView = () => {
    // Log screen view event
    screenViewLog({
      subsection1: ONBOARDING_ANALYTICS.ONBOARDIN,
      subsection2: ONBOARDING_ANALYTICS.STEP_FOUR,
    }).catch((_error) => {});
  };

  const next = (isSetFromModal = false) => {
    // Complete onboarding and navigate to home screen
    setOnboardingStep(4);
    // Log an event when the "Continue" button is pressed from the notification modal.
    if (isSetFromModal) sendModalActionEventLog(ONBOARDING_ANALYTICS.ACTION_EVENT_CONTINUE);
    navigation.replace(SCREENS.BOTTOM_TAB_STACK);
  };

  const handleAllowNotifications = async () => {
    // Log an event when the user allows notification permissions.
    sendNotificationPermissionEventLog(ONBOARDING_ANALYTICS.ACTION_EVENT_NOTIFICATION_ALLOW);
    // If permission is already granted, move to next step
    if (granted) {
      next();
      return;
    }

    // Request permission for notifications
    const hasPermission = await requestNotificationsPermission();
    if (!hasPermission) {
      let body = await buildTncAgreementRequestBody(TncAgreementType.PUSH_NOTIFICATION_PERMISSION, TncAgreementEvent.new, TncAgreementStatus.optOut)
      dispatch(fetchTncAgreementRequest({ body }));
      // If permission is denied, show alert to enable notifications in settings
      Alert.alert(
        'Notifications Access Required',
        'Please enable Notifications services in settings',
        [{ text: t('ok'), onPress: openSettings }],
      );
      return;
    }
    let body = await buildTncAgreementRequestBody(TncAgreementType.PUSH_NOTIFICATION_PERMISSION, TncAgreementEvent.new, TncAgreementStatus.optIn)
    dispatch(fetchTncAgreementRequest({ body }));
    // Move to next step
    next();
  };

  const insets = useSafeAreaInsets();

  const handleDenyNotifications = () => {
    setLastFocused('notif-will-do-this-later-button');
    // Log an event when the user denies notification permissions.
    sendNotificationPermissionEventLog(ONBOARDING_ANALYTICS.ACTION_EVENT_NOTIFICATION_DENY);
    // Log an event indicating that the modal will be displayed.
    sendModalActionEventLog(EVENT_ACTION_VIEW, ANALYTICS_METRIC_CONSTANTS.SCREEN_VIEW);
    setShowBottomSheet(true);
    if (setModalOpen) setModalOpen(true);
  };

  /**
   * Logs a user action event for notification permission during the onboarding process.
   * @param eventAction - The specific action performed by the user to be logged.
   */
  const sendNotificationPermissionEventLog = useCallback((eventAction: string) => {
    userActionLogEvent(ONBOARDING_ANALYTICS.ONBOARDIN, eventAction, ONBOARDING_ANALYTICS.STEP_FOUR);
  }, []);
  /**
   * Logs a user action event for the notification modal during the onboarding process.
   * @param eventAction - The specific action performed by the user to be logged.
   */
  const sendModalActionEventLog = useCallback(
    (eventAction: string, logType: string | null = null) => {
      if (logType) {
        appLogEvent(logType, {
          event_category: EVENT_CATEGORY_MODAL,
          event_action: eventAction,
          event_label: ONBOARDING_ANALYTICS.EVENT_LABEL_NOTIFICATION_MODAL,
        }).catch((_error) => {});
      } else {
        // Log the user action event for the notification modal
        // using the userActionLogEvent function.
        userActionLogEvent(
          EVENT_CATEGORY_MODAL,
          eventAction,
          ONBOARDING_ANALYTICS.EVENT_LABEL_NOTIFICATION_MODAL,
        );
      }
    },
    [],
  );
  return (
    <View style={{ flex: 1 }}>
      <ScrollView
        contentContainerStyle={styles.scrollViewContent}
        showsVerticalScrollIndicator={false}
        accessibilityElementsHidden={showBottomSheet || hideAccessibility}
        importantForAccessibility={
          showBottomSheet || hideAccessibility
            ? ImportantForAccessibility.NO_HIDE
            : ImportantForAccessibility.AUTO
        }
      >
        <View
          style={styles.content}
          importantForAccessibility={
            showBottomSheet || hideAccessibility
              ? ImportantForAccessibility.NO_HIDE
              : ImportantForAccessibility.AUTO
          }
          accessibilityElementsHidden={showBottomSheet || hideAccessibility}
        >
          <SvgXml xml={bell} accessible={false} testID="bell-icon" />
          <Heading
            textAlign={TEXT_ALIGN.CENTER}
            title={t('onboarding.notificationsPermissionTitle')}
            accessibilityLabel={`${t('onboarding.notificationsPermissionTitle')}`}
            accessibilityHint=""
            color={TEXTLINK_COLORS.NEUTRAL_HIGH}
            size={TEXT_SIZE.LARGE}
            testID="header-title"
          />
          <Text
            color={TEXTLINK_COLORS.NEUTRAL_HIGH}
            size={TEXT_SIZE.LARGE}
            text={t('onboarding.notificationsPermissionDescription')}
            weight={TEXT_WEIGHT.REGULAR}
            textAlign={TEXT_ALIGN.CENTER}
            testID="notifications-permission-description"
          />
        </View>
      </ScrollView>

      <View
        style={styles.actionGroup}
        importantForAccessibility={
          showBottomSheet || hideForAccessibility
            ? ImportantForAccessibility.NO_HIDE
            : ImportantForAccessibility.AUTO
        }
        accessibilityElementsHidden={showBottomSheet || hideForAccessibility}
      >
        <Button
          fullWidth
          theme={theme}
          testID="allow-notifications-button"
          accessible
          accessibilityLabel={`${t('onboarding.allowNotifications')}`}
          accessibilityHint={''}
          accessibilityRole="button"
          size={BUTTON_SIZE.LARGE}
          label={t('onboarding.allowNotifications')}
          onPress={handleAllowNotifications}
        />
        <View ref={setRef('notif-will-do-this-later-button')}>
          <Button
            fullWidth
            theme={theme}
            testID="notif-will-do-this-later-button"
            accessible
            size={BUTTON_SIZE.LARGE}
            variant={BUTTON_VARIANT.OUTLINED}
            accessibilityLabel={`${t('onboarding.willDoThisLater')}`}
            accessibilityHint={''}
            accessibilityRole="button"
            label={t('onboarding.willDoThisLater')}
            color={BUTTON_COLORS.PRIMARY}
            onPress={handleDenyNotifications}
          />
        </View>
      </View>
      <BottomSheet
        sidePadding
        height={ONBAORDING_BOTTOMSHEET_HEIGHT}
        variant="Modal"
        theme={theme}
        closeAccessibilityLabel={t('ada.closeButton')}
        testID="notifications-permission-heads-up-bottom-sheet"
        visibility={showBottomSheet}
        onClose={() => {
          restoreFocus();
          sendModalActionEventLog(ONBOARDING_ANALYTICS.ACTION_EVVENT_CLOSE);
          setShowBottomSheet(false);
          if (setModalOpen) setModalOpen(false);
        }}
        renderHeader={
          <Text
            testID="notifications-permission-heads-up-title"
            textAlign={TEXT_ALIGN.CENTER}
            weight={TEXT_WEIGHT.SEMI_BOLD}
            text={t('onboarding.headsupTitle')}
            accessibilityHint={''}
            accessibilityLabel={`${t('onboarding.headsupTitle')}, ${t('ada.heading')}`}
            color={TEXTLINK_COLORS.NEUTRAL_HIGH}
            size={TEXT_SIZE.X_LARGE}
          />
        }
        renderContent={
          <View style={{ height: 200 }}>
            <Text
              color={TEXTLINK_COLORS.NEUTRAL_HIGH}
              size={TEXT_SIZE.MEDIUM}
              weight={TEXT_WEIGHT.SEMI_BOLD}
              lineHeight={LINE_HEIGHT.LARGE}
              textAlign={TEXT_ALIGN.CENTER}
              accessible={true}
              accessibilityHint={''}
              accessibilityLabel={t('onboarding.headsupText')}
              testID="notifications-permission-heads-up-text"
              text={t('onboarding.headsupText')}
            />
          </View>
        }
        renderAction={
          <View style={[styles.bottomShitActionGroup, { paddingBottom: insets.bottom }]}>
            {/* <View style={styles.bottomShitActionGroup}> */}
            <Button
              fullWidth
              theme={theme}
              testID={'notifications-permission-continue-button'}
              size={BUTTON_SIZE.SMALL}
              accessible
              label={t('onboarding.continue')}
              accessibilityLabel={`${t('onboarding.continue')} `}
              accessibilityRole="button"
              accessibilityHint={''}
              onPress={() => next(true)} // Pass isSetFromModal as false
              // to indicate that the user is coming from the modal
            />
          </View>
        }
      />
    </View>
  );
};

export default OnboardingFour;

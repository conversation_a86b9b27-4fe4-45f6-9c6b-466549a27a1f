import { render } from '@testing-library/react-native';
import React from 'react';
import * as redux from 'react-redux';

import EmptyStatusCard from '../../../components/EmptyStatusCard/EmptyStatusCard';

import CommunityScreen from './community';

const useFetchStoreLocation = require('../../../app/hooks/useFetchStoreLocation');

const getStyles = require('./styles');

// Mock useFetchStoreLocation
jest.mock('../../../app/hooks/useFetchStoreLocation', () => jest.fn());

// Mock useTheme
jest.mock('pantry-design-system', () => ({
  useTheme: jest.fn(() => ({
    theme: {
      colors: {},
      fonts: {},
      dimensions: {},
      typography: {},
      borderDimens: {},
    },
  })),
}));

jest.mock('react-redux', () => ({
  useSelector: jest.fn(),
  useDispatch: () => jest.fn(),
}));

jest.mock('@react-navigation/native', () => ({
  useIsFocused: jest.fn(() => true),
  useFocusEffect: jest.fn((cb) => {
    cb();
  }),
}));

// Mock getStyles
jest.mock('./styles', () =>
  jest.fn(() => ({
    safeAreaContainer: { backgroundColor: 'red' },
    contentContainer: { padding: 10 },
  })),
);

// Mock useTranslation
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

// Mock EmptyStatusCard
jest.mock('../../../components/EmptyStatusCard/EmptyStatusCard', () => jest.fn(() => null));

// Mock SocialEmptyImage
jest.mock('../../../assets/images/svg/socialEmptyImage', () => ({
  SocialEmptyImage: 'mock-social-image',
}));

jest.mock('../../../components/OffShiftMessage', () => jest.fn(() => null));

describe('CommunityScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it.skip('renders EmptyStatusCard when socialTabEnabled is false', () => {
    jest.spyOn(redux, 'useSelector').mockReturnValue(false);
    const { getByTestId } = render(<CommunityScreen />);
    expect(EmptyStatusCard).toHaveBeenCalledWith(
      expect.objectContaining({
        source: 'mock-social-image',
        titleText: 'BulletinEmptyView.emptyMessageTitle',
        subTitleText: 'BulletinEmptyView.emptyMessageSubTitle',
        descriptionText: 'BulletinEmptyView.emptyMessageDescription',
      }),
    );
  });

  it('does not render EmptyStatusCard when socialTabEnabled is true', () => {
    jest.spyOn(redux, 'useSelector').mockReturnValue(true);
    render(<CommunityScreen />);
    expect(EmptyStatusCard).not.toHaveBeenCalled();
  });

  it('calls useFetchStoreLocation on mount', () => {
    jest.spyOn(redux, 'useSelector').mockReturnValue(true);
    render(<CommunityScreen />);
    expect(useFetchStoreLocation).toHaveBeenCalled();
  });

  it('applies styles from getStyles(theme)', () => {
    jest.spyOn(redux, 'useSelector').mockReturnValue(true);
    render(<CommunityScreen />);
    expect(getStyles).toHaveBeenCalledWith(
      expect.objectContaining({
        colors: expect.any(Object),
        fonts: expect.any(Object),
        dimensions: expect.any(Object),
        typography: expect.any(Object),
        borderDimens: expect.any(Object),
      }),
    );
  });
});

import { useFocusEffect } from '@react-navigation/native';
import { useTheme } from 'pantry-design-system';
import React, { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import useFetchStoreLocation from '../../../app/hooks/useFetchStoreLocation';
import { SocialEmptyImage } from '../../../assets/images/svg/socialEmptyImage';
import EmptyStatusCard from '../../../components/EmptyStatusCard/EmptyStatusCard';
import { useNetworkAlert } from '../../hooks/useNetworkAlert';
import { clearClockInStatus } from '../../store/reducers/clockInStatusSlice';
import { roleGovernanceEnabled, socialTabEnabled } from '../../store/selectors/featureFlagsSelectors';
import { callClockStatusAPI } from '../home/<USER>';
import { scheduleTabEnabled } from '../../store/selectors/featureFlagsSelectors';

import getStyles from './styles';
import { shouldDisplayFeature } from '../../../app/store/selectors/governance';
import OffShiftMessage from '../../../components/OffShiftMessage';
import { FeatureKey } from '../../../app/shared/constants';

export default function CommunityScreen() {
  useFetchStoreLocation();
  const { t } = useTranslation();
  const { theme } = useTheme();
  const styles = getStyles(theme);
  const socialEnabled = useSelector(socialTabEnabled);
  const dispatch = useDispatch();
  const { showAlertWithRetry, checkInternetConnection } = useNetworkAlert();
  const { isUserInStore, userType } = useSelector((state: any) => state?.profile);
  const isClockedIn = useSelector((state: any) => state.shift.clockedIn);
  const isRoleGoveranceEnabled = useSelector(roleGovernanceEnabled);
  const scheduleEnabled = useSelector(scheduleTabEnabled);

  // Calls the clock status API and handles network connectivity.
  const clockStatusAPIcall = async () => {
    const isOnline = await checkInternetConnection();
    if (!isOnline) await showAlertWithRetry(clockStatusAPIcall);
    callClockStatusAPI(dispatch);
  };

  useFocusEffect(
    useCallback(() => {
      if (scheduleEnabled) {
        clockStatusAPIcall(); // Get clock in status on resources screen load if schedule feature flag is enabled.
      }
      return () => {
        if (scheduleEnabled) {
          dispatch(clearClockInStatus());
        }
      };
    }, [scheduleEnabled]),
  );

  const isBulletinFeature = useMemo(() => {
    return shouldDisplayFeature(
      FeatureKey.BULLETIN,
      isUserInStore,
      isClockedIn,
      userType,
      true,
      isRoleGoveranceEnabled
    );
  }, [isUserInStore, isClockedIn, userType, isRoleGoveranceEnabled, socialEnabled]);

  return (
    <View style={styles.safeAreaContainer}>
      <View style={styles.contentContainer}>
        {(!socialEnabled) ? (
          <EmptyStatusCard
            source={SocialEmptyImage}
            titleText={t('BulletinEmptyView.emptyMessageTitle')}
            subTitleText={t('BulletinEmptyView.emptyMessageSubTitle')}
            descriptionText={t('BulletinEmptyView.emptyMessageDescription')}
          />
        ) : (!isBulletinFeature) ? (
          <OffShiftMessage />
        ) : null}
      </View>
    </View>
  );
}

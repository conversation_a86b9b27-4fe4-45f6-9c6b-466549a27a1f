import { IPAD_WIDTH } from "../../shared/constants";
import { StyleSheet } from "react-native";
import { Theme } from "pantry-design-system";

const getStyles = ({ colors }: Theme) => {
    const styles = StyleSheet.create({
        safeAreaContainer: {
            flex: 1,
            alignItems: 'center',
            backgroundColor: colors.pdsThemeColorBackgroundSunken,
        },
        contentContainer: {
            flex: 1,
            maxWidth: IPAD_WIDTH,
            width: '100%',
        },
    });
    return styles;
};

export default getStyles;

/**
 * AppPrivacy Screen
 * 
 * This screen displays the Associate App Privacy Policy, including frequently asked questions,
 * contact information, and links to the Associate Privacy Notice and ACI Privacy Policy.
 * 
 * Accessibility:
 * - Custom accessibility actions are provided for important links, allowing screen reader users
 *   to access the Associate Privacy Notice and ACI Privacy Policy using swipe up/down gestures.
 * - Focus management is handled via the useAccessibilityFocus hook to ensure the correct element
 *   receives focus when navigating between screens.
 * 
 * Features:
 * - Displays privacy policy content, questions, and answers.
 * - Provides accessible, actionable links for privacy notices and policies.
 * - Includes contact information (email, mailing address, toll-free number).
 * - Ensures extra space at the bottom of the scroll view for better UX.
 * 
 * Usage:
 * <AppPrivacy />
 * 
 * Dependencies:
 * - pantry-design-system for UI components.
 * - useAccessibilityFocus for accessibility focus management.
 * - react-i18next for translations.
 * - react-navigation for navigation.
 * 
 * @component
 */
import { View, ScrollView, Linking, Text as RNText, findNodeHandle, AccessibilityInfo, Platform } from 'react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';
import React, { RefObject } from 'react';
import getStyles from "./styles";
import { useTheme, Heading, Text, TextLink } from 'pantry-design-system';
import { useNavigation, NavigationProp, useRoute, RouteProp, useFocusEffect } from '@react-navigation/native';
import { useTranslation } from 'react-i18next';
import { ADA, SCREENS, TEXT_PANTRY_COLORS, TEXT_SIZE, TEXT_WEIGHT, TEXTLINK_COLORS, URLS } from '../../../shared/constants';
import { PROFILE_ANALYTICS } from '../../../../analytics/AnalyticsConstants';
import { AccessibilityActionEvent } from 'react-native';
import useAccessibilityFocus from '../../../../app/hooks/useAccessibilityFocus';

/**
 * Type for route params for AppPrivacy screen.
 *
 * @property {RefObject<View>} [backButtonRef] - Optional ref to the header back button.
 *   - On iOS, this ref is passed from the navigator to allow the screen to set accessibility focus
 *     to the back button when the screen is opened, improving VoiceOver accessibility.
 *   - On Android, this is not used; the header component itself manages focus.
 */
type AppPrivacyRouteParams = {
    backButtonRef?: RefObject<View>;
};

const AppPrivacy = () => {

    const { theme } = useTheme();
    const navigation = useNavigation<NavigationProp<any>>();
    const styles = getStyles(theme);
    const { t } = useTranslation()

    const route = useRoute<RouteProp<Record<string, AppPrivacyRouteParams>, string>>();

    /**
 * Accessibility Focus Management for Back Button
 *
 * On iOS, when navigating to this screen, we want VoiceOver to focus the header back button for better accessibility.
 * This is achieved by retrieving the backButtonRef from navigation params (passed from the navigator)
 * and calling AccessibilityInfo.setAccessibilityFocus on it after a short delay.
 *
 * On Android, this logic is not needed here; the header component (ProfileHeaderLeft) manages its own focus.
 *
 * Note: This logic uses two timeouts for reliability, as the header may not be ready immediately.
 */
    useFocusEffect(
        React.useCallback(() => {
            const backButtonRef = route.params?.backButtonRef;
            let timeout1: NodeJS.Timeout;
            let timeout2: NodeJS.Timeout;

            if (backButtonRef && backButtonRef.current) {
                timeout1 = setTimeout(() => {
                    const tag = findNodeHandle(backButtonRef.current);
                    if (tag) AccessibilityInfo.setAccessibilityFocus(tag);
                }, 1000);

                timeout2 = setTimeout(() => {
                    const tag = findNodeHandle(backButtonRef.current);
                    if (tag) AccessibilityInfo.setAccessibilityFocus(tag);
                }, 2000);
            }

            return () => {
                if (timeout1) clearTimeout(timeout1);
                if (timeout2) clearTimeout(timeout2);
            };
        }, [route.params?.backButtonRef])
    );

    const { setLastFocused, setRef } = useAccessibilityFocus(); // Custom hook for accessibility focus


    const Gap = () => {
        return <View style={{ height: theme.dimensions.pdsGlobalSpace400 }} />;
    }

    const LineStroke = () => {
        return <View style={styles.lineStroke} />;
    }


    const privacyNotice = (screen: string, testID: string) => {
        setLastFocused(testID);
        navigation.navigate(screen);
    }

    /**
    * Navigates to the WebView screen with the given URL and header title.
    *
    * @param {string} url - The URL to be loaded in the WebView.
    * @param {string} titleHeader - The title to be displayed in the WebView header.
    *
    * @example
    * handleWeViewNavigation(URLS.PRIVACY_POLICY, t("privacyPolicy"));
    */
    const handleWeViewNavigation = (url: string, titleHeader: string) => {
        navigation.navigate(SCREENS.WEBVIEW, { url, titleHeader, isOmniheaderPresent: false });
    };

    const email = t('appPrivacyPolicy.email');

    /**
     * Handles accessibility actions for important links in the AppPrivacy screen.
     *
     * This function is used to provide custom accessibility actions for screen reader users,
     * allowing them to activate either the Associate Privacy Notice or the ACI Privacy Policy
     * using swipe up/down gestures. The actionType parameter determines which action to perform.
     *
     * @param {AccessibilityActionEvent} event - The accessibility action event triggered by the user.
     * @param {'privacyNotice' | 'privacyPolicy'} actionType - The type of action to perform.
     *
     * - If actionType is 'privacyNotice', navigates to the Associate Privacy Notice screen.
     * - If actionType is 'privacyPolicy', navigates to the ACI Privacy Policy WebView.
     *
     * Example usage:
     *   onAccessibilityAction={event => handleAccessibilityAction(event, 'privacyNotice')}
     *   onAccessibilityAction={event => handleAccessibilityAction(event, 'privacyPolicy')}
     */
    const handleAccessibilityAction = (
        event: AccessibilityActionEvent,
        actionType: 'privacyNotice' | 'privacyPolicy'
    ) => {
        switch (event.nativeEvent.actionName) {
            case 'customAction':
                if (actionType === 'privacyNotice') {
                    privacyNotice(SCREENS.ASSOCIATE_PRIVACY_NOTICE, ADA.ASSOCIATE_PRIVACY_NOTICE);
                } else if (actionType === 'privacyPolicy') {
                    handleWeViewNavigation(
                        URLS.ACI_PRIVACY_POLICY,
                        t("appPrivacyPolicy.aciPrivacyPolicy"),
                        PROFILE_ANALYTICS.PRIVACY_AND_POLICY
                    );
                }
                break;
        }
    };

    const insets = useSafeAreaInsets();

    return (
        <View style={{ flex: 1, backgroundColor: theme.colors.pdsThemeColorBackgroundSunken, paddingBottom: insets.bottom }}>
            <ScrollView contentContainerStyle={styles.container}
                showsVerticalScrollIndicator={false}
                scrollEnabled={true}
                testID={'assoc-mobile-app-privacy-policy'}>
                <View style={styles.mainContainer}>
                    <Heading
                        testID={'assoc-mobile-app-privacy-policy-heading'}
                        textAlign="center"
                        title={t('appPrivacyPolicy.privacyPolicyHeader')}
                        color={TEXT_PANTRY_COLORS.NEUTRAL_HIGH}
                        size={TEXT_SIZE.MEDIUM}
                    />
                    <Gap />
                    <RNText>
                        <Text text={t('appPrivacyPolicy.lastUpdated')} weight={TEXT_WEIGHT.BOLD} />
                        <Text text={t('appPrivacyPolicy.updatedDate')} />
                    </RNText>
                    <Gap />
                    <Text text={t('appPrivacyPolicy.description1')} />
                    <Gap />

                    <View
                        accessible={true}
                        importantForAccessibility="yes"

                        accessibilityLabel={Platform.select({
                            ios:
                                t('appPrivacyPolicy.description2') + ' ' +
                                t('appPrivacyPolicy.associatePrivacyNotice') + ". " +
                                t('ada.iosPrivacyNotice'),
                            android:
                                t('appPrivacyPolicy.description2') + ' ' +
                                t('appPrivacyPolicy.associatePrivacyNotice') + ". " +
                                t('ada.androidPrivacyNotice')
                        })}
                        accessibilityActions={[
                            { name: 'customAction', label: t("ada.showAssociateAppPrivacyNotice") }
                        ]}
                        onAccessibilityAction={event => handleAccessibilityAction(event, 'privacyNotice')}

                    >
                        <RNText
                            accessible={false}
                            style={styles.description}
                            testID="notice-text"

                        >
                            {t('appPrivacyPolicy.description2') + ' '}
                            <RNText
                                ref={setRef(ADA.ASSOCIATE_PRIVACY_NOTICE)}
                                accessible={false}
                                onPress={() => privacyNotice(SCREENS.ASSOCIATE_PRIVACY_NOTICE, ADA.ASSOCIATE_PRIVACY_NOTICE)}
                                style={styles.linkEmail}
                                testID={ADA.ASSOCIATE_PRIVACY_NOTICE}
                            >
                                {t('appPrivacyPolicy.associatePrivacyNotice')}
                            </RNText>
                        </RNText>
                    </View>

                    <Gap />

                    <View accessible={true}
                        accessibilityRole="button"
                        accessibilityLabel={
                            t('appPrivacyPolicy.description3') +
                            ' ' +
                            t('appPrivacyPolicy.privacyPolicy') +
                            '. ' +
                            t('ada.privacyPolicyHint')
                        }
                        accessibilityActions={[
                            { name: 'customAction', label: t("ada.showAssociateAppPrivacyPolicy") }
                        ]}
                        onAccessibilityAction={event => handleAccessibilityAction(event, 'privacyPolicy')}
                    >
                        <RNText
                            style={styles.description}
                            testID="notice-text"
                            accessible={false}
                        >
                            {t("appPrivacyPolicy.description3") + ' '}
                            <RNText
                                onPress={() =>
                                    handleWeViewNavigation(
                                        URLS.ACI_PRIVACY_POLICY,
                                        t("appPrivacyPolicy.aciPrivacyPolicy"),
                                        PROFILE_ANALYTICS.PRIVACY_AND_POLICY
                                    )
                                }
                                style={styles.linkEmail}
                                testID="app-privacy-policy"
                                accessible={false}
                            >
                                {t("appPrivacyPolicy.privacyPolicy")}
                            </RNText>
                        </RNText>
                    </View>

                    <Gap />
                    <LineStroke />
                    <Gap />
                    <Heading
                        testID={'question1-heading'}
                        textAlign="left"
                        title={t('appPrivacyPolicy.question1')}
                        color='Neutral high'
                        size="Small"
                    />
                    <Gap />
                    <Text text={t('appPrivacyPolicy.answer1_1')} />
                    <Gap />
                    <View style={styles.bulletsContainer}>
                        <Heading title={"•"} />
                        <RNText style={{ flex: 1 }}>
                            <Text text={t('appPrivacyPolicy.answer1_2_header')} weight={TEXT_WEIGHT.BOLD} />
                            <Text text={t('appPrivacyPolicy.answer1_2')} />
                        </RNText>
                    </View>
                    <Gap />
                    <View style={styles.bulletsContainer}>
                        <Heading title={"•"} />
                        <RNText style={{ flex: 1 }}>
                            <Text text={t('appPrivacyPolicy.answer1_3_header')} weight={TEXT_WEIGHT.BOLD} />
                            <Text text={t('appPrivacyPolicy.answer1_3')} />
                        </RNText>
                    </View>
                    <Gap />
                    <View style={styles.bulletsContainer}>
                        <Heading title={"•"} />
                        <RNText style={{ flex: 1 }}>
                            <Text text={t('appPrivacyPolicy.answer1_4_header')} weight={TEXT_WEIGHT.BOLD} />
                            <Text text={t('appPrivacyPolicy.answer1_4')} />
                        </RNText>
                    </View>
                    <Gap />
                    <LineStroke />
                    <Gap />
                    <Heading
                        testID={'question2-heading'}
                        textAlign="left"
                        title={t('appPrivacyPolicy.question2')}
                        color='Neutral high'
                        size="Small"
                    />
                    <Gap />
                    <Text text={t('appPrivacyPolicy.answer2_1')} />
                    <Gap />
                    <View style={styles.bulletsContainer}>
                        <Heading title={"•"} />
                        <RNText style={{ flex: 1 }}>
                            <Text text={t('appPrivacyPolicy.answer2_2_header')} weight={TEXT_WEIGHT.BOLD} />
                            <Text text={t('appPrivacyPolicy.answer2_2')} />
                        </RNText>
                    </View>
                    <Gap />
                    <View style={styles.bulletsContainer}>
                        <Heading title={"•"} />
                        <RNText style={{ flex: 1 }}>
                            <Text text={t('appPrivacyPolicy.answer2_3_header')} weight={TEXT_WEIGHT.BOLD} />
                            <Text text={t('appPrivacyPolicy.answer2_3')} />
                        </RNText>
                    </View>
                    <Gap />
                    <View style={styles.bulletsContainer}>
                        <Heading title={"•"} />
                        <RNText style={{ flex: 1 }}>
                            <Text text={t('appPrivacyPolicy.answer2_4_header')} weight={TEXT_WEIGHT.BOLD} />
                            <Text text={t('appPrivacyPolicy.answer2_4')} />
                        </RNText>
                    </View>
                    <Gap />
                    <LineStroke />
                    <Gap />
                    <Heading
                        testID={'question3-heading'}
                        textAlign="left"
                        title={t('appPrivacyPolicy.question3')}
                        color='Neutral high'
                        size="Small"
                    />
                    <Gap />
                    <Text text={t('appPrivacyPolicy.answer3_1')} />
                    <Gap />
                    <Text text={t('appPrivacyPolicy.answer3_2')} />
                    <Gap />
                    <LineStroke />
                    <Gap />
                    <Heading
                        testID={'question4-heading'}
                        textAlign="left"
                        title={t('appPrivacyPolicy.question4')}
                        color='Neutral high'
                        size="Small"
                    />
                    <Gap />
                    <Text text={t('appPrivacyPolicy.answer4_1')} />
                    <Gap />
                    <Text text={t('appPrivacyPolicy.answer4_2')} />
                    <Gap />
                    <LineStroke />
                    <Gap />
                    <Heading
                        testID={'question6-heading'}
                        textAlign="left"
                        title={t('appPrivacyPolicy.question6')}
                        color='Neutral high'
                        size="Small"
                    />
                    <Gap />
                    <Text text={t('appPrivacyPolicy.answer6')} />
                    <Gap />
                    <LineStroke />
                    <Gap />
                    <Heading
                        testID={'question5-heading'}
                        textAlign="left"
                        title={t('appPrivacyPolicy.question5')}
                        color='Neutral high'
                        size="Small"
                    />
                    <Gap />
                    <Text text={t('appPrivacyPolicy.answer5_1')} />
                    <Gap />
                    <RNText>
                        <Text text={t('appPrivacyPolicy.mailingAddressHeader')} weight={TEXT_WEIGHT.BOLD} />{`\n`}
                        <Text text={t('appPrivacyPolicy.mailingAddress')} />
                    </RNText>
                    <Gap />
                    <Text
                        color={TEXTLINK_COLORS.NEUTRAL_HIGH}
                        text={t('appPrivacyPolicy.emailHeader')}
                        weight={TEXT_WEIGHT.BOLD}
                        size={TEXT_SIZE.MEDIUM}
                    />
                    <TextLink
                        onPress={() => Linking.openURL(`mailto:${email}`)} text={t('appPrivacyPolicy.email')}
                    />

                    <Gap />
                    <RNText>
                        <Text text={t('appPrivacyPolicy.tollFreeNumberHeader')} weight={TEXT_WEIGHT.BOLD} />{`\n`}
                        <Text text={t('appPrivacyPolicy.tollFreeNumber')} />
                    </RNText>
                    <Gap />
                </View>
            </ScrollView>
        </View>
    );
}
export default AppPrivacy;
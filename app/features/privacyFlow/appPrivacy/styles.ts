import { IPAD_WIDTH } from "../../../shared/constants";
import { Theme } from "pantry-design-system";
import { StyleSheet, Dimensions } from "react-native";

const { width } = Dimensions.get("window");

/**
 * Generates a stylesheet for the onboarding screen.
 * @param {Object} theme - The theme object containing dimensions and spacing.
 * @returns {Object} The styles object.
 */
const getStyles = ({ colors, dimensions, typography, fonts }: Theme) => {
  const styles = StyleSheet.create({
    /**
     * Main container for the screen.
     * Centers content and limits width for larger screens.
     */
    container: {
      backgroundColor: colors.pdsThemeColorBackgroundSunken,
      paddingHorizontal: dimensions.pdsGlobalSpace400,
    },
    mainContainer: {
      flex: 1,
      backgroundColor: colors.pdsThemeColorBackgroundSunken,
      maxWidth: IPAD_WIDTH,
      alignSelf: "center",
      justifyContent: "space-between",
      marginTop: dimensions.pdsGlobalSpace400
    },
    lineStroke: {
      height: 1,
      width: 'auto',
      backgroundColor: colors.pdsThemeColorOutlineNeutralLow
    },
    bulletsContainer: {
      flexDirection: "row",
      flex: 1,
      width: '100%',
      alignItems: "flex-start",
      gap: dimensions.pdsGlobalSpace200,
    },
    description: {
      color: colors.pdsThemeColorForegroundNeutralHigh,
      fontWeight: typography.pdsGlobalFontWeight400,
      fontSize: typography.pdsGlobalFontSize200,
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
      lineHeight: 20.5,
    },
    linkText: {
      color: colors.pdsThemeColorForegroundPrimary,
      fontWeight: typography.pdsGlobalFontWeight600,
      fontSize: typography.pdsGlobalFontSize100,
      textDecorationLine: "underline",
    },
    linkEmail: {
      color: colors.pdsThemeColorForegroundPrimary,
      fontWeight: typography.pdsGlobalFontWeight400,
      fontSize: typography.pdsGlobalFontSize200,
      textDecorationLine: "underline",
    }
  });
  return styles;
};

export default getStyles;

import { useNavigation } from '@react-navigation/native';
import { useTheme } from 'pantry-design-system';
import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Platform, View } from 'react-native';
import Pdf from 'react-native-pdf';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useSelector } from 'react-redux';

import { getStyles } from './styles';

/**
 * DocPdfViewer Component
 *
 * Displays a PDF document in a full-screen viewer with a custom header.
 * Supports both local (bundled) and remote PDF files for iOS and Android platforms.
 *
 * Props:
 * - route: React Navigation route object containing:
 *    - pdf: An object with platform-specific PDF sources (e.g., { ios: 'file.pdf', android: 'file.pdf', heading: 'Title' }).
 *    - remotePdf: (optional) An object with a `uri` property for a remote PDF (e.g., { uri: 'https://...' }).
 *
 * Features:
 * - Uses `react-native-pdf` for rendering PDF files.
 * - Dynamically sets the header title based on the provided PDF metadata.
 * - Handles loading progress and error events for debugging.
 * - Applies platform-specific logic for loading local assets on Android and iOS.
 * - Uses Pantry Design System for theming and styling.
 *
 * Example usage:
 * <DocPdfViewer route={{ params: { pdf: { ios: 'file.pdf', android: 'file.pdf', heading: 'My PDF' } } }} />
 */
const DocPdfViewer = ({ route }: any) => {
  const { theme } = useTheme();

  const { t } = useTranslation();

  const { isTablet } = useSelector((state: any) => state.deviceInfo);
  const styles = getStyles(theme, isTablet);
  const navigation = useNavigation();
  const { pdf } = route?.params;

  useEffect(() => {
    navigation.setOptions({
      headerTitle: pdf?.heading ? t(pdf.heading) : '',
    });
  }, [navigation, pdf]);

  const source = Platform.select({
    ios: pdf.ios,
    android: { uri: `bundle-assets://${pdf.android}` },
  });

  const insets = useSafeAreaInsets();

  return (
    <View
      style={[styles.container, { paddingBottom: insets.bottom }]}
      testID="docPdfViewer-container"
    >
      <View testID="pdf-viewer" style={{ flex: 1 }}>
        <Pdf style={{ flex: 1 }} trustAllCerts={false} source={source} />
      </View>
    </View>
  );
};

export default DocPdfViewer;

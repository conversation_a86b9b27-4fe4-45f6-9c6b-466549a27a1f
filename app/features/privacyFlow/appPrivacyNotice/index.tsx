import { useNavigation } from '@react-navigation/native';
import { useTheme, Text, TextLink } from 'pantry-design-system';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { View, ScrollView, Text as RNText, Linking } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { pdfMap, SCREENS } from '../../../shared/constants';

import getStyles from './styles';

import type { NavigationProp } from '@react-navigation/native';

const AppPrivacyNotice = () => {
    const { theme } = useTheme();
    const navigation = useNavigation<NavigationProp<any>>();
    const styles = getStyles(theme);
    const { t } = useTranslation();

    const Gap = () => {
        return <View style={{ height: theme.dimensions.pdsGlobalSpace400 }} />;
    };

    const email = t('appPrivacyPolicy.email');

    const insets = useSafeAreaInsets();

    return (
        <View
            edges={['top', 'bottom']}
            style={{
                flex: 1,
                backgroundColor: theme.colors.pdsThemeColorBackgroundSunken,
                paddingBottom: insets.bottom,
            }}
        >
            <ScrollView
                contentContainerStyle={styles.container}
                showsVerticalScrollIndicator={false}
                scrollEnabled={true}
                testID={'assoc-mobile-app-privacy-policy'}
            >
                <View style={styles.mainContainer}>
                    <Text text={t('appPrivacyNotice.privacyNoticeDescription')} />

                    <Gap />

                    <TextLink
                        testID={'associates-in-california'}
                        text={t('appPrivacyNotice.asoociatesInCalifornia')}
                        onPress={() =>
                            navigation.navigate(SCREENS.DOC_PDF_VIEWER, {
                                pdf: pdfMap.california,
                            })
                        }
                    />

                    <Gap />

                    <TextLink
                        testID={'associates-in-other-states'}
                        text={t('appPrivacyNotice.asoociatesInOtherStates')}
                        onPress={() =>
                            navigation.navigate(SCREENS.DOC_PDF_VIEWER, {
                                pdf: pdfMap.otherStates,
                            })
                        }
                    />

                    <Gap />
                    <RNText
                        style={styles.andOurText}
                        testID="and-our-text"
                        accessibilityLabel={t('appPrivacyNotice.questions')}
                    >
                        {t('appPrivacyNotice.questions') + ' '}

                        <RNText
                            onPress={() => Linking.openURL(`mailto:${email}`)}
                            style={styles.linkEmail}
                            testID="privacy-link"
                            accessible={true}
                            accessibilityRole="link"
                            accessibilityLabel={`${t('Privacy Policy')} link opens in web view`}
                        >
                            {t('appPrivacyNotice.email')}
                        </RNText>
                    </RNText>
                </View>
            </ScrollView>
        </View>
    );
};
export default AppPrivacyNotice;

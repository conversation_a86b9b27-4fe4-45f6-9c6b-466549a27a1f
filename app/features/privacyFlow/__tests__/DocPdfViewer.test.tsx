import React from 'react';
import { render } from '@testing-library/react-native';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import DocPdfViewer from '../docPdfViewer/index';

// Mocks
jest.mock('pantry-design-system', () => ({
  useTheme: () => ({
    theme: {
      dimensions: { pdsGlobalSpace400: 16 },
      colors: { pdsThemeColorBackgroundSunken: '#fff' },
      borderDimens: { pdsGlobalBorderRadius100: 4 },
      typography: { pdsGlobalFontSize100: 10, pdsGlobalFontWeight400: 400 },
      fonts: { pdsGlobalFontFamilyNunitoSans: 'NunitoSans' },
    },
  }),
}));

jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => `translated_${key}`,
  }),
}));

const mockSetOptions = jest.fn();
jest.mock('@react-navigation/native', () => ({
  useNavigation: jest.fn(),
}));

jest.mock('react-redux', () => ({
  useSelector: jest.fn(),
}));

jest.mock('react-native-pdf', () => {
  return {
    __esModule: true,
    default: jest.fn(() => null),
  };
});

describe('DocPdfViewer', () => {
  const pdf = {
    heading: 'appPrivacyNotice.asoociatesInOtherStates',
    ios: require('../../assets/non_ca_albertsons_associate_privacy_notice.pdf'),
    android: 'non_ca_albertsons_associate_privacy_notice.pdf',
  };

  beforeEach(() => {
    (useNavigation as jest.Mock).mockReturnValue({ setOptions: mockSetOptions });
    (useSelector as jest.Mock).mockReturnValue({ isTablet: false });
    jest.clearAllMocks();
  });

  it('sets header title with translated heading if pdf is present', () => {
    render(<DocPdfViewer route={{ params: { pdf } }} />);
    expect(mockSetOptions).toHaveBeenCalledWith({
      headerTitle: 'translated_appPrivacyNotice.asoociatesInOtherStates',
    });
  });

  it('renders PDF viewer with correct testID', () => {
    const { getByTestId } = render(<DocPdfViewer route={{ params: { pdf } }} />);
    expect(getByTestId('docPdfViewer-container')).toBeTruthy();
    expect(getByTestId('pdf-viewer')).toBeTruthy();
  });
});

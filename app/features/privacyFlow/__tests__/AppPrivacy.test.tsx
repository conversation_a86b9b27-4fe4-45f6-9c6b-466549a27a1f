import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import AppPrivacy from '../appPrivacy/index';
import { useNavigation } from '@react-navigation/native';
import { SCREENS, URLS } from '../../../shared/constants';

jest.mock('@react-navigation/native', () => ({
  useNavigation: jest.fn(),
  useRoute: jest.fn(() => ({
    params: {},
    name: 'AppPrivacy',
    key: 'AppPrivacy-key',
  })),
  useFocusEffect: jest.fn((cb) => cb()),
}));

jest.mock('pantry-design-system', () => ({
  useTheme: () => ({
    theme: {
      dimensions: {
        pdsGlobalSpace400: 16,
      },
      colors: {
        pdsThemeColorBackgroundSunken: '#fff',
      },
      borderDimens: {
        pdsGlobalBorderRadius100: 4,
      },
      typography: {
        pdsGlobalFontSize100: 10,
        pdsGlobalFontWeight400: 400
      },
      fonts: {
        pdsGlobalFontFamilyNunitoSans: "NunitoSans",
      },
    },
  }),
  Heading: jest.fn(({ title, testID, children }) => <text testID={testID}>{title}{children}</text>),
  Text: jest.fn(({ text, testID, children }) => <text testID={testID}>{text}{children}</text>),
  TextLink: jest.fn(({ text, testID, onPress }) => <text testID={testID} onClick={onPress}>{text}</text>),
  Checkbox: jest.fn(({ onChange, testID }) => <button onClick={onChange} testID={testID} />),
  Button: jest.fn(({ onPress, label, testID }) => (
    <button onClick={onPress} testID={testID}>
      {label}
    </button>
  )),

}));

jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key, // Mock translation keys directly
  }),
}));

describe('AppPrivacy Screen', () => {
  const mockNavigate = jest.fn();

  beforeEach(() => {
    (useNavigation as jest.Mock).mockReturnValue({
      navigate: mockNavigate,
      addListener: jest.fn(() => jest.fn()),
    });
    mockNavigate.mockClear();
  });

  it('should render all main headings', () => {
    const mockRoute = { params: {} };
    const { getByTestId } = render(<AppPrivacy route={mockRoute} />);
    expect(getByTestId('assoc-mobile-app-privacy-policy-heading')).toBeTruthy();
    expect(getByTestId('question1-heading')).toBeTruthy();
    expect(getByTestId('question2-heading')).toBeTruthy();
    expect(getByTestId('question3-heading')).toBeTruthy();
    expect(getByTestId('question4-heading')).toBeTruthy();
    expect(getByTestId('question5-heading')).toBeTruthy();
    expect(getByTestId('question6-heading')).toBeTruthy();
  });
  it('navigates to Associate Privacy Notice when associate-privacy-notice link is pressed', () => {
    const { getByTestId } = render(<AppPrivacy />);
    const link = getByTestId('associate-privacy-notice');
    fireEvent.press(link);
    expect(mockNavigate).toHaveBeenCalledWith(
      SCREENS.ASSOCIATE_PRIVACY_NOTICE
    );
  });

  it('navigates to WebView when app-privacy-policy link is pressed', () => {
    const mockRoute = { params: {} };
    const { getByTestId } = render(<AppPrivacy route={mockRoute} />);
    const link = getByTestId('app-privacy-policy');
    fireEvent.press(link);
    expect(mockNavigate).toHaveBeenCalledWith(SCREENS.WEBVIEW, {
      url: URLS.ACI_PRIVACY_POLICY,
      titleHeader: 'appPrivacyPolicy.aciPrivacyPolicy',
      isOmniheaderPresent: false,
    });
  });
});
import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { SCREENS } from '../../../shared/constants';
import AppPrivacyNotice from '../appPrivacyNotice/index';

jest.mock('react-native-svg', () => ({
  SvgXml: () => null,
}));

// Mock pantry-design-system components
jest.mock('pantry-design-system', () => ({
  useTheme: () => ({
    theme: {
      dimensions: {
        pdsGlobalSpace400: 16,
      },
      colors: {
        pdsThemeColorBackgroundSunken: '#fff',
      },
      borderDimens: {
        pdsGlobalBorderRadius100: 4,
      },
      typography: {
        pdsGlobalFontSize100: 10,
        pdsGlobalFontWeight400: 400
      },
      fonts: {
        pdsGlobalFontFamilyNunitoSans: "NunitoSans",
      },
    },
  }),
  Heading: jest.fn(({ title, testID }) => <text testID={testID}>{title}</text>),
  Text: jest.fn(({ text, testID }) => <text testID={testID}>{text}</text>),
  TextLink: jest.fn(({ text, testID }) => <text testID={testID}>{text}</text>),
  Checkbox: jest.fn(({ onChange, testID }) => <button onClick={onChange} testID={testID} />),
  Button: jest.fn(({ onPress, label, testID }) => (
    <button onClick={onPress} testID={testID}>
      {label}
    </button>
  )),
}));

// Mock i18next
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

// Mock React Navigation
const mockNavigate = jest.fn();
jest.mock('@react-navigation/native', () => {
  return {
    useNavigation: () => ({
      navigate: mockNavigate,
    }),
  };
});

const mockRoute = { params: {} };

describe('AppPrivacyNotice', () => {
  beforeEach(() => {
    mockNavigate.mockClear();
  });

  it('renders the screen and verifies main container testID', () => {
    const { getByTestId } = render(<AppPrivacyNotice route={mockRoute} />);
    expect(getByTestId('assoc-mobile-app-privacy-policy')).toBeTruthy();
  });



  it('navigates to California PDF when first TextLink is pressed', () => {
    const { getByTestId } = render(<AppPrivacyNotice route={mockRoute} />);
    fireEvent.press(getByTestId('associates-in-california'));

    expect(mockNavigate).toHaveBeenCalledWith(SCREENS.DOC_PDF_VIEWER, {
      pdf: expect.anything(),
    });
  });

  it('navigates to Other States PDF when second TextLink is pressed', () => {
    const { getByTestId } = render(<AppPrivacyNotice route={mockRoute} />);
    fireEvent.press(getByTestId('associates-in-other-states'));

    expect(mockNavigate).toHaveBeenCalledWith(SCREENS.DOC_PDF_VIEWER, {
      pdf: expect.anything(),
    });
  });

  it('renders email link with accessibility label', () => {
    const { getByTestId } = render(<AppPrivacyNotice route={mockRoute} />);
    const emailLink = getByTestId('privacy-link');
    expect(emailLink.props.accessibilityLabel).toBe('Privacy Policy link opens in web view');
    expect(emailLink.props.accessibilityRole).toBe('link');
  });

  it('renders and-our-text container', () => {
    const { getByTestId } = render(<AppPrivacyNotice route={mockRoute} />);
    expect(getByTestId('and-our-text')).toBeTruthy();
  });
});

import { useNavigation, useFocusEffect } from '@react-navigation/native';
import axios from 'axios';
import { jwtDecode } from 'jwt-decode';
import { useTheme, BottomSheet, Button, Text, Heading } from 'pantry-design-system';
import React, { useEffect, useState, useRef, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  View,
  Text as RnText,
  Pressable,
  Dimensions,
  AppState,
  Alert,
  TouchableOpacity,
  Platform,
  AccessibilityInfo,
  Animated,
  Easing,
  ScrollView,
  PixelRatio,
} from 'react-native';
import Device from 'react-native-device-info';
import { ActivityIndicator } from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { SvgXml } from 'react-native-svg';
import { URLSearchParams } from 'react-native-url-polyfill';
import SimpleLineIcons from 'react-native-vector-icons/SimpleLineIcons';
import { useDispatch, useSelector } from 'react-redux';

import ABAuditEngine from '../../../analytics/ABAuditEngine'; // Assuming you have an audit engine for logging
import {
  SIGNIN_ANALYTICS,
  LOGIN_ANALYTICS,
  EVENT_ACTION_VIEW,
  EVENT_CATEGORY_MODAL,
} from '../../../analytics/AnalyticsConstants';
import * as AnalyticsUtils from '../../../analytics/AnalyticsUtils'; // Assuming you have an analytics utility for setting user data
import {
  screenViewLog,
  userActionLogEvent,
  appLogEvent,
  ANALYTICS_METRIC_CONSTANTS,
} from '../../../analytics/AnalyticsUtils';
import { clearAnnouncements } from '../../../app/store/reducers/announcementSlice';
import { albertsonsLogo, albertsonsLogoTablet } from '../../../assets/images/svg/AlbertsonsLogo';
import { helpIcon } from '../../../assets/images/svg/ExternalLink';
import { HeartCare } from '../../../assets/images/svg/heartCare';
import AuthorableBG from '../../../components/AuthorableBg';
import LanguageSelect from '../../../components/languageSelect/LanguageSelect';
import { getFeatureFlag } from '../../config/api/restClient';
import { ANNOUNCEMENT_PATH } from '../../config/endPoints';
import { useFontScaleRerender } from '../../hooks';
import { useAccessibilityFocus } from '../../providers/AccessibilityFocus';
import {
  BANNER_THEMES,
  languages as globalLanguages,
  RT,
  SCREENS,
  URLS,
  TEXTLINK_COLORS,
  TEXTLINK_SIZE,
  ASSOCIATE_PROFILE,
  FALLBACK_BANNER,
  ADAM_FULL_SCALE,
  TEXT_SIZE,
  TERMS_AND_CONDITIONS_URL,
  TEST_EMPLOYEE_ID,
  APP_LANGUAGES,
  TEXT_ALIGN,
  TEXT_DECORATION,
  BOTTOM_SHEET_TYPE,
  ImportantForAccessibility,
  HEADING_SIZE,
  ADA,
  BOTTOM_INITIAL_VALUE,
  BOTTOM_FINAL_VALUE_TABLET,
  BOTTOM_FINAL_VALUE_PHONE,
  LOGIN_FADE_IN_DURATION,
  LOGIN_FADE_IN_DELAY,
  ANIMATION_OVERSHOOT_DURATION1,
  ANIMATION_SETTLE_DURATION1,
  DELAY_NEED,
  fontScaleLimit,
  TEST_PREFERRED_NAME,
} from '../../shared/constants';
import * as AsyncStorage from '../../store/AsyncStorage';
import { fetchAuthorableContentRequest } from '../../store/reducers/authorableContentSlice';
import {
  fetchProfileRequest,
  setBanner,
  setLoggedIn,
  setProfileAsTestAccount,
} from '../../store/reducers/profileSlice';
import { updateLanguage } from '../../store/reducers/selectLanguageSlice';
import { SecureStorage, TokenType } from '../../store/SecureStorage';
import { roleSelectionEnabled } from '../../store/selectors/featureFlagsSelectors';
import { getBottomSheetHeight, showErrorAlert } from '../../utils/AppUtils';

import getStyes from './styles';

import type { FL_TYPES } from '../../config/payloadTypes';
import type { LoginContent } from '../../misc/models/authorableModels';
import type { ProfileState } from '../../store/reducers/profileSlice';
import type { languagetypes } from '../../store/reducers/selectLanguageSlice';
import type { JwtPayload } from 'jwt-decode';
import type { AccessibilityActionEvent } from 'react-native';

interface ExtendedJwtPayload extends JwtPayload {
  employeeId?: string;
  preferred_username?: string;
  onpremisessamaccountname?: string; // LDAP userId
}

/**
 * LoginScreen component handles the login process for the application.
 * It manages the state for various UI elements and handles authentication
 * using OAuth2.0 with an external identity provider.
 */
const LoginScreen = (props: any) => {
  const { t } = useTranslation();

  const [openModal, setOpenModal] = useState<boolean>(false);
  const [showedSplash, setShowedSplash] = useState<boolean>(false);
  const [isApiError, setApiError] = useState<boolean>(false);
  const [authCode, setAuthCode] = useState<string>('');
  const [employeeId, setEmployeeId] = useState<string>('');
  const [welcomeModal, setWelcomeModal] = useState<boolean>(false);
  const [hasHandledProfile, setHasHandledProfile] = useState(false);

  const [measuredHeight, setMeasuredHeight] = useState<number | null>(null);

  const navigation = useNavigation();
  const dispatch = useDispatch();
  const { authorableContent } = useSelector((state: any) => state.authorableContent);
  const infoTemplate = authorableContent?.onboardingInfo;
  const profileError = useSelector((state: any) => state.profile.error);
  const isTablet = useSelector((state: any) => state.deviceInfo?.isTablet);
  const profileData: ProfileState = useSelector((state: any) => state.profile);
  const insets = useSafeAreaInsets();

  const appState = useRef(AppState.currentState);
  const isRoleSelectionEnabled = useSelector(roleSelectionEnabled); //Feature flag to check if role selection is enabled
  /* accessing current/activeted language */
  const currentLanguage = useSelector<{ language: languagetypes }, languagetypes>(
    (state) => state.language,
  );

  const [loginContent, setLoginContent] = useState<LoginContent>();
  const [disableAnimations, setDisableAnimations] = useState(false);
  const [animationFinished, setAnimationFinished] = useState(false);

  const [isMultiLine, setIsMultiLine] = useState(false);

  /* language modification local state */
  const [selectedLanguage, setSelectedLanguage] = useState<languagetypes>(currentLanguage);

  const [selectedLanguageBottomsheet, setSelectedLanguageBottomsheet] =
    useState<languagetypes>(currentLanguage);

  const { theme, switchTheme, switchThemeByType } = useTheme();
  const key = useFontScaleRerender();

  /* Secure Storage */
  const secureStorage = new SecureStorage();

  const { setRef, setLastFocused, handleScreenFocus, restoreLastFocus } = useAccessibilityFocus();

  useEffect(() => {
    const unsubscribeFocus = navigation.addListener('focus', () => {
      handleScreenFocus(SCREENS.LOGIN);
    });

    return () => {
      unsubscribeFocus?.();
    };
  }, [navigation]);

  useEffect(() => {
    if (authorableContent?.loginPage) {
      setLoginContent(authorableContent.loginPage);
    }
  }, [authorableContent]);

  // Usage
  const { height: deviceHeight } = Dimensions.get('window');
  const bottomSheetHeight = getBottomSheetHeight(deviceHeight, isTablet);

  useEffect(() => {
    const { loading: profileLoading, error, response, profile } = profileData;

    if (
      !hasHandledProfile &&
      !profileLoading &&
      error == null &&
      response?.errors == undefined &&
      (profile != null || profile != undefined)
    ) {
      setHasHandledProfile(true); // prevent re-trigger
      AnalyticsUtils.setUserProperties(profile);
      if (profile.facilityType === RT) {
        if (!showedSplash) {
          setShowedSplash(true);
          const mappedBanner = BANNER_THEMES.filter((banner) => {
            if (Array.isArray(banner.profileBanner)) {
              return banner?.profileBanner.indexOf(profile?.banner) > -1;
            } else {
              return banner?.profileBanner == profile?.banner;
            }
          });

          let defaultBanner = BANNER_THEMES[1];
          if (mappedBanner != null && mappedBanner.length > 0) {
            defaultBanner = mappedBanner[0];
          }
          switchThemeByType(defaultBanner.theme);
          dispatch(setBanner({ banner: defaultBanner.banner, bannerName: profile.legalEmployer }));

          props.navigation.replace('splashStack');
        } else {
          setShowedSplash(false); // This might re-trigger useEffect if showedSplash is not excluded from the deps array
        }
      } else {
        props.navigation.replace(SCREENS.CORPORATE_ERROR_SCREEN);
        switchTheme(FALLBACK_BANNER);
      }

      // Cleanup logic
      AsyncStorage.setItem(ANNOUNCEMENT_PATH, '');
      dispatch(clearAnnouncements());
    } else if (!hasHandledProfile && (error != null || response?.errors != null)) {
      setApiError(true);
    }
  }, [profileData.loading, profileData.error, profileData.response, profileData.profile]);

  useFocusEffect(
    useCallback(() => {
      screenViewLog({ subsection1: SIGNIN_ANALYTICS.SIGNIN_SCREEN_VIEW });
    }, []),
  );

  useEffect(() => {
    const checkAccessibilitySettings = async () => {
      const reduceMotionEnabled = await AccessibilityInfo.isReduceMotionEnabled();
      const screenReaderEnabled = await AccessibilityInfo.isScreenReaderEnabled();
      const fontScale = PixelRatio.getFontScale();
      const isFontScaleLarge = fontScale > fontScaleLimit;

      if (reduceMotionEnabled || screenReaderEnabled || isFontScaleLarge) {
        setDisableAnimations(true);
        // Directly set values instead of animating
        bottomAnim.setValue(isTablet ? BOTTOM_FINAL_VALUE_TABLET : BOTTOM_FINAL_VALUE_PHONE); // Final position
        loginOpacity.setValue(1); // Fully visible
      } else {
        setDisableAnimations(false);
      }
    };

    checkAccessibilitySettings();
    // Add an event listener to monitor changes in the app's state (foreground/background)
    const subscription = AppState.addEventListener('change', (nextAppState) => {
      // Check if the app is transitioning from inactive/background to active state
      if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
        screenViewLog({ subsection1: SIGNIN_ANALYTICS.SIGNIN_SCREEN_VIEW });
        checkAccessibilitySettings();
      }
      // Update the current app state
      appState.current = nextAppState;
    });

    // Cleanup function to remove the event listener when the component unmounts
    return () => {
      subscription.remove();
    };
  }, []);

  /*
   * Changes the language of the application.
   * This function updates the current language of the application using the i18n library,
   * performs any necessary initialization specific to the selected language, and then closes
   * the language selection modal.
   */
  const openModalLanguage = () => {
    setLastFocused(SCREENS.LOGIN, 'language-change-link');
    userActionLogEvent(
      SIGNIN_ANALYTICS.SIGNIN_EVENT_CATEGORY,
      LOGIN_ANALYTICS.LINK_LANGUAGE,
      LOGIN_ANALYTICS.LOGIN_SCREEN,
    );
    userActionLogEvent(
      EVENT_CATEGORY_MODAL,
      EVENT_ACTION_VIEW,
      LOGIN_ANALYTICS.LANGUAGE_SELECTION_MODAL,
    );
    setOpenModal(true);
  };

  const closeModalLanguage = () => {
    setOpenModal(false);
  };

  const openWelcomeModal = (testID: string) => {
    setWelcomeModal(!welcomeModal);
    setLastFocused(SCREENS.LOGIN, testID);
    userActionLogEvent(
      SIGNIN_ANALYTICS.SIGNIN_EVENT_CATEGORY,
      LOGIN_ANALYTICS.LINK_NOT_ASSOCIATE,
      LOGIN_ANALYTICS.LOGIN_SCREEN,
    );
    appLogEvent(ANALYTICS_METRIC_CONSTANTS.SCREEN_VIEW, {
      event_category: EVENT_CATEGORY_MODAL,
      event_action: EVENT_ACTION_VIEW,
      event_label: LOGIN_ANALYTICS.NOT_ASSOCIATE_MODAL,
    }).catch((_error) => { });
  };

  const onChangeLanguage = (languageObj: languagetypes) => {
    setSelectedLanguageBottomsheet(languageObj);
  };

  const updateLanguageBtn = () => {
    setSelectedLanguage(selectedLanguageBottomsheet);
    dispatch(updateLanguage(selectedLanguageBottomsheet));
    dispatch(fetchAuthorableContentRequest());
    userActionLogEvent(
      EVENT_CATEGORY_MODAL,
      LOGIN_ANALYTICS.UPDATE_LANGUAGE.replace('[LANGUAGE]', selectedLanguageBottomsheet.id),
      LOGIN_ANALYTICS.LANGUAGE_SELECTION_MODAL,
    );
    closeModalLanguage();
    restoreLastFocus(SCREENS.LOGIN);
  };

  /*
   * Handles the LDAP login process.
   */
  const config = {
    issuer: process.env.MSAUTH_ISSUER ?? '',
    clientId: process.env.MSAUTH_CLIENT_ID ?? '',
    clientSecret: process.env.MSAUTH_CLIENT_SECRET ?? '',
    redirectUrl: process.env.MSAUTH_REDIRECT_URL ?? '',
    scopeGraph: process.env.MSAUTH_SCOPES_GRAPH ?? '',
    loginScope: process.env.MSAUTH_LOGIN_SCOPE ?? '',
    serviceConfiguration: {
      authorizationEndpoint: process.env.MSAUTH_AUTHORIZATION_ENDPOINT ?? '',
      tokenEndpoint: process.env.MSAUTH_TOKEN_ENDPOINT ?? '',
    },
    grantTypeAuthorization: 'authorization_code',
    grantTypeJWTBearer: 'urn:ietf:params:oauth:grant-type:jwt-bearer',
    requestedTokenUseOBO: 'on_behalf_of',
  };

  // Handling login process
  const handleLogin = async () => {
    userActionLogEvent(
      SIGNIN_ANALYTICS.SIGNIN_EVENT_CATEGORY,
      SIGNIN_ANALYTICS.SIGNIN_EVENT_ACTION,
      SIGNIN_ANALYTICS.SIGNIN_EVENT_LABEL,
    );
    const email = await secureStorage.getToken(TokenType.lastUserName);
    const authUrl = `${config.serviceConfiguration.authorizationEndpoint}?response_type=code&client_id=${config.clientId}&redirect_uri=${encodeURIComponent(config.redirectUrl)}&scope=${encodeURIComponent(config.loginScope)}&login_hint=${email}&prompt=login`;
    handleWebViewNavigation(
      authUrl,
      t('login'),
      t('loginDescription'),
      '',
      handleNavigationStateChange,
    );
  };

  /**
   * Retrieves an access token using the provided authorization code.
   *
   * @param {string} code - The authorization code received from the authorization server.
   * @returns {Promise<void>} - A promise that resolves when the access token is successfully retrieved and processed.
   *
   * @throws {Error} - Throws an error if the token retrieval process fails.
   *
   * @remarks
   * This function sends a POST request to the token endpoint specified in the configuration.
   * It includes the necessary parameters such as grant type, client ID, client secret, and redirect URI.
   * If the login is successful, it sets the login success state to true, logs the token response,
   * retrieves the OBO token, and handles LDAP login.
   *
   * @example
   * ```typescript
   * const authorizationCode = 'example_code';
   * getAccessToken(authorizationCode)
   *   .then(() => {
   *     console.log('Access token retrieved successfully.');
   *   })
   *   .catch((error) => {
   *     console.error('Error retrieving access token:', error);
   *   });
   * ```
   */
  const getAccessToken = async (code: string, empId: string | null = null): Promise<string> => {
    try {
      const tokenResponse = await axios.post(
        `${config.serviceConfiguration.tokenEndpoint}`,
        new URLSearchParams({
          grant_type: config.grantTypeAuthorization,
          code: code,
          client_id: config.clientId,
          client_secret: config.clientSecret,
          redirect_uri: config.redirectUrl,
        }).toString(),
        { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } },
      );
      const middleAccessToken = tokenResponse.data.access_token;
      // Decode JWT token and save employeeId
      const decodedToken = jwtDecode<ExtendedJwtPayload>(middleAccessToken);
      const employeeId = empId ? empId : decodedToken?.employeeId; //TODO later need to remove this condition and empId param, once we have role base empolyeeId
      const ldap = decodedToken?.onpremisessamaccountname;
      if (employeeId) {
        secureStorage.saveToken(TokenType.employeeId, employeeId);
      }
      if (ldap) {
        AsyncStorage.setLDAP(ldap);
      }

      const userName = decodedToken?.preferred_username;
      if (userName) {
        const isTestAccountTemp = userName.toLowerCase() === TEST_PREFERRED_NAME.toLowerCase();
        isTestAccount.current = isTestAccountTemp;
        if (isTestAccountTemp) dispatch(setProfileAsTestAccount(isTestAccountTemp));
        secureStorage.saveToken(TokenType.lastUserName, userName);
      }

      // Save middle tier access token and refresh token
      secureStorage.saveToken(TokenType.middleTierAccessToken, middleAccessToken);
      secureStorage.saveToken(TokenType.middleTierRefreshToken, tokenResponse.data.refresh_token);

      // Calculate and save middle tier access token expiry
      const middleTierAccessTokenExpireIn = tokenResponse.data.expires_in;
      const currentTimestamp = Date.now() / 1000;
      const expirationTimestamp = currentTimestamp + middleTierAccessTokenExpireIn;
      secureStorage.saveToken(
        TokenType.middleTierAccessTokenExpiry,
        expirationTimestamp.toString(),
      );

      return employeeId ?? '';
    } catch (error) {
      return '';
    }
  };

  const handleLDAPLoginSuccess = async (employeeId: string) => {
    AnalyticsUtils.setDeviceIDInAppD(); // Set the user ID in AppDynamics for tracking
    ABAuditEngine.startNextSession(); // Start a new session for the random profile
    /**
     * Fetches the associate profile data using a unique session ID.
     *
     * This function retrieves a unique session ID from AsyncStorage and dispatches
     * the `fetchProfileRequest` action with the required parameters.
     */
    dispatch(setLoggedIn(true));
    const flParams: FL_TYPES = ASSOCIATE_PROFILE;
    AsyncStorage.uniqueSessionId().then((requestId) => {
      dispatch(
        fetchProfileRequest({
          requestId,
          empId: employeeId,
          fl: flParams,
        }),
      );
    });
  };

  /**
   * Fetches the employee ID using the provided authorization code.
   *
   * This function retrieves the access token using the authorization code and sets the employee ID
   * in the state. If the retrieval is successful, it calls `handleLDAPLoginSuccess` with the employee ID.
   * If an error occurs, it sets an API error state.
   *
   * @param {string} code - The authorization code received from the OAuth2.0 server.
   */
  const fetchEmployeeId = async (code: string, empId = '') => {
    try {
      const employeeId = empId
        ? await getAccessToken(code ?? '', empId)
        : await getAccessToken(code ?? '');
      if (employeeId) {
        setEmployeeId(employeeId);
        return employeeId;
      } else setApiError(true);
      return '';
    } catch (error) {
      console.error('Error during LDAP login:', error);
      setApiError(true);
      return '';
    }
  };

  /**
   * Navigates to the WebView screen with the provided URL and headers.
   *
   * @param {string} url - The URL to be loaded in the WebView.
   * @param {string} titleHeader - The title to be displayed in the WebView header.
   * @param {string} [subHeader] - An optional subtitle to be displayed in the WebView header.
   * @param {any} [handleNavigationStateChange] - An optional callback to handle navigation state changes in the WebView.
   */
  const handleWebViewNavigation = (
    url: string,
    titleHeader: string,
    subHeader?: string,
    subsection?: any,
    handleNavigationStateChange?: any,
  ) => {
    setLastFocused(SCREENS.LOGIN, 'ldap-login-button');
    navigation.navigate(SCREENS.WEBVIEW, {
      url,
      titleHeader,
      subHeader,
      onNavigationStateChange: handleNavigationStateChange,
      subsection,
      showExternalLink: false,
    });
  };

  /**
   * Navigates to the "Need Help Signing In" screen and manages accessibility focus.
   *
   * This function is used to navigate to the help screen for users who need assistance signing in.
   * It also sets the accessibility focus to the back button on the destination screen for improved
   * screen reader usability. The function uses a timeout to ensure the focus is set after navigation.
   *
   * @param {string} testID - The testID of the element that triggered the navigation, used for accessibility focus management.
   *
   * Example usage:
   *   needHelpSignIn('need-help-signing-link');
   */
  const needHelpSignIn = (testID: string) => {
    setLastFocused(SCREENS.LOGIN, testID);
    navigation.navigate(SCREENS.SIGN_IN_HELP);
    userActionLogEvent(
      SIGNIN_ANALYTICS.SIGNIN_EVENT_CATEGORY,
      LOGIN_ANALYTICS.LINK_NEED_HELP_SIGNING_IN,
      LOGIN_ANALYTICS.LOGIN_SCREEN,
    );
  };

  const isLoginSuccess = useRef(false);
  const isTestAccount = useRef(false);

  const handleNavigationStateChange = async (navState: any) => {
    const { url } = navState;
    if (url.startsWith(config.redirectUrl)) {
      // Parse authorization code from the URL

      const urlParams = new URLSearchParams(url.split('?')[1]);
      const code = urlParams.get('code');
      if (code && isLoginSuccess.current === false) {
        isLoginSuccess.current = true;
        navigation.goBack(); // Close the WebView
        setAuthCode(code);

        const requestId = await AsyncStorage.uniqueSessionId();
        const employeeID = await fetchEmployeeId(code);
        if (employeeID || isTestAccount.current) {
          const response = await getFeatureFlag(
            profileData?.banner || FALLBACK_BANNER,
            profileData?.slocData?.address?.zipcode,
            profileData?.profile?.division,
            requestId,
            employeeID,
          );

          const roleBasedFlag = response?.filter(
            (item: any) => item.featureFlagName === 'diaaLoginAs',
          );
          const isRoleSelectionEnabled = roleBasedFlag?.[0]?.featureFlagValue;

          //API ESFF with employeetId from above function
          // If role selection is enabled from above API response, handle user role flow
          if (isRoleSelectionEnabled) {
            // Handle user role if isRoleSelectionEnabled is enabled
            handleUserRoleFlow(employeeID);
          } else {
            // If role selection is not enabled, directly handle LDAP login success
            handleLDAPLoginSuccess(employeeID ?? '');
          }
        } else {
          /**
           * This block handles the rare case where employeeID is not present after login.
           * Logs a detailed error to AppDynamics for investigation.
           * The error object includes the code and a descriptive message for debugging.
           */
          // Sample error object structure
          const code = 'EmployeeID error';
          const message = 'Unauthorized access';
          const empIDError = new Error(`${code}: ${message}`);
          // Add bread scrum when employeeID is not present
          ABAuditEngine.leaveBreadcrumbWithTagAndNetworkError(
            'employeeId_error',
            new Error(empIDError + ': ' + empIDError.message),
          );
          // Show generic error message to the user when employeeID is not present
          showErrorAlert(t, t('genericError.title'), t('genericError.message'), () => {
            handleLogin();
          });
        }
      }
    }
  };

  /**
   * Handles the user role flow by displaying an alert with options to log in as leader, associate, or self.
   *
   * @param {string} [employeeID] - An employee ID to be used for login.
   */
  const handleUserRoleFlow = (employeeID: string) => {
    Alert.alert(
      t('selectRoleAlert.title') + '!', // Alert title
      t('selectRoleAlert.description'), // Alert message
      [
        {
          text: t('selectRoleAlert.loginAsLeader'),
          onPress: () => {
            const employeeId = authorableContent?.manager ?? TEST_EMPLOYEE_ID.MANAGER;
            secureStorage.saveToken(TokenType.employeeId, employeeId);
            handleLDAPLoginSuccess(employeeId);
          },
          style: 'default',
        },
        {
          text: t('selectRoleAlert.loginAsAssociate'), // Confirm button
          onPress: () => {
            const employeeId =
              authorableContent?.associate ?? TEST_EMPLOYEE_ID.ASSOCIATE_SCHEDULE_EMPID;
            secureStorage.saveToken(TokenType.employeeId, employeeId);
            handleLDAPLoginSuccess(employeeId);
          },
          style: 'default',
        },
        {
          text: t('selectRoleAlert.loginAsSelf'),
          onPress: () => {
            handleLDAPLoginSuccess(employeeID);
          },
          style: 'default',
        },
      ],
      { cancelable: false }, // Make alert non-cancelable
    );
  };

  const styles = getStyes(theme);

  const handleTermsOfUse = (testID) => {
    userActionLogEvent(
      SIGNIN_ANALYTICS.SIGNIN_EVENT_CATEGORY,
      SIGNIN_ANALYTICS.TERMS_EVENT_ACTION,
      SIGNIN_ANALYTICS.TERMS_PRIVACY_EVENT_LABEL,
    );
    setLastFocused(SCREENS.LOGIN, testID);
    navigation.navigate(SCREENS.WEBVIEW, {
      url: TERMS_AND_CONDITIONS_URL ?? '',
      titleHeader: t('Terms of Use'),
    });
  };

  const baseImagePath = process.env.ADAM_IMG_PATH ?? '';

  /**
   * Navigates to a specified screen and manages accessibility focus.
   *
   * This function is used to navigate to a given screen and, upon arrival,
   * set the accessibility focus to a specific element (typically a back button)
   * for improved screen reader usability. On Android, the header component
   * manages its own focus. On iOS, you may pass a backButtonRef via params if needed.
   *
   * @param {string} screen - The name of the screen to navigate to.
   * @param {string} testID - The testID of the element that triggered the navigation, used for accessibility focus management.
   *
   * Example usage:
   *   handleNavigation(SCREENS.PRIVACY_POLICY, 'privacy-policy');
   */
  const handleNavigation = (screen: string, testID: string) => {
    setLastFocused(SCREENS.LOGIN, testID);
    navigation.navigate(screen);
  };

  // to navigate careers webview
  const careersWebLink = (testID: string) => {
    setLastFocused(SCREENS.LOGIN, testID);
    restoreLastFocus(SCREENS.LOGIN);
    setWelcomeModal(false);
    userActionLogEvent(
      LOGIN_ANALYTICS.MODAL,
      LOGIN_ANALYTICS.LINK_VIEW_CAREER,
      LOGIN_ANALYTICS.NOT_ASSOCIATE_MODAL,
    );

    navigation.navigate(SCREENS.WEBVIEW, {
      url: URLS.VIEW_CAREERS,
      titleHeader: t('welcomeSheet.careers'),
      subsection: { subsection1: LOGIN_ANALYTICS.CAREERS, subsection2: '' },
    });
  };
  /**
   * Handles accessibility actions for important links in the AppPrivacy screen.
   *
   * This function is used to provide custom accessibility actions for screen reader users,
   * allowing them to activate either the Associate Privacy Notice or the ACI Privacy Policy
   * using swipe up/down gestures. The actionType parameter determines which action to perform.
   *
   * @param {AccessibilityActionEvent} event - The accessibility action event triggered by the user.
   * @param {'privacyNotice' | 'privacyPolicy'} actionType - The type of action to perform.
   *
   * - If actionType is 'privacyNotice', navigates to the Associate Privacy Notice screen.
   * - If actionType is 'privacyPolicy', navigates to the ACI Privacy Policy WebView.
   *
   * Example usage:
   *   onAccessibilityAction={event => handleAccessibilityAction(event, 'privacyNotice')}
   *   onAccessibilityAction={event => handleAccessibilityAction(event, 'privacyPolicy')}
   */
  const handleAccessibilityAction = (event: AccessibilityActionEvent, actionType: 'termsOfUse') => {
    switch (event.nativeEvent.actionName) {
      case 'customAction':
        if (actionType === 'termsOfUse') {
          handleTermsOfUse('terms-link');
        }
        break;
    }
  };

  const bottomAnim = useRef(new Animated.Value(BOTTOM_INITIAL_VALUE)).current;
  const loginOpacity = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (disableAnimations || measuredHeight === 0) {
      setAnimationFinished(true);
      return;
    }

    const finalHeight = measuredHeight + theme.dimensions.pdsGlobalSpace600;

    const overshootValue = finalHeight + theme.dimensions.pdsGlobalSpace400;

    // Animate only if animations are enabled
    Animated.sequence([
      Animated.timing(bottomAnim, {
        toValue: overshootValue,
        duration: ANIMATION_OVERSHOOT_DURATION1,
        easing: Easing.out(Easing.ease),
        useNativeDriver: false,
      }),
      Animated.timing(bottomAnim, {
        toValue: finalHeight,
        duration: ANIMATION_SETTLE_DURATION1,
        easing: Easing.in(Easing.ease),
        useNativeDriver: false,
      }),
    ]).start(() => {
      setTimeout(() => {
        setAnimationFinished(true);
      }, DELAY_NEED);
    });
  }, [disableAnimations, measuredHeight]); // rerun only when measuredHeight is ready

  useEffect(() => {
    if (disableAnimations) return;

    const timeout = setTimeout(() => {
      Animated?.timing(loginOpacity, {
        toValue: 1,
        duration: LOGIN_FADE_IN_DURATION,
        useNativeDriver: true,
      }).start();
    }, LOGIN_FADE_IN_DELAY);

    return () => clearTimeout(timeout);
  }, [disableAnimations]);

  return (
    <AuthorableBG
      imageBackground={
        baseImagePath + (authorableContent?.loginPage?.bgImage ?? undefined) + ADAM_FULL_SCALE
      }
      imageBackgroundTabletLandscape={
        baseImagePath +
        (authorableContent?.loginPage?.bgImageTabletLandscape ?? undefined) +
        ADAM_FULL_SCALE
      }
      imageBackgroundTabletPortrait={
        baseImagePath +
        (authorableContent?.loginPage?.bgImageTabletPortrait ?? undefined) +
        ADAM_FULL_SCALE
      }
      shouldAnimateGradient={true}
    >
      <View style={{ flex: 1 }}>
        <ScrollView
          contentContainerStyle={[
            styles.scrollContent,
            { paddingBottom: insets.bottom, paddingTop: insets.top },
          ]}
          keyboardShouldPersistTaps="handled"
          accessibilityElementsHidden={openModal || welcomeModal}
          importantForAccessibility={
            openModal || welcomeModal
              ? ImportantForAccessibility.NO_HIDE
              : ImportantForAccessibility.AUTO
          }
        >
          <View style={styles.overlayContent}>
            <Animated.View
              style={[
                styles.contentStyle,
                !animationFinished && { position: 'absolute', bottom: bottomAnim },
              ]}
            >
              <SvgXml
                xml={isTablet ? albertsonsLogoTablet : albertsonsLogo}
                accessible={true}
                accessibilityLabel={t('ada.companyLogo')}
              />
              <RnText
                style={Device.isTablet() ? styles?.welcomeTextTablet : styles?.welcomeText}
                testID="title-text"
              >
                {loginContent?.title}
              </RnText>
            </Animated.View>
            <Animated.View
              onLayout={(event) => {
                setMeasuredHeight(event.nativeEvent.layout.height);
              }}
              style={[styles.containerStyle, { opacity: loginOpacity }]}
            >
              <RnText style={styles.ldapText} testID="subTitle-text">
                {loginContent?.subTitle}
              </RnText>

              <Pressable
                ref={setRef('ldap-login-button')}
                style={isTablet ? styles.buttonContainerTablet : styles.buttonContainer}
                disabled={profileData?.loading || !animationFinished}
                testID="ldap-login-button"
                onPress={() => {
                  handleLogin();
                }}
                accessible={true}
                accessibilityHint={Platform.OS === 'ios' ? t('ada.doubleTapToActivate') : ''}
                accessibilityLabel={`${t('ada.signInHeading')}  ${t('ada.linkOpenInWebview')}`}
              >
                {profileData?.loading && (
                  <ActivityIndicator
                    color={theme.colors.pdsThemeColorBackgroundPrimary}
                    size={'small'}
                    style={{ marginRight: theme.dimensions.pdsGlobalSpace200 }}
                  />
                )}
                <RnText style={styles.buttonText}>{loginContent?.primaryCTA}</RnText>
              </Pressable>

              <TouchableOpacity
                ref={setRef('not-an-associate-link')}
                onPress={() => openWelcomeModal('not-an-associate-link')}
                testID="not-an-associate-link"
                style={styles.forgetPasswordText}
                disabled={!animationFinished}
                accessible={true}
                accessibilityLabel={`${t('notAnAssociate')} ${t('ada.button')}`}
                accessibilityHint={Platform.OS === 'ios' ? t('ada.doubleTapToActivate') : ''}
              >
                <Text
                  textAlign={TEXT_ALIGN.CENTER}
                  textDecoration={TEXT_DECORATION.UNDERLINE}
                  color={TEXTLINK_COLORS.NEUTRAL_HIGH_INVERSE}
                  text={t('notAnAssociate')}
                  size={TEXTLINK_SIZE.MEDIUM}
                  accessible={false}
                />
              </TouchableOpacity>

              <View style={styles.divider} />

              <View
                style={[styles.termAndPrivacyLink, { flexWrap: 'wrap' }]}
                testID="language-container"
              >
                <Text
                  accessible
                  textAlign={TEXT_ALIGN.LEFT}
                  color={TEXTLINK_COLORS.NEUTRAL_HIGH_INVERSE}
                  text={`${t('language')}: `}
                  size={TEXT_SIZE.MEDIUM}
                  testID="language"
                />
                <TouchableOpacity
                  ref={setRef('language-change-link')}
                  onPress={openModalLanguage}
                  disabled={!animationFinished}
                  accessibilityLabel={
                    selectedLanguage.id === APP_LANGUAGES.ENGLISH
                      ? t('ada.englishlanguageButton')
                      : t('ada.spanishLanguageButton')
                  }
                  accessibilityHint={Platform.OS === 'ios' ? t('ada.languageButtonHint') : ''}
                  accessible={true}
                  testID="language-change-link"
                >
                  <Text
                    textAlign={TEXT_ALIGN.CENTER}
                    textDecoration={TEXT_DECORATION.UNDERLINE}
                    color={TEXTLINK_COLORS.NEUTRAL_HIGH_INVERSE}
                    text={selectedLanguage.label}
                    size={TEXTLINK_SIZE.MEDIUM}
                    accessible={false}
                  />
                </TouchableOpacity>
              </View>

              <View
                style={styles.termAndPrivacyLink}
                testID="use-terms-privacy-container"
                accessible={true}
                accessibilityRole="link"
                accessibilityLabel={t('useOfApplication') + ' ' + t('Terms of Use')}
                accessibilityHint={
                  Platform.OS === 'ios' ? t('ada.access_link_swipe_up_down_ios') : ''
                }
                accessibilityActions={[
                  { name: 'customAction', label: t('Terms of Use') + t('ada.linkOpenInWebview') },
                ]}
                onAccessibilityAction={(event) => handleAccessibilityAction(event, 'termsOfUse')}
              >
                <RnText accessible={false} style={styles.useOfThis} testID="notice-text">
                  {t('useOfApplication') + ' '}

                  <RnText
                    ref={setRef('terms-of-use')}
                    accessible={false}
                    disabled={!animationFinished}
                    onPress={() => handleTermsOfUse('terms-link')}
                    style={styles.linkText}
                    testID="terms-link"
                  >
                    {t('Terms of Use') + '.'}
                  </RnText>
                </RnText>
              </View>
              <TouchableOpacity
                ref={setRef('associate-privacy-notice')}
                accessible={true}
                style={[styles.termAndPrivacyLink, { marginTop: 0, rowGap: 0 }]}
                testID="privacy-policy-container"
                accessibilityLabel={`${t('Privacy Policy')} ${t('ada.button')}`}
                accessibilityHint={Platform.OS === 'ios' ? t('ada.doubleTapToActivate') : ''}
                onPress={() => {
                  userActionLogEvent(
                    SIGNIN_ANALYTICS.SIGNIN_EVENT_CATEGORY,
                    SIGNIN_ANALYTICS.PRIVACY_EVENT_ACTION,
                    SIGNIN_ANALYTICS.TERMS_PRIVACY_EVENT_LABEL,
                  );
                  handleNavigation(SCREENS.PRIVACY_POLICY, 'associate-privacy-notice');
                }}
              >
                <RnText
                  ref={setRef(ADA.PRIVACY_POLICY)}
                  accessible={false}
                  disabled={!animationFinished}
                  onPress={() => {
                    userActionLogEvent(
                      SIGNIN_ANALYTICS.SIGNIN_EVENT_CATEGORY,
                      SIGNIN_ANALYTICS.PRIVACY_EVENT_ACTION,
                      SIGNIN_ANALYTICS.TERMS_PRIVACY_EVENT_LABEL,
                    );
                    handleNavigation(SCREENS.PRIVACY_POLICY, ADA.PRIVACY_POLICY);
                  }}
                  style={styles.linkText}
                  testID="associate-privacy-notice"
                >
                  {t('Privacy Policy')}
                </RnText>
              </TouchableOpacity>

              <TouchableOpacity
                ref={setRef('need-help-signing-link')}
                disabled={!animationFinished}
                accessible
                onPress={() => needHelpSignIn('need-help-signing-link')}
                accessibilityLabel={`${t('iNeedHelpSigningIn')} ${t('ada.button')}`}
                accessibilityHint={Platform.OS === 'ios' ? t('ada.doubleTapToActivate') : ''}
                style={styles.forgetPasswordText}
                testID="need-help-signing-link"
              >
                <SvgXml xml={helpIcon} style={styles.helpIconStyle} testID="help-icon" />
                <Text
                  textAlign={TEXT_ALIGN.CENTER}
                  textDecoration={TEXT_DECORATION.UNDERLINE}
                  color={TEXTLINK_COLORS.NEUTRAL_HIGH_INVERSE}
                  text={t('iNeedHelpSigningIn')}
                  size={TEXTLINK_SIZE.MEDIUM}
                />
              </TouchableOpacity>
            </Animated.View>
          </View>
        </ScrollView>
        {/* change language modal */}
        <BottomSheet
          sidePadding
          variant={BOTTOM_SHEET_TYPE.MODAL}
          theme={theme}
          testID="language-bottom-sheet"
          visibility={openModal}
          closeAccessibilityLabel={t('ada.closeButton')}
          onClose={() => {
            restoreLastFocus(SCREENS.LOGIN);
            setSelectedLanguageBottomsheet(selectedLanguage);
            closeModalLanguage();
          }}
          importantForAccessibility={ImportantForAccessibility.YES}
          accessibilityViewIsModal={true}
          renderHeader={
            <View style={styles.headingView} testID="bottom-sheet-header">
              <SimpleLineIcons
                name="globe"
                importantForAccessibility={ImportantForAccessibility.NO}
                accessible={false}
                style={{ marginBottom: 12 }}
                size={theme.dimensions.pdsGlobalSizeWidth800}
                color={theme.colors.pdsThemeColorBackgroundPrimary}
              />
              <Heading
                textAlign={TEXT_ALIGN.CENTER}
                title={t('selectYourLanguage')}
                color={TEXTLINK_COLORS.NEUTRAL_HIGH}
                size={HEADING_SIZE.Medium}
                accessibilityLabel={t('selectYourLanguage')}
              />
            </View>
          }
          renderContent={
            <View
              style={{ height: bottomSheetHeight, justifyContent: 'center', alignItems: 'center' }}
            >
              <LanguageSelect
                languages={globalLanguages}
                selectLang={selectedLanguageBottomsheet}
                changeLanguage={(lang: any) => onChangeLanguage(lang)}
              />
            </View>
          }
          renderAction={
            <View style={[styles.welcomeButtonView, { paddingBottom: insets.bottom }]}>
              <Button
                fullWidth
                theme={theme}
                testID={'confirm-update-language'}
                size={'Small'}
                label={t('updateLanguage')}
                onPress={updateLanguageBtn}
                accessibilityRole="none"
                accessibilityLabel={`${t('updateLanguage')} ${t('ada.button')}`}
                accessible={true}
                accessibilityHint={t('ada.languageButtonHint')}
                ref={setRef('confirm-update-language')}
              />
            </View>
          }
        />

        {/* welcome sheet modal */}
        <BottomSheet
          sidePadding
          variant={BOTTOM_SHEET_TYPE.MODAL}
          theme={theme}
          testID="welcome-bottom-sheet"
          visibility={welcomeModal}
          closeAccessibilityLabel={t('ada.closeButton')}
          onClose={() => {
            setLastFocused(SCREENS.LOGIN, 'not-an-associate-link');
            restoreLastFocus(SCREENS.LOGIN);
            setWelcomeModal(false);
          }}
          accessibilityViewIsModal={true}
          importantForAccessibility={ImportantForAccessibility.YES}
          renderHeader={<SvgXml xml={HeartCare} testID="speciality-care" />}
          renderContent={
            <View style={styles.descriptionContainer}>
              <Heading
                testID={'welcomeSheet.title'}
                textAlign={TEXT_ALIGN.CENTER}
                title={t('welcomeSheet.title')}
                color={TEXTLINK_COLORS.NEUTRAL_HIGH}
                size={HEADING_SIZE.Small}
                accessibilityLabel={t('welcomeSheet.title')}
              />
              <Text
                text={t('welcomeSheet.description1')}
                size={TEXT_SIZE.LARGE}
                testID="welcomeSheet.description1"
                textAlign={TEXT_ALIGN.CENTER}
                color={TEXTLINK_COLORS.NEUTRAL_HIGH}
              />
              <Text
                text={t('welcomeSheet.description2')}
                size={TEXT_SIZE.LARGE}
                testID="welcomeSheet.description2"
                textAlign={TEXT_ALIGN.CENTER}
                color={TEXTLINK_COLORS.NEUTRAL_HIGH}
              />
            </View>
          }
          renderAction={
            <View style={[styles.welcomeButtonView, { paddingBottom: insets.bottom }]}>
              <Button
                fullWidth
                theme={theme}
                testID={'view-careers-button'}
                size={TEXT_SIZE.SMALL}
                label={t('welcomeSheet.viewCareers')}
                accessible
                accessibilityRole="none"
                accessibilityLabel={t('welcomeSheet.viewCareers')}
                accessibilityHint={
                  Platform.OS === 'ios'
                    ? t('ada.linkOpenInWebview') + t('ada.doubleTapToActivate')
                    : t('ada.linkOpenInWebview')
                }
                onPress={() => careersWebLink('not-an-associate-link')}
              />
            </View>
          }
        />
      </View>
    </AuthorableBG>
  );
};
export default LoginScreen;

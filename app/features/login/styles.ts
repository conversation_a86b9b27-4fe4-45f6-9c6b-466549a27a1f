import { Theme } from 'pantry-design-system';
import { Dimensions, ImageStyle, StyleSheet, TextStyle, ViewStyle } from "react-native";
import Device from 'react-native-device-info';

const { width, height } = Dimensions.get("window");


const getStyes = ({ colors, fonts, borderDimens, dimensions, typography, borderStyle }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      alignItems: "center",
      justifyContent: "center",
      width: width,
      height: height,
      padding: 16,
    } as ViewStyle,
    loginContainer: {
      alignSelf: "center",
      width: "100%",
      alignItems: "center",
      justifyContent: "space-between",
      position: "absolute",
      bottom: 32,
      left: 16,
      right: 16,
      minHeight: 350,
    } as ViewStyle,
    firstLoginContainer: {
      alignSelf: 'center',
      width: '100%',
      alignItems: 'center',
      justifyContent: 'space-between',
      position: 'absolute',
      left: 16,
      right: 16,
      minHeight: 116,
    } as ViewStyle,
    firstLoginContainerTablet: {
      alignSelf: 'center',
      width: '100%',
      alignItems: 'center',
      justifyContent: 'space-between',
      position: 'absolute',
      left: 16,
      right: 16,
      minHeight: 156,
    } as ViewStyle,
    loadContainer: {
      alignSelf: "center",
      width: "100%",
      alignItems: "center",
    },
    loginContainerTablet: {
      alignSelf: "center",
      width: "100%",
      alignItems: "center",
      justifyContent: "space-between",
      position: "absolute",
      bottom: 74,
      minHeight: 390,
    } as ViewStyle,
    ldapContainer: {
      width: "100%",
      justifyContent: "center",
      alignContent: "center",
    } as ViewStyle,
    ldapContainerTablet: {
      width: "100%",
      justifyContent: "center",
      alignContent: "center",
      top: dimensions.pdsGlobalSpace1000,
    } as ViewStyle,
    horizontalLine: {
      height: 1,
      backgroundColor: colors.pdsThemeColorOutlineNeutralLow,
      width: "95%", // Full width
      alignSelf: "center",
      marginTop: dimensions.pdsGlobalSpace400,
      marginBottom: dimensions.pdsGlobalSpace400,
    },
    welcomeText: {
      fontSize: typography.pdsGlobalFontSize600,
      fontFamily: fonts.pdsGlobalFontFamilyPoppins,
      alignSelf: "center",
      fontWeight: typography.pdsGlobalFontWeight500,
      color: colors.pdsThemeColorForegroundNeutralHighInverse,
      lineHeight: 33.6,
      marginTop: dimensions.pdsGlobalSpace400,
    } as TextStyle,
    welcomeTextTablet: {
      fontSize: typography.pdsGlobalFontSize800,
      fontFamily: fonts.pdsGlobalFontFamilyPoppins,
      alignSelf: 'center',
      fontWeight: typography.pdsGlobalFontWeight500,
      color: colors.pdsThemeColorForegroundNeutralHighInverse,
      lineHeight: 40.8,
      marginTop: dimensions.pdsGlobalSpace400,
    } as TextStyle,
    ldapText: {
      marginTop: dimensions.pdsGlobalSpace400,
      fontSize: borderDimens.pdsGlobalBorderRadius300,
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
      fontWeight: typography.pdsGlobalFontWeight600,
      color: colors.pdsThemeColorForegroundNeutralHighInverse,
      textAlign: "center",
      lineHeight: 23.2,
    } as TextStyle,
    buttonContainer: {
      marginTop: dimensions.pdsGlobalSpace500,
      minHeight: dimensions.pdsGlobalSpace1200,
      borderRadius: borderDimens.pdsGlobalBorderRadius1000,
      backgroundColor: colors.pdsThemeColorBackgroundBaseline,
      width: "100%",
      justifyContent: "center",
      alignItems: "center",
      flexDirection: "row",
    } as ViewStyle,
    buttonContainerTablet: {
      marginTop: dimensions.pdsGlobalSpace500,
      minHeight: dimensions.pdsGlobalSpace1200,
      width: 343,
      borderRadius: borderDimens.pdsGlobalBorderRadius1000,
      backgroundColor: colors.pdsThemeColorBackgroundBaseline,
      justifyContent: "center",
      alignItems: "center",
      flexDirection: "row",
      marginHorizontal: 'auto',
    } as ViewStyle,
    ldapIcon: {
      width: dimensions.pdsGlobalSpace600,
      height: dimensions.pdsGlobalSpace600,
      resizeMode: "contain",
    } as ImageStyle,
    buttonText: {
      fontSize: typography.pdsGlobalFontSize300,
      fontWeight: typography.pdsGlobalFontWeight600,
      color: colors.pdsThemeColorForegroundNeutralHigh,
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
      textAlign: "center",
      marginLeft: dimensions.pdsGlobalSpace300,
    } as TextStyle,
    containerStyle: {
      alignItems: 'center', width: '100%',
    },
    contentStyle: {
      alignItems: 'center',
    },
    bottomContainer: {
      width: "100%",
      justifyContent: "center",
      alignContent: "center",
    } as ViewStyle,
    languageText: {
      fontSize: typography.pdsGlobalFontSize200,
      fontWeight: typography.pdsGlobalFontWeight600,
      color: colors.pdsThemeColorForegroundNeutralHighInverse,
      textAlign: "center",
      textDecorationLine: typography.pdsGlobalFontTextDecorationUnderline,
    } as TextStyle,
    termsText: {
      fontSize: typography.pdsGlobalFontSize100,
      fontWeight: typography.pdsGlobalFontWeight600,
      color: colors.pdsThemeColorForegroundNeutralHighInverse,
      textAlign: "center",
      marginHorizontal: dimensions.pdsGlobalSpace50,
      marginVertical: dimensions.pdsGlobalSpace50,
    } as TextStyle,
    termAndPrivacyLink: {
      flexDirection: "row",
      alignSelf: "center",
      alignItems: "center",
      justifyContent: "center",
      marginTop: dimensions.pdsGlobalSpace400,
    } as ViewStyle,
    forgetPasswordText: {
      flexDirection: "row",
      alignSelf: "center",
      marginTop: dimensions.pdsGlobalSpace400,
      marginBottom: dimensions.pdsGlobalSpace400
    },
    helpIconStyle: {
      marginTop: dimensions.pdsGlobalSpace100,
      marginRight: dimensions.pdsGlobalSpace100
    },
    descriptionContainer: {
      marginTop: dimensions.pdsGlobalSpace400,
      marginHorizontal: dimensions.pdsGlobalSpace200,
      alignItems: "center",
      justifyContent: 'center',
      gap: dimensions.pdsGlobalSpace300,
    },
    headingView: {
      alignItems: 'center'
    },
    extraContainerSpace: {
      bottom: 30
    },
    welcomeButtonView: {
      marginTop: dimensions.pdsGlobalSpace500,
      marginBottom: dimensions.pdsGlobalSpace800
    },
    linkText: {
      color: colors.pdsThemeColorForegroundNeutralHighInverse,
      fontWeight: typography.pdsGlobalFontWeight600,
      fontSize: typography.pdsGlobalFontSize200,
      textDecorationLine: "underline",
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSansSemiBold600,
    },
    useOfThis: {
      color: colors.pdsThemeColorForegroundNeutralHighInverse,
      fontWeight: typography.pdsGlobalFontWeight400,
      fontSize: typography.pdsGlobalFontSize200,
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
      lineHeight: 20.5,
      textAlign: "center"
    },
    notAnAssociate: {
      marginTop: dimensions.pdsGlobalSpace400,
    },
    scrollContent: {
      flexGrow: 1,
    },
    overlayContent: {
      flex: 1,
      justifyContent: 'flex-end',
      alignItems: 'center',
      padding: 24,
    },
    divider: {
      height: 1,
      backgroundColor: 'white',
      width: '100%',
      marginVertical: 10,
    },
  });

  return styles;
};

export default getStyes;

import { Theme } from 'pantry-design-system';
import { ImageStyle, StyleSheet, TextStyle } from "react-native";

//customising styles based on themes
const getStyles = ({ colors, borderDimens, dimensions, borderStyle, fonts, typography }: Theme) => {
    const styles = StyleSheet.create({
        row: {
            flexDirection: "row",
            alignItems: "center",
            gap: 8,
            marginHorizontal: 16,
            marginVertical: 8,
        },
        container: {
            backgroundColor: colors.pdsThemeColorBackgroundInfo,
            paddingTop: dimensions.pdsGlobalSpace100,
        },
        icon: {
            color: colors.pdsThemeColorBackgroundPrimaryHigh,
            width: dimensions.pdsGlobalSizeWidth200,
            height: dimensions.pdsGlobalSizeWidth200,
        } as ImageStyle,
        heading: {
            fontFamily: fonts.pdsGlobalFontFamilyPoppins,
            fontSize: typography.pdsGlobalFontSize500,
            color: colors.pdsThemeColorForegroundNeutralHigh,
            fontWeight: typography.pdsGlobalFontWeight500,
            marginBottom: dimensions.pdsGlobalSpace100,
            textAlign: "center",
            lineHeight: 28,
        } as TextStyle,
        subHeading: {
            fontSize: typography.pdsGlobalFontSize300,
            color: colors.pdsGlobalColorGeneralBlackA100,
            fontWeight: typography.pdsGlobalFontWeight400,
            textAlign: "center",
            fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
            lineHeight: 20,
        } as TextStyle
    });

    return styles
}

export default getStyles;

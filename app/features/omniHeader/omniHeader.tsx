/* eslint-disable @typescript-eslint/no-explicit-any */
import { CommonActions, useNavigation } from '@react-navigation/native';
import { useTheme } from 'pantry-design-system';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Text, Image, TouchableOpacity, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import AntDesign from 'react-native-vector-icons/AntDesign';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import { useSelector } from 'react-redux';

import icons from '../../../assets/icons';
import { SCREENS, SHIFT_ENDING_DURATION } from '../../shared/constants';
import { durationInWords } from '../../utils/TimeUtils';
import { HOME_CONSTANTS } from '../home/<USER>';

import getStyles from './styles';

const OmniHeader = (): React.ReactElement => {
  const { t } = useTranslation();
  const navigation = useNavigation();
  const { theme } = useTheme(); // Fething themes from ThemeProvider
  const styles = getStyles(theme); // Get styles from theme
  const insets = useSafeAreaInsets();

  const {
    clockedIn,
    lunchStarted,
    lunchStartTime,
    lunchEnded,
    clockInTime,
    durationInMinutes,
    sessionEnding,
    lunchDurationInMinutes,
    clockedOut,
    lateClockInTimeDuration,
    lateClockOutTimeDuration,
  } = useSelector((state: any) => state.shift); //Accessing from global state
  const { isUserInStore } = useSelector((state: any) => state.profile);

  const redirectHome = (): void => {
    navigation.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [{ name: SCREENS.HOME_STACK }],
      }),
    );
  };

  const {
    SHIFT_STARTS_IN_MINUTES,
    LUNCH_STARTED_AGO,
    LUNCH_STARTED_NOW,
    ON_SHIFT,
    CLOCKED_IN,
    SHIFT_ENDING_SOON,
    SHIFT_STARTED,
    SHIFT_ENDED,
  } = HOME_CONSTANTS;

  if (isUserInStore) {
    // If the associate is not clocked in, show Shift information
    if (!clockedIn && !clockedOut) {
      return (
        <View style={[styles.container, { paddingTop: insets.top }]}>
          <TouchableOpacity
            style={styles.row}
            onPress={redirectHome}
            testID="omni-header-shift-start"
            accessibilityRole="button" // Marks this as a button
            accessible={true} // Ensures the TouchableOpacity is accessible
            accessibilityLabel={
              durationInMinutes > 0
                ? `${t(SHIFT_STARTS_IN_MINUTES).replace('{time}', durationInMinutes)}.`
                : `${t(SHIFT_STARTED).replace('{timeDifference}', lateClockInTimeDuration)}`
            }
            accessibilityHint={t('omniHeaderShiftStartHint')} // Describes the action
          >
            <Image
              source={icons.TIME_CLOCK_IN}
              style={styles.icon}
              accessibilityIgnoresInvertColors={false}
            />
            <Text style={styles.subHeading} allowFontScaling={false}>
              {durationInMinutes > 0
                ? `${t(SHIFT_STARTS_IN_MINUTES).replace('{time}', durationInMinutes)}.`
                : `${t(SHIFT_STARTED).replace('{timeDifference}', lateClockInTimeDuration)}`}
            </Text>
          </TouchableOpacity>
        </View>
      );
    }
    // If the associate is clocked in but lunch hasn't started or ended, show shift clocked in details
    else if (clockedIn && !lunchStarted && !lunchEnded) {
      return (
        <View
          style={[
            styles.container,
            { paddingTop: insets.top, backgroundColor: theme.colors.pdsGlobalColorGeneralGreen10 },
          ]}
        >
          <TouchableOpacity
            style={styles.row}
            onPress={redirectHome}
            testID="omni-header-on-shift"
            accessibilityRole="button" // Marks this as a button
            accessible={true} // Ensures the TouchableOpacity is part of the accessibility tree
            accessibilityLabel={`${t(ON_SHIFT)}. ${t(CLOCKED_IN).replace('{time}', clockInTime)}`} // Describes the button's content
            accessibilityHint={t('omniHeaderShiftStartHint')} // Describes the action of the button
          >
            <Image
              source={icons.TIME_ON_SHIFT}
              style={styles.icon}
              accessibilityIgnoresInvertColors={false}
            />
            <Text style={styles.subHeading} allowFontScaling={false}>
              {`${t(ON_SHIFT)}. ${t(CLOCKED_IN).replace('{time}', clockInTime)}`}.
            </Text>
          </TouchableOpacity>
        </View>
      );
    }
    // If the associate is on lunch, show lunch information
    else if (lunchStarted && !lunchEnded) {
      return (
        <View
          style={[
            styles.container,
            { paddingTop: insets.top, backgroundColor: theme.colors.pdsGlobalColorGeneralYellow10 },
          ]}
        >
          <TouchableOpacity
            accessibilityRole="button"
            style={styles.row}
            testID="omni-header-lunch-started"
            onPress={redirectHome}
          >
            <AntDesign
              accessible={true} // Ensures the icon is also accessible
              accessibilityLabel="Alarm Icon" // Describes the icon's function or meaning
              accessibilityRole="image" // Specifies that it's a non-interactive image (icon)
              name="pause"
              accessibilityHint={''}
              size={24}
              color={theme.colors.pdsThemeColorForegroundPrimary}
            />
            <Text
              style={styles.subHeading}
              accessible={true} // Ensures the text is accessible
              allowFontScaling={false}
              accessibilityLabel={
                lunchDurationInMinutes > 0
                  ? t(LUNCH_STARTED_AGO)
                    .replace('{timeDifference}', lunchDurationInMinutes)
                    .replace('{time}', lunchStartTime)
                  : t(LUNCH_STARTED_NOW).replace('{time}', lunchStartTime)
              }
              accessibilityRole="text" // Role for text elements
              accessibilityHint={t('omniHeaderShiftStartHint')} // Describes the action of the button
            >
              {lunchDurationInMinutes > 0
                ? t(LUNCH_STARTED_AGO)
                  .replace('{timeDifference}', lunchDurationInMinutes)
                  .replace('{time}', lunchStartTime)
                : t(LUNCH_STARTED_NOW).replace('{time}', lunchStartTime)}
              .
            </Text>
          </TouchableOpacity>
        </View>
      );
    }
    // Informing associate's shift is ending soon and already shift ended time duration
    else if (lunchStarted && lunchEnded && sessionEnding <= SHIFT_ENDING_DURATION && !clockedOut) {
      return (
        <View
          style={[
            styles.container,
            { paddingTop: insets.top, backgroundColor: theme.colors.pdsThemeColorBackgroundError },
          ]}
        >
          <TouchableOpacity
            style={styles.row}
            onPress={redirectHome}
            testID="omni-header-shift-ending"
            accessible={true} // Ensures this element is accessible
            accessibilityLabel={
              sessionEnding > 0
                ? t(SHIFT_ENDING_SOON).replace('{time}', sessionEnding)
                : t(SHIFT_ENDED).replace(
                  '{timeDifference}',
                  durationInWords(lateClockOutTimeDuration, t),
                )
            }
            accessibilityRole="button" // Indicates it's a button
            accessibilityHint={t('omniHeaderShiftStartHint')}
          >
            <MaterialIcons
              accessible={true} // Ensures the icon is also accessible
              accessibilityLabel="Alarm Alert" // Describes the icon's function or meaning
              accessibilityRole="image" // Specifies that it's a non-interactive image (icon)
              name="alarm"
              size={24}
              accessibilityHint={''}
              color={theme.colors.pdsThemeColorForegroundPrimary}
            />
            <Text
              style={styles.subHeading}
              accessibilityHint={''}
              accessibilityLabel={
                sessionEnding > 0
                  ? t(SHIFT_ENDING_SOON).replace('{time}', sessionEnding)
                  : t(SHIFT_ENDED).replace(
                    '{timeDifference}',
                    durationInWords(lateClockOutTimeDuration, t),
                  )
              }
              allowFontScaling={false}
            >
              {sessionEnding > 0
                ? t(SHIFT_ENDING_SOON).replace('{time}', sessionEnding)
                : t(SHIFT_ENDED).replace(
                  '{timeDifference}',
                  durationInWords(lateClockOutTimeDuration, t),
                )}
            </Text>
          </TouchableOpacity>
        </View>
      );
    }

    // Non of the sate matches not showing Omniheader
    else {
      return (
        <View
          style={{
            paddingTop: insets.top,
            backgroundColor: theme.colors.pdsThemeColorBackgroundRaised,
          }}
        />
      );
    }
  } else {
    return (
      <View
        style={{
          paddingTop: insets.top,
          backgroundColor: theme.colors.pdsThemeColorBackgroundRaised,
        }}
      />
    );
  }
};

export default OmniHeader;

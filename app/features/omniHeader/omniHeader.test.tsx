import { render } from '@testing-library/react-native';
import { useTheme } from 'pantry-design-system';
import React from 'react';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';

import OmniHeader from './omniHeader';

jest.mock('@react-navigation/native', () => ({
    useNavigation: jest.fn(() => ({
        dispatch: jest.fn(),
    })),
}));
jest.mock('react-native-vector-icons/AntDesign', () => 'AntDesign');
jest.mock('react-native-vector-icons/MaterialIcons', () => 'MaterialIcons');

jest.mock('pantry-design-system', () => ({
    useTheme: jest.fn(),
}));

jest.mock('../../utils/TimeUtils', () => ({
    calculateTimeDiff: jest.fn(),
    calculateTimeDiffinMinutes: jest.fn(),
    convertStringToDate: jest.fn(),
    getCurrentTime: jest.fn(),
}));

jest.mock('react-i18next', () => ({
    useTranslation: () => ({
        t: (key: string, params: Record<string, any> = {}) =>
            key
                .replace('{time}', params.time || '10')
                .replace('{timeDifference}', params.timeDifference || '20 mins'),
    }),
}));

const mockStore = configureStore([]);

describe('OmniHeader', () => {
    let store;

    beforeEach(() => {
        store = mockStore({
            profile: { isUserInStore: true },
            shift: {
                clockedIn: false,
                lunchStarted: false,
                lunchEnded: false,
                clockedOut: false,
                durationInMinutes: 10,
                lateClockInTimeDuration: 5,
                sessionEnding: 0,
                lunchDurationInMinutes: 0,
                lateClockOutTimeDuration: 0,
            },
        });
        (useTheme as jest.Mock).mockReturnValue({
            theme: {
                colors: { pdsThemeColorBackgroundPrimary: '#ffffff' },
                fonts: { pdsGlobalFontFamilyPoppins: 'Poppins' },
                dimensions: { pdsGlobalSizeWidth200: 16 },
                typography: { pdsGlobalFontSize500: 18 },
                borderDimens: { pdsGlobalBorderRadius300: 8 },
            },
        });
    });

    it('renders the correct view when the user is not clocked in or clocked out', () => {
        const { getByTestId, getByRole } = render(
            <Provider store={store}>
                <OmniHeader />
            </Provider>,
        );

        expect(getByRole('button')).toBeTruthy();
        expect(getByTestId('omni-header-shift-start')).toBeTruthy();
    });

    it('renders the correct view when the user is clocked in', () => {
        store = mockStore({
            profile: { isUserInStore: true },
            shift: {
                clockedIn: true,
                lunchStarted: false,
                lunchEnded: false,
                clockedOut: false,
                clockInTime: '10:00 AM',
            },
        });
        const { getByTestId } = render(
            <Provider store={store}>
                <OmniHeader />
            </Provider>,
        );
        expect(getByTestId('omni-header-on-shift')).toBeTruthy();
    });

    it('renders the correct view when the user is on lunch', () => {
        store = mockStore({
            profile: { isUserInStore: true },
            shift: {
                clockedIn: true,
                lunchStarted: true,
                lunchEnded: false,
                lunchDurationInMinutes: 20,
            },
        });

        const { getByTestId } = render(
            <Provider store={store}>
                <OmniHeader />
            </Provider>,
        );
        expect(getByTestId('omni-header-lunch-started')).toBeTruthy();
    });

    it('renders the correct view when the shift is ending soon', () => {
        store = mockStore({
            shift: {
                clockedIn: true,
                lunchStarted: true,
                lunchEnded: true,
                sessionEnding: 10,
                lateClockOutTimeDuration: 5,
            },
            profile: { isUserInStore: true },
        });

        const { getByTestId } = render(
            <Provider store={store}>
                <OmniHeader />
            </Provider>,
        );

        expect(getByTestId('omni-header-shift-ending')).toBeTruthy();
    });
});

/**
 * AuthLogout Component
 *
 * This component handles the logout process for the application. It uses a WebView to navigate
 * to the logout endpoint and clears all user-related data upon successful logout.
 * After clearing the data, it resets the navigation stack and redirects the user to the authentication stack.
 */

import React, { useEffect, useRef } from "react";
import WebView from "../../../components/WebView";
import { useDispatch } from "react-redux";
import { CommonActions, useNavigation } from "@react-navigation/native";
import { SecureStorage } from "../../store/SecureStorage";
import * as AsyncStorage from "../../store/AsyncStorage";
import ABAuditEngine from "../../../analytics/ABAuditEngine";
import { clearProfile } from "../../store/reducers/profileSlice";
import { clearAnnouncements } from "../../store/reducers/announcementSlice";
import Cookies from "@react-native-cookies/cookies";
import { useSelector } from "react-redux";
import { setUserProperties, userActionLogEvent } from "../../../analytics/AnalyticsUtils";
import { LOGIN_ANALYTICS } from "../../../analytics/AnalyticsConstants";
import { SCREENS } from "../../shared/constants";
import { cleanupImageCache } from "../home/<USER>";
import { cancelAllRequests } from "../../config/api/restClient";
const AuthLogout = () => {
  // Access the Redux state using the useSelector hook
  const profileState = useSelector((state: any) => state.profile);
  const { cacheImgUrls, allCacheImgUrls } = useSelector((state: any) => state.announcements);

  // Navigation hook to manage navigation actions
  const navigation = useNavigation();

  // Redux dispatch hook to dispatch actions
  const dispatch = useDispatch();

  // Instance of SecureStorage to manage secure data
  const secureStorage = new SecureStorage();

  // Configuration for logout process
  const config = {
    redirectUrl: process.env.MSAUTH_LOGOUT_REDIRECT_URL ?? "", // Redirect URL after logout
    serviceConfiguration: {
      endSessionEndpoint: process.env.MSAUTH_END_SESSION_ENDPOINT ?? "", // Logout endpoint
    },
  };

  useEffect(() => {
    cancelAllRequests();
  }, [])

  // Construct the logout URL with the redirect URI
  const authUrl = `${config.serviceConfiguration.endSessionEndpoint}?post_logout_redirect_uri=${encodeURIComponent(config.redirectUrl)}`;

  const isLogOutSuccess = useRef(false);
  /**
   * Handle navigation state changes in the WebView.
   * * This function is triggered when the WebView navigates to a new URL.
   * * It checks if the URL matches the redirect URL after logout and performs necessary cleanup actions.
   * * These actions include clearing cookies, secure storage, AsyncStorage, and user data from the analytics engine.
   * * Finally, it resets the navigation stack to redirect the user to the authentication stack.
   * @param navState - The navigation state object from the WebView.
   */
  const handleNavigationStateChange = async (navState: any) => {
    const { url } = navState;

    // Check if the URL matches the redirect URL after logout
    if (url.startsWith(config.redirectUrl) || url.includes(encodeURIComponent(config.redirectUrl))) {
      try {
        if (!isLogOutSuccess.current) {
          isLogOutSuccess.current = true; // Set the logout success flag to true

          // Dispatch an action to clear the user profile from Redux store
          dispatch(clearProfile());

          // Clear announcements from Redux store and clean up cached images
          dispatch(clearAnnouncements())
          await cleanupImageCache([], [...cacheImgUrls, ...allCacheImgUrls]);

          ABAuditEngine.startNextSession();

          // Clear cookies
          await Cookies.clearAll();

          await secureStorage.clearAllExcept();

          // Clear AsyncStorage except for specific keys
          await AsyncStorage.clearAllExcept();

          // Clear all user data from the analytics engine
          await ABAuditEngine.clearAllUserData();

          // Start a new session in the analytics engine

          // Log a user action event for logout confirmation
          userActionLogEvent(LOGIN_ANALYTICS.EVENT_CATEGORY, LOGIN_ANALYTICS.EVENT_ACTION, LOGIN_ANALYTICS.EVENT_LABEL);

          // Set user analytics data empty after logout
          setUserProperties();

          // Reset the navigation stack and redirect to the authentication stack
          setTimeout(() => {

            navigation.dispatch(
              CommonActions.reset({
                index: 0, // Reset navigation stack index
                routes: [{ name: SCREENS.AUTH_STACK }], // Navigate to the authentication stack
              })
            );
          }, 1000); // Delay to ensure all actions are completed
        }

      } catch (error) {
        console.error("Error during logout cleanup:", error);

      }
    }
  };

  return (
    <WebView
      route={{
        params: {
          url: authUrl,
          onNavigationStateChange: handleNavigationStateChange,
          showFooter: false,
        },
      }}
    />
  );
};

export default AuthLogout;

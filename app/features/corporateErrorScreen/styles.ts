import { Dimensions, StyleSheet } from 'react-native';

import { IPAD_WIDTH } from '../../shared/constants';

import type { Theme } from 'pantry-design-system';
import type { EdgeInsets } from 'react-native-safe-area-context';

//customising styles based on themes
const getStyles = (
    { colors, fonts, dimensions, typography, borderDimens }: Theme,
    isTablet: boolean,
    insets: EdgeInsets,
) => {
    const { width } = Dimensions.get('window');
    const scrrenWidth = isTablet ? IPAD_WIDTH : width;
    const bannerGroupWidth: number = scrrenWidth - dimensions.pdsGlobalSpace1100 * 2;
    const styles = StyleSheet.create({
        actionGroupSpacing: {
            flex: 1,
        },
        backButton: {
            alignItems: 'center',
            backgroundColor: colors.pdsThemeColorBackgroundPrimary,
            borderRadius: borderDimens.pdsGlobalBorderRadius1000,
            justifyContent: 'center',
            marginBottom: dimensions.pdsGlobalSpace500,
            marginHorizontal: dimensions.pdsGlobalSpace1400,
            minHeight: dimensions.pdsGlobalSizeHeight800,
            paddingVertical: dimensions.pdsGlobalSpace400,
            width: '90%',
        },
        backButtonText: {
            color: colors.pdsThemeColorForegroundNeutralHigh,
            fontFamily: fonts.pdsGlobalFontFamilyPoppins,
            fontSize: typography.pdsGlobalFontSize500,
            lineHeight: dimensions.pdsGlobalSpace500,
            marginBottom: dimensions.pdsGlobalSpace500,
            marginTop: dimensions.pdsGlobalSpace500,
            textAlign: 'center',
        },
        bannerLogoConatiner: {
            alignItems: 'center',
            flexDirection: 'row',
            flexWrap: 'wrap',
            justifyContent: 'center',
            marginTop: dimensions.pdsGlobalSpace500,
            width: bannerGroupWidth,
        },
        bannerLogoItem: {
            marginHorizontal: 5,
            marginTop: 10,
        },
        companyLogoConatiner: {
            alignItems: 'center',
            marginTop: dimensions.pdsGlobalSpace1300,
            width: bannerGroupWidth,
        },

        container: {
            flex: 1,
            alignItems: 'center',
            // justifyContent: "center",
            backgroundColor: colors.pdsThemeColorBackgroundBaseline,
            paddingBottom: insets.bottom,
            paddingTop: insets.top,
        },
        contentConatiner: {
            marginTop: 150, //the value 150 is not from the PDS design tokens,
            marginHorizontal: dimensions.pdsGlobalSpace400,
            alignItems: 'center',
            marginBottom: dimensions.pdsGlobalSpace1600,
        },
        contentContainer: {
            flex: 1,
        },
        descriptionContainer: {
            alignItems: 'center',
            gap: dimensions.pdsGlobalSpace400,
            marginHorizontal: dimensions.pdsGlobalSpace400,
            marginTop: dimensions.pdsGlobalSpace400,
        },

        errorIcon: {
            marginTop: dimensions.pdsGlobalSpace100,
        },

        errorMessageContainer: {
            flexDirection: 'row',
            gap: dimensions.pdsGlobalSpace50,
            marginHorizontal: dimensions.pdsGlobalSpace600,
            marginTop: dimensions.pdsGlobalSpace200,
        },
        footerView: {
            // flex: 1,
            width: isTablet ? IPAD_WIDTH : '100%',
            alignSelf: 'center',
            paddingHorizontal: dimensions.pdsGlobalSizeWidth200,
            maxWidth: isTablet ? IPAD_WIDTH : undefined,
            backgroundColor: colors.pdsThemeColorBackgroundBaseline,
            paddingBottom: insets.bottom,
        },
        mainContainer: {
            alignItems: 'center',
            alignSelf: 'center',
            backgroundColor: colors.pdsThemeColorBackgroundBaseline,
            flex: 1,
            maxWidth: isTablet ? IPAD_WIDTH : undefined,
        },
        underLineImage: {
            height: dimensions.pdsGlobalSizeWidth200,
            marginTop: dimensions.pdsGlobalSpace500,
            width: 186,
        },
    });

    return styles;
};

export default getStyles;

/* eslint-disable @typescript-eslint/no-explicit-any */
/**
 * CorporateErrorScreen Component
 *
 * This component displays an error screen for corporate users. It includes:
 * - A company logo and banner logos.
 * - A personalized greeting with the user's first name.
 * - An error message with an icon and description.
 * - A footer with an action button to navigate back or log out.
 *
 * Dependencies:
 * - React, Redux, React Native, Pantry Design System, and i18next for translations.
 * - Custom assets like logos and SVGs.
 *
 * Props: None
 * State: Uses Redux state for profile and device information.
 */

import { useNavigation } from '@react-navigation/native';
import { useTheme, Heading, Text, Button } from 'pantry-design-system';
import React, { useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { View, ScrollView } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useDispatch, useSelector } from 'react-redux';

import BannerLogos from '../../../assets/images/bannerLogos';
import { AlbertsonsCompanies } from '../../../assets/images/bannerLogos/AlbertsonsCompanies';
import { Underline } from '../../../assets/images/svg/Underline';
import { Warning } from '../../../assets/images/svg/Warning';
import { useWeekCalendar, useFontScaleRerender } from '../../hooks';
import {
  LISTED_BANNERS,
  TEXTLINK_COLORS,
  FALLBACK_BANNER,
  ALBERTSONS_LOGO,
  TEXT_ALIGN,
  TEXT_SIZE,
  HEADING_SIZE,
} from '../../shared/constants';
import * as AsyncStorage from '../../store/AsyncStorage';
import {
  fetchEmployeeScheduleRequest,
} from '../../store/reducers/employeeScheduleSlice';
import { fetchfeatureFlagRequest } from '../../store/reducers/featureFlagSlice';
import { SecureStorage, TokenType } from '../../store/SecureStorage';
import { scheduleTabEnabled } from '../../store/selectors/featureFlagsSelectors';

import getStyles from './styles';

import type { AssociateProfile } from '../../misc/models/ProfileModal';

const CorporateErrorScreen = (): React.ReactElement => {
  // Translation hook for internationalization
  const { t } = useTranslation();
  const key = useFontScaleRerender();

  // Redux selectors for profile and device information
  const profile: AssociateProfile = useSelector((state: any) => state.profile.profile);
  const { isTablet } = useSelector((state: any) => state.deviceInfo);

  // Theme hook for styling
  const { theme } = useTheme();

  // Dynamic underline height based on device type
  const underlineHeight = 25;

  const insets = useSafeAreaInsets();

  // Styles generated dynamically based on theme and device type
  const styles = getStyles(theme, isTablet, insets);

  // Navigation hook for screen transitions
  const navigation = useNavigation();
  const dispatch = useDispatch();

  const secureStorage = new SecureStorage();

  const { weekNumber, year, weekStartDate, weekEndDate } = useWeekCalendar([]);

  const scheduleEnabled = useSelector(scheduleTabEnabled);

  const hasFetchedRef = useRef(false);

  // Fetch the employee ID from secure storage asynchronously
  const fetchEmployeeSchedule = async (
    weekNumberToFetch = weekNumber,
    yearToFetch = year,
    fromDate?: string,
    toDate?: string,
  ) => {
    const requestId = await AsyncStorage.uniqueSessionId();
    const employeeId = await secureStorage.getToken(TokenType.employeeId);

    dispatch(
      fetchEmployeeScheduleRequest({
        params: {
          requestId,
          empId: employeeId,
          weekNumber: weekNumberToFetch,
          year: yearToFetch,
          ...(fromDate && toDate ? { fromDate, toDate } : {}), // Only include if both are set
          isSilent: true,
        },
      }),
    );
  };

  // Fetch the employee schedule only once, and only when scheduleEnabled is true
  useEffect(() => {
    if (scheduleEnabled && !hasFetchedRef.current) {
      hasFetchedRef.current = true;
      fetchEmployeeSchedule(weekNumber, year, weekStartDate, weekEndDate);
    }
  }, [scheduleEnabled, weekNumber, year, weekStartDate, weekEndDate]);

  /**
   * Handles the navigation logic based on the onboarding completion status and the current onboarding step.
   *
   * If the onboarding is completed, it navigates to the 'BOTTOM_TAB_STACK' screen.
   * Otherwise, it navigates to the appropriate onboarding screen based on the current onboarding step.
   *
   * @async
   * @function onPressContinue
   */
  const onPressContinue = async (): Promise<void> => {
    navigation.replace('splashStack', { skip: true });
  };
  React.useEffect(() => {
    fetchFeatureFlagApi().catch((_) => { });
  }, []);

  // Function to fetch feature flags
  const fetchFeatureFlagApi = async (): Promise<void> => {
    const requestId = await AsyncStorage.uniqueSessionId();
    dispatch(
      fetchfeatureFlagRequest({
        requestId,
        banner: profile?.banner || FALLBACK_BANNER,
      }),
    );
  };

  // Extract the first name from the profile
  const firstName = profile?.names?.[0]?.firstName;

  return (
    <View style={styles.container}>
      {/* Main layout: scrollable content + fixed footer */}
      <View key={key} style={styles.contentContainer}>
        <ScrollView showsVerticalScrollIndicator={false}>
          <View style={styles.mainContainer}>
            {/* Company Logo and Banner Logos */}
            <View style={styles.companyLogoConatiner}>
              <AlbertsonsCompanies
                width={ALBERTSONS_LOGO.WIDTH}
                height={ALBERTSONS_LOGO.HEIGHT}
                testID="albertsons-companies"
              />
              <View accessible={false} style={styles.bannerLogoConatiner}>
                {LISTED_BANNERS.map((item, index) => {
                  return (
                    <View key={index} style={styles.bannerLogoItem}>
                      <BannerLogos testID={item} name={item} />
                    </View>
                  );
                })}
              </View>
            </View>

            {/* Content Section */}
            <View style={styles.contentConatiner}>
              {/* User Greeting */}

              <Heading
                title={`${t('corporateError.hi')}, ${firstName ?? ''}!`}
                size={HEADING_SIZE.Large}
                textAlign={TEXT_ALIGN.CENTER}
                color={TEXTLINK_COLORS.NEUTRAL_HIGH}
                accessibilityLabel={`${t('corporateError.hi')}, ${firstName ?? ''}!`}
                testID="hi-firstname-heading"
              />

              {/* Error Message */}
              <View style={styles.errorMessageContainer}>
                <View style={styles.errorIcon}>
                  <Warning
                    size={theme.dimensions.pdsGlobalSizeWidth200}
                    color={theme.colors.pdsThemeColorForegroundWarning}
                  />
                </View>
                <Heading
                  title={t('corporateError.errorMessage')}
                  headingAccessibilityLabel={`${t('corporateError.warning')} ${t('corporateError.errorMessage')}`}
                  size={HEADING_SIZE.XSmall}
                  testID="corporateError.errorMessage"
                  textAlign={TEXT_ALIGN.CENTER}
                  color={TEXTLINK_COLORS.NEUTRAL_HIGH}
                />
              </View>

              {/* Underline Image */}
              <Underline
                testID={'underline'}
                height={underlineHeight}
                color={theme.colors.pdsGlobalColorBrandSutro40}
              />

              {/* Error Descriptions */}
              <View style={styles.descriptionContainer}>
                <Text
                  text={t('corporateError.description1')}
                  size={TEXT_SIZE.LARGE}
                  testID="corporateError.description1"
                  textAlign={TEXT_ALIGN.CENTER}
                />
                <Text
                  text={t('corporateError.description2')}
                  size={TEXT_SIZE.LARGE}
                  testID="corporateError.description2"
                  textAlign={TEXT_ALIGN.CENTER}
                />
              </View>
            </View>

            {/* Spacer */}
            <View style={styles.actionGroupSpacing} />
          </View>
        </ScrollView>
      </View>

      {/* Footer with Action Button */}
      <View style={styles.footerView}>
        <Button
          testID={'continue-button'}
          fullWidth
          theme={theme}
          size={TEXT_SIZE.LARGE}
          label={t('corporateError.continue')}
          onPress={onPressContinue}
          accessible={true}
          accessibilityRole="button"
          accessibilityLabel={t('corporateError.continue')}
          accessibilityHint={''}
        />
      </View>
    </View>
  );
};

export default CorporateErrorScreen;

import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { Provider } from 'react-redux';
import { useNavigation } from '@react-navigation/native';
import configureStore from "redux-mock-store";
import CorporateErrorScreen from '../corporateErrorScreen';
import { SCREENS } from '../../shared/constants';

// Mock Redux Store
const mockStore = configureStore([]);

jest.mock('@react-navigation/native', () => ({
    useNavigation: jest.fn()
}));

jest.mock('react-i18next', () => ({
    useTranslation: () => ({
        t: (key: string) => {
            // Return actual text for common keys
            const translations = {
                'hi': 'Hi',
                'corporateError.errorMessage': 'We encountered an error',
                'corporateError.description1': 'We are experiencing technical difficulties',
                'corporateError.description2': 'Please try again later',
                'continue': 'Continue',
            };
            return translations[key] || key;
        },
    }),
}));

jest.mock('react-native', () => {
    const RN = jest.requireActual('react-native');
    return Object.setPrototypeOf(
        {
            View: ({ children, testID, style }: any) => (
                <div testID={testID} style={style}>{children}</div>
            ),
            SafeAreaView: ({ children, style }: any) => (
                <div style={style}>{children}</div>
            ),
            TouchableOpacity: ({ children, onPress, testID }: any) => (
                <button onClick={onPress} testID={testID}>{children}</button>
            ),
            Text: ({ children, testID }: any) => (
                <span testID={testID}>{children}</span>
            ),
        },
        RN
    );
});

jest.mock("pantry-design-system", () => ({
    useTheme: jest.fn(() => ({
        theme: {
            colors: {
                pdsThemeColorBackgroundBaseline: "#ffffff",
                pdsGlobalColorBrandSutro40: "#00f",
                pdsThemeColorForegroundError: "#f00",
                pdsThemeColorForegroundNeutralHigh: "#000",
                pdsThemeColorOutlineNeutralHigh: "#1F1E1E",
                pdsThemeColorBackgroundPrimary: "#8C0E12",
                pdsThemeColorOutlineNeutralHighInverse: "#FFFFFF",
                pdsThemeColorBackgroundBaselineInverse: "#1F1E1E",
            },
            fonts: {
                pdsGlobalFontFamilyPoppins: "Poppins",
                pdsGlobalFontFamilyNunitoSans: "NunitoSans"
            },
            dimensions: {
                pdsGlobalSpace400: 16,
                pdsGlobalSpace1300: 150,
                pdsGlobalSpace500: 20,
                pdsGlobalSpace600: 24,
                pdsGlobalSpace50: 8,
                pdsGlobalSpace200: 10,
                pdsGlobalSizeWidth200: 200,
                pdsGlobalSpace1600: 64,
                pdsGlobalSpace100: 4,
                pdsGlobalSpace1100: 32,
            },
            typography: {
                pdsGlobalFontSize500: 18,
                pdsGlobalFontSize200: 14,
                pdsGlobalFontWeight700: "700"
            },
            borderDimens: {
                pdsGlobalBorderRadius300: 8,
                pdsGlobalBorderRadius1000: 10
            },
        },
    })),
    Heading: jest.fn(({ title, testID, children }) => (
        <div testID={testID}>{title || children}</div>
    )),
    Text: jest.fn(({ text, testID, children }) => (
        <div testID={testID}>{text || children}</div>
    )),
    ActionGroup: jest.fn(({ topActionCTA_PrimaryOnPress, topActionCTA_PrimaryLabel, testID }) => (
        <button onClick={topActionCTA_PrimaryOnPress} testID={testID}>
            {topActionCTA_PrimaryLabel}
        </button>
    )),
    Button: jest.fn(({ onPress, label, testID, children }) => (
        <button onClick={onPress} testID={testID}>
            {label || children}
        </button>
    )),
    ThemeProvider: ({ children }: { children: React.ReactNode }) => (
        <>{children}</>
    ),
}));

jest.mock('../../../assets/images/bannerLogos/AlbertsonsCompanies', () => ({
    __esModule: true,
    AlbertsonsCompanies: jest.fn(({ width, height }) => (
        <svg height={height} width={width} testID="albertsons-companies" />
    )),
}));

jest.mock('../../../assets/images/bannerLogos', () => {
    return {
        __esModule: true,
        default: jest.fn(({ name }: { name: string }) => (
            <div testID={`banner-image-${name}`} />
        )),
    };
});

jest.mock('../../../assets/images/svg/Warning', () => ({
    __esModule: true,
    Warning: jest.fn(({ size, color }) => (
        <svg width={size} height={size} fill={color} testID="warning" />
    ))
}));

jest.mock('../../../assets/images/svg/Underline', () => ({
    __esModule: true,
    Underline: jest.fn(({ height, color }) => (
        <svg height={height} fill={color} testID="underline" />
    )),
}));

jest.mock('../../shared/constants', () => ({
    LISTED_BANNERS: ['banner1', 'banner2'],
    ALBERTSONS_LOGO: { 'WIDTH': 116, 'HEIGHT': 26 },
    SCREENS: {
        AUTH_LOGOUT: 'AUTH_LOGOUT',
        SPLASH_STACK: 'splashStack',
    },
    TEXTLINK_COLORS: {
        NEUTRAL_HIGH_INVERSE: 'Neutral high inverse'
    },
    SIZE: {
        LARGE: "Large",
        MEDIUM: "Medium",
        SMALL: "Small",
        X_SMALL: "XSmall",
        XX_SMALL: "XXSmall",
    },
    HEADING_SIZE: {
        Large: "Large"
    },
    TEXT_ALIGN: {
        CENTER: 'center'
    },
    TEXT_SIZE: {
        Large: "Large"
    },
    TEXT_WEIGHT: {
        SEMI_BOLD: 'Semi bold'
    },
    TEST_EMPLOYEE_ID: {
        ASSOCIATE_SCHEDULE_EMPID: 'mock-employee-id'
    }
}));

const mockProfile = {
    profile: {
        names: [{ firstName: 'John' }],
    },
};

const initialState = {
    profile: mockProfile,
    deviceInfo: {
        isTablet: false,
    },
    language: {
        id: "en",
        name: "English"
    },
};

const renderWithProviders = (component: React.ReactElement, storeState = initialState) => {
    const store = mockStore(storeState);
    return render(
        <Provider store={store}>
            {component}
        </Provider>
    );
};

describe('CorporateErrorScreen', () => {
    const mockNavigate = jest.fn();

    beforeEach(() => {
        (useNavigation as jest.Mock).mockReturnValue({
            replace: mockNavigate,
        });

        jest.clearAllMocks();
    });

    it('renders correctly with all elements', async () => {
        const { getByTestId } = renderWithProviders(<CorporateErrorScreen />);

        expect(getByTestId('hi-firstname-heading')).toBeTruthy();
        expect(getByTestId('corporateError.errorMessage')).toBeTruthy();
        expect(getByTestId('corporateError.description1')).toBeTruthy();
        expect(getByTestId('corporateError.description2')).toBeTruthy();
        expect(getByTestId('albertsons-companies')).toBeTruthy();
        expect(getByTestId('warning')).toBeTruthy();
        expect(getByTestId('underline')).toBeTruthy();
        expect(getByTestId("continue-button")).toBeTruthy();
    });

    it('navigates to Splash when continue button is pressed', () => {
        const { getByTestId } = renderWithProviders(<CorporateErrorScreen />);

        fireEvent.press(getByTestId('continue-button'));
        expect(mockNavigate).toHaveBeenCalledWith('splashStack', { skip: true });
    });

    it('renders all banner logos with correct test IDs', () => {
        const { getAllByTestId } = renderWithProviders(<CorporateErrorScreen />);

        const bannerLogos = getAllByTestId(/banner-image-/);
        expect(bannerLogos.length).toBe(2);
    });

    it('displays correct user name', () => {
        const { getByTestId } = renderWithProviders(<CorporateErrorScreen />);

        const heading = getByTestId('hi-firstname-heading');
        expect(heading).toBeTruthy();
        // The heading should contain the user's first name
    });

    it('handles tablet view correctly', () => {
        const tabletState = {
            ...initialState,
            deviceInfo: {
                isTablet: true,
            },
        };

        const { getByTestId } = renderWithProviders(<CorporateErrorScreen />, tabletState);

        expect(getByTestId('hi-firstname-heading')).toBeTruthy();
        expect(getByTestId('continue-button')).toBeTruthy();
    });

    it('handles different user names correctly', () => {
        const differentUserState = {
            ...initialState,
            profile: {
                profile: {
                    names: [{ firstName: 'Jane' }],
                },
            },
        };

        const { getByTestId } = renderWithProviders(<CorporateErrorScreen />, differentUserState);

        expect(getByTestId('hi-firstname-heading')).toBeTruthy();
    });

    it('handles missing user name gracefully', () => {
        const noNameState = {
            ...initialState,
            profile: {
                profile: {
                    names: [],
                },
            },
        };

        const { getByTestId } = renderWithProviders(<CorporateErrorScreen />, noNameState);

        expect(getByTestId('hi-firstname-heading')).toBeTruthy();
    });

    it('handles Spanish language correctly', () => {
        const spanishState = {
            ...initialState,
            language: {
                id: "es",
                name: "Spanish"
            },
        };

        const { getByTestId } = renderWithProviders(<CorporateErrorScreen />, spanishState);

        expect(getByTestId('hi-firstname-heading')).toBeTruthy();
        expect(getByTestId('continue-button')).toBeTruthy();
    });

    it('handles undefined language gracefully', () => {
        const undefinedLanguageState = {
            ...initialState,
            language: undefined,
        };

        const { getByTestId } = renderWithProviders(<CorporateErrorScreen />, undefinedLanguageState);

        expect(getByTestId('hi-firstname-heading')).toBeTruthy();
        expect(getByTestId('continue-button')).toBeTruthy();
    });
});
import { Platform } from 'react-native';

import images from '../../assets/images';

import type { BannerNames } from '../../assets/images/bannerLogos';
import type { Theme } from 'pantry-design-system';
import type { AccessibilityRole, ImageSourcePropType } from 'react-native';
import { RESOURCES_ANALYTICS } from '../../analytics/AnalyticsConstants';

export const DISPLAY_TYPES = {
  NONE: 'none',
  FLEX: 'flex',
};

export const BOTTOM_TABS = 5;
export const BOTTOM_TABBAR_PADDING_IPHONE = 88;
export const ONBAORDING_BOTTOMSHEET_HEIGHT = 0.4;

export const SCREENS = {
  AUTH_STACK: 'authStack',
  BOTTOM_TAB_STACK: 'BOTTOM_TAB_STACK',
  HOME_STACK: 'homeStack',
  HOME: 'home',
  GROWTH: 'growth',
  RESOURCES: 'resources',
  TRANSLATIONS: 'translations',
  COMMUNITY: 'community',
  SCHEDULE: 'schedule',
  TODOS: 'todos',
  SUPPORT: 'support',
  LOGIN: 'login',
  WEBVIEW: 'WebViewScreen',
  SIGN_IN_HELP: 'SignInHelp',
  SPLASHSCREEN: 'splashScreen',
  APP_LAUNCH: 'AppLaunch',
  PROFILE: 'profile',
  HOME_SCREEN: 'HomeScreen',
  ANNOUNCEMENTS_VIEW_ALL: 'AnnouncementViewAll',
  ANNOUNCEMENTS_DETAILS: 'AnnouncementDetails',
  ERROR_AUTH: 'SessionOutModal',
  ACCOUNT_LINK_STACK: 'accountLinkStack',
  ACCOUNT_LINK_STEP_INTRO: 'accountLinkIntro',
  ACCOUNT_LINK_STEP_TWO: 'accountLinkTwo',
  ACCOUNT_LINK_USER_INPUT_STEP_ONE: 'accountLinkStepOne',
  ACCOUNT_LINK_STEP_ONE_VERIFY: 'accountLinkOneVerify',
  ACCOUNT_LINK_ALL_DONE: 'accountLinkAllDone',
  ONBOARDING_ONE: 'onboardingOne',
  ONBOARDING_TWO: 'onboardingTwo',
  ONBOARDING_THREE: 'onboardingThree',
  ONBOARDING_FOUR: 'onboardingFour',
  ONBOARDING_FLOW: 'onboardingFlow',
  ONBOARDING_STACK: 'onboardingStack',
  ONBOARDING_STORE_DIRECTOR: 'onboardingStoreDirector',
  LOCATION_GOVERNED: 'locationGoverned',
  CORPORATE_ERROR_SCREEN: 'corporateErrorScreen',
  AUTH_LOGOUT: 'AuthLogout',
  SESSION_OUT: 'SessionOutModal',
  PROFILE_SESSION_OUT: 'SessionModalLayout',
  NOTIFICATIONS: 'NotificationsScreen',
  PRIVACY_POLICY: 'Privacy Policy',
  ASSOCIATE_PRIVACY_NOTICE: 'AssociatePrivacyNotice',
  DOC_PDF_VIEWER: 'DocPdfViewer',
  HR_AND_PAYROLL: 'Hr-Payroll',
  PROFILE_SCREEN: 'ProfileScreen',
  SETTINGS: 'Settings',
  LANGUAGE: 'Language',
  PROFILE_PREFERENCE: 'ProfilePreference',
};

export const ON_BOARDING = {
  ONBOARDING_ONE: 1,
  ONBOARDING_TWO: 2,
  ONBOARDING_THREE: 3,
  ONBOARDING_FOUR: 4,
  ONBOARDING_STEPS: 4,
};

export const URLS = {
  TERMS_OF_USE_PDF: process.env.TERMS_OF_USE_PDF,
  PRIVACY_POLICY: process.env.PRIVACY_POLICY,
  HRANDPAYROLL_URL: process.env.HRANDPAYROLL_URL,
  ENROLLMENT_AND_CHANGES: process.env.ENROLLMENT_AND_CHANGES,
  MY_BENEFITS: process.env.MY_BENEFITS,
  HEALTH_AND_WELL_BEING: process.env.HEALTH_AND_WELL_BEING,
  LEAVE_OF_ABSENCE: process.env.LEAVE_OF_ABSENCE,
  FINANCIAL_WELLNESS: process.env.FINANCIAL_WELLNESS,
  BENEFIT_CONTACTS: process.env.BENEFIT_CONTACTS,
  MY_ACI: process.env.MY_ACI,
  GET_PAYSTUBS: process.env.GET_PAYSTUBS,
  UPDATE_DIRECT_DEPOSIT_ACCOUNT: process.env.UPDATE_DIRECT_DEPOSIT_ACCOUNT,
  LOST_OR_DAMAGED_CHECK: process.env.LOST_OR_DAMAGED_CHECK,
  TAX_CHANGE_WITHHOLDINGS: process.env.TAX_CHANGE_WITHHOLDINGS,
  GET_TAX_DOCUMENTS: process.env.GET_TAX_DOCUMENTS,
  EDIT_MY_ACI: process.env.EDIT_MY_ACI_WEB_LINK,
  UPDATE_PASSWORD: process.env.UPDATE_PASSWORD,
  FORGOT_PASSWORD: process.env.FORGOT_PASSWORD,
  FIND_MY_LDAP: process.env.FIND_MY_LDAP,
  FIND_MY_EMPLOYEE_ID: process.env.FIND_MY_EMPLOYEE_ID,
  OTHER_SIGN_IN_ISSUES: process.env.OTHER_SIGN_IN_ISSUES,
  DEEP_LINK_UMA_REWARDS: process.env.DEEP_LINK_UMA_REWARDS_TAB,
  FEEDBACK: process.env.FEEDBACK,
  ACI_PRIVACY_POLICY: process.env.ACI_PRIVACY_POLICY,
  VIEW_CAREERS: process.env.VIEW_CAREERS,
  SEND_FEEDBACK: process.env.SEND_FEEDBACK,
  CREATE_ACCOUNT: process.env.CREATE_ACCOUNT,
  LEARNING: process.env.LEARNING,
  ASSIGNED_LEARNINGS: process.env.ASSIGNED_LEARNINGS,
  JOURNEYS: process.env.JOURNEYS,
};

export const TERMS_AND_CONDITIONS_URL = Platform.select({
  android: `https://docs.google.com/gview?embedded=true&url=${encodeURIComponent(URLS.TERMS_OF_USE_PDF)}`,
  ios: URLS.TERMS_OF_USE_PDF, // Direct URL for iOS
});

export const languages = [
  {
    id: 'en',
    label: 'English',
    value: 'english',
    image: images.LANGUAGES.EN,
  },
  {
    id: 'es',
    label: 'Spanish (Español)',
    value: 'spanish',
    image: images.LANGUAGES.ES,
  },
];

export const APP_LANGUAGES = {
  ENGLISH: 'en',
  SPANISH: 'es',
};
export const ProfileConstants = {
  PROFILE_PREFERENCES: 'Manage your info and preferences.',
  HR_PAYROLL: 'Get your paystubs and HR updates.',
  SETTINGS: 'Manage language and app settings.',
  PREFERENCE_HEADER: 'Profile and preferences',
  HR_HEADER: 'HR and payroll',
  SETTINGS_HEADER: 'Settings',
  EMP_NO: 'Employee #',
  EMP_POINTS: 'Points',
};

export const TokenTypes = {
  employeeId: 'employeeId',
};

//user of Retail Associates of Albertsons
export const RT = 'RT';

export const ADAM_FULL_SCALE = '?scl=1&defaultImage=Not_Available';

export const SHIFT_ENDING_DURATION = 30; //in minutes
export const HOURS_REMAIN_TO_CLOCK_IN = 1; // 1 hour before shift start
export const HOURS_AROUND_SHIFT = 3; // Hours before and after shift start to allow login/logout checks

export const TABLET_LANDSCAPE_WIDTH_STYLE = {
  width: '70%',
  alignSelf: 'center',
};
export const IPAD_WIDTH = 736;
export const ANIMATION_TIME = 600;
export const ANIMATION_OUT_TIME = 250;
export const ANIMATION_OUT = 150;

export const ADA = {
  ACCESSIBILITY_FOCUS: 500,
  ACCESSIBILITY_BACK_BUTTON_FOCUS: 300,
  PRIVACY_POLICY: 'privacy-policy',
  ASSOCIATE_PRIVACY_NOTICE: 'associate-privacy-notice',
};

/**
 * A constant object representing the list of job roles.
 * Each key corresponds to a specific job role, and its value is the string representation of that role.
 */
export const USER_ROLES = {
  ASSOCIATE: 'Associate',
  LEADER: 'Leader',
};

export const FALLBACK_BANNER = 'albertsons';
/**
 * Generates a list of benefit resources with labels, URLs, and test IDs.
 *
 * @param t - A translation function used to localize the labels.
 * @returns An array of objects representing benefit resources.
 *
 * Each object contains:
 * - `label`: The localized label for the resource.
 * - `url`: The URL for the resource.
 * - `testID`: A unique identifier for testing purposes.
 * - `link` (optional): A descriptive link name.
 */
export const BENEFIT_RESOURCES = (t: any) => [
  {
    label: t('enrollmentAndChanges'),
    url: URLS.ENROLLMENT_AND_CHANGES,
    testID: 'enrollment-and-changes',
  },
  {
    label: t('myBenefits'),
    url: URLS.MY_BENEFITS,
    testID: 'my-benefits',
    link: 'My benefits',
    openInExternalBrowser: true,
  },
  {
    label: t('healthAndWellBeing'),
    url: URLS.HEALTH_AND_WELL_BEING,
    testID: 'health-and-well-being',
    link: 'Health and Well-Being',
  },
  {
    label: t('leaveOfAbsence'),
    url: URLS.LEAVE_OF_ABSENCE,
    testID: 'leave-of-absence',
  },
  {
    label: t('401KAndFinancialWellness'),
    url: URLS.FINANCIAL_WELLNESS,
    testID: '401k-and-financial-wellness',
    link: '401(k) and Financial Wellnesss',
  },
  {
    label: t('benefitContacts'),
    url: URLS.BENEFIT_CONTACTS,
    testID: 'benefit-contacts',
    link: 'Benefit Contacts',
  },
];

export const SIGN_IN_CARD_OPTION_HEIGHT = 56;

/**
 * Returns an array of help option objects for the SignInHelp screen.
 *
 * Each option includes:
 * - `key`: A unique identifier used for internal logic.
 * - `title`: A localized display title from the translation function `t`.
 * - `testID`: A test identifier for use in automated testing.
 *
 * @param {Function} t - Translation function (typically from `useTranslation()`).
 * @returns {Array<{ key: string, title: string, testID: string }>} Array of help option objects.
 */
export const HELP_OPTIONS = (t: any) => [
  { key: 'ldap', title: t('helpOptions.ldap'), testID: 'ldap-option' },
  { key: 'employeeId', title: t('helpOptions.employeeId'), testID: 'employee-id-option' },
  { key: 'resetPassword', title: t('helpOptions.resetPassword'), testID: 'reset-password-option' },
  { key: 'other', title: t('helpOptions.other'), testID: 'other-option' },
];

/**
 * Generates a list of payroll-related resources with labels, URLs, and test IDs.
 *
 * @param t - A translation function used to localize the labels.
 * @returns An array of objects representing payroll resources.
 *
 * Each object contains:
 * - `label`: The localized label for the resource.
 * - `url`: The URL for the resource.
 * - `testID`: A unique identifier for testing purposes.
 * - `link` (optional): A descriptive link name.
 */
export const PAYROLL = (t: any) => [
  {
    label: t('getPaystubs'),
    url: URLS.GET_PAYSTUBS,
    testID: 'get-paystubs',
    link: 'get-paystubs',
  },
  {
    label: t('changeTaxWithholdings'),
    url: URLS.TAX_CHANGE_WITHHOLDINGS,
    testID: 'change-tax-withholdings',
    link: 'change-tax-withholdings',
  },
  {
    label: t('updateDirectDepositAccount'),
    url: URLS.UPDATE_DIRECT_DEPOSIT_ACCOUNT,
    testID: 'update-direct-deposit-account',
    link: 'update-direct-deposit-account',
  },
  {
    label: t('lostOrDamagedCheck'),
    url: URLS.LOST_OR_DAMAGED_CHECK,
    testID: 'lost-or-damaged-check',
    link: 'lost-or-damaged-check',
  },
  {
    label: t('getTaxDocuments'),
    url: URLS.GET_TAX_DOCUMENTS,
    testID: 'get-tax-documents',
    link: 'get-tax-documents',
  },
];

//These are banners we currently handling for associate able access the app
export const LISTED_BANNERS: BannerNames[] = [
  'Safeway',
  'Albertsons',
  'Vons',
  'Jewel Osco',
  'ACME',
  'Shaws',
  'Tom Thumb',
  'United Express',
  'United Super Markets',
  'Randalls',
  'Albertsons Market',
  'StarMarket',
  'Pavilions',
  'Kings',
  'Market Street',
  'Haggen',
  'Carrs',
  'Andronicons',
  'Balduccis',
  'Amigos',
  'Lucky',
  'Albertsons Market Street',
];

export const BANNER_THEMES = [
  {
    profileBanner: 'ACME MID-ATLANTIC',
    banner: 'acme',
    theme: 'goldenGateTheme',
  },
  {
    profileBanner: [
      'ALBERTSONS DENVER',
      'ALBERTSONS INTERMTN',
      'ALBERTSONS PORTLAND',
      'ALBERTSONS SEATTLE',
      'ALBERTSONS SOCAL',
      'ALBERTSONS SOUTHERN',
      'ALBERTSONS SOUTHWEST',
      'ALBERTSONS UNITED',
    ],
    banner: 'albertsons',
    theme: 'sutroTheme',
  },
  {
    profileBanner: 'ALBERTSONS MARKET STREET INTERMTN',
    banner: 'albertsons',
    theme: 'sutroTheme',
  },
  {
    profileBanner: 'ALBERTSONS MARKET UNITED',
    banner: 'albertsons',
    theme: 'sutroTheme',
  },
  {
    profileBanner: 'ALBERTSONS SPECIALTY CARE MEDCART',
    banner: 'albertsons',
    theme: 'sutroTheme',
  },
  {
    profileBanner: 'AMIGOS UNITED',
    banner: 'amigos',
    theme: 'sutroTheme',
  },
  {
    profileBanner: "ANDRONICO'S NORCAL",
    banner: 'andronicos',
    theme: 'presidioTheme',
  },
  {
    profileBanner: "BALDUCCI'S MID-ATLANTIC",
    banner: 'balduccis',
    theme: 'fortPointTheme',
  },
  {
    profileBanner: 'CARRS SEATTLE',
    banner: 'carrs',
    theme: 'goldenGateTheme',
  },
  {
    profileBanner: 'HAGGEN HAGGEN',
    banner: 'haggen',
    theme: 'presidioTheme',
  },
  {
    profileBanner: 'JEWEL - OSCO',
    banner: 'jewelosco',
    theme: 'goldenGateTheme',
  },
  {
    profileBanner: 'KINGS MID - ATLANTIC',
    banner: 'kings',
    theme: 'sutroTheme',
  },
  {
    profileBanner: 'LUCKY INTERMTN',
    banner: 'albertsons',
    theme: 'sutroTheme',
  },
  {
    profileBanner: 'MARKET STREET UNITED',
    banner: 'albertsons',
    theme: 'sutroTheme',
  },
  {
    profileBanner: "PAK 'N SAVE FOODS NORCAL",
    banner: 'albertsons',
    theme: 'sutroTheme',
  },
  {
    profileBanner: 'PAVILIONS SOCAL',
    banner: 'pavilions',
    theme: 'bakerTheme',
  },
  {
    profileBanner: 'RANDALLS SOUTHERN',
    banner: 'randallas',
    theme: 'goldenGateTheme',
  },
  {
    profileBanner: [
      'SAFEWAY DENVER',
      'SAFEWAY INTERMTN',
      'SAFEWAY MID - ATLANTIC',
      'SAFEWAY NORCAL',
      'SAFEWAY PORTLAND',
      'SAFEWAY SEATTLE',
      'SAFEWAY SOUTHWEST',
      'SAFEWAY SPECIALTY CARE',
    ],
    banner: 'safeway',
    theme: 'goldenGateTheme',
  },
  {
    profileBanner: "SHAW'S",
    banner: 'shaws',
    theme: 'presidioTheme',
  },
  {
    profileBanner: "STAR MARKET SHAW'S",
    banner: 'starmarket',
    theme: 'presidioTheme',
  },
  {
    profileBanner: 'TOM THUMB SOUTHERN',
    banner: 'tomthumb',
    theme: 'goldenGateTheme',
  },
  {
    profileBanner: 'UNITED',
    banner: 'united',
    theme: 'sutroTheme',
  },
  {
    profileBanner: 'UNITED EXPRESS UNITED',
    banner: 'united',
    theme: 'sutroTheme',
  },
  {
    profileBanner: 'UNITED PHARMACY',
    banner: 'united',
    theme: 'sutroTheme',
  },
  {
    profileBanner: ['VONS NORCAL', 'VONS SOCAL', 'VONS SOUTHWEST', 'VONS SPECIALTY CARE'],
    banner: 'vons',
    theme: 'goldenGateTheme',
  },
];

export const STAR_BRAND_THEME = (bannerTheme: string, designTheme: Theme) => {
  switch (bannerTheme) {
    case 'albertsons':
    case 'amigos':
    case 'kings':
    case 'united':
      return designTheme.colors.pdsGlobalColorBrandSutro40;
    case 'safeway':
    case 'vons':
    case 'randalls':
    case 'tomthumb':
    case 'acme':
    case 'jewelosco':
    case 'carrs':
      return designTheme.colors.pdsGlobalColorBrandGoldenGate40;
    case 'pavilions':
      return designTheme.colors.pdsGlobalColorBrandBaker40;
    case 'shaws':
    case 'haggen':
    case 'andronicos':
    case 'starmarket':
      return designTheme.colors.pdsGlobalColorBrandPresidio40;
    case 'balduccis':
      return designTheme.colors.pdsGlobalColorBrandFortPoint40;
    default:
      return designTheme.colors.pdsGlobalColorBrandSutro40;
  }
};

// Store radius
export const STORE_RADIUS = 500;
// FL_TYPES Constants
export const ASSOCIATE_PROFILE = 'associateProfile';
export const STORE = 'store';
export const UCA_PROFILE = 'ucaProfile';
export const LOYALTY = 'loyalty';

export const TEXTLINK_COLORS = {
  NEUTRAL_HIGH: 'Neutral high',
  NEUTRAL_MEDIUM: 'Neutral medium',
  NEUTRAL_LOW: 'Neutral low',
  NEUTRAL_HIGH_INVERSE: 'Neutral high inverse',
};

export const TEXT_PANTRY_COLORS = {
  NEUTRAL_HIGH: 'Neutral high',
  NEUTRAL_LOW: 'Neutral low',
};

export const TEXT_SIZE = {
  X_LARGE: 'Xlarge',
  LARGE: 'Large',
  MEDIUM: 'Medium',
  SMALL: 'Small',
  X_SMALL: 'Xsmall',
  XX_SMALL: 'XXsmall',
};

export const BUTTON_SIZE = {
  SMALL: 'Small',
  MEDIUM: 'Medium',
  LARGE: 'Large',
};

export const BUTTON_VARIANT = {
  FILLED: 'Filled',
  OUTLINED: 'Outlined',
  SUBTLE: 'Subtle',
  TRANSAPRENT: 'Transparent',
  OUTLINED_INVERSE: 'Outlined inverse',
};

export const BUTTON_COLORS = {
  PRIMARY: 'Primary',
  NEUTRAL: 'Neutral',
};

export const LINE_HEIGHT = {
  LARGE: 'Large',
};

export const TEXT_DECORATION = {
  UNDERLINE: 'Underline',
};

export const TEXT_WEIGHT = {
  REGULAR: 'Regular',
  SEMI_BOLD: 'Semi bold',
  BOLD: 'Bold',
  EXTRA_BOLD: 'Extra bold',
};

export const TEXTLINK_SIZE = {
  LARGE: 'Large',
  MEDIUM: 'Medium',
  SMALL: 'Small',
  X_SMALL: 'XSmall',
  XX_SMALL: 'XXSmall',
};

export const HEADING_SIZE = {
  XXSmall: 'XXSmall',
  XSmall: 'XSmall',
  Small: 'Small',
  Medium: 'Medium',
  Large: 'Large',
};

export const TEXTLINK_WEIGHT = {
  REGULAR: 'Regular',
  SEMI_BOLD: 'Semi bold',
  BOLD: 'Bold',
};

export const TEXT_ALIGN = {
  LEFT: 'left',
  CENTER: 'center',
  RIGHT: 'right',
  JUSTIFY: 'justify',
};

// Celebration banner theme images
const BAKER: ImageSourcePropType = require('../../assets/images/celebrationCard/baker.png');
const FORT_POINT: ImageSourcePropType = require('../../assets/images/celebrationCard/fort-point.png');
const GOLDEN_GATE: ImageSourcePropType = require('../../assets/images/celebrationCard/golden-gate.png');
const PRESIDIO: ImageSourcePropType = require('../../assets/images/celebrationCard/presidio.png');
const SUTRO: ImageSourcePropType = require('../../assets/images/celebrationCard/sutro.png');

const logos: { [key: string]: ImageSourcePropType } = {
  albertsons: SUTRO,
  safeway: GOLDEN_GATE,
  vons: GOLDEN_GATE,
  randalls: GOLDEN_GATE,
  tomthumb: GOLDEN_GATE,
  acmemarkets: GOLDEN_GATE,
  jewelosco: GOLDEN_GATE,
  carrsqc: GOLDEN_GATE,
  pavilions: BAKER,
  kingsfoodmarkets: SUTRO,
  shaws: PRESIDIO,
  haggen: PRESIDIO,
  andronicos: PRESIDIO,
  starmarket: PRESIDIO,
  balduccis: FORT_POINT,
  amigos: FORT_POINT,
  united: SUTRO,
  lucky: GOLDEN_GATE,
};

export { logos };

export const TEXTDECORATION = {
  None: 'None',
};

export const LOCATION_STATE = {
  PRECISE: 'precise',
  DENIED: 'denied',
  APPROXIMATE: 'approximate',
};

export const LEGAL_CONSENT = {
  LOCATION_PRECISE: 'Legal Consent: Location Precise',
  LOCATION_DENIED: "Legal Consent: Location Don't allow",
  LOCATION_APPROXIMATE: 'Legal Consent: Location Approximate',
  LOYALTY_ACCOUNT_OPT_IN: 'Legal Consent: Account Linkage',
};

export const ERROR_CODES = {
  STORE_NOT_FOUND: 'ASP-0100',
  AD_TOKEN_FAILED: 'ASP-0104',
  INVALID_OTP: 'ASP-0112',
  LOGIN_ID_NOT_FOUND: 'ASP-0123',
  PHONE_NUMBER_NOT_FOUND: 'ASP-0125',
  USER_DATA_NOT_FOUND: 'ASP-0142',
  MISSING_LAT_LNG: 'ASP-0400',
};

export const STICKY_DAYS = {
  BIRTHDAY: 7,
  WORK_ANNIVERSARY: 5,
};

export const MOBILE_OS = {
  IOS: 'ios',
  ANDROID: 'android',
};
export const WorkConstants = {
  SINGLE_YEAR: 1,
  MILESTONE_YEARS_FACTOR: 5,
  TIME: {
    HOURS_IN_DAY: 24,
    MINUTES_IN_HOUR: 60,
    SECONDS_IN_MINUTE: 60,
    MILLISECONDS_IN_SECOND: 1000,
  },
};

/**
 * Enum for user types.
 * - NON_RESTRICTED: User has unrestricted access to features.
 * - RESTRICTED: User has restricted access to features.
 */

export enum UserType {
  RESTRICTED = 'RESTRICTED',
  NON_RESTRICTED = 'NON_RESTRICTED',
}

/**
 * Enum for feature types.
 * - ON_LOCATION_ONLY: Feature is available only when the user is in the geofence.
 * - ON_LOCATION_AND_ON_SHIFT: Feature is available only when the user is in the geofence and clocked in.
 */ export enum FeatureType {
  ON_LOCATION_ONLY = 'on-location-only',
  ON_LOCATION_AND_ON_SHIFT = 'on-location-and-on-shift',
}

/**
 * Enum for feature keys used in governance rules.
 * - CLOCK_MODULE: Clock-in/clock-out module.
 * - HR_PAYROLL: HR & Payroll features.
 * - ANNOUNCEMENTS: Announcements feature.
 */ export enum FeatureKey {
  CLOCK_MODULE = 'clockModule',
  LEARNING = 'Learning',
  HR_PAYROLL = 'HR & Payroll',
  ANNOUNCEMENTS = 'Announcements',
  PERSONALIZED_MESSAGING = 'Personalized_Messaging',
  RESOURCES_LEARNING = "Resources_Learning",
  BULLETIN = "Bulletin",
  NOTIFICATIONS = "Notifications",
}

/**
 * Enum for user type values as returned by the API.
 *
 * - FLSA_STATUS__NON_RESTRICTED: "EXEMPT" — Represents a non-restricted user with FLSA exempt status.
 * - HOURLY_SALARIED_NON_RESTRICTED: "Salaried" — Represents a non-restricted user who is salaried.
 * - FLSA_STATUS_RESTRICTED: "NON_EXEMPT" — Represents a restricted user with FLSA non-exempt status.
 * - HOURLY_SALARIED_RESTRICTED: "Hourly" — Represents a restricted user who is hourly.
 *
 * This enum is used to map API user type responses to internal user access logic.
 */
export enum USERTYPE_API_RESPONSE {
  FLSA_STATUS_EXEMPT = 'EXEMPT',
  SALARIED = 'Salaried',
  FLSA_STATUS_NONEXEMPT = 'NONEXEMPT',
  HOURLY = 'Hourly',
}

/**
 * Maps each feature key to its required user type or feature type for governance.
 * - CLOCK_MODULE, ANNOUNCEMENTS, PERSONALIZED_MESSAGING, LEARNING: Available only when on location and on shift.
 */
export const governanceRules: Record<FeatureKey, UserType | FeatureType> = {
  [FeatureKey.CLOCK_MODULE]: FeatureType.ON_LOCATION_ONLY,
  [FeatureKey.LEARNING]: FeatureType.ON_LOCATION_AND_ON_SHIFT,
  [FeatureKey.HR_PAYROLL]: FeatureType.ON_LOCATION_AND_ON_SHIFT,
  [FeatureKey.ANNOUNCEMENTS]: FeatureType.ON_LOCATION_AND_ON_SHIFT,
  [FeatureKey.PERSONALIZED_MESSAGING]: FeatureType.ON_LOCATION_AND_ON_SHIFT,
  [FeatureKey.RESOURCES_LEARNING]: FeatureType.ON_LOCATION_AND_ON_SHIFT,
  [FeatureKey.BULLETIN]: FeatureType.ON_LOCATION_AND_ON_SHIFT,
  [FeatureKey.NOTIFICATIONS]: FeatureType.ON_LOCATION_AND_ON_SHIFT,

};

/**
 * Enum for celebration types.
 */
export const CELEBRATION_TYPES = {
  MILESTONE: 'Milestone',
  WORKIVERSARY: 'Workiversary',
  BIRTHDAY: 'Birthday',
} as const;

export const CONFETTI_ANIMATION_DELAY = 8000;

// Optional configuration
export const HAPTICS_OPTIONS = {
  enableVibrateFallback: true,
  ignoreAndroidSystemSettings: false,
};

export const CLOSE = 'close';
export const pdfMap = {
  california: {
    heading: 'appPrivacyNotice.asoociatesInCalifornia',
    ios: require('../../assets/california_albertsons_associate_privacy_notice.pdf'),
    android: 'california_albertsons_associate_privacy_notice.pdf',
  },
  otherStates: {
    heading: 'appPrivacyNotice.asoociatesInOtherStates',
    ios: require('../../assets/non_ca_albertsons_associate_privacy_notice.pdf'),
    android: 'non_ca_albertsons_associate_privacy_notice.pdf',
  },
};

export const TEST_EMPLOYEE_ID = {
  MANAGER: '3434802',
  ASSOCIATE: '20208676',
  ASSOCIATE_SCHEDULE_EMPID: '20737452', //For schedule data
};

export const TEST_PREFERRED_NAME = '<EMAIL>';

export const ADA_ROLES = {
  NONE: 'none' as AccessibilityRole,
  BUTTON: 'button' as AccessibilityRole,
  LINK: 'link' as AccessibilityRole,
  HEADING: 'heading' as AccessibilityRole,
  TEXT: 'text' as AccessibilityRole,
  IMAGE: 'image' as AccessibilityRole,
  CHECKBOX: 'checkbox' as AccessibilityRole,
  RADIO: 'radio' as AccessibilityRole,
  SWITCH: 'switch' as AccessibilityRole,
  DIALOG: 'dialog' as AccessibilityRole,
};

export const ALBERTSONS_LOGO = {
  WIDTH: 116,
  HEIGHT: 26,
};

export const TEXT_REPLACEMENTS = {
  PRIVACY_POLICY: '[privacy_policy]',
  TERMS_OF_USE: '[terms_of_use]',
};

export const BOTTOM_SHEET_TYPE = {
  MODAL: 'Modal',
  INTERACTIVE: 'Interactive',
};

export const FACTOR_TYPE = {
  EMAIL: 'email',
  PHONE: 'phone',
};

export const OTP_REQUEST_STATUS = {
  PENDING_ACTIVATION: 'PENDING_ACTIVATION',
  ACTIVE: 'Active',
};

export const TIMEOUT = {
  APP_LAUNCH: 3000, // 2 seconds
  ON_BOARDING_FOUR: {
    HIDE: 50,
    SHOW: 2000,
  },
};

export const networkCodes = [
  'ECONNABORTED', // timeout
  'ECONNREFUSED', // connection refused
  'ENOTFOUND', // DNS error
  'EAI_AGAIN', // DNS error (temporary)
  'ENETUNREACH', // No internet
  'ECONNRESET', // connection reset
  'ETIMEDOUT', // connection timeout
  'ERR_NETWORK', // general network error
];

export const RADIO_BUTTON = {
  CHECKED: 'checked',
  UNCHECKED: 'unchecked',
};

export const ImportantForAccessibility = {
  YES: 'yes',
  NO: 'no',
  NO_HIDE: 'no-hide-descendants',
  AUTO: 'auto',
};

export const AccountLinkingPending = 'enrollment is pending';

export const RESOURCES_LINKS = (t: any) => [
  {
    label: t('resourcesScreen.assignedLearning'),
    text: t('resourcesScreen.assignedLearningDesc'),
    url: URLS.ASSIGNED_LEARNINGS,
    testID: 'assign-learning-testID',
    event_action: RESOURCES_ANALYTICS.ASSIGNED_JOURNEYS,
  },
  {
    label: t('resourcesScreen.journeys'),
    text: t('resourcesScreen.journeysDesc'),
    url: URLS.JOURNEYS,
    testID: 'journeys-testID',
    openInExternalBrowser: true,
    event_action: RESOURCES_ANALYTICS.JOURNEYS,
  },
];

export const COMPLETE_SHIFT = 'completeShift' as const;
export const TODAY_SHIFT = 'todayShift' as const;
export const NEXT_SHIFT = 'nextShift' as const;
export const EXCEPTION = 'exception' as const;
export const FUTURE_SCHEDULED = 'futureScheduled' as const;

export type ShiftState =
  | typeof COMPLETE_SHIFT
  | typeof TODAY_SHIFT
  | typeof NEXT_SHIFT
  | typeof EXCEPTION
  | typeof FUTURE_SCHEDULED;

export const LOGIN_FADE_IN_DELAY = 1000;
export const LOGIN_FADE_IN_DURATION = 500;

export const ANIMATION_OVERSHOOT_DURATION = 1000;
export const ANIMATION_OVERSHOOT_DURATION1 = 800;
export const ANIMATION_SETTLE_DURATION = 400;
export const ANIMATION_SETTLE_DURATION1 = 400;

export const BOTTOM_INITIAL_VALUE = 96;
export const BOTTOM_FINAL_VALUE_TABLET = 470;
export const BOTTOM_FINAL_VALUE_PHONE = 400;
export const BOTTOM_FINAL_VALUE_PHONE1 = 425;
export const BOTTOM_OVERSHOOT_VALUE_TABLET = 490;
export const BOTTOM_OVERSHOOT_VALUE_PHONE = 415;
export const BOTTOM_OVERSHOOT_VALUE_PHONE1 = 430;

export const DELAY_NEED = 1200;

export const fontScaleLimit = 2.0;

export const ANIMATION_LAYOUT = {
  WIDTH: 186,
  HEIGHT: 25,
  STROKE_WIDTH: 2.5,
  DURATION: 1000,
};

export const TNC_AGREEMENT = {
  LOYALTY_TNC: {
    ID: 'LTY01',
    VERSION: '1.0',
    NAME: 'LOYALTY_TERMS',
  },
  LOCATION_PERMISSION: {
    ID: 'LOC01',
    VERSION: '1.0',
    NAME: 'LOCATION_PERMISSION',
  },
  PUSH_NOTIFICATION_PERMISSION: {
    ID: 'PNS01',
    VERSION: '1.0',
    NAME: 'PUSH_NOTIFICATION_PERMISSION',
  },
  TERMS_AND_CONDITIONS: {
    ID: 'TNC01',
    VERSION: '1.0',
    NAME: 'TERMS_CONDITIONS',
  },
  PRIVACY_POLICY: {
    ID: 'PRV01',
    VERSION: '1.0',
    NAME: 'PRIVACY_POLICY',
  },
};

export enum TncAgreementType {
  LOYALTY_TNC = 'LOYALTY_TNC',
  LOCATION_PERMISSION = 'LOCATION_PERMISSION',
  PUSH_NOTIFICATION_PERMISSION = 'PUSH_NOTIFICATION_PERMISSION',
  TERMS_AND_CONDITIONS = 'TERMS_AND_CONDITIONS',
  PRIVACY_POLICY = 'PRIVACY_POLICY',
}

export enum TncAgreementEvent {
  new = 'new',
  update = 'update',
}

export enum TncAgreementStatus {
  optIn = 'opt-in',
  optOut = 'opt-out',
}

export const SPANSISH_WEEK_CODES = ["dom", "lun", "mar", "mié", "jue", "vie", "sáb"]

export const ENGLISH_WEEK_CODES = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"]


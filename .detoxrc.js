/** @type {Detox.DetoxConfig} */
// import Config from "MyApp/App/Config";
// const { add, myVariable } = require('./utils'); // CommonJS
module.exports = {
  testRunner: {
    args: {
      '$0': 'jest',
      config: 'e2e/jest.config.js'
    },
    jest: {
      setupTimeout: 120000
    }
  },
  apps: {
    'ios.debug': {
      type: 'ios.app',
      binaryPath: "ios/build/Build/Products/Debug-iphonesimulator/AssociateApp.app",


      build: 'xcodebuild -workspace ios/AssociateApp.xcworkspace -scheme AssociateApp -configuration Debug -sdk iphonesimulator -derivedDataPath ios/build'
    },
    'ios.release': {
      type: 'ios.app',
      binaryPath: 'ios/app/build/outputs/app/debug/AssociateApp.app',
      build: 'xcodebuild -workspace ios/AssociateApp.xcworkspace -scheme AssociateApp -configuration Release -sdk iphonesimulator -derivedDataPath ios/build'
    },
    'android.debug': {
      type: 'android.apk',
      binaryPath: 'android/app/build/outputs/apk/debug/app-debug.apk',

      testBinaryPath: 'android/app/build/outputs/apk/androidTest/debug/app-debug-androidTest.apk',
      build: 'cd android && ./gradlew assembleDebug assembleAndroidTest -DtestBuildType=debug',

      reversePorts: [
        8081
      ]
    },
    'android.release': {
      type: 'android.apk',
      binaryPath: 'android/app/build/outputs/apk/release/app-release.apk',
      build: 'cd android && ./gradlew assembleRelease assembleAndroidTest -DtestBuildType=release'
    }
  },
  devices: {
    simulator: {
      type: 'ios.simulator',
      device: {
        type: 'iPhone 16'
      }
    },
    "hyperexecute.raw.device": {
      type: 'android.attached',
      device: {
        adbName: '.*'
      }
    },

    cloud: {
      provider: 'lambdatest',
      hostname: 'https://applive.lambdatest.com/app',
      username: '<EMAIL>',
      accessKey: 'URqZtVGn5TM5Lz16BsjDBaNWnoulWTtcNZARPNqTYdyVf9rKtx',
    },
    emulator: {
      type: 'android.emulator',
      device: {
        avdName: 'Pixel_7_Pro'
      }
    }
  },
  //   lambdatest: {
  //     type: 'android.cloud',
  //     // platform: 'Android',
  //     device: {
  //       avdName: 'Galaxy S23'
  //     },
  //     cloud: {
  //       provider: 'lambdatest',
  //       hostname: 'https://applive.lambdatest.com/app',
  //       username: '<EMAIL>',
  //       accessKey: 'URqZtVGn5TM5Lz16BsjDBaNWnoulWTtcNZARPNqTYdyVf9rKtx',
  //   }
  // }
  // },
  configurations: {
    'ios.sim.debug': {
      device: 'simulator',
      app: 'ios.debug',

    },
    'ios.sim.release': {
      device: 'simulator',
      app: 'ios.release'
    },
    'android.att.debug': {
      device: 'attached',
      app: 'android.debug'
    },
    'android.att.release': {
      device: 'attached',
      app: 'android.release'
    },
    'android.emu.debug': {
      device: 'emulator',
      app: 'android.debug'
    },
    'android.emu.release': {
      device: 'emulator',
      app: 'android.release'
    },
    'android.lambdatest.debug': {
      "device": "hyperexecute.raw.device",
      "app": "android.debug"
    },
    'ios.lambdatest.debug': {
      device: 'lambdatest',
      app: 'ios.debug',
    }

  }
};

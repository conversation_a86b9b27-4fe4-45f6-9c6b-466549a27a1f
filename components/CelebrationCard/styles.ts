import { Dimensions, StyleSheet } from 'react-native';

import { IPAD_WIDTH } from '../../app/shared/constants';

import type { Theme } from 'pantry-design-system';

const getStyles = (
  { colors, fonts, typography, borderDimens, dimensions }: Theme,
  isTablet = true,
) => {
  // Constants
  const screenWidth = Dimensions.get('window').width;
  const SIDE_MARGIN = 40;
  const TABLET_SIDE_MARGIN = 100;
  const MAX_CARD_WIDTH = 706;
  const CARD_HEIGHT = 125;
  const SHADOW_OPACITY = 0.2;
  const SHADOW_COLOR = '#000';
  const SHADOW_OFFSET = { width: 0, height: 2 };
  const SHADOW_RADIUS = 4;
  const ELEVATION = 6;
  const IMAGE_SECTION_MARGIN_RIGHT = 8;
  const CLOSE_ICON_OFFSET_TOP = -10;
  const CLOSE_ICON_OFFSET_RIGHT = -27;

  const cardWidth = Math.min(screenWidth - SIDE_MARGIN * 2, MAX_CARD_WIDTH);

  const styles = StyleSheet.create({
    body: {
      marginTop: dimensions.pdsGlobalSpace200,
    },
    card: {
      alignItems: 'flex-start',
      alignSelf: 'center',
      borderRadius: borderDimens.pdsGlobalBorderRadius200,
      elevation: ELEVATION,
      flexDirection: 'row',
      height: CARD_HEIGHT,
      marginBottom: dimensions.pdsGlobalSpace100,
      maxWidth: IPAD_WIDTH,
      padding: dimensions.pdsGlobalSpace400,
      position: 'relative',
      shadowColor: SHADOW_COLOR,
      shadowOffset: SHADOW_OFFSET,
      shadowOpacity: SHADOW_OPACITY,
      shadowRadius: SHADOW_RADIUS,
      width: isTablet ? IPAD_WIDTH - TABLET_SIDE_MARGIN : cardWidth,
    },
    closeButton: {
      position: 'absolute',
      right: dimensions.pdsGlobalSpace300,
      top: dimensions.pdsGlobalSpace400,
      zIndex: 5,
    },
    closeText: {
      color: colors.pdsThemeColorForegroundPrimary,
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
      fontSize: typography.pdsGlobalFontSize900,
    },
    icon: {
      color: colors.pdsThemeColorOutlinePrimary,
      right: CLOSE_ICON_OFFSET_RIGHT,
      top: CLOSE_ICON_OFFSET_TOP,
    },
    imageSection: {
      alignItems: 'center',
      flexDirection: 'column',
      justifyContent: 'center',
      marginRight: IMAGE_SECTION_MARGIN_RIGHT,
    },
    singleCard: {
      alignItems: 'flex-start',
      alignSelf: 'center',
      borderRadius: borderDimens.pdsGlobalBorderRadius200,
      elevation: ELEVATION,
      flexDirection: 'row',
      height: CARD_HEIGHT,
      marginVertical: dimensions.pdsGlobalSpace100,
      maxWidth: IPAD_WIDTH,
      padding: dimensions.pdsGlobalSpace400,
      position: 'relative',
      shadowColor: SHADOW_COLOR,
      shadowOffset: SHADOW_OFFSET,
      shadowOpacity: SHADOW_OPACITY,
      shadowRadius: SHADOW_RADIUS,
    },
    textContainer: {
      flex: 1,
    },
  });

  return styles;
};

export default getStyles;

import React from 'react';
import { fireEvent, render } from '@testing-library/react-native';
import CelebrationCard, { CelebrationCardProps } from '.';
import { CELEBRATION_TYPES } from '../../app/shared/constants';

jest.mock('@react-native-firebase/app', () => ({
  getApp: jest.fn(() => ({})),
}));

jest.mock("pantry-design-system", () => ({
  useTheme: jest.fn(() => ({
    theme: {
      colors: {},
      fonts: {},
      dimensions: {},
      typography: {},
      borderDimens: {},
    }
  })),
  Text: jest.fn(({ text, testID }) => <text testID={testID}>{text}</text>),
  Heading: jest.fn(({ title, testID }) => <text testID={testID}>{title}</text>),

}));

jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string, params?: Record<string, any>) => {
      const translations: Record<string, string> = {
        'milestoneFirstHeading': 'First Year Milestone!',
        'milestoneFirstBody': 'Welcome to the {banner} team!',
        'milestoneMultiHeading': 'Congratulations on {years} years!',
        'milestoneMultiBody': 'You joined in {year} and have been with us for {years} years!',
        'workiversaryHeading': 'Work Anniversary!',
        'workiversaryBody': 'Celebrating {years} years with us!',
        'birthdayHeading': 'Happy Birthday!',
        'birthdayBody': 'Happy Birthday, {firstName}! Enjoy your day with the {banner} team!',
        'CelebrationBanner': 'Celebration Banner',
        'CelebrationImage': 'Celebration Image',
        'DismissCelebrationCard': 'Dismiss Celebration Card',
      };
      if (params) {
        return Object.entries(params).reduce(
          (acc, [key, value]) => acc.replace(`{${key}}`, value.toString()),
          translations[key]
        );
      }
      return translations[key];
    },
  }),
}));

jest.mock('react-redux', () => ({
  useSelector: jest.fn(() => false),
}));

jest.mock('../../assets/images/svg/CelebrationIcon', () => ({
  celebrationIcon: '<svg></svg>',
}));

describe('CelebrationCard', () => {
  const mockOnClose = jest.fn();
  const mockSource = { uri: 'test-image' };
  const mockDate = new Date();

  const baseProps = {
    id: 'test-card',
    type: CELEBRATION_TYPES.MILESTONE,
    banner: 'Safeway',
    source: mockSource,
    onClose: mockOnClose,
    date: mockDate,
    expiryDays: 7,
  };

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly for first year milestone', () => {
    const { getByText, getByTestId } = render(
      <CelebrationCard
        {...baseProps}
        type={CELEBRATION_TYPES.MILESTONE} />
    );

    expect(getByTestId('celebration-heading')).toHaveTextContent('First Year Milestone!');
    expect(getByTestId('celebration-text')).toHaveTextContent('Welcome to the Safeway team!');
  });

  it('renders correctly for multi-year milestone', () => {
    const { getByTestId } = render(
      <CelebrationCard
        {...baseProps}
        type={CELEBRATION_TYPES.MILESTONE}
        yearsWorked={5}
        yearStarted={2018} />
    );

    expect(getByTestId('celebration-heading')).toHaveTextContent('Congratulations on 5 years!');
    expect(getByTestId('celebration-text')).toHaveTextContent('You joined in 2018 and have been with us for 5 years!');
  });

  it('renders correctly for workiversary', () => {
    const { getByTestId } = render(
      <CelebrationCard
        {...baseProps}
        type={CELEBRATION_TYPES.WORKIVERSARY}
        yearsWorked={3}
      />
    );

    expect(getByTestId('celebration-heading')).toHaveTextContent('Work Anniversary!');
    expect(getByTestId('celebration-text')).toHaveTextContent('Celebrating 3 years with us!');
  });

  it('renders correctly for birthday', () => {
    const { getByTestId } = render(
      <CelebrationCard
        {...baseProps}
        type={CELEBRATION_TYPES.BIRTHDAY}
        firstName="John"
        banner="Safeway"
      />
    );

    expect(getByTestId('celebration-heading')).toHaveTextContent('Happy Birthday!');
    expect(getByTestId('celebration-text')).toHaveTextContent('Happy Birthday, John! Enjoy your day with the Safeway team!');
  });


  it('applies different styles for tablet', () => {
    jest.mock('react-redux', () => ({
      useSelector: jest.fn(() => true),
    }));

    const { getByTestId } = render(
      <CelebrationCard {...baseProps} />
    );

    const card = getByTestId('accessible-celebration-summary');
    expect(card.props.style).toBeTruthy();
  });

  it('applies single card styles when isSingleCard is true', () => {
    const { getByTestId } = render(
      <CelebrationCard  {...baseProps} isSingleCard={true} />
    );

    const card = getByTestId('accessible-celebration-summary');
    expect(card.props.style).toBeTruthy();
  });


});


describe('CelebrationCard', () => {
  const mockProps = {
    id: 'Birthday-2023',
    type: 'Birthday',
    firstName: 'John',
    banner: 'Safeway',
    onClose: jest.fn(),
    showDismiss: true
  };

  it('should not render when essential props are missing', () => {
    const { queryByTestId } = render(<CelebrationCard  {...mockProps} type="Birthday" id={'Birthday-test'} />);
    expect(queryByTestId('celebration-banner-undefined')).toBeNull();
  });

  it('should handle image loading error', () => {
    const mockProps: CelebrationCardProps = {
      id: 'Birthday-2023',
      type: CELEBRATION_TYPES.BIRTHDAY, // Use the actual enum value
      firstName: 'John',
      banner: 'Marketing',
      onClose: jest.fn(),
      source: { uri: 'invalid-image' },
      testID: 'Birthday-test'
    };

    const { getByTestId } = render(<CelebrationCard {...mockProps} />);
    const imageBackground = getByTestId('Birthday-test');

    fireEvent(imageBackground, 'onError');
    // Should still render the card content even if image fails to load
    expect(getByTestId('celebration-heading')).toBeTruthy();
  });
});
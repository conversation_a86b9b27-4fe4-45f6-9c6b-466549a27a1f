import { useTheme } from 'pantry-design-system';
import getStyles from "./styles";
import React, { useEffect } from 'react';
import { Text, Heading } from 'pantry-design-system';
import { View, ImageBackground, ImageSourcePropType, Pressable } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { SvgXml } from 'react-native-svg';
import { celebrationIcon } from '../../assets/images/svg/CelebrationIcon';
import { CELEBRATION_TYPES, TEXTLINK_COLORS, TEXTLINK_SIZE, TEXTLINK_WEIGHT } from '../../app/shared/constants';
import { closeIcon } from '../../assets/images/svg/CloseIcon';
import { CakeSvg } from '../../assets/images/svg/CakeSvg';
import { CelebrationSvg } from '../../assets/images/svg/CelebrationSvg';
import { userActionLogEvent } from '../../analytics/AnalyticsUtils';
import { EVENT_ACTION_VIEW, EVENT_CATEGORY_MODAL, } from '../../analytics/AnalyticsConstants';

export type CelebrationType = (typeof CELEBRATION_TYPES)[keyof typeof CELEBRATION_TYPES];
/**
 * Props for the CelebrationCard component.
 */
export interface CelebrationCardProps {
    /** Optional unique identifier for the card (used in testID and keys) */
    id: string;
    /** Type of celebration to determine messaging and image */
    type: CelebrationType;
    /** Number of years worked (for milestones or workiversary) */
    yearsWorked?: number;
    /** Year started at the company (used in milestone messages) */
    yearStarted?: number;
    /** Optional string to personalize the message (e.g., department name or event) */
    banner?: string;
    /** First name of the celebrant (used in birthday messages) */
    firstName?: string;
    /** Background image for the card */
    source?: ImageSourcePropType;
    /** Callback fired when the close button is pressed */
    onClose?: () => void;
    /** Flag to determine if this is a standalone card (impacts styling) */
    isSingleCard?: boolean;
    /** Show Card Dismiss button */
    showDismiss?: boolean;
    testID?: string;
    analyticsCategory?: string; // Added to track the page from which the card is accessed
}

/**
 * Internal structure for translated and styled celebration content.
 */
type CelebrationContent = {
    heading: string;
    body: string;
};

/**
 * A stylized, themed celebration card used to mark employee milestones,
 * birthdays, and anniversaries with contextual content and imagery.
 */
const CelebrationCard: React.FC<CelebrationCardProps> = ({
    id,
    type,
    yearsWorked = 1,
    yearStarted,
    banner,
    firstName,
    source,
    onClose,
    isSingleCard = false,
    testID,
    showDismiss = false,
    analyticsCategory = ""
}) => {
    const { theme } = useTheme();
    const isTablet = useSelector((state: { deviceInfo: { isTablet: boolean } }) => state.deviceInfo?.isTablet);
    const styles = getStyles(theme, isTablet);
    const { t } = useTranslation();

    /**
     * Returns translated heading, and body based on celebration type.
     */
    const getContent = (type: string): CelebrationContent => {
        if (type === CELEBRATION_TYPES.MILESTONE && yearsWorked === 1) {
            return {
                heading: t('milestoneFirstHeading'),
                body: t('milestoneFirstBody', { banner }),
            };
        } else if (type === CELEBRATION_TYPES.MILESTONE) {
            return {
                heading: t('milestoneMultiHeading', { years: yearsWorked }),
                body: t('milestoneMultiBody', { years: yearsWorked, year: yearStarted }),
            };
        } else if (type === CELEBRATION_TYPES.WORKIVERSARY) {
            return {
                heading: t('workiversaryHeading'),
                body: t('workiversaryBody', { years: yearsWorked }),
            };
        } else if (type === CELEBRATION_TYPES.BIRTHDAY) {
            return {
                heading: t('birthdayHeading'),
                body: t('birthdayBody', { firstName, banner }),
            };
        }

        return {
            heading: '',
            body: '',
        };
    };

    const { heading, body } = getContent(type);

    useEffect(() => {
        userActionLogEvent(
            EVENT_CATEGORY_MODAL,
            EVENT_ACTION_VIEW,
            analyticsCategory + heading,
        );
    }, [heading]);

    return (
        <View testID={testID}>
            <ImageBackground
                key={id}
                accessible={false}
                testID={`celebration-banner-${id}`}
                imageStyle={{ borderRadius: 8 }}
                style={[!isSingleCard ? styles.card : styles.singleCard]}
                source={source}>
                <View style={styles.imageSection}>
                    <SvgXml
                        xml={celebrationIcon}
                        style={styles.icon}
                        testID="celebration-icon"
                        color={theme.colors.pdsThemeColorBackgroundPrimary}
                        fill={theme.colors.pdsThemeColorBackgroundPrimary}
                    />
                    {type === CELEBRATION_TYPES.BIRTHDAY ? (
                        <SvgXml
                            xml={CakeSvg}
                            testID='celebration-image'
                        />
                    ) : (
                        <SvgXml
                            xml={CelebrationSvg}
                            testID='celebration-image'
                        />
                    )}
                </View>
                <View accessible={true}
                    accessibilityLabel={`${heading} ${body}`}
                    style={styles.textContainer}
                    testID="accessible-celebration-summary">
                    <Heading
                        color={TEXTLINK_COLORS.NEUTRAL_HIGH}
                        size={isTablet ? TEXTLINK_SIZE.MEDIUM : TEXTLINK_SIZE.X_SMALL}
                        title={heading}
                        weight={TEXTLINK_WEIGHT.BOLD}
                        testID='celebration-heading'
                    />

                    <View style={styles.body}>
                        <Text
                            color={TEXTLINK_COLORS.NEUTRAL_HIGH}
                            size={isTablet ? TEXTLINK_SIZE.LARGE : TEXTLINK_SIZE.SMALL}
                            text={body}
                            weight={TEXTLINK_WEIGHT.SEMI_BOLD}
                            testID='celebration-text'
                        />
                    </View>
                </View>
                {showDismiss &&
                    <Pressable
                        accessible={true}
                        accessibilityRole="button"
                        accessibilityLabel={t('DismissCelebrationCard')}
                        testID='close-button'
                        style={styles.closeButton}
                        onPress={onClose}>
                        <SvgXml
                            xml={closeIcon}
                            color={theme.colors.pdsThemeColorForegroundPrimary}
                            fill={theme.colors.pdsThemeColorForegroundPrimary}
                        />
                    </Pressable>
                }

            </ImageBackground>
        </View>
    );
};

export default CelebrationCard;

import React from 'react';
import { render } from '@testing-library/react-native';
import EmptyStatusCard from './EmptyStatusCard';
import { EmptyStatusPlaceHolderProps } from '../../interfaces/common';

const mockProps: EmptyStatusPlaceHolderProps = {
    source: '<svg><rect width="100" height="100" fill="blue" /></svg>',
    titleText: 'Coming Soon',
    subTitleText: 'We are still working on this.',
    descriptionText: 'Stay tuned for updates!',
};

const renderWithTheme = (props: Partial<EmptyStatusPlaceHolderProps> = {}) => {
    return render(
        <EmptyStatusCard {...mockProps} {...props} />
    );
};

describe('EmptyStatusCard', () => {
    it('renders SVG image when source is provided', () => {
        const { getByTestId } = renderWithTheme();
        expect(getByTestId('empty-status-image')).toBeTruthy();
    });

    it('does not render SVG if source is not provided', () => {
        const { queryByTestId } = renderWithTheme({ source: undefined });
        expect(queryByTestId('empty-status-image')).toBeNull();
    });

    it('does not render titleText if not provided', () => {
        const { queryByTestId } = renderWithTheme({ titleText: undefined });
        expect(queryByTestId('header-title')).toBeNull();
    });
    it('does not render subTitleText if not provided', () => {
        const { queryByTestId } = renderWithTheme({ subTitleText: undefined });
        expect(queryByTestId('sub-header-title')).toBeNull();
    });
    it('does not render descriptionText if not provided', () => {
        const { queryByTestId } = renderWithTheme({ descriptionText: undefined });
        expect(queryByTestId('description-text')).toBeNull();
    });
});

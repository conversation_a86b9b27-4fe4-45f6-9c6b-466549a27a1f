import React from "react";
import { View } from "react-native";
import { useTheme, Heading, Text } from "pantry-design-system";
import { SvgXml } from "react-native-svg";
import getStyles from "./styles";
import { HEADING_SIZE, TEXTLINK_COLORS, TEXTLINK_WEIGHT } from "../../app/shared/constants";
import { EmptyStatusPlaceHolderProps } from '../../interfaces/common';
import { useTranslation } from "react-i18next";

const EmptyStatusCard = ({ source, titleText, subTitleText, descriptionText }: EmptyStatusPlaceHolderProps) => {
    const { theme } = useTheme();
    const styles = getStyles(theme);
    const { t } = useTranslation();

    return (
        <>
            <View style={styles.StatusPlaceholder} accessible={false}>
                {source && <SvgXml
                    xml={source}
                    style={styles.imageStyle}
                    testID="empty-status-image"
                />}
                <View style={styles.textContainer}>
                    {titleText && <Heading
                        accessible
                        testID={"header-title"}
                        textAlign="center"
                        title={titleText}
                        accessibilityRole="header"
                        accessibilityLabel={titleText}
                        color={TEXTLINK_COLORS.NEUTRAL_MEDIUM}
                        size={HEADING_SIZE.XSmall}
                    />}
                    {subTitleText && <Text
                        color={TEXTLINK_COLORS.NEUTRAL_MEDIUM}
                        text={subTitleText}
                        weight={TEXTLINK_WEIGHT.REGULAR}
                        accessible={true}
                        testID={"sub-header-title"}
                        textDecoration="None"
                        textAlign="center"
                        accessibilityLabel={subTitleText}
                        size={HEADING_SIZE.XXSmall}
                    />}
                    {descriptionText && <Text
                        color={TEXTLINK_COLORS.NEUTRAL_MEDIUM}
                        text={descriptionText}
                        weight={TEXTLINK_WEIGHT.REGULAR}
                        accessible={true}
                        testID={"description-text"}
                        textDecoration="None"
                        textAlign="center"
                        accessibilityLabel={descriptionText}
                        size={HEADING_SIZE.XXSmall}
                    />}
                </View>
            </View>

        </>
    );
};

export default EmptyStatusCard;

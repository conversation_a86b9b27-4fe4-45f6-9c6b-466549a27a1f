import { IPAD_WIDTH } from "../../app/shared/constants";
import { Theme } from "pantry-design-system";
import { StyleSheet } from "react-native";

//customising styles based on themes
const getStyles = (
    { colors, dimensions }: Theme,
) => {
    const styles = StyleSheet.create({
        StatusPlaceholder: {
            alignItems: "center",
            textAlign: "center",
            paddingVertical: dimensions.pdsGlobalSpace400,
            paddingHorizontal: dimensions.pdsGlobalSpace800,
            height: "100%",
            backgroundColor: colors.pdsThemeColorBackgroundSunken,
        },
        textContainer: {
            gap: dimensions.pdsGlobalSpace200,
        },
        imageStyle: {
            width: dimensions.pdsGlobalSpace400,
            height: dimensions.pdsGlobalSpace400,
            marginBottom: dimensions.pdsGlobalSpace200,
        }
    });
    return styles;
};

export default getStyles;

import React from 'react';
import { useTranslation } from 'react-i18next';
import { SafeAreaView } from 'react-native-safe-area-context';

import { NoServer } from '../../assets/images/svg/NoServerConnection';
import StatusPlaceholder from '../StatusPlaceholder/StatusPlaceholder';

type RateLimitModalProps = {
    handleTryagain: () => void;
};

const RateLimitModal: React.FC<RateLimitModalProps> = ({ handleTryagain }) => {
    const { t } = useTranslation();

    const retryAgain = () => {
        handleTryagain();
    };

    return (
        <SafeAreaView edges={['top', 'bottom']}>
            <StatusPlaceholder
                source={NoServer}
                status={t('errorPlaceholder.rateLimitError')}
                subHeader={t('errorPlaceholder.rateLimitErrorSubtitle')}
                styles={{}}
                showButton={true}
                buttonText={t('errorPlaceholder.tryAgain')}
                onPress={retryAgain}
                buttonAccessHint={t('schedule.retryServerAccessibilityHint')}
            />
        </SafeAreaView>
    );
};

export default RateLimitModal;

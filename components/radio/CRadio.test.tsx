import React from 'react';
import { render } from '@testing-library/react-native';
import CRadio from './CRadio';
import { View } from 'react-native';

describe('CRadio', () => {
  it('renders without crashing', () => {
    const { getByTestId } = render(<CRadio status={false} />);
    expect(getByTestId('radio-button-wrapper')).toBeTruthy();
  });

  it('applies correct borderColor when status is false (unchecked)', () => {
    const { UNSAFE_getAllByType } = render(<CRadio status={false} />);
    const outerCircle = UNSAFE_getAllByType(View)[1]; // Index 1 = inner radio view
    const flattened = Array.isArray(outerCircle.props.style)
      ? Object.assign({}, ...outerCircle.props.style)
      : outerCircle.props.style;

    expect(flattened.borderColor).toBe('gray');
  });

  it('applies correct borderColor when status is true (checked)', () => {
    const { UNSAFE_getAllByType } = render(<CRadio status={true} color="red" />);
    const outerCircle = UNSAFE_getAllByType(View)[1];
    const flattened = Array.isArray(outerCircle.props.style)
      ? Object.assign({}, ...outerCircle.props.style)
      : outerCircle.props.style;

    expect(flattened.borderColor).toBe('red');
  });

  it('does not render inner circle when status is false', () => {
    const { queryAllByTestId } = render(<CRadio status={false} />);
    expect(queryAllByTestId('radio-button-inner').length).toBe(0);
  });
});

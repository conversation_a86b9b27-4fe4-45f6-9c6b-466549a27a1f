import { useTheme } from 'pantry-design-system';
import React from 'react';
import { View } from 'react-native';
import { getStyles } from './styles';
import { ImportantForAccessibility } from '../../app/shared/constants';

interface CustomRadioProps {
  status: boolean;
  color?: string;
  uncheckedColor?: string;
}

const CRadio = ({
  status,
  color = 'black',
  uncheckedColor = 'gray'
}: CustomRadioProps) => {
  const theme = useTheme();
  const styles = getStyles(theme);

  return (
    <View testID="radio-button-wrapper">
    <View
      accessible={false}
      importantForAccessibility={ImportantForAccessibility.NO_HIDE}
      style={[
        styles.outerCircle,
        { borderColor: status ? color : uncheckedColor },
      ]}
    >
      {status && (
        <View
          testID="radio-button-inner"
          style={[styles.innerCircle, { backgroundColor: color }]}
        />
      )}
    </View>
  </View>
  );
};

export default CRadio;

import { Theme } from 'pantry-design-system';
import { StyleSheet } from "react-native";


export default ({ colors, dimensions, borderDimens }: Theme) => {
    return StyleSheet.create({
        modalContainer: {
            position: "relative",
            height: "100%",
            marginBottom: -100,
            elevation: 10,
            backgroundColor: "transparet",
        },
        tabletModalContainer: {
            flex: 1,
            justifyContent: "center",
            alignItems: "center",
        },
        modalContent: {
            backgroundColor: colors.pdsThemeColorBackgroundBaseline,
            borderTopRightRadius: 25,
            borderTopLeftRadius: 25,
            position: "absolute",
            minHeight: 100,
            bottom: 0,
            left: 0,
            width: "100%",
        },
        tabletModalPotraitContent: {
            borderRadius: 20,
            position: "relative",
            alignSelf: 'center',
            width: "60%",
            height: "40%",
            margin: 10
        },
        tabletModalLandscapeContent: {
            width: "40%",
            height: "40%",
            position: "relative",
        },
        closeBtn: {
            position: "absolute",
            right:10,
            top: 10,
            zIndex: 10,
        },
        tabletCloseBtn: {
            right:20,
            top: 20,
            fontSize: 30
        },
        closeIcon: {
            fontSize: 22,
            color: colors.pdsThemeColorForegroundPrimary
        },
        tabletCloseIcon: {
            fontSize: 30,
        }
    });
}
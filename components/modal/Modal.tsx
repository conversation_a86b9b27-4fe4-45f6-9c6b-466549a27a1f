import { TouchableOpacity, View } from "react-native";
import React, { ReactNode } from 'react';
import { Modal } from "react-native-paper";
import AntDesin from "react-native-vector-icons/AntDesign";
import { useTheme } from 'pantry-design-system'
import getStyles from "./Styles"

type propsData = {
    children: ReactNode;
    visible: boolean;
    onDismiss: () => void;
    testID?: string;
    isTablet?: boolean;
    isLandscape?: boolean;
};
const CModal = (props: propsData) => {
    const { children, visible, onDismiss, testID, isTablet, isLandscape } = props;
    const { theme } = useTheme();

    const styles = getStyles(theme);
    return (
        <Modal
            visible={visible}
            onDismiss={onDismiss}
            contentContainerStyle={[styles.modalContainer, isTablet && styles.tabletModalContainer]}
            testID={testID}
        >
            <View style={[styles.modalContent, isTablet && styles.tabletModalPotraitContent, isTablet && isLandscape && styles.tabletModalLandscapeContent]} testID="modal-content">
                <TouchableOpacity style={[styles.closeBtn, isTablet && styles.tabletCloseBtn]} onPress={onDismiss} testID="close-btn">
                    <AntDesin name="close" style={[styles.closeIcon, isTablet && styles.tabletCloseIcon]} />
                </TouchableOpacity>
                {children}
            </View>
        </Modal>
    );
};

export default CModal;


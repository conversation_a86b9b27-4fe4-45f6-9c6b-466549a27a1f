import React from "react";
import { render, screen } from "@testing-library/react-native";
import CModal from "./Modal";
import { useTheme } from "pantry-design-system";
import { View } from "react-native";

jest.mock('react-native-paper', () => {
  const originalModule = jest.requireActual('react-native-paper');
  return {
    ...originalModule,
    Modal: ({ visible, children }: { visible: boolean; children: React.ReactNode }) =>
      visible ? <>{children}</> : null,
  };
});

jest.mock("pantry-design-system", () => ({
  useTheme: jest.fn(),
}));

jest.mock("./Styles", () => () => ({
  modalContainer: {},
  modalContent: {},
  closeBtn: {},
}));

jest.mock("react-native-vector-icons/AntDesign", () => "AntDesign");

describe("CModal Component", () => {
  let mockOnDismiss: jest.Mock;

  beforeEach(() => {
    mockOnDismiss = jest.fn();
    (useTheme as jest.Mock).mockReturnValue({ theme: {} });
  });

  it("should render correctly when visible is true", () => {
    render(
        <CModal visible={true} onDismiss={mockOnDismiss}>
          <View />
        </CModal>
    );
    expect(screen.getByTestId("modal-content")).toBeTruthy();
  });

  it("should not render when visible is false", () => {
    render(
        <CModal visible={false} onDismiss={mockOnDismiss}>
          <View />
        </CModal>
    );
    expect(screen.queryByTestId("modal-content")).toBeNull();
  });
});

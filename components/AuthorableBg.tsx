import FastImage from '@d11/react-native-fast-image';
import { useTheme } from 'pantry-design-system';
import React, { useState, useEffect } from 'react';
import { StyleSheet, Dimensions, View, Platform } from 'react-native';
import { LinearGradient } from 'react-native-linear-gradient';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useSelector } from 'react-redux';

import images from '../assets/images';
import { Colors } from '../constants/Colors';
interface Props {
    imageBackground?: string;
    imageBackgroundTabletLandscape?: string;
    imageBackgroundTabletPortrait?: string;
    children: React.ReactNode;
    colors?: string[];
    shouldAnimateGradient?: boolean;
}

const isPortrait = () => {
    const dim = Dimensions.get('screen');
    return dim.height >= dim.width;
};

const AuthorableBG = ({
    children,
    imageBackground: image,
    imageBackgroundTabletLandscape: imageTabletLandscape,
    imageBackgroundTabletPortrait: imageTabletPortrait,
    colors = [],
    shouldAnimateGradient,
}: Props) => {
    const [backgroundImage, setBackgroundImage] = useState({});
    const isTablet = useSelector((state: any) => state.deviceInfo?.isTablet);
    const isLandscape = useSelector((state: any) => state.deviceInfo.isLandscape);

    const [gradientColors, setGradientColors] = useState([
        Colors.authorableBgColorStart,
        Colors.authorableBgColorStart,
        Colors.authorableBgColorStart,
        Colors.authorableBgColorEnd,
        Colors.authorableBgColorEnd,
    ]);

    const portrait = isPortrait();

    const { theme } = useTheme(); // Fething themes from ThemeProvider

    useEffect(() => {
        if (isTablet && isLandscape) {
            setBackgroundImage(
                imageTabletLandscape ? { uri: imageTabletLandscape } : images.LANDSCAPE_TABLET_BG,
            );
        } else if (isTablet && portrait) {
            setBackgroundImage(
                imageTabletPortrait ? { uri: imageTabletPortrait } : images.PORTRAIT_TABLET_BG,
            );
        } else {
            setBackgroundImage(image ? { uri: image } : images.LOGIN_BG);
        }
    }, [isLandscape, portrait, isTablet, image, imageTabletLandscape, imageTabletPortrait]);

    useEffect(() => {
        if (shouldAnimateGradient) {
            const timeout = setTimeout(() => {
                setGradientColors([
                    Colors.authorableBgColorStart,
                    Colors.authorableBgColorStart,
                    Colors.authorableBgColorStart,
                    Colors.authorableBgColorEnd,
                    Colors.authorableBgColorEnd,
                    Colors.authorableBgColorEnd,
                    Colors.authorableBgColorEnd,
                    Colors.authorableBgColorEnd,
                ]);
            }, 100); // Let login animation start first

            return () => clearTimeout(timeout);
        }
    }, [shouldAnimateGradient]);

    const styles = StyleSheet.create({
        backgroundImage: {
            ...StyleSheet.absoluteFillObject,
            zIndex: -1,
        },
        bgImageStyle: {
            flex: 1,
            height: Dimensions.get('window').height,
            resizeMode: 'cover',
            width: Dimensions.get('window').width,
        },
        bgImageStyle: {
            flex: 1,
            height: Dimensions.get('window').height,
            resizeMode: 'cover',
            width: Dimensions.get('window').width,
        },
        container: {
            alignItems: 'center',
            flex: 1,
            height: Dimensions.get('window').height,
            resizeMode: 'cover',
            width: Dimensions.get('window').width,
        },
        container: {
            alignItems: 'center',
            flex: 1,
            height: Dimensions.get('window').height,
            justifyContent: 'center',
            resizeMode: 'cover',
            width: Dimensions.get('window').width,
        },
        viewContainer: {
            flex: 1,
        },
        viewContainer: {
            flex: 1,
        },
    });

    const getBottomMarginForHeight = (): number => {
        const height = Dimensions.get('window').height;

        if (height <= 880) {
            return theme.dimensions.pdsGlobalSpace100; // 4
        } else if (height <= 890) {
            return theme.dimensions.pdsGlobalSpace200; // 8
        } else if (height <= 900) {
            return theme.dimensions.pdsGlobalSpace600; // 24
        } else if (height <= 910) {
            return theme.dimensions.pdsGlobalSpace1000; // 40
        } else if (height <= 920) {
            return theme.dimensions.pdsGlobalSpace1400; // 64
        } else if (height <= 930) {
            return theme.dimensions.pdsGlobalSpace1700; // 96
        } else {
            return theme.dimensions.pdsGlobalSpace1700; // Default to max margin
        }
    };

    return (
        <View style={styles.viewContainer}>
            <FastImage
                testID="background-image"
                source={
                    backgroundImage?.uri
                        ? {
                            uri: backgroundImage.uri,
                            priority: FastImage.priority.normal,
                            cache: FastImage.cacheControl.immutable,
                        }
                        : images.LOGIN_BG // fallback static image
                }
                style={styles.backgroundImage}
                resizeMode={FastImage.resizeMode.cover}
            ></FastImage>
            <LinearGradient testID="linear-gradient" colors={gradientColors} style={styles.container}>
                <SafeAreaView
                    edges={['top', 'bottom']}
                    style={Platform.OS === 'android' ? { marginBottom: getBottomMarginForHeight() } : {}}
                >
                    {children}
                </SafeAreaView>
            </LinearGradient>
        </View>
    );
};
export default AuthorableBG;

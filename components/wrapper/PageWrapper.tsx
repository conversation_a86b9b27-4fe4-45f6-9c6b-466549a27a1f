import { View,  StyleProp, ViewStyle } from "react-native";
import React, { ReactNode } from 'react';
import { useTheme } from 'pantry-design-system'
import GetStyles from "./PageWrapperStyles";

interface propsType {
  children: ReactNode;
  style?: StyleProp<ViewStyle>;
  testID?: string;
}
export function PageWrapper(props: propsType) {
  const { children, style, testID } = props;
  const { theme } = useTheme(); /* Fething themes from ThemeProvider */
  const styles = GetStyles(theme); /* Fetching styles based on theme */
  return <View style={[styles.container, style]} testID={testID}>{children}</View>;

}

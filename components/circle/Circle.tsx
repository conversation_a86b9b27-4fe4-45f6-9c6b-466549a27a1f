import { View } from "react-native";
import React from 'react';
import { useTheme } from 'pantry-design-system'

interface propsTypes {
  size?: number;
  color?: string;
}
export function Circle(props: propsTypes) {
  const { theme } = useTheme();
  const { colors } = theme
  const { size = 24, color = colors.pdsThemeColorBackgroundPrimary } = props;
  return (
    <View
      style={{
        width: size,
        height: size,
        borderColor: color,
        borderWidth: 1.5,
        borderRadius: 99,
      }}
    />
  );
}

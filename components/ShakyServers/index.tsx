/**
 * Displays server error state with retry option
 * @component
 * @param {Function} handleTryagain - Retry callback function
 *
 * Features:
 * - Shows server error illustration
 * - Localized error messages
 * - Accessible retry button
 * - SafeAreaView container
 *
 * @example
 * <ShakyServers handleTryagain={fetchData} />
 */
import React from 'react';
import { useTranslation } from 'react-i18next';
import { SafeAreaView } from 'react-native-safe-area-context';

import { NoServer } from '../../assets/images/svg/NoServerConnection';
import StatusPlaceholder from '../StatusPlaceholder/StatusPlaceholder';

type ShakyServersProps = {
    handleTryagain: () => void;
};

const ShakyServers: React.FC<ShakyServersProps> = ({ handleTryagain }) => {
    const { t } = useTranslation();

    const retryAgain = () => {
        handleTryagain();
    };

    return (
        <SafeAreaView edges={['top', 'bottom']}>
            <StatusPlaceholder
                source={NoServer}
                status={t('errorPlaceholder.serverErrorDescription')}
                subHeader={t('errorPlaceholder.shakyServersSubtitle')}
                styles={{}}
                showButton={true}
                buttonText={t('errorPlaceholder.retry')}
                onPress={retryAgain}
                buttonAccessHint={t('schedule.retryServerAccessibilityHint')}
            />
        </SafeAreaView>
    );
};

export default ShakyServers;

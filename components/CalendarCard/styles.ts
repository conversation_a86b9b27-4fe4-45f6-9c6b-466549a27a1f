import { Theme } from "pantry-design-system";
import { Platform, StyleSheet, TextStyle } from "react-native";

const getStyles = ({
  colors,
  fonts,
  typography,
  borderDimens,
  dimensions,
}: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flexDirection: "row",
      alignItems: "center", // Keeps elements aligned
      width: "85%",
      paddingHorizontal: dimensions.pdsGlobalSpace400,
      paddingVertical: dimensions.pdsGlobalSpace400,
      borderRadius: borderDimens.pdsGlobalBorderRadius100,
      borderWidth: 1,
      margin: 5,
      flex: 1,
    },
    buttonContainer: { flex: 1, width: '100%', minHeight: 40 },
    content: {
      flex: 1,
      justifyContent: "center", // Ensures content is vertically aligned
    },
    firstLine: {
      flexDirection: "row",
      alignItems: "center",
    },
    firstLineWithSub: {
      marginBottom: dimensions.pdsGlobalSpace200, // Push first line slightly up if second line exists
    },
    icon: {
      marginRight: dimensions.pdsGlobalSpace200,
    },
    icon1: {
      marginRight: dimensions.pdsGlobalSpace200,
      alignSelf: 'flex-start',
    },
    title: {
      fontSize: 16,
      fontWeight: typography.pdsGlobalFontWeight600,
      color: "#333",
    },
    defaultStyle: {
      fontSize: typography.pdsGlobalFontSize200,
      color: colors.pdsThemeColorForegroundNeutralHigh,
      fontWeight: typography.pdsGlobalFontWeight400,
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
    },
    dayStyle: {
      fontSize: typography.pdsGlobalFontSize300,
      color: colors.pdsThemeColorForegroundNeutralHigh,
      fontWeight: typography.pdsGlobalFontWeight500,
      fontFamily: fonts.pdsGlobalFontFamilyPoppins,
    },
    subTitle: {
      fontSize: typography.pdsGlobalFontSize200,
      color: colors.pdsThemeColorOutlineNeutralHigh,
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
      fontWeight: typography.pdsGlobalFontWeight400,
    },
    noStyle: {},
    chevronContainer: {
      justifyContent: "center",
      alignItems: "center",
      paddingLeft: 16, // Spacing between text and chevron
    },

    modalTitle: {
      fontSize: typography.pdsGlobalFontSize500,
      fontWeight: typography.pdsGlobalFontWeight500,
      fontFamily: fonts.pdsGlobalFontFamilyPoppins,
    },

    modal: {
      justifyContent: "flex-end",
      margin: 0,
    },
    modalContent: {
      paddingTop: Platform.OS === "ios" ? 40 : 0,
      backgroundColor: "white",
      borderTopLeftRadius: 20,
      borderTopRightRadius: 20,
      height: "100%",
    },
    modalHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      borderBottomWidth: 1,
      borderBottomColor: "#ddd",
      paddingBottom: 10,
      paddingTop: 18,
      paddingHorizontal: 10,
    },

    modalDataContainer: {
      marginTop: 20,
      paddingHorizontal: dimensions.pdsGlobalSpace400,
      display: "flex",
      flexDirection: "column",
      gap: 25,
    },

    shiftDataCOntainer: {
      display: "flex",
      flexDirection: "row",
      gap: 10,
      alignItems: "center",
    },

    shiftData: {
      fontSize: typography.pdsGlobalFontSize300,
      fontWeight: typography.pdsGlobalFontWeight400,
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
      color: colors.pdsThemeColorForegroundNeutralHigh,
    },
    shiftData_: {
      fontSize: typography.pdsGlobalFontSize200,
      fontWeight: typography.pdsGlobalFontWeight400,
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
      color: colors.pdsThemeColorForegroundNeutralLow,
    },
    manageSchedule: {
      fontSize: typography.pdsGlobalFontSize200,
      fontWeight: typography.pdsGlobalFontWeight400,
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
      color: colors.pdsThemeColorForegroundNeutralMedium,
    },
    addToCalendar: {
      display: "flex",
      flexDirection: "column",
      borderRadius: 8,
      borderWidth: 1,
      borderColor: colors.pdsThemeColorOutlineNeutralLow,
      padding: dimensions.pdsGlobalSpace400,
      gap: dimensions.pdsGlobalSpace300,
    },

    addToCalendarHeadingContainer: {
      display: "flex",
      flexDirection: "row",
      alignItems: "center",
      gap: 5,
    },

    addToCalendarHeading: {
      fontSize: typography.pdsGlobalFontSize400,
      fontWeight: typography.pdsGlobalFontWeight500,
      fontFamily: fonts.pdsGlobalFontFamilyPoppins,
      color: colors.pdsThemeColorForegroundNeutralHigh,
    },

    description: {
      fontSize: typography.pdsGlobalFontSize200,
      fontWeight: typography.pdsGlobalFontWeight400,
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
      color: colors.pdsThemeColorForegroundNeutralHigh,
    },

    signOutBtn: {
      borderColor: colors.pdsThemeColorOutlinePrimary,
      borderStyle: "solid",
      borderWidth: borderDimens.pdsGlobalBorderWidth100,
      width: "100%",
      minHeight: 40,
      paddingVertical: 0,
      borderRadius: borderDimens.pdsGlobalBorderRadius1000,
    },

    btnLabel: {
      color: colors.pdsThemeColorForegroundPrimary,
      fontWeight: typography.pdsGlobalFontWeight600,
      fontSize: typography.pdsGlobalFontSize300,
      paddingVertical: 0,
      textTransform: "none",
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
    },

    errorContainer: {
      display: "flex",
      flexDirection: "row",
      borderRadius: borderDimens.pdsGlobalBorderRadius100,
      paddingVertical: dimensions.pdsGlobalSpace200,
      paddingHorizontal: dimensions.pdsGlobalSpace400,
      backgroundColor: colors.pdsThemeColorBackgroundError,
      gap: dimensions.pdsGlobalSpace200,
      marginTop: 20,
      marginHorizontal: 10,
    },

    expectionText: {
      color: colors.pdsThemeColorForegroundNeutralHigh,
      fontWeight: typography.pdsGlobalFontWeight600,
      fontSize: typography.pdsGlobalFontSize100,
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
    },
    contentFlexBox: {
      justifyContent: "flex-end",
      flexDirection: "row",
    },
    textFlexBox: {
      textAlign: "left",
      color: "#fff",
      alignSelf: "stretch",
    },
    titleTextComponent: {
      fontSize: 16,
      lineHeight: 23,
      fontWeight: "700",
      fontFamily: "NunitoSans12pt-Bold",
      color: "white",
    },
    bodyTextComponent: {
      fontSize: 14,
      lineHeight: 20,
      fontFamily: "NunitoSans12pt-Regular",
    },
    copy: {
      paddingTop: 16,
      gap: 2,
      flex: 1,
    },
    pdsIconButton: {
      position: "absolute",
      top: 0,
      left: 0,
      borderRadius: 999,
      overflow: "hidden",
      zIndex: 0,
    },
    closeButton: {
      width: 20,
      height: 32,
    },
    innerContent: {
      alignSelf: "stretch",
      justifyContent: "flex-end",
      flexDirection: "row",
    },
    basePopupContent: {
      width: "50%",
      borderRadius: 8,
      position: "absolute",
      backgroundColor: "#1f1e1e",
      paddingLeft: 16,
      paddingRight: 12,
      paddingBottom: 12,
      minWidth: 224,
      maxWidth: 360,
      flex: 1,
      top: 133,
      left: 87,
    },
    tip: {
      color: "black",
      position: "absolute",
      bottom: -8,
      left: Platform.OS == "ios" ? 51 : 54,
    },
    closeIcon: {
      position: "absolute",
      top: 8,
      right: -3,
      zIndex: 1,
    },
  });
  return styles;
};

export default getStyles;

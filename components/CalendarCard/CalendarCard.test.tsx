import React from "react";
import { render, fireEvent, act, waitFor } from "@testing-library/react-native";
import CalendarCard from "../CalendarCard";
import { ThemeProvider } from "pantry-design-system";
import dayjs from "dayjs";
import RNCalendarEvents from "react-native-calendar-events";
import configureS<PERSON> from "redux-mock-store";
import { Provider } from "react-redux";
import { presentEventCreatingDialog } from "react-native-add-calendar-event";

jest.mock('../../analytics/AnalyticsUtils', () => ({
  screenViewLog: jest.fn(),
  userActionLogEvent: jest.fn(),
  setUserProperties: jest.fn(),
}));

jest.mock("react-native-add-calendar-event", () => ({
  presentEventCreatingDialog: jest.fn(),
}));

jest.mock("react-native-calendar-events", () => ({
  requestPermissions: jest.fn(),
  checkPermissions: jest.fn(),
  fetchAllEvents: jest.fn(),
  saveEvent: jest.fn(),
  removeEvent: jest.fn(),
  findCalendars: jest.fn(),
}));

// Mock Theme for Testing
const mockTheme = {
  colors: {
    pdsThemeColorForegroundPositive: "#28a745",
    pdsThemeColorOutlineNeutralHigh: "#6c757d",
    pdsThemeColorOutlineAccent: "#17a2b8",
    pdsThemeColorForegroundError: "#dc3545",
    pdsThemeColorOutlineError: "#ff0000",
    pdsThemeColorOutlineNeutralMedium: "#b5b5b5",
  },
  typography: {
    pdsGlobalFontSize100: 14,
  },
};

jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => key,
    i18n: {
      language: 'en'
    }
  }),
}));

jest.mock("react-native-permissions", () => ({
  PERMISSIONS: {
    ANDROID: {
      READ_CALENDAR: "android.permission.READ_CALENDAR",
      WRITE_CALENDAR: "android.permission.WRITE_CALENDAR",
    },
    IOS: {
      CALENDARS: "ios.permission.CALENDARS",
    },
  },
  RESULTS: {
    UNAVAILABLE: "unavailable",
    DENIED: "denied",
    LIMITED: "limited",
    GRANTED: "granted",
    BLOCKED: "blocked",
  },
  check: jest.fn(),
  request: jest.fn(),
}));

const renderWithTheme = (component, store) => {
  return render(
    <Provider store={store}>
      <ThemeProvider theme={mockTheme}>{component}</ThemeProvider>
    </Provider>
  );
};

const mockTime = {
  start: dayjs().toISOString(),
  end: dayjs().add(2, "hour").toISOString(),
};

const mockStore = configureStore([]);

describe("CalendarCard Component", () => {
  let store: any;
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();

    store = mockStore({
      stores: {
        content: [
          {
            locationId: "12345",
            address: {
              line1: "123 Main St",
              line2: "Apt 4B",
              city: "New York",
              state: "NY",
              zipcode: "10001",
            },
          },
        ],
      },
      profile: { banner: 'safeway' },
      // Add language state to prevent undefined error
      language: {
        id: "en",
        name: "English"
      },
    });
  });

  it("renders store information correctly", async () => {
    const { getByTestId } = renderWithTheme(
      <CalendarCard state="todayShift" time={mockTime} />,
      store
    );

    fireEvent.press(getByTestId("calendar-card-todayShift"));
    await waitFor(() => {
      expect(getByTestId("store-info")).toBeTruthy();
    });
  });

  it("renders completeShift state correctly", () => {
    const { getByTestId } = renderWithTheme(
      <CalendarCard state="completeShift" time={mockTime} />, store
    );
    expect(getByTestId("calendar-card-completeShift")).toBeTruthy();
    expect(getByTestId("icon-completeShift")).toBeTruthy();
    expect(getByTestId("chevron-icon")).toBeTruthy();
  });

  it("renders todayShift state correctly", () => {
    const { getByTestId } = renderWithTheme(
      <CalendarCard state="todayShift" time={mockTime} />,
      store
    );
    expect(getByTestId("calendar-card-todayShift")).toBeTruthy();
    expect(getByTestId("icon-todayShift")).toBeTruthy();
  });

  it("renders nextShift state correctly", () => {
    const { getByTestId } = renderWithTheme(
      <CalendarCard state="nextShift" time={mockTime} />, store
    );
    expect(getByTestId("calendar-card-nextShift")).toBeTruthy();
    expect(getByTestId("icon-nextShift")).toBeTruthy();
  });

  it("renders exception state correctly", () => {
    const { getByTestId, getByText } = renderWithTheme(
      <CalendarCard state="exception" time={mockTime} />, store
    );
    expect(getByTestId("calendar-card-exception")).toBeTruthy();
    expect(getByTestId("icon-exception")).toBeTruthy();
  });

  it("renders futureScheduled state correctly without an icon", () => {
    const { getByTestId, queryByTestId } = renderWithTheme(
      <CalendarCard state="futureScheduled" time={mockTime} />, store
    );
    expect(getByTestId("calendar-card-futureScheduled")).toBeTruthy();
    expect(queryByTestId("icon-futureScheduled")).toBeNull();
  });

  it("renders chevron icon correctly", () => {
    const { getByTestId } = renderWithTheme(
      <CalendarCard state="futureScheduled" time={mockTime} />, store
    );
    expect(getByTestId("chevron-icon")).toBeTruthy();
  });

  it("opens and closes the modal for completeShift state", () => {
    const { getByTestId, queryByTestId } = renderWithTheme(
      <CalendarCard state="completeShift" time={mockTime} />, store
    );

    // Modal should not be visible initially
    expect(queryByTestId("modal")).toBeNull();

    // Open the modal
    fireEvent.press(getByTestId("calendar-card-completeShift"));
    expect(getByTestId("modal")).toBeTruthy();

    // Close the modal
    fireEvent.press(getByTestId("icon-cross"));
    expect(queryByTestId("modal")).toBeNull();
  });

  it("toggle the modal of calender info", () => {
    const { getByTestId, queryByTestId } = renderWithTheme(
      <CalendarCard state="nextShift" time={mockTime} />,
      store
    );
    expect(queryByTestId("modal")).toBeNull();
    fireEvent.press(getByTestId("calendar-card-nextShift"));
    expect(getByTestId("modal")).toBeTruthy();
    expect(queryByTestId("manageCalendarCard")).toBeTruthy();
    fireEvent.press(getByTestId("info-icon"));
    expect(queryByTestId("manage-schedule-modal")).toBeTruthy();
    fireEvent.press(getByTestId("close-icon"));
    expect(queryByTestId("manage-schedule-modal")).toBeNull();
    fireEvent.press(getByTestId("calendar-card-nextShift"));
    expect(queryByTestId("modal")).toBeNull();
  });

  it("opens and closes the modal for exception state", () => {
    const { getByTestId, queryByTestId } = renderWithTheme(
      <CalendarCard state="exception" time={mockTime} />, store
    );

    // Modal should not be visible initially
    expect(queryByTestId("modal")).toBeNull();

    // Open the modal
    fireEvent.press(getByTestId("calendar-card-exception"));
    expect(getByTestId("modal")).toBeTruthy();
  });

  it("renders modal content correctly for completeShift state", () => {
    const { getByTestId, getByText } = renderWithTheme(
      <CalendarCard state="completeShift" time={mockTime} />, store
    );

    // Open the modal
    fireEvent.press(getByTestId("calendar-card-completeShift"));

    // Check modal content
    expect(getByText("completedShift")).toBeTruthy();
  });

  it("renders modal content correctly for exception state", () => {
    const { getByTestId, getByText } = renderWithTheme(
      <CalendarCard state="exception" time={mockTime} />, store
    );

    // Open the modal
    fireEvent.press(getByTestId("calendar-card-exception"));

    // Check modal content
    expect(getByText("completedShift")).toBeTruthy();
    expect(getByText("completedDescription")).toBeTruthy();
  });

  it("requests calendar permissions", async () => {
    RNCalendarEvents.requestPermissions.mockResolvedValue("authorized");
    const result = await RNCalendarEvents.requestPermissions();
    expect(result).toBe("authorized");
  });

  it("checks calendar permissions", async () => {
    RNCalendarEvents.checkPermissions.mockResolvedValue("authorized");
    const result = await RNCalendarEvents.checkPermissions();
    expect(result).toBe("authorized");
  });

  it("fetches all calendar events", async () => {
    const mockEvents = [{ id: "1", title: "Event 1" }];
    RNCalendarEvents.fetchAllEvents.mockResolvedValue(mockEvents);
    const events = await RNCalendarEvents.fetchAllEvents();
    expect(events).toEqual(mockEvents);
  });

  it("saves a calendar event", async () => {
    RNCalendarEvents.requestPermissions.mockResolvedValue("authorized");
    const { getByTestId, getByText } = renderWithTheme(
      <CalendarCard state="nextShift" time={mockTime} />, store
    );

    // Open the modal
    await fireEvent.press(getByTestId("calendar-card-nextShift"));
    await fireEvent.press(getByTestId("addToCalendar-button"));

    // Check modal content
    expect(presentEventCreatingDialog).toHaveBeenCalled();
  });

  it("removes a calendar event", async () => {
    RNCalendarEvents.removeEvent.mockResolvedValue(true);
    const result = await RNCalendarEvents.removeEvent("1");
    expect(result).toBe(true);
  });

  it("handles empty store data gracefully", () => {
    const emptyStore = mockStore({
      stores: { content: [] },
      store: { data: { stores: { content: [] } } },
      profile: { banner: 'safeway' },
      // Add language state for empty store as well
      language: {
        id: "en",
        name: "English"
      },
    });

    const { getByTestId } = renderWithTheme(
      <CalendarCard state="todayShift" time={mockTime} />,
      emptyStore
    );

    expect(getByTestId("calendar-card-todayShift")).toBeTruthy();
  });

  it("handles Spanish language correctly", () => {
    const spanishStore = mockStore({
      stores: {
        content: [
          {
            locationId: "12345",
            address: {
              line1: "123 Main St",
              line2: "Apt 4B",
              city: "New York",
              state: "NY",
              zipcode: "10001",
            },
          },
        ],
      },
      profile: { banner: 'safeway' },
      language: {
        id: "es",
        name: "Spanish"
      },
    });

    const { getByTestId } = renderWithTheme(
      <CalendarCard state="todayShift" time={mockTime} />,
      spanishStore
    );

    expect(getByTestId("calendar-card-todayShift")).toBeTruthy();
  });

  it("handles undefined language gracefully", () => {
    const noLanguageStore = mockStore({
      stores: {
        content: [
          {
            locationId: "12345",
            address: {
              line1: "123 Main St",
              line2: "Apt 4B",
              city: "New York",
              state: "NY",
              zipcode: "10001",
            },
          },
        ],
      },
      profile: { banner: 'safeway' },
      language: null, // Test with null language
    });

    const { getByTestId } = renderWithTheme(
      <CalendarCard state="todayShift" time={mockTime} />,
      noLanguageStore
    );

    expect(getByTestId("calendar-card-todayShift")).toBeTruthy();
  });
});

import React, { useEffect, useRef, useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  findNodeHandle,
  AccessibilityInfo,
  ScrollView,
} from "react-native";
import { useTheme, Text as PDSText, Heading, Button } from "pantry-design-system";
import getStyles from "./styles";
import Icon from "react-native-vector-icons/MaterialCommunityIcons";
import dayjs from "dayjs"; // Import dayjs for date formatting
import { SvgXml } from "react-native-svg";
import {
  clockIcon,
  exceptionIcon,
  nextShiftIcon,
} from "../../assets/images/svg/CalendarCardIcons";

type ShiftTime = {
  /** ISO string, e.g., '2025-08-08T08:15' */
  start: string;
  /** ISO string, e.g., '2025-08-08T16:45' */
  end: string;
};

type ShiftState =
  | 'completeShift'      // A shift that has been completed
  | 'todayShift'         // The current day's shift
  | 'nextShift'          // The upcoming shift
  | 'exception'          // Shift with an exception or issue
  | 'futureScheduled';   // A future, scheduled shift

export interface CalendarCardProps {
  /** Shift time range in ISO format */
  time: {
    start: string;
    end: string;
    workedStart?: string; // Add this
    workedEnd?: string;   // Add this
  };
  /** Type of shift to determine UI state/styling */
  state: ShiftState;
}

import {
  CalendarIcon,
  LocationIcon,
  TimeIcon,
  StoreMap,
  ManageIcon,
  QuestionIcon,
  ErrorIcon,
  CrossIcon,
  tip,
} from "../../assets/images/svg/ShiftDetailIcons";
// import { Button } from "react-native-paper";
import RNCalendarEvents from "react-native-calendar-events";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { presentEventCreatingDialog } from "react-native-add-calendar-event";
import { Store } from "../../app/misc/models/ProfileModal";
import { ADA_ROLES, BUTTON_COLORS, BUTTON_SIZE, BUTTON_VARIANT, COMPLETE_SHIFT, EXCEPTION, FUTURE_SCHEDULED, NEXT_SHIFT, TEXT_ALIGN, TEXT_SIZE, TEXT_WEIGHT, TEXTLINK_COLORS, TODAY_SHIFT } from "../../app/shared/constants";
import advancedFormat from 'dayjs/plugin/advancedFormat';
import { userActionLogEvent } from "../../analytics/AnalyticsUtils";
import { SCHEDULE_ANALYTICS } from "../../analytics/AnalyticsConstants";
import { languagetypes } from "app/store/reducers/selectLanguageSlice";

dayjs.extend(advancedFormat);

const ICONS = {
  completeShift: "check",
  todayShift: "access-alarm",
  nextShift: "calendar-clock-outline",
  exception: "warning-outline",
};

const COLORS = {
  completeShift: "#F0EEED",
  todayShift: "#EAF5F0",
  nextShift: "#EAF5F0",
  futureScheduled: "#FFFFFF",
  exception: "#FCF0F0",
};

const getShiftDetails = (
  time: {
    start: string,
    end: string,
    workedStart: string,
    workedEnd: string
  },
  state: CalendarCardProps["state"]
) => {

  const language = useSelector((state: any) => state.language);

  const startDate = dayjs(time.start);
  const endDate = dayjs(time.end);

  const pastWorkStartDate = dayjs(time.workedStart);
  const pastWorkEndDate = dayjs(time.workedEnd);

  // Get the end of the week from the end date
  const weekEndDate = endDate.endOf('week');
  const lastDay = weekEndDate.format("MMM D")

  const { t } = useTranslation();

  const timeFormat = language?.id === "es" ? "h:mm a" : "h:mm A";


  const formattedTimeRange = `${startDate.format(timeFormat)} ${t('schedule.to')} ${endDate.format(
    timeFormat
  )}`;

  const workedTimeRange = `${pastWorkStartDate.format(timeFormat)} ${t('schedule.to')} ${pastWorkEndDate.format(
    timeFormat
  )}`;


  const formattedDay = startDate.format("dddd, MMMM Do");

  switch (state) {
    case COMPLETE_SHIFT:
      return {
        title: workedTimeRange,
        accessibilityLabel: t('ada.completedShift').replace('${formattedDay}', formattedDay).replace('${formattedTimeRange}', workedTimeRange),

      };
    case TODAY_SHIFT:
      return {
        title: t('ada.todayShiftTitle'),
        subTitle: t('ada.scheduledTitle').replace('${formattedTimeRange}', formattedTimeRange),
        accessibilityLabel: t('ada.todayShift').replace('${formattedDay}', formattedDay).replace('${formattedTimeRange}', formattedTimeRange),
      };
    case NEXT_SHIFT:
      return {
        title: t('ada.nextShiftTitle'),
        subTitle: t('ada.scheduledTitle').replace('${formattedTimeRange}', formattedTimeRange),
        accessibilityLabel: t('ada.nextShift').replace('${formattedDay}', formattedDay).replace('${formattedTimeRange}', formattedTimeRange),
      };
    case FUTURE_SCHEDULED:
      return {
        title: t('ada.scheduledTitle').replace('${formattedTimeRange}', formattedTimeRange),
        accessibilityLabel: t('ada.futureScheduled').replace('${formattedDay}', formattedDay).replace('${formattedTimeRange}', formattedTimeRange),
      };
    case EXCEPTION:
      return {
        title: workedTimeRange,
        subTitle: t("submitResponse").replace('{lastDay}', lastDay),
        accessibilityLabel: t('ada.exception').replace('${formattedDay}', formattedDay).replace('${formattedTimeRange}', workedTimeRange),
      };
    default:
      return {
        title: t('ada.defaultTitle'),
        subTitle: formattedTimeRange,
        accessibilityLabel: t('ada.defaultShift').replace('${formattedTimeRange}', formattedTimeRange),
      };
  }
};

const CalendarCard: React.FC<CalendarCardProps> = ({ state, time }) => {
  const { theme } = useTheme();
  const styles = getStyles(theme);
  const [isModalVisible, setModalVisible] = useState(false);
  const { t } = useTranslation();

  // Accessibility and interaction refs
  const closeRef = useRef(null);
  const infoIconRef = useRef(null);
  const tooltipHeadingRef = useRef(null);
  const closeButtonRef = useRef(null);

  const currentLanguage = useSelector<{ language: languagetypes }, languagetypes>(
    (state) => state.language,
  );
  const toggleModal = () => {
    const name = `${SCHEDULE_ANALYTICS.SHIFT} {date} - {time}`
    const eventActionName = name.replace("{date}", dayjs(time?.start).format('MM/DD/YYYY')).replace("{time}", formatTimeRange(displayStartTime, displayEndTime));

    userActionLogEvent(
      SCHEDULE_ANALYTICS.SCHEDULE_CATEGORY,
      eventActionName,
      SCHEDULE_ANALYTICS.SHIFT_SELECTOR_LABEL,
    );
    setModalVisible(!isModalVisible);
  };

  // Focus close icon in modal when it opens (accessibility)
  useEffect(() => {
    if (isModalVisible && closeRef.current) {
      userActionLogEvent(
        SCHEDULE_ANALYTICS.SCHEDULE_SHIFT_CATEGORY,
        SCHEDULE_ANALYTICS.SCHEDULE_ACTION,
        SCHEDULE_ANALYTICS.SCHEDULE_SHIFT_SHEET_LABEL,
      );

      const nodeHandle = findNodeHandle(closeRef.current);
      if (nodeHandle) {
        // Delay a bit to ensure modal is rendered
        setTimeout(() => {
          AccessibilityInfo.setAccessibilityFocus(nodeHandle);
        }, 500);
      }
    }
  }, [isModalVisible]);

  // Redux store selectors
  const slocData: Store = useSelector((state: any) => state.profile?.slocData);
  const profileData = useSelector((state: any) => state.profile);

  /**
 * Format a date string into long readable format like "Monday, January 1, 2025"
 * @param {string} dateString - ISO date string
 * @returns {string}
 */
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);

    // Pick locale based on language
    const locale = currentLanguage?.id === 'es' ? 'es-ES' : 'en-US';

    return date.toLocaleDateString(locale, {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const { title, subTitle, accessibilityLabel } = getShiftDetails(
    {
      start: time.start,
      end: time.end,
      workedStart: time.workedStart || time.start, // Use workedStart if available, fallback to start
      workedEnd: time.workedEnd || time.end,       // Use workedEnd if available, fallback to end
    },
    state
  );

  // For the modal time display, show worked hours for completed shifts
  const displayStartTime = (state === COMPLETE_SHIFT || state === EXCEPTION) && time.workedStart
    ? time.workedStart
    : time.start;

  const displayEndTime = (state === COMPLETE_SHIFT || state === EXCEPTION) && time.workedEnd
    ? time.workedEnd
    : time.end;

  const [showInfo, setShowInfo] = useState(false);

  /**
 * Get icon color based on shift state
 */
  const fetchIconColor = (state: string) => {
    switch (state) {
      case COMPLETE_SHIFT:
        return theme.colors.pdsThemeColorForegroundPositive;
      case TODAY_SHIFT:
        return theme.colors.pdsThemeColorOutlineNeutralHigh;
      case NEXT_SHIFT:
        return theme.colors.pdsThemeColorOutlineNeutralHigh;
      case EXCEPTION:
        return theme.colors.pdsThemeColorForegroundError;
      default:
        return theme.colors.pdsThemeColorOutlineNeutralHigh;
    }
  };
  /**
   * Get border color for card based on shift state
   */
  const fetchBorderColor = (state: string) => {
    switch (state) {
      case COMPLETE_SHIFT:
        return theme.colors.pdsThemeColorOutlineNeutralMedium;
      case TODAY_SHIFT:
      case NEXT_SHIFT:
        return theme.colors.pdsGlobalColorGeneralGreen70;
      case EXCEPTION:
        return theme.colors.pdsThemeColorOutlineError;
      default:
        return theme.colors.pdsThemeColorOutlineNeutralHigh;
    }
  };
  /**
   * Return the appropriate title text style based on shift state
   */
  const fetchTitleStyle = (state: string) => {
    switch (state) {
      case COMPLETE_SHIFT:
      case FUTURE_SCHEDULED:
      case EXCEPTION:
        return styles.defaultStyle;
      case TODAY_SHIFT:
      case NEXT_SHIFT:
        return styles.dayStyle;
      default:
        return styles.title;
    }
  };
  /**
   * Return appropriate icon JSX element based on shift state
   */
  const fetchIcon = (state: string) => {
    switch (state) {
      case COMPLETE_SHIFT:
        return (
          <Icon
            name={ICONS[state]}
            size={17}
            color={fetchIconColor(state)}
            style={styles.icon}
            testID="icon-completeShift"
          />
        );
      case TODAY_SHIFT:
        return (
          <SvgXml
            xml={clockIcon}
            accessible={false}
            importantForAccessibility="no"
            style={styles.icon}
            testID="icon-todayShift"
          />
        );
      case NEXT_SHIFT:
        return (
          <SvgXml
            xml={nextShiftIcon}
            accessible={false}
            importantForAccessibility="no"
            style={styles.icon}
            testID="icon-nextShift"
          />
        );
      case EXCEPTION:
        return (
          <SvgXml
            xml={exceptionIcon}
            accessible={false}
            importantForAccessibility="no"
            style={styles.icon}
            testID="icon-exception"
          />
        );
      default:
        return null;
    }
  };

  // Static icon color used in cards
  const iconColor = theme.colors.pdsThemeColorOutlinePrimary;

  /**
 * Format shift time range into string like "8:00 AM to 4:00 PM"
 */
  const formatTimeRange = (start: string, end: string): string => {
    const startTime = new Date(start).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });

    const endTime = new Date(end).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });

    return `${startTime} to ${endTime}`;
  };

  // Accessibility focus handling for tooltip open/close
  useEffect(() => {
    if (showInfo) {
      const timeout = setTimeout(() => {
        if (closeButtonRef.current) {
          const node = findNodeHandle(closeButtonRef.current);
          if (node) {
            AccessibilityInfo.setAccessibilityFocus(node);
          }
        }
      }, 100); // delay gives time for layout/render

      return () => clearTimeout(timeout);
    } else {
      const timeout = setTimeout(() => {
        if (infoIconRef.current) {
          const node = findNodeHandle(infoIconRef.current);
          if (node) {
            AccessibilityInfo.setAccessibilityFocus(node);
          }
        }
      }, 100);

      return () => clearTimeout(timeout);
    }
  }, [showInfo]);

  /**
   * Returns a human-readable duration string between the given start and end times,
   * formatted as "Xd Yh Zmin", where X = days, Y = hours, Z = minutes.
   *
   * The result varies depending on the shift status:
   * - For **future shifts**, it returns a string like "Starting in 1d 2h 30min"
   * - For **completed shifts**, it returns "1d 2h 30min worked"
   * - If the shift is **currently active**, it returns an empty string
   * - If the end time is already passed for a past shift, it returns a localized "Shift over" message
   *
   * @param {string} start - ISO string of the shift start datetime
   * @param {string} end - ISO string of the shift end datetime
   * @param {ShiftState} state - The state of the shift (e.g., 'completeShift', 'exception', etc.)
   * @returns {string} A formatted duration string based on the shift state and timing
   */
  const getExactDuration = (
    start: string,
    end: string,
    state: ShiftState,
  ): string => {
    const now = new Date();
    const startDate = new Date(start);
    const endDate = new Date(end);

    const isSameDay =
      now.getFullYear() === startDate.getFullYear() &&
      now.getMonth() === startDate.getMonth() &&
      now.getDate() === startDate.getDate();

    const isCurrentlyWorking =
      isSameDay && now >= startDate && now <= endDate;

    if (isCurrentlyWorking) {
      return "";
    }

    const isInFuture = startDate > now;
    const isFutureShift = (state !== 'completeShift' && state !== 'exception') && isInFuture;

    const from = isFutureShift ? now : startDate;
    const to = isFutureShift ? startDate : endDate;

    const diffMs = to.getTime() - from.getTime();
    if (diffMs < 0) return `${t("shiftOver")}`;

    const totalMinutes = diffMs / (1000 * 60);

    const days = Math.floor(totalMinutes / (60 * 24));
    const remainingMinutesAfterDays = totalMinutes % (60 * 24);

    const hours = Math.floor(remainingMinutesAfterDays / 60);
    const minutes = Math.round(remainingMinutesAfterDays % 60);

    // Build the string without empty values or extra spaces
    let duration = '';
    if (days > 0) {
      duration += `${days}d `;
    }
    duration += `${hours}h ${minutes}min`;

    return isFutureShift
      ? `${t("starting")} ${duration}`
      : `${duration} ${t("worked")}`;
  };
  /**
   * Generates a detailed, accessible description of a shift's schedule,
   * including date, time range, and duration.
   *
   * This function is intended for use with screen readers or accessibility labels.
   * It constructs a full sentence that helps visually impaired users understand:
   * - When the shift is scheduled or completed
   * - The exact start and end time
   * - The duration in days, hours, and minutes
   *
   * Special cases:
   * - If the shift is currently active (ongoing), an empty string is returned to avoid redundancy.
   * - If the shift has already ended, a localized "Shift over" message is returned.
   *
   * Example outputs:
   * - "Scheduled on Monday, August 5, 2025 from 9:00 AM to 5:00 PM, starting in 1 day, 2 hours."
   * - "Completed shift on Monday, August 4, 2025 from 9:00 AM to 5:00 PM, lasting 8 hours."
   *
   * @param {string} start - ISO date string representing shift start time
   * @param {string} end - ISO date string representing shift end time
   * @param {ShiftState} state - The current state of the shift (e.g., 'completeShift', 'exception', etc.)
   * @returns {string} A full sentence summarizing the shift timing for screen reader usage
   */
  const getAccessibleDuration = (
    start: string,
    end: string,
    state: ShiftState,
  ): string => {
    const now = new Date();
    const startDate = new Date(start);
    const endDate = new Date(end);

    const isSameDay =
      now.getFullYear() === startDate.getFullYear() &&
      now.getMonth() === startDate.getMonth() &&
      now.getDate() === startDate.getDate();

    const isCurrentlyWorking =
      isSameDay && now >= startDate && now <= endDate;

    if (isCurrentlyWorking) {
      return ""; // Accessibility string disabled
    }

    const isInFuture = startDate > now;
    const isFutureShift = (state !== 'completeShift' && state !== 'exception') && isInFuture;

    const from = isFutureShift ? now : startDate;
    const to = isFutureShift ? startDate : endDate;

    const diffMs = to.getTime() - from.getTime();
    if (diffMs < 0) return t("shiftOver");

    const totalMinutes = diffMs / (1000 * 60);

    const days = Math.floor(totalMinutes / (60 * 24));
    const remainingMinutesAfterDays = totalMinutes % (60 * 24);

    const hours = Math.floor(remainingMinutesAfterDays / 60);
    const minutes = Math.round(remainingMinutesAfterDays % 60);

    const startTimeStr = startDate.toLocaleTimeString(undefined, {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });

    const endTimeStr = endDate.toLocaleTimeString(undefined, {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });

    const dateStr = startDate.toLocaleDateString(undefined, {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });

    const durationParts: string[] = [];
    if (days > 0) durationParts.push(`${days} ${days === 1 ? "day" : "days"}`);
    if (hours > 0) durationParts.push(`${hours} ${hours === 1 ? "hour" : "hours"}`);
    if (minutes > 0 || durationParts.length === 0) {
      durationParts.push(`${minutes} ${minutes === 1 ? "minute" : "minutes"}`);
    }

    const durationStr = durationParts.join(", ");

    if (isFutureShift) {
      return `${t("schedule.scheduledTime").replace('{startTimeStr}', startTimeStr).replace('{endTimeStr}', endTimeStr).replace('{durationStr}', durationStr)}`;
    }

    return `${t("schedule.completedTime").replace('{startTimeStr}', startTimeStr).replace('{endTimeStr}', endTimeStr).replace('{durationStr}', durationStr)}`;
  };

  const handleAddToCalendar = async () => {
    userActionLogEvent(
      SCHEDULE_ANALYTICS.SCHEDULE_SHIFT_CATEGORY,
      SCHEDULE_ANALYTICS.ADD_TO_CALENDAR,
      SCHEDULE_ANALYTICS.SCHEDULE_SHIFT_SHEET_LABEL,
    );

    const shiftStartDate = new Date(time?.start);
    const shiftEndDate = new Date(time?.end);

    const startDate = new Date(shiftStartDate);
    const endDate = new Date(shiftEndDate);

    try {
      const permission = await RNCalendarEvents.requestPermissions();
      if (permission !== "authorized") {
        console.warn("Calendar permission denied");
        return;
      }

      // Build location string with proper null checks
      const locationParts = [];
      if (slocData?.address?.line1) locationParts.push(slocData.address.line1);
      if (slocData?.address?.city) locationParts.push(slocData.address.city);
      if (slocData?.address?.state) locationParts.push(slocData.address.state);
      if (slocData?.address?.zipcode) locationParts.push(slocData.address.zipcode);

      const location = locationParts.length > 0 ? locationParts.join(', ') : '';

      await presentEventCreatingDialog({
        title: `${profileData?.profile?.departmentName}`,
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        location: location,
        notes: t("scheduledShift"),
        alarms: [{ date: -15 }],
      });
    } catch (error) {
      console.error("Error adding event to calendar:", error);
    }


  };

  /**
 * Accessibility-friendly string for screen readers, describing the full shift
 */
  const endDate = dayjs(time.end);
  const weekEndDate = endDate.endOf('week');
  const lastDay = weekEndDate.format("MMM D")

  return (
    <>
      <TouchableOpacity
        style={[
          styles.container,
          {
            backgroundColor: COLORS[state],
            borderColor: fetchBorderColor(state),
          },
        ]}
        accessibilityLabel={accessibilityLabel}
        accessibilityRole={ADA_ROLES.NONE}
        testID={`calendar-card-${state}`}
        onPress={toggleModal}
      >
        {/* Left Side (Icon + Text) */}
        <View style={styles.content}>
          {/* First Line (Icon + Title) */}
          <View
            style={[
              styles.firstLine,
              subTitle ? styles.firstLineWithSub : styles.noStyle,
            ]}
            testID={`calendar-title-${state}`}
          >
            {state !== FUTURE_SCHEDULED && fetchIcon(state)}

            <Heading
              accessible={false}
              testID={'title'}
              textAlign={TEXT_ALIGN.CENTER}
              title={title}
              color={TEXTLINK_COLORS.NEUTRAL_HIGH}
              accessibilityHint={''}
              size={TEXT_SIZE.X_SMALL}
              style={fetchTitleStyle(state)}
            />
          </View>

          {/* Second Line (if applicable) */}
          {subTitle && (
            <View
              testID={`calendar-subtitle-${state}`}
            >
              <PDSText
                accessible={false}
                color={TEXTLINK_COLORS.NEUTRAL_HIGH}
                size={TEXT_SIZE.MEDIUM}
                text={subTitle}
                weight={state === EXCEPTION ? TEXT_WEIGHT.SEMI_BOLD : TEXT_WEIGHT.REGULAR}
                testID="subTitle"
              />
            </View>
          )}
        </View>

        {/* Right Side (Chevron) */}
        <View style={styles.chevronContainer} testID="chevron-container">
          <Icon
            name="chevron-right"
            size={24}
            color={theme.colors.pdsThemeColorOutlineNeutralHigh}
            testID="chevron-icon"
          />
        </View>
      </TouchableOpacity>

      {isModalVisible && (
        <Modal
          visible={isModalVisible}
          animationType="slide"
          onRequestClose={() => {
            userActionLogEvent(
              SCHEDULE_ANALYTICS.SCHEDULE_SHIFT_CATEGORY,
              SCHEDULE_ANALYTICS.CLOSE,
              SCHEDULE_ANALYTICS.SCHEDULE_SHIFT_SHEET_LABEL,
            );
            setModalVisible(false)
          }}
          style={styles.modal}
          testID="modal"
          accessible={true}
          accessibilityRole={ADA_ROLES.DIALOG}
        >
          <View style={styles.modalContent}>
            <ScrollView>
              <View style={styles.modalHeader}>
                <Text
                  style={styles.modalTitle}
                  allowFontScaling={false}
                  accessibilityLabel={`${state === "completeShift" || state === "exception" ? t("completedShift") : t("scheduledShift")} Heading`}
                >
                  {state === "completeShift" || state === "exception"
                    ? t("completedShift")
                    : t("scheduledShift")}
                </Text>
                <TouchableOpacity
                  onPress={toggleModal}
                  accessible={true}
                  accessibilityRole={ADA_ROLES.BUTTON}
                  accessibilityLabel={t('ada.closeButton')}
                  ref={closeRef}
                >
                  <SvgXml
                    xml={CrossIcon(iconColor)}
                    style={styles.icon}
                    testID="icon-cross"
                  />
                </TouchableOpacity>
              </View >

              {state === EXCEPTION && (
                <View style={styles.errorContainer}>
                  <SvgXml
                    xml={ErrorIcon}
                    style={styles.icon}
                    testID="icon-error"
                    accessible={false}
                    accessibilityRole={ADA_ROLES.IMAGE}
                  />
                  <Text
                    style={styles.expectionText}
                    accessibilityLabel={`${t("missedPunch").replace('{lastDay}', lastDay)} `}
                  >
                    {`${t("submitResponse").replace('{lastDay}', lastDay)}`}
                  </Text>
                </View >
              )}

              <View style={styles.modalDataContainer}>
                <View style={styles.shiftDataCOntainer}>
                  <SvgXml
                    xml={CalendarIcon}
                    style={styles.icon}
                    testID="icon-calendar"
                    accessible={false}
                    accessibilityRole={ADA_ROLES.IMAGE}
                  />
                  <Text style={styles.shiftData} accessible={true}>{formatDate(time?.start)}</Text>
                </View>
                <View style={styles.shiftDataCOntainer}>
                  <SvgXml
                    xml={TimeIcon}
                    style={styles.icon1}
                    testID="icon-time"
                    accessible={false}
                    accessibilityRole={ADA_ROLES.IMAGE}
                  />
                  <View
                    accessible={getAccessibleDuration(displayStartTime, displayEndTime, state) !== ""}
                    accessibilityRole="text"
                    accessibilityLabel={getAccessibleDuration(displayStartTime, displayEndTime, state)}
                    importantForAccessibility="yes"
                  >
                    <Text
                      style={styles.shiftData}
                      accessible={false}
                    >
                      {formatTimeRange(displayStartTime, displayEndTime)}
                    </Text>
                    <Text
                      style={styles.shiftData_}
                      accessible={false}
                    >
                      {getExactDuration(displayStartTime, displayEndTime, state)}
                    </Text>
                  </View>
                </View >
                <View style={styles.shiftDataCOntainer}>
                  <SvgXml
                    xml={LocationIcon}
                    style={styles.icon1}
                    testID="icon-location"
                    accessible={false}
                    accessibilityRole={ADA_ROLES.IMAGE}
                  />
                  {slocData ? <Text style={styles.shiftData} testID="store-info" accessibilityLabel={`${t('scheduledAt')} ${slocData?.brand?.name} #${slocData?.locationId} ${slocData?.address?.line1}, ${slocData?.address?.city}, ${slocData?.address?.state} ${slocData?.address?.zipcode}`}>
                    {`${slocData?.brand?.name} #${slocData?.locationId}\n`}
                    {`${slocData?.address?.line1}\n`}
                    {`${slocData?.address?.city}, ${slocData?.address?.state} ${slocData?.address?.zipcode}`}
                  </Text> : <Text style={styles.shiftData} testID="store-info" accessible={false}>
                    -
                  </Text>}
                </View>
                <View style={styles.shiftDataCOntainer}>
                  <SvgXml
                    xml={StoreMap}
                    style={styles.icon1}
                    testID="icon-storeMap"
                    accessible={false}
                    accessibilityRole={ADA_ROLES.IMAGE}
                  />
                  <Text style={styles.shiftData}>{profileData?.profile?.jobName}</Text>
                </View>
                {
                  state !== COMPLETE_SHIFT && state !== EXCEPTION && (
                    <View
                      style={styles.shiftDataCOntainer}
                      testID="manageCalendarCard"
                    >
                      <Text
                        style={styles.manageSchedule}
                        testID="manage-schedule-text"
                        accessible={false}
                      >
                        {t("manageSchedule")}
                      </Text>
                      <TouchableOpacity
                        ref={infoIconRef}
                        onPress={() => {
                          setShowInfo((prev) => !prev);
                          userActionLogEvent(
                            SCHEDULE_ANALYTICS.SCHEDULE_SHIFT_CATEGORY,
                            SCHEDULE_ANALYTICS.MANAGE_SCHEDULE_INFO_BUG,
                            SCHEDULE_ANALYTICS.SCHEDULE_SHIFT_SHEET_LABEL,
                          );
                        }}
                        accessible={true}
                        accessibilityLabel={t("ada.manageScheduleBtn")}
                        testID="info-icon" // Added testID for the "Manage" icon
                      >
                        <SvgXml
                          xml={ManageIcon}
                          style={styles.icon1}
                          accessibilityRole={ADA_ROLES.IMAGE}
                        />
                      </TouchableOpacity>
                    </View>
                  )
                }

                {
                  showInfo && (
                    <View
                      style={styles.basePopupContent}
                      ref={tooltipHeadingRef}
                      testID="manage-schedule-modal"
                      accessibilityViewIsModal={true}
                    >
                      <View style={[styles.content, styles.contentFlexBox]}>
                        <View style={styles.copy}>
                          <Text
                            style={[styles.titleTextComponent, styles.textFlexBox]}
                            accessible={true}
                            accessibilityLabel={t("manageSchedule")}
                          >
                            {t("manageSchedule")}
                          </Text>
                          <Text
                            style={[styles.bodyTextComponent, styles.textFlexBox]}
                            accessibilityLabel={t("ontactyourstoredirectorifyouneedschedulechanges")}
                            accessible={true}
                          >
                            {t("ontactyourstoredirectorifyouneedschedulechanges")}
                          </Text>
                        </View>
                        <TouchableOpacity
                          style={[styles.closeButton, styles.contentFlexBox]}
                          onPress={() => {
                            setShowInfo(false)
                          }
                          }
                          activeOpacity={0.8}
                          accessible={true}
                          ref={closeButtonRef}
                          accessibilityRole={ADA_ROLES.BUTTON}
                          accessibilityLabel={t("closeButton")}
                          testID="close-icon" // Added testID for the close button
                        >
                          <SvgXml
                            xml={CrossIcon("white", "16")}
                            style={styles.closeIcon}
                            accessible={false}
                            accessibilityRole={ADA_ROLES.IMAGE}
                          />
                        </TouchableOpacity>
                      </View>
                      <SvgXml xml={tip} style={styles.tip} />
                    </View>
                  )
                }

                <View style={styles.addToCalendar}>
                  <View style={styles.addToCalendarHeadingContainer}>
                    <SvgXml
                      xml={
                        state === COMPLETE_SHIFT || state === EXCEPTION
                          ? QuestionIcon
                          : CalendarIcon
                      }
                      style={styles.icon1}
                      testID="icon-addToCalendar"
                      accessible={false}
                    />
                    <Text style={styles.addToCalendarHeading} accessibilityLabel={
                      state === "completeShift" || state === "exception"
                        ? `${t("hoursIssue")} Heading` :
                        `${t("addToYourCalendar")} Heading`}>
                      {state === "completeShift" || state === "exception"
                        ? t("hoursIssue")
                        : t("addToYourCalendar")}
                    </Text>
                  </View>
                  <Text style={styles.description}>
                    {state === COMPLETE_SHIFT || state === EXCEPTION
                      ? t("completedDescription")
                      : t("schedulesDescription")}
                  </Text>

                  {state !== COMPLETE_SHIFT && state !== EXCEPTION && (
                    <View style={styles.buttonContainer}>
                      <Button
                        fullWidth
                        theme={theme}
                        testID="addToCalendar-button"
                        accessible
                        size={BUTTON_SIZE.MEDIUM}
                        variant={BUTTON_VARIANT.OUTLINED}
                        accessibilityLabel={`${t("addToCalendarBtn")} ${t("ada.doubleTapToActivate")}`}
                        accessibilityRole="button"
                        accessibilityHint={''}
                        label={t("addToCalendarBtn")}
                        color={BUTTON_COLORS.PRIMARY}
                        onPress={handleAddToCalendar}
                      />
                    </View>
                  )}
                </View>
              </View >
            </ScrollView>
          </View >
        </Modal >
      )}
    </>
  );
};

export default CalendarCard;
// Enhanced types for the StatusCard component

export interface ShiftData {
  workDate: string;
  startTime?: string;
  endTime?: string;
}

export interface ShiftState {
  shiftStartDate: string;
  shiftEndDate: string;
}

export interface ScheduleData {
  data?: {
    scheduleResponse?: {
      schedule?: ShiftData[];
    };
  };
}

export interface ProfileState {
  isUserInStore: boolean;
}

export interface ReduxState {
  shift: ShiftState;
  empSchedule: ScheduleData;
  profile: ProfileState;
}

export interface StatusConfig {
  title: string;
  subtitle: string;
  color: string;
  titleColor: string;
  iconType: 'clock' | 'lunch';
}

export type StatusConfigMap = Record<string, StatusConfig>;

import { useMemo } from 'react';
import { useSelector } from 'react-redux';
import dayjs from 'dayjs';
import { SCHEDULE_STATUS, StatusCardProps } from '../../app/features/schedule/scheduleConstants';
import { isShiftEndingSoon, isShiftOver, isLateForShift, isShiftStartingSoon } from '../../app/utils/TimeUtils';
import { ReduxState } from './types';

/**
 * Custom hook for determining the current shift status based on Redux state
 * @returns The appropriate status for the status card
 */
export const useStatusDetermination = (
    clockInStatus: boolean
): StatusCardProps['status'] => {
    const { shiftStartDate, shiftEndDate } = useSelector((state: ReduxState) => state.shift);
    const schedule = useSelector((state: ReduxState) => 
        state.empSchedule?.data?.scheduleResponse?.schedule || []
    );
    const { isUserInStore } = useSelector((state: ReduxState) => state.profile);

    return useMemo(() => {
        const now = dayjs().toDate();
        const today = dayjs().format("YYYY-MM-DD");
        const todayShift = schedule?.find((s) => s.workDate === today);

        // Use the passed clockInStatus instead of calculating clockedIn
        const clockedIn = clockInStatus;

        // If user is not in store or no shift for today, prompt to check schedule
        if (!isUserInStore || !todayShift) {
            return SCHEDULE_STATUS.checkSchedule;
        }

        // If user is clocked in
        if (clockedIn) {
            if (shiftEndDate) {
                if (isShiftOver(shiftEndDate, now, shiftStartDate, clockedIn)) {
                    return SCHEDULE_STATUS.shiftOver;
                }
                if (isShiftEndingSoon(shiftEndDate, now)) {
                    return SCHEDULE_STATUS.shouldClockOut;
                }
            }
            return SCHEDULE_STATUS.clockedIn;
        }

        // If user is not clocked in
        if (shiftStartDate) {
            if (shiftEndDate && isShiftOver(shiftEndDate, now, shiftStartDate, clockedIn)) {
                return SCHEDULE_STATUS.checkSchedule;
            }
            if (isLateForShift(shiftStartDate, now)) {
                return SCHEDULE_STATUS.shouldClockIn;
            }
            if (isShiftStartingSoon(shiftStartDate, now)) {
                return SCHEDULE_STATUS.shiftStartingSoon;
            }
        }

        // Default fallback
        return SCHEDULE_STATUS.checkSchedule;
    }, [shiftStartDate, shiftEndDate, schedule, isUserInStore, clockInStatus]);
};

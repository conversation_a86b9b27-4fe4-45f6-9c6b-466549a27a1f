import dayjs from 'dayjs';
import { useTheme, Heading, Text, Button } from 'pantry-design-system';
import React, { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';
import { SvgXml } from 'react-native-svg';
import { useSelector } from 'react-redux';
import { useNavigation } from '@react-navigation/native';

import { HOME_CONSTANTS } from '../../app/features/home/<USER>';
import { SCHEDULE_STATUS } from '../../app/features/schedule/scheduleConstants';
import {
  TEXTLINK_COLORS,
  TEXT_SIZE,
  TEXT_WEIGHT,
  TEXTDECORATION,
  FALLBACK_BANNER,
  TEXT_PANTRY_COLORS,
  ImportantForAccessibility,
  SCREENS
} from '../../app/shared/constants';
import { capitalizeFirstLetter } from '../../app/utils/helpers';
import { getClockIcon, getDinnerIcon } from '../../assets/images/svg/CalendarCardIcons';

import getStyles from './styles';
import { useStatusDetermination } from './useStatusDetermination';

import type { StatusConfigMap } from './types';
import type { StatusCardProps } from '../../app/features/schedule/scheduleConstants';
import { userActionLogEvent } from '../../analytics/AnalyticsUtils';
import { SCHEDULE_ANALYTICS } from '../../analytics/AnalyticsConstants';

const StatusCard: React.FC<StatusCardProps> = ({ clockedIn }) => {
  const { TIME_TO_CLOCKOUT, ON_LUNCH, ON_SHIFT, SHIFT_STARTING_SOON, SHIFT_OVER, SHOULD_CLOCK_IN } =
    HOME_CONSTANTS;

  const {
    shiftStartTime,
    shiftDuration,
    durationInMinutes,
    todaysShift,
    scheduleShift,
    clockInTime,
  } = useSelector((state: any) => state.shift);

  const { theme } = useTheme();
  const styles = getStyles(theme);
  const { t } = useTranslation();
  const banner = useSelector((state: any) => state.profile?.banner);
  const bannerName = capitalizeFirstLetter(banner ? banner : FALLBACK_BANNER);

  // Determine the status from the custom hook
  const determinedStatus = useStatusDetermination(clockedIn ?? false);

  useEffect(() => {
    userActionLogEvent(
      SCHEDULE_ANALYTICS.SCHEDULE_SHIFT_CATEGORY,
      SCHEDULE_ANALYTICS.SCHEDULE_ACTION,
      SCHEDULE_ANALYTICS.SCHEDULE_SHIFT_CARDS_LABEL,
    );
  }, [clockedIn, determinedStatus])

  const navigation = useNavigation();
  //Remain minutes string to convert in to hours and minutes
  let remainTimeString = '0 min';
  if (durationInMinutes && durationInMinutes > 0) {
    const hours = Math.floor(durationInMinutes / 60);
    const minutes = durationInMinutes % 60;
    if (hours > 0 && minutes > 0) {
      remainTimeString = `${hours}h ${minutes}min`;
    } else if (hours > 0) {
      remainTimeString = `${hours}h`;
    } else {
      remainTimeString = `${minutes}min`;
    }
  }

  let formattedScheduleString = '';
  if (scheduleShift && Object.keys(scheduleShift).length > 0 && scheduleShift.startTime) {
    // Format: "Monday, Oct. 1"
    const scheduleStartTime = dayjs(scheduleShift.startTime);
    const scheduleEndTime = dayjs(scheduleShift.endTime);
    const formattedDate = dayjs(scheduleShift.startTime).format('dddd, MMM. D');
    formattedScheduleString = `${formattedDate}\n${scheduleStartTime.format('hh:mm A')} - ${scheduleEndTime.format('hh:mm A')}`;
  }

  // Configuration for different status types
  const statusConfig: StatusConfigMap = useMemo(
    () => ({
      [SCHEDULE_STATUS.shiftStartingSoon]: { // this will show when shift is starting soon
        title: t(SHIFT_STARTING_SOON),
        subtitle: shiftDuration, //'9:00 AM - 2:30 PM' 
        color: theme.colors.pdsThemeColorForegroundInfo,
        titleColor: theme.colors.pdsThemeColorForegroundNeutralHigh,
        iconType: 'clock' as const,
      },
      // this will show when user should clock in
      [SCHEDULE_STATUS.shouldClockIn]: {
        title: t(SHOULD_CLOCK_IN),
        subtitle: shiftDuration, // '9:00 AM - 2:30 PM',
        color: theme.colors.pdsThemeColorForegroundInfo,
        titleColor: theme.colors.pdsThemeColorForegroundNeutralHigh,
        iconType: 'clock' as const,
      },
      // this will show when user is clocked in
      [SCHEDULE_STATUS.clockedIn]: {
        title: t(ON_SHIFT),
        subtitle: shiftStartTime
          ? t('schedule.ClockedInAt').replace('{time}', clockInTime)
          : t(ON_SHIFT),
        color: theme.colors.pdsThemeColorForegroundPositive,
        titleColor: theme.colors.pdsThemeColorForegroundPositive,
        iconType: 'clock' as const,
      },
      // this will show when user is on lunch
      [SCHEDULE_STATUS.onLunch]: {
        title: t(ON_LUNCH),
        subtitle:
          shiftStartTime && durationInMinutes
            ? t('schedule.StartedAgoAt')
              .replace('{timeAgo}', remainTimeString)
              .replace('{startTime}', clockInTime)
            : t(ON_LUNCH),
        color: theme.colors.pdsThemeColorForegroundWarning,
        titleColor: theme.colors.pdsThemeColorForegroundWarning,
        iconType: 'lunch' as const,
      },
      // this will show when user should clock out
      [SCHEDULE_STATUS.shouldClockOut]: {
        title: t(TIME_TO_CLOCKOUT),
        subtitle:
          shiftStartTime && durationInMinutes
            ? t('schedule.ClockedInAgoAt')
              .replace('{timeAgo}', remainTimeString)
              .replace('{startTime}', clockInTime)
            : t(TIME_TO_CLOCKOUT),
        color: theme.colors.pdsThemeColorForegroundError,
        titleColor: theme.colors.pdsThemeColorForegroundNeutralHigh,
        iconType: 'clock' as const,
      },
      // this will show when shift is over
      [SCHEDULE_STATUS.shiftOver]: {
        title: t(SHIFT_OVER),
        subtitle:
          shiftStartTime && durationInMinutes
            ? t('schedule.ClockedInAgoAt')
              .replace('{timeAgo}', remainTimeString)
              .replace('{startTime}', clockInTime)
            : t(SHIFT_OVER),
        color: theme.colors.pdsThemeColorForegroundError,
        titleColor: theme.colors.pdsThemeColorForegroundNeutralHigh,
        iconType: 'clock' as const,
      },
      // this will show when user should check schedule
      [SCHEDULE_STATUS.checkSchedule]: {
        title: '', // We are using this as fallback, can take from constants
        subtitle: '',
        color: theme.colors.pdsThemeColorForegroundInfo,
        titleColor: theme.colors.pdsThemeColorForegroundNeutralHigh,
        iconType: 'clock' as const,
      },
    }),
    [theme, t, shiftStartTime, durationInMinutes],
  );

  const currentConfig = determinedStatus ? statusConfig[determinedStatus] : null;

  if (!currentConfig) {
    console.warn(`Unknown status: ${determinedStatus}`);
    return null;
  }

  const { title, subtitle, color, titleColor, iconType } = currentConfig;

  const renderIcon = () => {
    const iconProps = {
      accessible: false,
      accessibilityRole: 'image' as const,
      style: styles.icon,
      testID: `icon-${iconType}Status`,
    };

    if (iconType === 'lunch') {
      return <SvgXml importantForAccessibility={ImportantForAccessibility.NO as 'no'} xml={getDinnerIcon(40, 40, color)} {...iconProps} />;
    }

    return <SvgXml importantForAccessibility={ImportantForAccessibility.NO as 'no'} xml={getClockIcon(40, 40, color)} {...iconProps} />;
  };

  // Check if both schedule Shift and todays Shift are empty, undefined, null, or empty object
  const now = dayjs();
  const isScheduleShiftEmpty = !scheduleShift || Object.keys(scheduleShift).length === 0;
  const isTodaysShiftEmpty = !todaysShift || Object.keys(todaysShift).length === 0;
  const isTodaysShiftEnded =
    todaysShift?.endTime && dayjs(todaysShift.endTime).isBefore(now) && clockedIn === false;
  const isTodaysShiftStartsLater =
    todaysShift?.startTime &&
    dayjs(todaysShift.startTime).diff(now, 'hour', true) > 1 &&
    clockedIn === false;

  if (
    isScheduleShiftEmpty &&
    (isTodaysShiftEmpty || isTodaysShiftEnded || isTodaysShiftStartsLater)
  ) {
    return null;
  }

  return (
    <View style={[styles.card, { borderColor: color }]} testID={'status-card'}>
      {determinedStatus === SCHEDULE_STATUS.checkSchedule ? (
        <>
          <View style={styles.checkScheduleTextContainer}>
            <Heading
              accessible
              testID={'schedule-card-heading'}
              title={bannerName}
              color={TEXT_PANTRY_COLORS.NEUTRAL_HIGH}
              size={TEXT_SIZE.X_SMALL}
            />
            <Text
              accessible
              testID={'schedule-text'}
              color={TEXTLINK_COLORS.NEUTRAL_HIGH}
              size={TEXT_SIZE.LARGE}
              text={formattedScheduleString || t('schedule.noShiftsScheduleed')}
              weight={TEXT_WEIGHT.REGULAR}
              textDecoration={TEXTDECORATION.None}
            />
          </View>
          <View style={styles.checkScheduleButtonWrapper}>
            <Button
              theme={theme}
              width={'100%'}
              size={TEXT_SIZE.SMALL}
              label={t('Check schedule')}
              onPress={() => {
                navigation.navigate(SCREENS.SCHEDULE);
              }}
              customContentDescription={t('ada.checkScheduleBtn')}
              accessible={true}
              testID="check-schedule-button"
            />
          </View>
        </>
      ) : (
        <>
          <View style={styles.iconContainer}>{renderIcon()}</View>
          <View style={styles.textContainer} accessible={false}>
            <Heading
              accessible
              testID={'status-title'}
              title={title}
              color={titleColor}
              accessibilityLabel={title}
            />
            <Text
              accessible
              testID={'status-subtitle'}
              color={TEXTLINK_COLORS.NEUTRAL_HIGH}
              size={TEXT_SIZE.LARGE}
              text={subtitle}
              weight={TEXT_WEIGHT.REGULAR}
              textDecoration={TEXTDECORATION.None}
            />
          </View>
        </>
      )}
    </View>
  );
};

export default StatusCard;

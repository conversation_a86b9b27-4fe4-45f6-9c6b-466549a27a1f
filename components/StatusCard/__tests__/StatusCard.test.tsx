// Example test structure for StatusCard component
// Note: This is a template - actual tests would need proper setup

import React from 'react';
import { render } from '@testing-library/react-native';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
// IMPORTANT: StatusCard must be exported as default from components/StatusCard/index.tsx
import StatusCard from '../index';

jest.mock('../../../analytics/AnalyticsUtils', () => ({
  screenViewLog: jest.fn(),
  userActionLogEvent: jest.fn(),
  setUserProperties: jest.fn(),
}));

// Mock dependencies
jest.mock('../useStatusDetermination', () => ({
  useStatusDetermination: () => "clockedIn",
}));

// Mock useNavigation from react-navigation
jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useNavigation: () => ({
    navigate: jest.fn(),
  }),
}));

const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      shift: (state = {
        shiftStartTime: "9:00 AM",
        shiftDuration: "9:00 AM - 2:30 PM",
        durationInMinutes: 150,
        todaysShift: { id: 1 },
        scheduleShift: { id: 1 },
      }) => state,
      empSchedule: (state = { data: { scheduleResponse: { schedule: [{ workDate: "2025-08-05" }] } } }) => state,
      profile: (state = { isUserInStore: true }) => state,
    },
    preloadedState: {
      shift: {
        shiftStartTime: "9:00 AM",
        shiftDuration: "9:00 AM - 2:30 PM",
        durationInMinutes: 150,
        todaysShift: { id: 1 },
        scheduleShift: { id: 1 },
      },
      empSchedule: { data: { scheduleResponse: { schedule: [{ workDate: "2025-08-05" }] } } },
      profile: { isUserInStore: true },
      ...initialState,
    },
  });
};

describe('StatusCard', () => {
  const renderWithProviders = (component: React.ReactElement, initialState = {}) => {
    const store = createMockStore(initialState);
    return render(<Provider store={store}>{component}</Provider>);
  };

  describe('Status Display', () => {
    it('should display clocked in status correctly', () => {
      const { getByTestId } = renderWithProviders(
        <StatusCard clockedIn={true} />
      );
      expect(getByTestId('status-card')).toBeTruthy();
      expect(getByTestId('status-title')).toBeTruthy();
      expect(getByTestId('status-subtitle')).toBeTruthy();
    });

    it('should show appropriate icon for lunch status', () => {
      // Simulate lunch status by mocking the hook
      jest.spyOn(require('../useStatusDetermination'), 'useStatusDetermination').mockReturnValue('onLunch');
      const { getByTestId } = renderWithProviders(
        <StatusCard clockedIn={false} />
      );
      expect(getByTestId('icon-lunchStatus')).toBeTruthy();
    });

    it('should show clock icon for shift statuses', () => {
      // Simulate clockedIn status by mocking the hook
      jest.spyOn(require('../useStatusDetermination'), 'useStatusDetermination').mockReturnValue('clockedIn');
      const { getByTestId } = renderWithProviders(
        <StatusCard clockedIn={true} />
      );
      expect(getByTestId('icon-clockStatus')).toBeTruthy();
    });
  });

  describe('Accessibility', () => {
    it('should have proper accessibility labels', () => {
      // Simulate clockedIn status by mocking the hook
      jest.spyOn(require('../useStatusDetermination'), 'useStatusDetermination').mockReturnValue('clockedIn');
      const { getByLabelText } = renderWithProviders(
        <StatusCard clockedIn={true} />
      );
      // Test accessibility label includes title
      expect(getByLabelText(/on Shift/i)).toBeTruthy();
    });
  });

  describe('Status Determination', () => {
    it('should use automatic status from the hook', () => {
      jest.spyOn(require('../useStatusDetermination'), 'useStatusDetermination').mockReturnValue('clockedIn');
      const { getByTestId } = renderWithProviders(
        <StatusCard clockedIn={true} />
      );
      expect(getByTestId('status-card')).toBeTruthy();
    });
  });
});


import { StyleSheet } from 'react-native';

import { IPAD_WIDTH } from '../../app/shared/constants';

import type { Theme } from 'pantry-design-system';

//customising styles based on themes
const getStyles = ({ colors, borderDimens, dimensions }: Theme) => {
  const styles = StyleSheet.create({
    card: {
      borderColor: colors.pdsThemeColorBorderNeutral,
      borderWidth: borderDimens.pdsGlobalBorderWidth100,
      flexDirection: 'row',
      borderRadius: borderDimens.pdsGlobalBorderRadius200, // design tokens are not available
      padding: dimensions.pdsGlobalSpace300,
      marginVertical: dimensions.pdsGlobalSpace200,
      backgroundColor: colors.pdsThemeColorBackgroundBaseline,
      alignSelf: 'center',
      maxWidth: IPAD_WIDTH,
      width: '100%',
    },
    checkScheduleButtonWrapper: {
      alignItems: 'flex-start',
      flex: 1,
      justifyContent: 'center',
      paddingLeft: dimensions.pdsGlobalSpace200,
    },
    checkScheduleTextContainer: {
      alignItems: 'flex-start',
      flex: 1.3,
      justifyContent: 'center',
      paddingRight: dimensions.pdsGlobalSpace300,
    },
    icon: {
      height: dimensions.pdsGlobalSizeWidth800,
      marginRight: dimensions.pdsGlobalSpace100,
      width: dimensions.pdsGlobalSizeWidth800,
    },
    iconContainer: {
      alignSelf: 'flex-start',
      marginTop: dimensions.pdsGlobalSpace100,
    },
    textContainer: {
      flex: 1,
      marginLeft: 10, // dont have PDS design token for 10
    },
  });

  return styles;
};

export default getStyles;

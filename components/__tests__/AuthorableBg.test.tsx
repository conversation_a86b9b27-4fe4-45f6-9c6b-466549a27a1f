import React from 'react';
import { render, screen, act } from '@testing-library/react-native';
import { View } from 'react-native';
import AuthorableBG from '../AuthorableBG';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';
import { processColor } from 'react-native';

const convertColorToInt = (color: string): number => {
  const result = processColor(color);
  if (typeof result !== 'number') throw new Error('Invalid color conversion');
  return result;
};

jest.mock('@d11/react-native-fast-image', () => {
  const React = require('react');
  const { View } = require('react-native');
  return (props: any) => <View {...props} />;
});

jest.mock('../../assets/images', () => ({
  LOGIN_BG: { testUri: 'default-login-bg' },
  PORTRAIT_TABLET_BG: { testUri: 'tablet-portrait-bg' },
  LANDSCAPE_TABLET_BG: { testUri: 'tablet-landscape-bg' },
}));

jest.useFakeTimers();

const mockStore = configureStore([]);
const renderWithProvider = (component: React.ReactElement, storeOverrides = {}) => {
  const defaultState = {
    deviceInfo: {
      isTablet: false,
      isLandscape: false,
    },
  };
  const store = mockStore({ ...defaultState, ...storeOverrides });
  return render(<Provider store={store}>{component}</Provider>);
};

export const useTheme = () => ({
  theme: {
    colors: { pdsThemeColorBackgroundBaseline: "#ffffff" },
    fonts: { pdsGlobalFontFamilyPoppins: "Poppins" },
    dimensions: { pdsGlobalSpace800: 24 },
    typography: { pdsGlobalFontSize500: 18 },
  },
});

jest.mock('@d11/react-native-fast-image', () => {
  const React = require('react');
  const { View, Image } = require('react-native');
  const MockFastImage = (props: any) => (
    <View {...props}>
      <Image {...props} />
      {props.children}
    </View>
  );
  MockFastImage.resizeMode = { cover: 'cover' };
  MockFastImage.priority = { normal: 'normal' };
  MockFastImage.cacheControl = { immutable: 'immutable' };
  return MockFastImage;
});

describe('AuthorableBG Component', () => {

  it('renders default login background for non-tablet devices', () => {
    renderWithProvider(
      <AuthorableBG>
        <View />
      </AuthorableBG>
    );

    const images = screen.getAllByTestId('background-image');
    const defaultBg = images.find(img => img.props.source?.testUri === 'default-login-bg');

    expect(defaultBg).toBeTruthy();
  });


  it('renders tablet portrait image when in portrait mode', () => {
    renderWithProvider(
      <AuthorableBG imageBackgroundTabletPortrait="https://portrait-bg.com">
        <View />
      </AuthorableBG>,
      { deviceInfo: { isTablet: true, isLandscape: false } }
    );

    const images = screen.getAllByTestId('background-image');
    const portraitBg = images.find(img => img.props.source?.uri === 'https://portrait-bg.com');

    expect(portraitBg).toBeTruthy();
  });


  it('renders tablet landscape image when in landscape mode', () => {
    renderWithProvider(
      <AuthorableBG imageBackgroundTabletLandscape="https://landscape-bg.com">
        <View />
      </AuthorableBG>,
      { deviceInfo: { isTablet: true, isLandscape: true } }
    );

    const images = screen.getAllByTestId('background-image');
    const landscapeBg = images.find(img => img.props.source?.uri === 'https://landscape-bg.com');

    expect(landscapeBg).toBeTruthy();
  });


  it('renders custom gradient colors initially if passed', () => {
    const customColors = ['#00448800', '#00448800', '#00448800', '#004488E5', '#004488E5'];

    renderWithProvider(
      <AuthorableBG colors={customColors}>
        <View />
      </AuthorableBG>
    );

    const linearGradient = screen.getByTestId('linear-gradient');
    const receivedColors = linearGradient.props.colors;

    // Ensure count matches
    expect(receivedColors.length).toBe(customColors.length);

    // Confirm colors match after conversion
    customColors.forEach(color => {
      const nativeInt = convertColorToInt(color);
      expect(receivedColors).toContain(nativeInt);
    });
  });


  it('animates gradient colors when shouldAnimateGradient is true', () => {
    renderWithProvider(
      <AuthorableBG shouldAnimateGradient><View /></AuthorableBG>
    );
    act(() => {
      jest.advanceTimersByTime(150);
    });
    const linearGradient = screen.getByTestId('linear-gradient');
    expect(linearGradient.props.colors.length).toBeGreaterThan(5); // animation added colors
  });

  it('renders children inside SafeAreaView and LinearGradient', () => {
    renderWithProvider(
      <AuthorableBG>
        <View testID="child-content" />
      </AuthorableBG>
    );
    expect(screen.getByTestId('child-content')).toBeTruthy();
    expect(screen.getByTestId('linear-gradient')).toBeTruthy();
  });
});

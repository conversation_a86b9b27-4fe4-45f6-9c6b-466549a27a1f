import React from 'react';
import MaterialIcons from "react-native-vector-icons/MaterialIcons";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";

interface propsType {
  size?: number;
  color?: string;
}

export const ArrowRight = (props: propsType) => {
  const { size = 24, color = "#1F1E1E" } = props;
  return (
    <MaterialIcons
      name="keyboard-arrow-right"
      size={size}
      color={color}
      {...{
        width: size,
        height: size,
      }}
    />
  );
};

export const ArrowLeft = (props: propsType) => {
  const { size = 24, color = "#1F1E1E" } = props;
  return (
    <MaterialCommunityIcons
      name="arrow-left"
      accessibilityRole="image"
      accessible={true}
      accessibilityLabel={"left arrow"}
      size={size}
      color={color}
      {...{
        width: size,
        height: size,
      }}
    />
  );
};

export const ArrowDown = (props: propsType) => {
  const { size = 24, color = "#1F1E1E" } = props;
  return (
    <MaterialIcons
      name="keyboard-arrow-down"
      size={size}
      color={color}
      {...{
        width: size,
        height: size,
      }}
    />
  );
};

export const ArrowUp = (props: propsType) => {
  const { size = 24, color = "#1F1E1E" } = props;
  return (
    <MaterialIcons
      name="keyboard-arrow-up"
      size={size}
      color={color}
      {...{
        width: size,
        height: size,
      }}
    />
  );
};

export const Refresh = (props: propsType) => {
  const { size = 24, color = "#1F1E1E" } = props;
  return (
    <MaterialCommunityIcons
     accessibilityRole="image"
     accessible={true}
     accessibilityLabel={"refresh"}
      name="refresh"
      size={size}
      color={color}
      {...{
        width: size,
        height: size,
      }}
    />
  );
};
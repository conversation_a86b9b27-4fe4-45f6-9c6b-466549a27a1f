import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { useTheme, BottomSheet, Button, Text, Heading } from 'pantry-design-system';
import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { AppState, View, TouchableOpacity, Linking, Pressable } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { WebView as RNWebView } from 'react-native-webview';

import { LOGIN_ANALYTICS, LOYALTY_ANALYTICS } from '../../analytics/AnalyticsConstants';
import { screenViewLog, userActionLogEvent } from '../../analytics/AnalyticsUtils';
import { useNetworkAlert } from '../../app/hooks/useNetworkAlert';
import { useAccessibilityFocus } from '../../app/providers/AccessibilityFocus';
import {
  URLS,
  TEXTLINK_COLORS,
  TEXT_SIZE,
  ImportantForAccessibility,
  BUTTON_SIZE,
  TEXT_ALIGN,
  TEXT_WEIGHT,
  HEADING_SIZE,
  SCREENS,
} from '../../app/shared/constants';
import { BackIcon } from '../../assets/icons/BackIcon';
import { ExternalLinkIcon } from '../../assets/icons/ExternalLinkIcon';
import { ForwardIcon } from '../../assets/icons/ForwardIcon';
import { ReloadIcon } from '../../assets/icons/ReloadIcon';
import { WarningIcon } from '../../assets/icons/WarningIcon';
import { setPreventHeaderAccessibility } from '../../app/store/reducers/accessibilitySlice';
import { useDispatch } from 'react-redux';

import getStyles from './styles';

import type { WebViewNavigation } from 'react-native-webview';

interface WebViewProps {
  route: {
    params?: {
      url?: string;
      headerTitle?: string;
      titleHeader?: string;
      previousTab?: string;
      subHeader?: string;
      refreshIcon?: any;
      subsection?: any;
      leftHeaderBtnPress?: () => void;
      onNavigationStateChange?: any;
      isOmniheaderPresent?: boolean;
      isHeaderRight?: boolean | undefined;
      showExternalLink?: boolean;
      showFooter?: boolean;
    };
  };
}

const WebView: React.FC<WebViewProps> = ({ route }) => {
  const { theme } = useTheme();
  const { t } = useTranslation();
  const navigation = useNavigation();
  const styles = getStyles(theme);
  const webViewRef = useRef<RNWebView>(null);
  const { showAlertWithRetry, checkInternetConnection } = useNetworkAlert();
  const [canGoBack, setCanGoBack] = useState(false);
  const [canGoForward, setCanGoForward] = useState(false);
  const finalWebURL = route?.params?.url ?? URLS.HRANDPAYROLL_URL ?? 'https://example.com';
  const [currentUrl, setCurrentUrl] = useState(finalWebURL);
  const title = route?.params && route.params?.titleHeader ? route.params?.titleHeader : '';
  const [finalTitle, setFinalTitle] = useState<string | undefined>(title);
  const subsection = route?.params?.subsection?.subsection1 ?? '';
  const finalSubHeader = route?.params && route.params?.subHeader ? route.params?.subHeader : '';
  const appState = useRef(AppState.currentState);
  const [externalView, setexternalView] = useState<boolean>(false);
  const showExternalButton = route?.params?.showExternalLink ?? true;
  const showRefreshButton = route?.params?.isHeaderRight ?? true;
  const showFooter = route?.params?.showFooter ?? true;
  const eventAction = route?.params?.subsection;
  const { setLastFocused, setRef, handleScreenFocus, restoreLastFocus } = useAccessibilityFocus();
  const dispatch = useDispatch();

  useEffect(() => {
    const unsubscribeFocus = navigation.addListener('focus', () => {
      handleScreenFocus(SCREENS.WEBVIEW);
    });

    return () => {
      unsubscribeFocus?.();
    };
  }, [navigation]);

  const fetchSubsection = (subsection: string) => {
    if (subsection) {
      screenViewLog({
        subsection1: subsection,
        subsection2: route?.params?.subsection?.subsection2,
        subsection3: route?.params?.subsection?.subsection3,
        event_label_link: route?.params?.subsection?.eventLabel,
        event_category_link: route?.params?.subsection?.eventCategory,
      });
    }
  };

  const refreshLogEvent = (): void => {
    const eventActionName = eventAction?.refreshAction
      ? eventAction?.refreshAction
      : LOGIN_ANALYTICS.REFRESH;
    const eventLabel = eventAction?.refreshLabel ?? '';
    const eventCategory = eventAction?.refreshCategory ?? '';
    if (eventLabel && eventCategory) {
      userActionLogEvent(eventCategory, eventActionName, eventLabel);
    }
  };

  const closeActionLogEvent = () => {
    const eventCategory = subsection;
    const eventAction = LOGIN_ANALYTICS.BACK;
    const eventLabel = route?.params?.subsection?.backButtonLabel
      ? route.params.subsection.backButtonLabel
      : '';
    const backButtonCategory = route?.params?.subsection?.backButtonCategory ?? '';

    if (backButtonCategory && eventLabel) {
      userActionLogEvent(backButtonCategory, LOYALTY_ANALYTICS.LOYALTY_BACK_ARROW, eventLabel);
    } else if (eventCategory && eventLabel) {
      userActionLogEvent(eventCategory, eventAction, eventLabel);
    }
  };

  useFocusEffect(
    React.useCallback(() => {
      fetchSubsection(subsection);
    }, []),
  );

  const handleError = async (): Promise<void> => {
    const isOnline = await checkInternetConnection();
    if (!isOnline) await showAlertWithRetry(handleError);
  };

  const reloadWebView = (): void => {
    refreshLogEvent();
    webViewRef.current?.reload();
  };

  const handleNavigationChange = (navState: WebViewNavigation) => {
    const urlChanged = navState?.url !== currentUrl;

    // Update state
    setCurrentUrl(navState?.url);
    setCanGoBack(navState?.canGoBack);
    setCanGoForward(navState?.canGoForward);

    // Trigger callback only if URL changed
    if (urlChanged && route?.params?.onNavigationStateChange) {
      route?.params?.onNavigationStateChange(navState);
    }
  };
  useEffect(() => {
    // Add an event listener to monitor changes in the app's state (foreground/background)
    const subscription = AppState.addEventListener('change', (nextAppState) => {
      // Check if the app is transitioning from inactive/background to active state
      if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
        fetchSubsection(subsection);
      }
      // Update the current app state
      appState.current = nextAppState;
    });

    // Cleanup function to remove the event listener when the component unmounts
    return () => {
      subscription.remove();
    };
  }, []);

  const handleHeaderClose = () => {
    if (route.params?.leftHeaderBtnPress) {
      route.params.leftHeaderBtnPress();
    } else {
      navigation.goBack();
    }
    closeActionLogEvent();
  };

  useEffect(() => {
    dispatch(setPreventHeaderAccessibility(externalView));
  }, [externalView, dispatch]);

  const handleExternalLinkPress = () => {
    setLastFocused(SCREENS.WEBVIEW, 'external-link-icon');
    restoreLastFocus(SCREENS.WEBVIEW);
    setexternalView(true);
  };

  const handleExternalLinkBackPress = () => {
    restoreLastFocus(SCREENS.WEBVIEW);
    setexternalView(false);
  };

  // Hide the bottom tab bar when this screen is displayed
  useEffect(() => {
    navigation.setOptions({
      headerTitle: finalTitle,
      isOmniheaderPresent: route?.params?.isOmniheaderPresent ?? false,
      goBack: () => handleHeaderClose(),
      tabBarStyle: { display: 'none' },
    });

    // Restore the tab bar when leaving the screen
    return () => {
      navigation.setOptions({
        tabBarStyle: undefined, // Reset to default
      });
    };
  }, [navigation, finalTitle]);

  const insets = useSafeAreaInsets();

  return (
    <View style={[styles.container, { paddingBottom: insets.bottom }]}>
      {finalSubHeader && (
        <View
          style={styles.subHeaderText}
          accessibilityElementsHidden={externalView}
          importantForAccessibility={
            externalView ? 'no-hide-descendants' : 'auto'
          }
        >
          <Text
            text={finalSubHeader}
            size={TEXT_SIZE.MEDIUM}
            testID="welcomeSheet.description2"
            textAlign={'center'}
            color={TEXTLINK_COLORS.NEUTRAL_HIGH}
          />
        </View>
      )}
      <RNWebView
        accessibilityHint="Shows web link, in web view"
        ref={webViewRef}
        style={styles.webView}
        testID="safeway-webview"
        originWhitelist={['*']}
        source={{ uri: finalWebURL }}
        startInLoadingState={true}
        onMessage={(message) => {
          if (title === undefined || title == null || title == '')
            setFinalTitle(message.nativeEvent.data);
        }}
        onError={handleError}
        onHttpError={handleError}
        onNavigationStateChange={handleNavigationChange}
        userAgent="Mozilla/5.0 (Linux; Android 10; Mobile; rv:89.0) Gecko/89.0 Firefox/89.0"
        javaScriptCanOpenWindowsAutomatically={true}
        injectedJavaScript={`
          (function() {
            window.ReactNativeWebView.postMessage(document.title);
            const hideCookieBanner = () => {
              const cookieElements = document.querySelectorAll('[id*="cookie"], [class*="cookie"]');
              cookieElements.forEach(el => {
                el.style.display = 'none';
                el.setAttribute('aria-hidden', 'true');
              });
            };

            hideCookieBanner();
            setInterval(hideCookieBanner, 1000); // Retry in case banner appears later
          })();
          true;
        `}
        javaScriptEnabled={true}
        accessibilityElementsHidden={externalView}
        importantForAccessibility={
          externalView ? 'no-hide-descendants' : 'auto'
        }
      />
      {showFooter && (
        <View
          style={styles.bottomBar}
          accessibilityElementsHidden={externalView}
          importantForAccessibility={
            externalView ? 'no-hide-descendants' : 'auto'
          }
        >
          <TouchableOpacity
            accessibilityRole="button"
            onPress={() => webViewRef?.current?.goBack()}
            disabled={!canGoBack}
            style={{ opacity: !canGoBack ? 0.4 : 1 }}
          >
            <BackIcon
              color={theme.colors.pdsThemeColorOutlinePrimary}
              size={theme.dimensions.pdsGlobalSizeHeight400}
            />
          </TouchableOpacity>
          <TouchableOpacity
            accessibilityRole="button"
            onPress={() => webViewRef?.current?.goForward()}
            disabled={!canGoForward}
            style={[styles.closeGap, { opacity: !canGoForward ? 0.4 : 1 }]}
          >
            <ForwardIcon
              color={theme.colors.pdsThemeColorOutlinePrimary}
              size={theme.dimensions.pdsGlobalSizeHeight400}
            />
          </TouchableOpacity>
          <TouchableOpacity
            accessibilityRole="button"
            ref={setRef('external-link-icon')}
            onPress={handleExternalLinkPress}
          >
            {showExternalButton && (
              <ExternalLinkIcon
                color={theme.colors.pdsThemeColorOutlinePrimary}
                size={theme.dimensions.pdsGlobalSizeHeight400}
              />
            )}
          </TouchableOpacity>
          <TouchableOpacity accessibilityRole="button" onPress={() => reloadWebView()}>
            {showRefreshButton && (
              <ReloadIcon
                color={theme.colors.pdsThemeColorOutlinePrimary}
                size={theme.dimensions.pdsGlobalSizeHeight400}
              />
            )}
          </TouchableOpacity>
        </View>
      )}
      <BottomSheet
        sidePadding
        variant={'Modal'}
        theme={theme}
        testID="welcome-bottom-sheet"
        visibility={externalView}
        closeAccessibilityLabel={t('ada.closeButton')}
        onClose={handleExternalLinkBackPress}
        accessibilityViewIsModal={true}
        importantForAccessibility={ImportantForAccessibility.YES}
        renderHeader={<WarningIcon size={theme.dimensions.pdsGlobalSizeHeight800} />}
        renderContent={
          <View style={styles.descriptionContainer}>
            <Heading
              textAlign={TEXT_ALIGN.CENTER}
              title={t('externalModal.heading')}
              accessibilityLabel={t('externalModal.heading')}
              color={TEXTLINK_COLORS.NEUTRAL_HIGH}
              size={HEADING_SIZE.Medium}
              weight={TEXT_WEIGHT.SEMI_BOLD}
            />
            <Text
              text={t('externalModal.subtext')}
              size={TEXT_SIZE.LARGE}
              textAlign={TEXT_ALIGN.CENTER}
              weight={TEXT_WEIGHT.SEMI_BOLD}
              color={TEXTLINK_COLORS.NEUTRAL_HIGH}
            />
          </View>
        }
        renderAction={
          <View style={[styles.buttonView, { paddingBottom: insets.bottom }]}>
            <Button
              fullWidth
              theme={theme}
              size={BUTTON_SIZE.SMALL}
              label={t('externalModal.continueInExternal')}
              accessibilityHint={''}
              accessible
              accessibilityRole="none"
              accessibilityLabel={t('externalModal.continueInExternalADALabel')}
              onPress={() => {
                if (currentUrl) {
                  Linking.openURL(currentUrl);
                }
                handleExternalLinkBackPress();
              }}
            />

            <Pressable
              onPress={handleExternalLinkBackPress}
              style={styles.buttonContainer}
              accessible={true}
              accessibilityHint={''}
              accessibilityLabel={t('ada.backButton')}
            >
              <Text
                text={t('externalModal.back')}
                size={TEXT_SIZE.LARGE}
                accessible={false}
                color={theme.colors.pdsThemeColorBackgroundPrimary}
                weight={TEXT_WEIGHT.SEMI_BOLD}
                textAlign={TEXT_ALIGN.CENTER}
              />
            </Pressable>
          </View>
        }
      />
    </View>
  );
};

export default WebView;

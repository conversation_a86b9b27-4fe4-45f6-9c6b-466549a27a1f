import { StyleSheet, Dimensions } from 'react-native';

import type { Theme } from 'pantry-design-system';

export default ({ colors, fonts, typography, borderDimens, dimensions }: Theme) => {
    const { width } = Dimensions.get('window');

    return StyleSheet.create({
        bottomBar: {
            backgroundColor: colors.pdsThemeColorBackgroundBaseline,
            borderColor: colors.pdsThemeColorOutlinePrimary,
            flexDirection: 'row',
            justifyContent: 'space-between',
            paddingHorizontal: dimensions.pdsGlobalSpace800,
            paddingVertical: dimensions.pdsGlobalSpace300,
        },
        buttonContainer: {
            alignItems: 'center',
            backgroundColor: colors.pdsThemeColorBackgroundBaseline,
            borderColor: colors.pdsThemeColorBackgroundNeutralLow,
            borderRadius: borderDimens.pdsGlobalBorderRadius1000,
            borderWidth: borderDimens.pdsGlobalBorderWidth100,
            flexDirection: 'row',
            height: dimensions.pdsGlobalSpace1000,
            justifyContent: 'center',
            width: '100%',
        },
        buttonView: {
            gap: dimensions.pdsGlobalSpace500,
            marginBottom: dimensions.pdsGlobalSpace800,
            marginTop: dimensions.pdsGlobalSpace700,
            paddingHorizontal: dimensions.pdsGlobalSpace400,
        },
        closeGap: {
            marginLeft: -dimensions.pdsGlobalSpace1400,
        },
        container: {
            backgroundColor: colors.pdsThemeColorBackgroundSunken,
            flex: 1,
        },
        descriptionContainer: {
            alignItems: 'center',
            gap: dimensions.pdsGlobalSpace600,
            justifyContent: 'center',
            marginHorizontal: dimensions.pdsGlobalSpace200,
            marginTop: dimensions.pdsGlobalSpace200,
        },
        subHeaderText: {
            backgroundColor: colors.pdsThemeColorBackgroundBaseline,
            color: colors.pdsThemeColorForegroundNeutralHigh,
            fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
            fontSize: typography.pdsGlobalFontSize200,
            fontWeight: typography.pdsGlobalFontWeight400,
            paddingHorizontal: dimensions.pdsGlobalSpace500,
            paddingVertical: dimensions.pdsGlobalSpace200,
            textAlign: 'center',
        },
        webView: { width: width },
    });
};

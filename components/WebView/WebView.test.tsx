import React from "react";
import { render, fireEvent, act } from "@testing-library/react-native";
import WebView from "./index";
import { useNavigation } from "@react-navigation/native";
import { URLS } from "../../app/shared/constants";
import * as useNetworkAlertHook from "../../app/hooks/useNetworkAlert";
import { useDispatch } from 'react-redux';

jest.mock('../../app/providers/AccessibilityFocus', () => ({
  useAccessibilityFocus: () => ({
    setRef: jest.fn(),
    setLastFocused: jest.fn(),
    handleScreenFocus: jest.fn(),
    restoreLastFocus: jest.fn(),
  }),
  AccessibilityFocusProvider: ({ children }: { children: React.ReactNode }) => children,
}));

jest.mock("@react-native-firebase/app", () => ({
  getApp: jest.fn(() => ({
    name: "[DEFAULT]",
  })),
}));
jest.mock('../../app/hooks/useAccessibilityFocus', () => () => ({
  setLastFocused: jest.fn(),
  setRef: () => null,
  restoreFocus: jest.fn(),
}));
jest.mock("@react-navigation/native", () => {
  return {
    useNavigation: jest.fn(),
    CommonActions: {
      navigate: jest.fn()
    },
    useFocusEffect: jest.fn((cb) => cb()),
  };
});
const mockWebView = jest.fn();

jest.mock("react-native-webview", () => ({
  WebView: (props: any) => {
    mockWebView(props); // capture props
    return null; // don't render anything
  },
}));
jest.mock("react-native/Libraries/Linking/Linking", () => ({
  openURL: jest.fn(),
  canOpenURL: jest.fn(() => Promise.resolve(true)),
}));

jest.mock("../../analytics/AnalyticsUtils", () => ({
  screenViewLog: jest.fn(),
  userActionLogEvent: jest.fn(),
}));

jest.mock('pantry-design-system', () => ({
  useTheme: jest.fn(() => ({
    theme: {
      colors: { pdsThemeColorBackgroundBaseline: "#ffffff" },
      fonts: { pdsGlobalFontFamilyNunitoSans: "Nunito Sans" },
      dimensions: { pdsGlobalSpace800: 24 },
      typography: { pdsGlobalFontSize500: 18 },
      borderDimens: { pdsGlobalBorderRadius1000: 8 },
    },
  })),
  Button: jest.fn(({ onPress, label, testID }) => (
    <button onClick={onPress} testID={testID}>
      {label}
    </button>
  )),
  Heading: jest.fn(({ title, testID }) => <text testID={testID}>{title}</text>),
  Text: jest.fn(({ text, testID }) => <text testID={testID}>{text}</text>),
  BottomSheet: jest.fn(),

}));
jest.mock("../../assets/icons/BackIcon", () => ({
  BackIcon: ({ testID = "BackIcon" }) => <text testID={testID}>Back</text>,
}));
jest.mock("../../assets/icons/ForwardIcon", () => ({
  ForwardIcon: ({ testID = "ForwardIcon" }) => <text testID={testID}>Forward</text>,
}));
jest.mock("../../assets/icons/ExternalLinkIcon", () => ({
  ExternalLinkIcon: ({ testID = "ExternalLinkIcon" }) => <text testID={testID}>External</text>,
}));
jest.mock("../../assets/icons/ReloadIcon", () => ({
  ReloadIcon: ({ testID = "ReloadIcon" }) => <text testID={testID}>Reload</text>,
}));
jest.mock("../../assets/icons/WarningIcon", () => ({
  WarningIcon: () => <></>,
}));

jest.mock('react-redux', () => ({
  useDispatch: jest.fn(),
}));
describe("WebView", () => {
  const mockNavigate = jest.fn();
  const mockGoBack = jest.fn();
  const mockSetOptions = jest.fn();
  const mockDispatch = jest.fn();


  beforeEach(() => {
    (useNavigation as jest.Mock).mockReturnValue({
      navigate: mockNavigate,
      goBack: mockGoBack,
      setOptions: mockSetOptions,
      addListener: jest.fn((event, callback) => {
        if (event === 'focus') {
          setTimeout(callback, 0);
        }
        return jest.fn();
      }),
    });
    (useDispatch as jest.Mock).mockReturnValue(mockDispatch);
    jest.spyOn(useNetworkAlertHook, "useNetworkAlert").mockReturnValue({
      showAlertWithRetry: jest.fn(),
      checkInternetConnection: jest.fn(() => Promise.resolve(true)),
    });
  });

  it("renders default WebView URL if route param is undefined", () => {
    const { getByTestId } = render(<WebView route={{ params: {} }} />);
    expect(getByTestId("BackIcon")).toBeTruthy();
  });

  it("renders WebView with provided URL", () => {
    const testUrl = URLS.GET_PAYSTUBS;

    render(
      <WebView
        route={{
          params: {
            url: testUrl,
          },
        }}
      />
    );

    expect(mockWebView).toHaveBeenCalledWith(
      expect.objectContaining({
        source: { uri: testUrl },
      })
    );
  });

  it("renders subHeader if provided", () => {
    const { getByTestId } = render(
      <WebView
        route={{
          params: {
            url: "https://example.com",
            subHeader: "SubHeader Text",
          },
        }}
      />
    );
    expect(getByTestId("welcomeSheet.description2").props.children).toBe("SubHeader Text");
  });
  it("opens BottomSheet on external link press", () => {
    const { getByTestId, queryByText } = render(
      <WebView
        route={{
          params: {
            url: "https://example.com",
            showExternalLink: true,
          },
        }}
      />
    );

    expect(queryByText("You’re about to leave the app.")).toBeNull();
    fireEvent.press(getByTestId("ExternalLinkIcon"));
  });

  it("refreshes WebView on reload icon press", () => {
    const reloadMock = jest.fn();
    const webViewRef = { current: { reload: reloadMock } };

    jest.spyOn(React, "useRef").mockReturnValueOnce(webViewRef as any);

    const { getByTestId } = render(
      <WebView
        route={{
          params: {
            url: "https://example.com",
            showExternalLink: true,
          },
        }}
      />
    );

    fireEvent.press(getByTestId("ReloadIcon"));
  });
  it("navigates back using leftHeaderBtnPress", () => {
    const leftPress = jest.fn();
    render(
      <WebView
        route={{
          params: {
            url: "https://example.com",
            leftHeaderBtnPress: leftPress,
          },
        }}
      />
    );

    const options = mockSetOptions.mock.calls[0][0];
    act(() => {
      options.goBack();
    });
  });

  it("calls onNavigationStateChange prop when WebView state changes", () => {
    const navCallback = jest.fn();
    const testNavState = {
      url: "https://updated.com",
      canGoBack: true,
      canGoForward: false,
    };

    render(
      <WebView
        route={{
          params: {
            url: "https://example.com",
            onNavigationStateChange: navCallback,
          },
        }}
      />
    );

    const onNavChange = mockWebView.mock.calls[0][0].onNavigationStateChange;
    act(() => {
      onNavChange(testNavState);
    });
  });
});

import { IPAD_WIDTH } from "../../app/shared/constants";
import { Theme } from "pantry-design-system";
import { StyleSheet, Dimensions } from "react-native";
/**
 * Generates a stylesheet for the loyalty screen.
 * @param {Object} theme - The theme object containing dimensions and spacing.
 * @returns {Object} The styles object.
 */
const getStyles = ({ colors, dimensions, typography, fonts }: Theme) => {
    const { height } = Dimensions.get("window");

    const styles = StyleSheet.create({
        /**
         * Main container for the screen.
         * Centers content and limits width for larger screens.
         */
        container: {
            flex: 1,
            backgroundColor: colors.pdsThemeColorBackgroundBaseline,
            maxWidth: IPAD_WIDTH,
            alignSelf: "center",
            width: '100%'
        },
        mainContainer: {
            alignItems: "center",
            paddingHorizontal: dimensions.pdsGlobalSpace300,
            paddingVertical: dimensions.pdsGlobalSpace500,
        },
        /** For u image */
        imageContainer: {
            alignSelf: "center",
            marginBottom: dimensions.pdsGlobalSpace600,
            color: colors.pdsThemeColorOutlinePrimary,
        },
        descriptionContainer: {
            marginLeft: dimensions.pdsGlobalSpace50,
            marginRight: dimensions.pdsGlobalSpace50,
            justifyContent: 'center',
            alignItems: 'center',
        },
        headingTop: {
            marginTop: dimensions.pdsGlobalSpace500
        },
        spaceTop: {
            marginTop: dimensions.pdsGlobalSpace800
        },
        createAccount: {
            flexDirection: "row",
            alignItems: "center",
            flexWrap: "wrap"
        },
        /** Bottom stick view which coontains bottong and checkbox */
        footerView: {
            maxWidth: IPAD_WIDTH,
            flex: 1,
            alignItems: 'center'
        },
        infoContainer: {
            marginTop: dimensions.pdsGlobalSpace600,
            width: '100%',
            alignSelf: 'center'
        },
        /**
         *Buttons Container.
         */
        buttonContainer: {
            padding: dimensions.pdsGlobalSpace300,
            paddingBottom: 0,
            position: 'absolute',
            bottom: 0,
            width: '95%',
        },
        buttonSubView: {
            flex: 1,
            marginBottom: dimensions.pdsGlobalSpace500,
        },
        bottomDown: {
            flex: 1,
            marginBottom: dimensions.pdsGlobalSpace200,
        },
    });
    return styles;
};

export default getStyles;
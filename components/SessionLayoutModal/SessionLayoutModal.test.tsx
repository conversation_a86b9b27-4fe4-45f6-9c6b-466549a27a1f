// components/__tests__/SessionModalLayout.test.tsx
import React from "react";
import { render, fireEvent } from "@testing-library/react-native";
import SessionLayoutModal from "./index";

const mockSvg = `<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100"><rect width="100" height="100" fill="blue" /></svg>`;

jest.mock('@react-native-firebase/app', () => ({
    getApp: jest.fn(() => ({
        name: '[DEFAULT]',
    })),
}));

jest.mock('pantry-design-system', () => {
    const actual = jest.requireActual('pantry-design-system');
    return {
        ...actual,
        useTheme: jest.fn(() => ({ theme: 'mockTheme' })),
    };
});

jest.mock("../AppHeader", () => "AppHeader");

jest.mock("pantry-design-system", () => ({
    useTheme: jest.fn(() => ({
        theme: {
            colors: { pdsThemeColorBackgroundBaseline: "#ffffff", pdsThemeColorOutlinePrimary: '#000' },
            fonts: { pdsGlobalFontFamilyPoppins: "Poppins" },
            dimensions: { pdsGlobalSpace300: 12, pdsGlobalSpace500: 20, pdsGlobalSpace800: 32, pdsGlobalSpace600: 24, },
            typography: { pdsGlobalFontSize500: 18 },
        },
    })),
    Heading: jest.fn(({ title, testID }) => <text testID={testID}>{title}</text>),
    Text: jest.fn(({ text, testID }) => <text testID={testID}>{text}</text>),
    Button: jest.fn(({ onPress, label, testID }) => (
        <button onClick={onPress} testID={testID}>
            {label}
        </button>
    )),
}));

jest.mock('react-i18next', () => ({
    useTranslation: () => ({
        t: (key: string) => key,
    }),
}));

jest.mock('@react-navigation/native', () => ({
    useNavigation: jest.fn(() => ({
        navigate: jest.fn(),
    })),
    useFocusEffect: jest.fn((cb) => {
        cb();
    }),
}));

jest.mock('react-redux', () => ({
    useSelector: jest.fn(),
}));

jest.mock("react-native-svg", () => ({
    SvgXml: jest.fn(({ xml, testID }) => <svg testID={testID}>{xml}</svg>),
}));

describe("SessionLayoutModal", () => {
    const defaultProps = {
        svgXml: mockSvg,
        headerTitle: "Session Expired",
        heading: "You've been signed out",
        description: "For your security, we’ve logged you out. Please sign in again.",
        buttonText: "Sign In Again",
        onButtonPress: jest.fn(),
        onClosePress: jest.fn(),
        testIDPrefix: "session-test"
    };

    it("renders all UI elements correctly", () => {
        const { getByTestId } = render(<SessionLayoutModal {...defaultProps} />);
        expect(getByTestId("session-test-image")).toBeTruthy();
        expect(getByTestId("session-test-heading").children[0]).toBe(defaultProps.heading);
        expect(getByTestId("session-test-description").children[0]).toBe(defaultProps.description);
        expect(getByTestId("session-test-button").children[0]).toBe(defaultProps.buttonText);
    });

    it("triggers onButtonPress when CTA button is pressed", () => {
        const { getByTestId } = render(<SessionLayoutModal {...defaultProps} />);
        fireEvent.press(getByTestId("session-test-button"));
        expect(defaultProps.onButtonPress).toHaveBeenCalled();
    });

    // it("triggers onClosePress when close icon is pressed", () => {
    //     const { getByTestId } = render(<SessionLayoutModal {...defaultProps} />);
    //     fireEvent.press(getByTestId("home-header-right-cross-button"));
    //     expect(defaultProps.onClosePress).toHaveBeenCalled();
    // });
});
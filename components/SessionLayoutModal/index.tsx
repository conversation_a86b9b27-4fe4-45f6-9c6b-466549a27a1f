// components/SessionModalLayout.tsx
/**
 * Reusable session modal layout component for displaying error/session states
 * @component
 *
 * @param {string} svgXml - SVG image XML string to display
 * @param {string} headerTitle - Modal header title text
 * @param {string} heading - Main heading text
 * @param {string} description - Descriptive text content
 * @param {string} buttonText - Action button text
 * @param {Function} onButtonPress - Button press handler
 * @param {Function} onClosePress - Close icon press handler
 * @param {string} [testIDPrefix="session-modal"] - Test ID prefix for testing
 *
 * @features
 * - Consistent styling using theme
 * - Full accessibility support
 * - Cross-platform layout adjustments
 * - Customizable test IDs
 * - Header with close button
 * - Responsive SVG image display
 *
 * @example
 * <SessionModalLayout
 *   svgXml={errorSvg}
 *   headerTitle="Session Expired"
 *   heading="Oops!"
 *   description="Your session has timed out"
 *   buttonText="Sign In Again"
 *   onButtonPress={handleSignIn}
 *   onClosePress={closeModal}
 * />
 */
import { useTheme, Text, Button, Heading } from 'pantry-design-system';
import React from 'react';
import { View, Platform } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { SvgXml } from 'react-native-svg';

import HomeHeaderRightCross from '../../app/features/home/<USER>';
import { TEXTLINK_SIZE, TEXTLINK_COLORS } from '../../app/shared/constants';
import AppHeader from '../AppHeader';

import getStyles from './styles';

interface SessionModalLayoutProps {
    svgXml: string;
    headerTitle: string;
    heading: string;
    description: string;
    buttonText: string;
    onButtonPress: () => void;
    onClosePress: () => void;
    testIDPrefix?: string;
}

const SessionModalLayout: React.FC<SessionModalLayoutProps> = ({
    svgXml,
    headerTitle,
    heading,
    description,
    buttonText,
    onButtonPress,
    onClosePress,
    testIDPrefix = 'session-modal',
}) => {
    const { theme } = useTheme();
    const styles = getStyles(theme);

    return (
        <SafeAreaView edges={['top', 'bottom']} style={styles.container}>
            <AppHeader
                isOmniheaderPresent={false}
                headerTitle={headerTitle}
                headerRight={<HomeHeaderRightCross onBackPress={onClosePress} />}
            />

            <View style={styles.mainContainer}>
                <SvgXml
                    xml={svgXml}
                    accessible
                    accessibilityRole="image"
                    accessibilityLabel="Error Image"
                    style={styles.imageContainer}
                    testID={`${testIDPrefix}-image`}
                />

                <Heading
                    accessible
                    testID={`${testIDPrefix}-heading`}
                    textAlign="center"
                    title={heading}
                    color={TEXTLINK_COLORS.NEUTRAL_HIGH}
                    size="medium"
                />

                <View style={styles.headingTop} />

                <View style={styles.descriptionContainer}>
                    <Text
                        color={TEXTLINK_COLORS.NEUTRAL_HIGH}
                        size={TEXTLINK_SIZE.LARGE}
                        text={description}
                        weight="Regular"
                        textDecoration="None"
                        lineHeight={TEXTLINK_SIZE.LARGE}
                        textAlign="center"
                        testID={`${testIDPrefix}-description`}
                    />
                </View>
            </View>

            <View style={styles.footerView}>
                <View style={styles.buttonContainer}>
                    <View style={[styles.buttonSubView, Platform.OS === 'ios' && styles.bottomDown]}>
                        <Button
                            theme={theme}
                            fullWidth
                            size={TEXTLINK_SIZE.LARGE}
                            label={buttonText}
                            customContentDescription={buttonText}
                            onPress={onButtonPress}
                            accessible
                            accessibilityLabel={buttonText}
                            accessibilityRole="button"
                            testID={`${testIDPrefix}-button`}
                        />
                    </View>
                </View>
            </View>
        </SafeAreaView>
    );
};

export default SessionModalLayout;

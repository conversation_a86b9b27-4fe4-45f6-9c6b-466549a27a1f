import { IPAD_WIDTH } from '../../app/shared/constants';
import { Theme } from 'pantry-design-system';
import { StyleSheet, TextStyle } from "react-native";

//customising styles based on themes
const getStyles = ({ colors, fonts, typography, borderDimens, dimensions }: Theme, isTablet: boolean) => {
    const styles = StyleSheet.create({
        container: {
            padding: 10,
            backgroundColor: colors.pdsThemeColorBackgroundBaseline,
            borderBottomWidth: 1,
            borderColor: colors.pdsThemeColorOutlineNeutralLow,
        },
        icon: {
            marginLeft: 5
        },
        header: {
            flexDirection: "row",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: 10,
            alignSelf: 'center'
        },
        weekTitle: {
            textAlign: 'center',
            alignSelf: 'center',
            fontSize: typography.pdsGlobalFontSize200,
            color: colors.pdsThemeColorOutlineNeutralHigh,
            fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
            fontWeight: typography.pdsGlobalFontWeight700,
        },
        titleContainer: { width: '70%' },
        arrow: {
            fontSize: 20,
            fontWeight: typography.pdsGlobalFontWeight700,
            color: colors.pdsThemeColorBackgroundPrimary,
        },
        weekContainer: {
            flexDirection: "row",
            justifyContent: "space-between",
            backgroundColor: colors.pdsThemeColorBackgroundBaseline,
            paddingVertical: 10,
            flexWrap: "wrap",
        },
        dayContainer: {
            alignItems: "center",
            justifyContent: "center",
            width: 40, // Fixed width
            height: 65, // Fixed height to avoid stretching
            borderRadius: 10,
            borderWidth: 2, // Consistent border width (even when not selected)
            borderColor: "transparent", // Prevents size change on selection
        },
        arrowButton: {
            width: 30,
            height: 30,
            backgroundColor: "transparent",
            alignItems: "center",
            justifyContent: "center",
        },
        today: {
            backgroundColor: colors.pdsThemeColorBackgroundBaselineInverse,
            borderRadius: 5,
        },
        selectedDay: {
            borderWidth: 1,
            borderColor: "purple",
            borderRadius: 10,
        },
        dayText: {
            fontSize: 14,
            color: colors.pdsThemeColorOutlineNeutralHigh,
            fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
            fontWeight: typography.pdsGlobalFontWeight400,
            margin: 2,
        },
        dateText: {
            fontSize: 18,
            fontWeight: typography.pdsGlobalFontWeight700,
            color: colors.pdsThemeColorOutlineNeutralHigh,
            fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
            margin: 2,
        },
        selectedText: {
            color: colors.pdsThemeColorOutlineNeutralHighInverse
        },
        dot: {
            width: 6,
            height: 6,
            backgroundColor: colors.pdsThemeColorOutlineNeutralHigh,
            borderRadius: 3,
            position: 'absolute',
            bottom: 0,
        },
        statusContainer: {
            justifyContent: "center",
            alignItems: "center",
            textAlign: "center",
            padding: 20,
        },
        image: {
            marginBottom: 10,
        },
        text: {
            fontSize: typography.pdsGlobalFontSize300,
            fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
            fontWeight: typography.pdsGlobalFontWeight400,
            lineHeight: 23.2,
            textAlign: "center",
            marginLeft: 25,
            marginRight: 25,
            marginBottom: 10,
            width: "68%",
        },
        retryButton: {
            justifyContent: "center",
            flexDirection: 'row',
            alignSelf: 'center',
            height: 40,
            backgroundColor: colors.pdsThemeColorBackgroundPrimary,
            borderRadius: borderDimens.pdsGlobalBorderRadius1000,
            padding: 10,
            width: 150,
        },
        retryImage: {
            marginRight: 8,
        },
        retryText: {
            color: colors.pdsThemeColorBackgroundBaseline,
            fontSize: typography.pdsGlobalFontSize300,
            fontWeight: typography.pdsGlobalFontWeight500,
            fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
        },
        summaryContainer: {
            flexDirection: 'row',
            justifyContent: 'space-around',
            borderTopWidth: borderDimens.pdsGlobalBorderWidth0,
            borderColor: colors.pdsThemeColorOutlineNeutralLow,
            shadowColor: colors.pdsThemeColorOutlineNeutralHigh,
            shadowOffset: { width: borderDimens.pdsGlobalBorderWidth0, height: borderDimens.pdsGlobalBorderWidth300 },
            shadowOpacity: 0.1,
            width: "100%",
            marginTop: dimensions.pdsGlobalSpace500,
        },

        showSummaryStyle: {
            paddingVertical: dimensions.pdsGlobalSpace400,
            marginTop: dimensions.pdsGlobalSpace500,
            borderTopWidth: borderDimens.pdsGlobalBorderWidth100,
        },
        divider: {
            width: 1,
            backgroundColor: colors.pdsThemeColorOutlineNeutralLow,
            marginHorizontal: 16,
            marginVertical: 5,
        },
        hoursSummaryContainer: {
            alignItems: 'center',
            marginTop: -20,
        },
        hoursButton: {
            flexDirection: 'row',
            alignItems: 'center',
            paddingHorizontal: 14,
            borderRadius: borderDimens.pdsGlobalBorderRadius1000,
            borderWidth: borderDimens.pdsGlobalBorderWidth100,
            borderColor: colors.pdsThemeColorOutlineNeutralLow,
            backgroundColor: colors.pdsThemeColorBackgroundBaseline,
            height: 32,
        },
        hoursButtonText: {
            color: colors.pdsThemeColorBackgroundPrimary,
            fontSize: typography.pdsGlobalFontSize200,
            fontWeight: typography.pdsGlobalFontWeight600,
            fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
            marginRight: 8,
        },
        arrowUp: {
            transform: [{ scaleY: -1 }],
            color: colors.pdsThemeColorBackgroundPrimary,
        },
        summaryTextontainer: {
            alignItems: 'center',
        },
        timeRow: {
            flexDirection: 'row',
            alignItems: 'flex-end',
        },
        timeNumber: {
            fontSize: typography.pdsGlobalFontSize300,
            fontWeight: typography.pdsGlobalFontWeight700,
            marginRight: 4,
            color: colors.pdsThemeColorOutlineNeutralHigh,
            fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
        },
        timeUnit: {
            fontSize: typography.pdsGlobalFontSize100,
            fontWeight: typography.pdsGlobalFontWeight400,
            marginRight: 8,
            marginBottom: 4,
            color: colors.pdsThemeColorOutlineNeutralMedium,
            fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
        },
        subText: {
            fontSize: typography.pdsGlobalFontSize100,
            color: colors.pdsThemeColorOutlineNeutralMedium,
            fontWeight: typography.pdsGlobalFontWeight600,
            marginTop: 4,
            fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
        },
        ptoSummaryPadding: { padding: dimensions.pdsGlobalSpace400 },
        ptoSummaryRow: { flexDirection: 'row', justifyContent: 'space-evenly', width: isTablet ? IPAD_WIDTH : "100%" },
    });
    return styles
}

export default getStyles;
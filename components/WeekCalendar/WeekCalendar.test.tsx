import React from "react";
import { render, fireEvent } from "@testing-library/react-native";
import WeekCalendar from "../WeekCalendar";
import dayjs from "dayjs";
import { Provider } from "react-redux";
import configureStore from "redux-mock-store";
import { ThemeProvider } from "pantry-design-system";

// Mock Redux Store
const mockStore = configureStore([]);
const initialState = {
  deviceInfo: { isTablet: false },
  empSchedule: {
    data: {
      scheduleResponse: {
        errors: [],
        workedHours: [],
        balances: [],
      },
    },
    loading: false,
  },
  language: {
    id: "en",
    name: "English"
  },
};

jest.mock("../StatusPlaceholder/StatusPlaceholder", () => "StatusPlaceholder");

jest.mock('../../analytics/AnalyticsUtils', () => ({
  screenViewLog: jest.fn(),
  userActionLogEvent: jest.fn(),
  setUserProperties: jest.fn(),
}));

// Mock react-i18next
jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => {
      // Return actual English text for common keys
      const translations = {
        'schedule.to': 'to',
        'schedule.from': 'from',
        'hours': 'Hours',
        'pto': 'PTO',
        // Add other translations as needed
      };
      return translations[key] || key;
    },
    i18n: {
      language: 'en'
    }
  }),
}));

jest.mock("pantry-design-system", () => ({
  useTheme: jest.fn(() => ({
    theme: {
      colors: {
        pdsThemeColorOutlineNeutralHigh: "#1F1E1E",
        pdsThemeColorBackgroundPrimary: "#8C0E12",
        pdsThemeColorOutlineNeutralHighInverse: "#FFFFFF",
        pdsThemeColorBackgroundBaseline: "#FFFFFF",
        pdsThemeColorBackgroundBaselineInverse: "#1F1E1E",
      },
      fonts: { pdsGlobalFontFamilyNunitoSans: "NunitoSans" },
      typography: { pdsGlobalFontSize200: 14, pdsGlobalFontWeight700: "700" },
      borderDimens: { pdsGlobalBorderRadius1000: 10 },
      dimensions: { pdsGlobalSpace500: 20 },
    },
  })),
  ThemeProvider: ({ children }: { children: React.ReactNode }) => (
    <>{children}</>
  ),
}));

const renderWithProviders = (
  component: React.ReactElement,
  storeState = initialState
) => {
  const store = mockStore(storeState);
  return render(
    <Provider store={store}>
      <ThemeProvider>{component}</ThemeProvider>
    </Provider>
  );
};

jest.mock("react-native-vector-icons/MaterialIcons", () => "MaterialIcons");

const mockWeekItems = [
  {
    dateStr: dayjs().startOf("week").add(1, "day").format("YYYY-MM-DD"),
    isToday: true,
    isSelected: false,
    hasShift: true,
    accessibilityLabel: "Sunday, shift available",
  },
  {
    dateStr: dayjs().startOf("week").add(3, "day").format("YYYY-MM-DD"),
    isToday: false,
    isSelected: false,
    hasShift: false,
    accessibilityLabel: "Wednesday, no scheduled shift",
  },
  {
    dateStr: dayjs().startOf("week").add(5, "day").format("YYYY-MM-DD"),
    isToday: false,
    isSelected: false,
    hasShift: true,
    accessibilityLabel: "Today, shift available",
  }
];

const mockNavigateWeek = jest.fn();
const mockWeekStart = dayjs().startOf("week").format("MMMM D");
const mockWeekEnd = dayjs().startOf("week").add(6, "day").format("MMMM D");

describe("WeekCalendar Component", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should render the current week correctly", () => {
    const { getByTestId } = renderWithProviders(
      <WeekCalendar
        weekItems={mockWeekItems}
        navigateWeek={mockNavigateWeek}
        weekStart={mockWeekStart}
        weekEnd={mockWeekEnd}
      />
    );

    expect(getByTestId("week-range")).toHaveTextContent(
      `${mockWeekStart} to ${mockWeekEnd}`
    );
  });

  it("should navigate to the previous week when left arrow is pressed", () => {
    const { getByTestId } = renderWithProviders(
      <WeekCalendar
        weekItems={mockWeekItems}
        navigateWeek={mockNavigateWeek}
        weekStart={mockWeekStart}
        weekEnd={mockWeekEnd}
      />
    );

    fireEvent.press(getByTestId("prev-week-button"));

    expect(mockNavigateWeek).toHaveBeenCalledWith("prev");
  });

  it("should navigate to the next week when right arrow is pressed", () => {
    const { getByTestId } = renderWithProviders(
      <WeekCalendar
        weekItems={mockWeekItems}
        navigateWeek={mockNavigateWeek}
        weekStart={mockWeekStart}
        weekEnd={mockWeekEnd}
      />
    );

    fireEvent.press(getByTestId("next-week-button"));

    expect(mockNavigateWeek).toHaveBeenCalledWith("next");
  });

  it("should highlight today's date", () => {
    const { getByTestId } = renderWithProviders(
      <WeekCalendar
        weekItems={mockWeekItems}
        navigateWeek={mockNavigateWeek}
        weekStart={mockWeekStart}
        weekEnd={mockWeekEnd}
      />
    );
    const today = dayjs().format("YYYY-MM-DD");
    // expect(getByTestId(`date-${today}`)).toBeTruthy();
    mockWeekItems.forEach(({ dateStr }) => {
      expect(getByTestId(`date-${dateStr}`)).toBeTruthy();
    });
  });

  it("should toggle PTO summary visibility when button is pressed", () => {
    const { getByTestId } = renderWithProviders(
      <WeekCalendar
        weekItems={mockWeekItems}
        navigateWeek={mockNavigateWeek}
        weekStart={mockWeekStart}
        weekEnd={mockWeekEnd}
      />
    );
    const viewButton = getByTestId("view-hours-button");
    fireEvent.press(viewButton);
    expect(getByTestId("hours-label")).toBeTruthy();
    expect(getByTestId("pto-balance-label")).toBeTruthy();
  });

  it("handles Spanish language correctly", () => {
    const spanishState = {
      ...initialState,
      language: {
        id: "es",
        name: "Spanish"
      },
    };

    const { getByTestId } = renderWithProviders(
      <WeekCalendar
        weekItems={mockWeekItems}
        navigateWeek={mockNavigateWeek}
        weekStart={mockWeekStart}
        weekEnd={mockWeekEnd}
      />,
      spanishState
    );

    expect(getByTestId("week-range")).toHaveTextContent(
      `${mockWeekStart} to ${mockWeekEnd}`
    );
  });
});

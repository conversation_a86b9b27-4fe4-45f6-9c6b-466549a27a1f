/**
* WeekCalendar Component
*
* Displays a week view calendar with navigation, daily shift indicators, and a PTO summary.
* - Shows the current week range (Sunday–Saturday) and allows navigation to previous/next weeks.
* - Highlights today and selected days.
* - Displays a dot for days with scheduled shifts.
* - Shows a summary of total worked hours from timeCodeSummary and total accrued vacation days.
* - Allows toggling the PTO summary with smooth animations.
*
* Props:
* - weekItems: Array of week day objects with date, selection, and shift info.
* - navigateWeek: Function to navigate to previous or next week.
* - weekStart: Formatted string for the start of the week.
* - weekEnd: Formatted string for the end of the week.
* - onRetry: Function to retry loading data on error.
*
* Redux:
* - Uses empSchedule from Redux to get worked hours and balances.
*
* Calculations:
* - totalWRKMinutes: Sum of WRK time codes from timeCodeSummary.
* - totalHours, totalMinutes: Conversion of totalWRKMinutes to hours and minutes.
* - totalVacationAccruedValue: Sum of accruedValue for VACATION balances.
*
* Accessibility:
* - Provides accessibility labels for navigation and summary.
*/
import React, { useState, useEffect, useRef } from "react";
import { View, Text, TouchableOpacity, findNodeHandle, AccessibilityInfo } from "react-native";
import dayjs from "dayjs";
import { useTheme } from 'pantry-design-system'
import getStyles from "./styles";
import { useSelector } from "react-redux";
import { noShiftScheduled, noSchedulesReleased } from "../../assets/images/svg/scheduleSvg";
import { NoNetwork } from "../../assets/images/svg/NoNetworkConnection";
import StatusPlaceholder from "../StatusPlaceholder/StatusPlaceholder";
import { useTranslation } from "react-i18next";
import Animated, { useSharedValue, useAnimatedStyle, withTiming, Easing, runOnJS } from "react-native-reanimated";
import { ANIMATION_TIME, ANIMATION_OUT_TIME, ANIMATION_OUT } from "../../app/shared/constants";
import { CircularChevronLeft } from "../../assets/icons/CircularChevronLeft";
import { CircularChevronRight } from "../../assets/icons/CircularChevronRight";
import { ChevronDown } from "../../assets/icons/ChevronDown";
import { ChevronUp } from "../../assets/icons/ChevronUp";
import { screenViewLog } from "../../analytics/AnalyticsUtils";
import { SCHEDULE_ANALYTICS } from "../../analytics/AnalyticsConstants";

interface WeekItem {
  dateStr: string;
  isToday: boolean;
  isSelected: boolean;
  hasShift: boolean;
  accessibilityLabel: string;
}

interface WeekCalendarProps {
  weekItems: WeekItem[];
  navigateWeek: (direction: "prev" | "next") => void;
  weekStart: string;
  weekEnd: string;
  onRetry: () => void;
}

const WeekCalendar: React.FC<WeekCalendarProps> = ({ weekItems, navigateWeek, weekStart, weekEnd, onRetry }) => {

  const { t } = useTranslation()
  const { theme } = useTheme(); // Fetching themes from ThemeProvider
  const isTablet = useSelector((state: any) => state.deviceInfo?.isTablet);
  const styles = getStyles(theme, isTablet); // Fetching styles based on theme
  const [scheduleNotReleased, setScheduleNotReleased] = useState(false);
  const [noNetworkConnection, setNoNetworkConnection] = useState(true);
  const [showPTOSummary, setShowPTOSummary] = useState(false);

  const padding = useSharedValue(0);
  const animatedHeight = useSharedValue(0);
  const opacity = useSharedValue(0);

  const weeklyHoursRef = useRef(null)


  const { data, error: scheduleError, loading: scheduleLoading } = useSelector((state: any) => state.empSchedule);

  const scheduleResponse = data?.scheduleResponse;

  const requestSuccessErrors = scheduleResponse?.errors;
  const weeklyHours = scheduleResponse?.workedHours;
  const balances = scheduleResponse?.balances;

  let workedHours = weeklyHours || [];

  useEffect(() => {
    if (requestSuccessErrors) {
      screenViewLog({ subsection1: SCHEDULE_ANALYTICS.SCHEDULE_NO_SHIFTS_CATEGORY });
    }
  }, [requestSuccessErrors])

  useEffect(() => {
    if (scheduleNotReleased) {
      screenViewLog({ subsection1: SCHEDULE_ANALYTICS.SCHEDULE_NOT_RELEASED });
    }
  }, [scheduleNotReleased])

  // Calculate timeCodeSummary totals from past worked hours
  const calculateTimeCodeTotals = (workedHoursData: any[]) => {
    const timeCodeTotals: { [key: string]: number } = {};

    workedHoursData?.forEach((item: any, index: number) => {
      if (item.timeCodeSummary && Array.isArray(item.timeCodeSummary)) {
        item.timeCodeSummary.forEach((timeCode: any) => {
          const name = timeCode.name?.trim();

          if (name && typeof timeCode.time === 'number') {
            const previousTotal = timeCodeTotals[name] || 0;
            timeCodeTotals[name] = previousTotal + timeCode.time;
          }
        });
      }
    });
    return timeCodeTotals;
  };

  // Get WRK time from timeCodeSummary (this is the actual worked time)
  const timeCodeTotals = calculateTimeCodeTotals(workedHours);
  const totalWRKMinutes = timeCodeTotals["WRK"] || 0;

  const totalHours = Math.floor(totalWRKMinutes / 60);
  const totalMinutes = totalWRKMinutes % 60;

  const vacationBbalances = balances || [];

  const totalVacationAccruedValue = Array.isArray(vacationBbalances)
    ? vacationBbalances
      ?.filter((item: any) => item.balanceName === "VACATION")
      ?.reduce((sum: number, item: any) => sum + (item.accruedValue || 0), 0)
    : 0;

  /**
* Toggles the visibility of the PTO summary section with smooth animations.
*
* - When expanding:
*    - Sets the summary to visible.
*    - Animates the height to 70.
*    - Fades in content by setting opacity to 1.
*    - Applies padding for spacing.
*
* - When collapsing:
*    - Fades out content by setting opacity to 0 over 150ms.
*    - Once faded out, collapses height and padding to 0.
*    - Hides the summary by setting `showPTOSummary` to false.
* @returns {void}
*/
  const showPTOtoggleView = () => {
    const expand = !showPTOSummary;

    if (expand) {
      const reactTag = findNodeHandle(weeklyHoursRef.current);
      if (reactTag) {
        setTimeout(() => {
          AccessibilityInfo.setAccessibilityFocus(reactTag);
        }, 100)
      }

      // Opening animation
      setShowPTOSummary(true);
      animatedHeight.value = withTiming(70, {
        duration: ANIMATION_TIME,
        easing: Easing.out(Easing.cubic),
      });
      opacity.value = withTiming(1, { duration: ANIMATION_TIME });
      padding.value = withTiming(styles.ptoSummaryPadding?.padding, {
        duration: ANIMATION_TIME
      });
    } else {
      // Closing animation - fade out content first, then collapse
      opacity.value = withTiming(0, { duration: ANIMATION_OUT }, (finished) => {
        if (finished) {
          animatedHeight.value = withTiming(0, {
            duration: ANIMATION_OUT_TIME,
            easing: Easing.in(Easing.cubic)
          });
          padding.value = withTiming(0, { duration: ANIMATION_OUT_TIME });
          runOnJS(setShowPTOSummary)(false);
        }
      });
    }
  };

  /**
* Animated style for the PTO summary container.
*
* Controls:
*  - height: Expands or collapses vertically.
*  - opacity: Fades content in and out.
*  - paddingVertical: Provides vertical spacing during animation.
*  - overflow: Keeps content hidden during transitions.
*/
  const animatedStyle = useAnimatedStyle(() => ({
    height: animatedHeight.value,
    opacity: opacity.value,
    overflow: "hidden",
    paddingVertical: padding.value,
  }));


  const handleDatePress = (date: string) => {
    // setSelectedDate(date);
  };

  return (
    <>
      <View style={styles.container}>
        {/* Week Title */}
        <View style={[styles.header, isTablet ? { width: '70%', } : { width: '98%' }]} testID="week-header">
          <TouchableOpacity
            onPress={() => navigateWeek("prev")}
            style={[styles.arrowButton]}
            accessible={true}
            accessibilityLabel={t("schedule.previousWeek")}
            testID="prev-week-button"
          >
            <CircularChevronLeft
              color={theme.colors.pdsThemeColorForegroundPrimary}
              size={theme.dimensions.pdsGlobalSizeHeight600}
            />
          </TouchableOpacity>
          <View style={styles.titleContainer}>
            <Text style={styles.weekTitle}
              accessible={true}
              accessibilityRole="text"
              accessibilityLabel={`${weekStart} ${t('schedule.to')} ${weekEnd}`}
              testID="week-range"
            >
              {`${weekStart} ${t('schedule.to')} ${weekEnd}`}
            </Text>
          </View>
          <TouchableOpacity onPress={() => navigateWeek("next")} style={[styles.arrowButton]}
            accessible={true}
            accessibilityLabel={t("schedule.nextWeek")}
            testID="next-week-button"
          >
            <CircularChevronRight
              color={theme.colors.pdsThemeColorForegroundPrimary}
              size={theme.dimensions.pdsGlobalSizeHeight600}
            />
          </TouchableOpacity>
        </View>
        {/* Week Days */}
        <View style={styles.ptoSummaryRow}>
          <View style={[styles.weekContainer, isTablet ? { width: "70%", alignSelf: 'center' } : {}]} testID="week-days-container">
            {weekItems.map(({ dateStr, isToday, isSelected, hasShift, accessibilityLabel }) => (
              <TouchableOpacity
                key={dateStr}
                style={[
                  styles.dayContainer,
                  isToday || isSelected ? styles.today : {},
                ]}
                onPress={() => handleDatePress(dateStr)}
                testID={`date-${dateStr}`}
                accessible={true}
                accessibilityRole="button"
                accessibilityLabel={accessibilityLabel}
              >
                <Text
                  allowFontScaling={false}
                  style={[styles.dayText, (isToday || isSelected) && styles.selectedText]}>
                  {dayjs(dateStr).format("ddd")}
                </Text>
                <Text
                  allowFontScaling={false}
                  style={[styles.dateText, (isToday || isSelected) && styles.selectedText]}>
                  {dayjs(dateStr).format("D")}
                </Text>
                {/* Dot for shift dates */}
                {hasShift && <View style={[
                  styles.dot,
                  isToday && { backgroundColor: theme.colors.pdsThemeColorBackgroundBaseline } // Use your red color here
                ]} testID={`dot-${dateStr}`} />}
              </TouchableOpacity>
            ))}
            <Animated.View
              style={[styles.summaryContainer, animatedStyle, showPTOSummary && styles.showSummaryStyle]}
            >
              <View style={styles.summaryTextontainer}
                ref={weeklyHoursRef}
                accessible={true}
                accessibilityRole="text"
                accessibilityLabel={
                  totalHours > 0 && totalMinutes > 0
                    ? `${totalHours} ${t("ada.hours")} ${totalMinutes} ${t("schedule.minutes")} ${t("schedule.weeklyHoursWorked")}`
                    : totalHours > 0
                      ? `${totalHours} ${t("ada.hours")} ${t("schedule.weeklyHoursWorked")}`
                      : totalMinutes > 0
                        ? `${totalMinutes} ${t("schedule.minutes")} ${t("schedule.weeklyHoursWorked")}`
                        : `0 ${t("ada.hours")} ${t("schedule.weeklyHoursWorked")}`
                }
              >

                <View style={styles.timeRow} accessible={false}>
                  <Text allowFontScaling={false} style={styles.timeNumber} accessible={false}>{totalHours}</Text>
                  <Text allowFontScaling={false} style={styles.timeUnit} accessible={false}>{t("schedule.hours")}</Text>


                  {totalHours > 0 && totalMinutes > 0 && (
                    <>
                      <Text allowFontScaling={false} style={styles.timeNumber} accessible={false}>{totalMinutes}</Text>
                      <Text allowFontScaling={false} style={styles.timeUnit} accessible={false}>{t("schedule.minutes")}</Text>
                    </>
                  )}
                </View>
                <Text allowFontScaling={false} style={styles.subText} testID="hours-label">
                  {t("schedule.weeklyHoursWorked")}
                </Text>
              </View>

              <View style={styles.divider} />

              <View style={styles.summaryTextontainer}

                accessible={true}
                accessibilityRole="text"
                accessibilityLabel={`${totalVacationAccruedValue} ${t("schedule.days")} ${t("schedule.accuredPTObalance")}`}
              >
                <View style={styles.timeRow}>
                  <Text allowFontScaling={false} style={styles.timeNumber} accessible={false}>{totalVacationAccruedValue}</Text>
                  <Text allowFontScaling={false} style={styles.timeUnit} accessible={false}>{t("schedule.days")}</Text>
                </View>

                <Text allowFontScaling={false} style={styles.subText} testID="pto-balance-label">
                  {t("schedule.accuredPTObalance")}
                </Text>
              </View >
            </Animated.View >
          </View >
        </View >
      </View >
      <View style={styles.hoursSummaryContainer}>
        <TouchableOpacity style={styles.hoursButton} onPress={() => showPTOtoggleView()}
          accessible={true}
          accessibilityLabel={showPTOSummary ? t('hideHoursSummary') : t('viewHoursSummary')}
          accessibilityState={{ expanded: showPTOSummary }}
          accessibilityHint={showPTOSummary ? t("ada.hideHoursSummary") : t("ada.viewHoursSummary")}
        >
          <Text allowFontScaling={false} style={styles.hoursButtonText} testID="view-hours-button">{showPTOSummary ? t('schedule.hideHoursSummary') : t('schedule.viewHoursSummary')}</Text>
          {!showPTOSummary ? <ChevronDown color={theme.colors.pdsThemeColorForegroundPrimary} size={theme.dimensions.pdsGlobalSizeHeight400} />
            : <ChevronUp color={theme.colors.pdsThemeColorForegroundPrimary} size={theme.dimensions.pdsGlobalSizeHeight400} />}
        </TouchableOpacity>
      </View>

      {
        !scheduleLoading && (scheduleError ?
          <StatusPlaceholder source={NoNetwork} onPress={onRetry} status={t('schedule.noNetworkConnection')} isElevation styles={styles} showButton={noNetworkConnection} buttonText={t('schedule.retryButton')} /> :
          requestSuccessErrors &&
          <StatusPlaceholder source={noShiftScheduled} status={t('schedule.noShiftsScheduleed')} isElevation styles={styles} />)
      }

      {scheduleNotReleased && (<StatusPlaceholder source={noSchedulesReleased} isElevation status={t('schedule.noSchedulesReleased')} styles={styles} />)}
    </>
  );
};

export default WeekCalendar;
import React from "react";
import { render, screen, fireEvent } from "@testing-library/react-native";
import StatusPlaceholder from "./StatusPlaceholder";
import configureStore from "redux-mock-store";
import { Provider } from "react-redux";

// Mock Pantry Design System
jest.mock("react-i18next", () => ({
    useTranslation: () => ({
        t: (key: string) => key,
    }),
}));

jest.mock('pantry-design-system', () => ({
    useTheme: () => ({ theme: { colors: {}, dimensions: {}, typography: {}, fonts: {}, borderDimens: {} } }),
    Text: ({ text }) => <>{text}</>,
    Heading: ({ title }) => <>{title}</>,
}));

const mockStyles = {
    containerStyles: {},
    imageStyle: {},
    retryButton: {},
    retryImage: {},
};

const mockStore = configureStore([]);
const store = mockStore({
    deviceInfo: { isTablet: false },
});

const renderWithProvider = (ui) => {
    return render(<Provider store={store}>{ui}</Provider>);
};

describe("StatusPlaceholder Component", () => {
    it("renders correctly with given props", () => {
        renderWithProvider(<StatusPlaceholder source="<svg></svg>" status="Active" showButton={false} styles={mockStyles} />);
        expect(screen.getByTestId("status-placeholder")).toBeTruthy();
    });

    it("applies the correct accessibility labels", () => {
        const { getByTestId } = renderWithProvider(<StatusPlaceholder source="<svg></svg>" status="Calendar Status" styles={mockStyles} />);
        expect(getByTestId("status-placeholder")).toBeTruthy();
    });

    it("renders the SvgXml component", () => {
        const { getByTestId } = renderWithProvider(<StatusPlaceholder source="<svg></svg>" status="Confirmed" showButton={false} styles={mockStyles} />);
        expect(getByTestId("status-placeholder")).toBeTruthy();
    });

    it("displays retry button when showButton is true", () => {
        renderWithProvider(<StatusPlaceholder source="<svg></svg>" status="Error" showButton={true} buttonText="Retry" styles={mockStyles} />);
        expect(screen.getByTestId("retry-button")).toBeTruthy();
    });

    it("does not display retry button when showButton is false", () => {
        const { queryByTestId } = renderWithProvider(<StatusPlaceholder source="<svg></svg>" status="Confirmed" showButton={false} styles={mockStyles} />);
        expect(queryByTestId("retry-button")).toBeNull();
    });

    it("triggers onPress function when retry button is clicked", () => {
        const onPressMock = jest.fn();
        const { getByTestId } = renderWithProvider(
            <StatusPlaceholder source="<svg></svg>" status="Error" showButton={true} buttonText="Retry" styles={mockStyles} onPress={onPressMock} />
        );
        fireEvent.press(getByTestId("retry-button"));
        expect(onPressMock).toHaveBeenCalled();
    });
});

import React from "react";
import { View, TouchableOpacity } from "react-native";
import { SvgXml } from "react-native-svg";
import getStyles from "./Styles";
import { useTheme, Text } from 'pantry-design-system'
import { StatusPlaceHolderProps } from '../../interfaces/common';
import { retry } from "../../assets/images/svg/profileIcons";
import { TEXTLINK_COLORS, TEXT_SIZE, TEXT_WEIGHT, TEXT_ALIGN } from "../../app/shared/constants";
import { useSelector } from "react-redux";

const StatusPlaceholder = ({ source, status, showButton, buttonText, styles, buttonAccessHint, onPress, subHeader, isElevation = false }: StatusPlaceHolderProps) => {
    const { theme } = useTheme();
    const isTablet = useSelector((state: { deviceInfo: { isTablet: boolean } }) => state.deviceInfo.isTablet);

    const style = getStyles(theme, isTablet);
    return (
        <View style={[style.StatusPlaceholder, styles.containerStyles]} accessible={false} accessibilityRole="image" accessibilityLabel={status} testID="status-placeholder">
            {source && <SvgXml xml={source} style={[styles.imageStyle, style.image, isElevation ? style.elevation : null]} />}
            {subHeader &&
                <Text
                    color={TEXTLINK_COLORS.NEUTRAL_HIGH}
                    size={TEXT_SIZE.LARGE}
                    text={subHeader}
                    weight={TEXT_WEIGHT.BOLD}
                    lineHeight={TEXT_SIZE.LARGE}
                    textAlign={TEXT_ALIGN.CENTER}
                />
            }
            {status &&
                <View style={isElevation ? style.textSpace : null}>
                    <Text
                        color={TEXTLINK_COLORS.NEUTRAL_HIGH}
                        size={TEXT_SIZE.LARGE}
                        text={status}
                        weight={TEXT_WEIGHT.REGULAR}
                        lineHeight={TEXT_SIZE.LARGE}
                        textAlign={TEXT_ALIGN.CENTER}
                    />
                </View>
            }
            {showButton && <TouchableOpacity testID="retry-button" style={style.retryButton} accessible={true} accessibilityRole="button" accessibilityLabel={buttonText} accessibilityHint={buttonAccessHint} onPress={onPress}>
                <SvgXml xml={retry} style={[style.retryImage]} />
                <Text
                    color={TEXTLINK_COLORS.NEUTRAL_HIGH_INVERSE}
                    size={TEXT_SIZE.LARGE}
                    text={buttonText}
                    weight={TEXT_WEIGHT.SEMI_BOLD}
                    lineHeight={TEXT_SIZE.LARGE}
                    textAlign={TEXT_ALIGN.CENTER}
                />
            </TouchableOpacity>}
        </View>
    );
};

export default StatusPlaceholder;

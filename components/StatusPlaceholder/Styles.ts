import { Theme } from 'pantry-design-system';
import { StyleSheet } from "react-native";
import { IPAD_WIDTH } from "../../app/shared/constants";

//customising styles based on themes
const getStyles = ({ colors, fonts, typography, borderDimens, dimensions }: Theme, isTablet) => {
    const Styles = StyleSheet.create({
        StatusPlaceholder: {
            alignItems: "center",
            textAlign: "center",
            padding: dimensions.pdsGlobalSpace500,
            backgroundColor: colors.pdsThemeColorBackgroundSunken,
            height: "100%",
            width: isTablet ? IPAD_WIDTH : "100%",
            alignSelf: "center",
        },
        image: {
            margin: dimensions.pdsGlobalSpace300, //not present 
        },
        elevation: {
            elevation: 12,
            borderRadius: 76,
            shadowColor: colors.pdsGlobalShadowLowBottomColor,
            shadowOffset: { width: 0, height: 1 },
            shadowOpacity: 1,
            shadowRadius: 12,
        },
        textSpace: {
            marginHorizontal: dimensions.pdsGlobalSpace1300,
        },
        text: {
            fontSize: typography.pdsGlobalFontSize300,
            fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
            fontWeight: typography.pdsGlobalFontWeight400,
            lineHeight: 23.2,
            marginTop: typography.pdsGlobalFontSize50,
            textAlign: "center",
        },
        retryButton: {
            justifyContent: "center",
            flexDirection: 'row',
            alignSelf: 'center',
            height: dimensions.pdsGlobalSpace1000,
            backgroundColor: colors.pdsThemeColorBackgroundPrimary,
            borderRadius: borderDimens.pdsGlobalBorderRadius1000,
            paddingVertical: dimensions.pdsGlobalSpace200,
            paddingHorizontal: dimensions.pdsGlobalSpace500,
            marginTop: dimensions.pdsGlobalSpace500,
        },
        retryImage: {
            marginRight: dimensions.pdsGlobalSpace200,
            alignSelf: 'center',
        },
        retryText: {
            color: colors.pdsThemeColorBackgroundBaseline,
            fontSize: typography.pdsGlobalFontSize300,
            fontWeight: typography.pdsGlobalFontWeight600,
            fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
        },
    });
    return Styles
}

export default getStyles;

import { View, StyleSheet } from "react-native";
import React from 'react';
import { useTheme, Text } from "pantry-design-system";
import { useTranslation } from "react-i18next";
import { TABLET_LANDSCAPE_WIDTH_STYLE, TEXTDECORATION, TEX<PERSON>INK_COLORS, TEXTLINK_SIZE, TEXTLINK_WEIGHT } from "../../app/shared/constants";
import DeviceInfo from 'react-native-device-info';

export default function Footer() {
  const { t } = useTranslation()

  const year = new Date().getFullYear();
  const bitriseBuildNumber = process.env.BITRISE_BUILD_NUMBER ? ` (${process.env.BITRISE_BUILD_NUMBER})` : ''; // Ensure BITRISE_BUILD_NUMBER is defined

  const version = DeviceInfo.getVersion(); // Returns version

  return (
    <View>
      <View style={{ alignItems: "center" }}>
        <Text
          color={TEXTLINK_COLORS.NEUTRAL_MEDIUM}
          size={TEXTLINK_SIZE.X_SMALL}
          text={t('appName')}
          weight={TEXTLINK_WEIGHT.REGULAR}
          textDecoration={TEXTDECORATION.None}
          textAlign={TABLET_LANDSCAPE_WIDTH_STYLE.alignSelf}
          testID="appName"
        />
        <Text
          color={TEXTLINK_COLORS.NEUTRAL_MEDIUM}
          size={TEXTLINK_SIZE.X_SMALL}
          text={`${t('version')} ${version}${bitriseBuildNumber}`}
          weight={TEXTLINK_WEIGHT.REGULAR}
          textDecoration={TEXTDECORATION.None}
          textAlign={TABLET_LANDSCAPE_WIDTH_STYLE.alignSelf}
          testID="version"
        />
        <Text
          color={TEXTLINK_COLORS.NEUTRAL_MEDIUM}
          size={TEXTLINK_SIZE.X_SMALL}
          SETTINGS_CONSTANTS
          text={t("copyrightText").replace("{year}", year.toString())}
          weight={TEXTLINK_WEIGHT.REGULAR}
          textDecoration={TEXTDECORATION.None}
          textAlign={TABLET_LANDSCAPE_WIDTH_STYLE.alignSelf}
          testID="copyright"
        />
      </View>
    </View>
  );
}

const styels = StyleSheet.create({
  text1: {
    color: "#4B4B49",
    fontWeight: "400",
    fontSize: 14,
  },
});

import React from "react";
import { render } from "@testing-library/react-native";
import NotificationScreen from ".";
import EmptyStatusCard from "../EmptyStatusCard/EmptyStatusCard";

// ✅ Mock `useNavigation` before render
jest.mock("@react-navigation/native", () => ({
    ...jest.requireActual("@react-navigation/native"),
    useNavigation: jest.fn(),
}));

jest.mock("react-i18next", () => ({
    useTranslation: () => ({
        t: (key: string) => key,
    }),
}));


jest.mock("../EmptyStatusCard/EmptyStatusCard", () => "EmptyStatusCard");


jest.mock("pantry-design-system", () => ({
    useTheme: () => ({
        theme: {
            colors: { pdsThemeColorBackgroundBaseline: "#ffffff" },
            fonts: { pdsGlobalFontFamilyPoppins: "Poppins" },
            dimensions: { pdsGlobalSpace400: 16 },
            typography: { pdsGlobalFontSize500: 18 },
            borderDimens: { pdsGlobalBorderRadius300: 8 },
        },
    }),
}));

describe('NotificationScreen', () => {
    it('should not render EmptyStatusCard when resourceTabEnabled is true', () => {
        render(<NotificationScreen />);
        expect(EmptyStatusCard).toBeTruthy();
    });
});

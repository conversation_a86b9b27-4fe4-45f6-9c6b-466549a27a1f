import { useTheme } from 'pantry-design-system';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';

import { NotificationEmptyImage } from '../../assets/images/svg/notificationEmptyImage';
import EmptyStatusCard from '../../components/EmptyStatusCard/EmptyStatusCard';
import getStyles from '../SessionOutModal/styles';

const NotificationScreen = () => {
    const { t } = useTranslation();
    const { theme } = useTheme();
    const styles = getStyles(theme);

    return (
        <View style={styles.safeAreaContainer}>
            <View style={styles.container}>
                <EmptyStatusCard
                    source={NotificationEmptyImage}
                    titleText={t('NotificationEmptyView.emptyMessageTitle')}
                    subTitleText={t('NotificationEmptyView.emptyMessageSubTitle')}
                    descriptionText={t('NotificationEmptyView.emptyMessageDescription')}
                />
            </View>
        </View>
    );
};

export default NotificationScreen;

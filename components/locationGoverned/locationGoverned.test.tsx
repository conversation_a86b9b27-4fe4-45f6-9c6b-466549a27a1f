import React, { act } from "react";
import { render, fireEvent, waitFor } from "@testing-library/react-native";
import LocationGoverned from "./index";
import { Linking, Text, TouchableOpacity } from "react-native";
import { Provider } from "react-redux";
import configureS<PERSON> from "redux-mock-store";

jest.mock("../../app/utils/helpers", () => ({
    checkLocationPermission: jest.fn(),
}));

jest.mock('../../app/store/AsyncStorage', () => ({
    setOnboardingStep: jest.fn(),
}));

jest.spyOn(Linking, 'openSettings').mockImplementation(() => {
    return Promise.resolve();
});

jest.mock("react-native-permissions", () => {
    return {
        check: jest.fn(() => Promise.resolve("granted")),
        request: jest.fn(() => Promise.resolve("granted")),
        PERMISSIONS: {
            ANDROID: {
                ACCESS_FINE_LOCATION: "android.permission.ACCESS_FINE_LOCATION",
            },
            IOS: {
                LOCATION_WHEN_IN_USE: "ios.permission.LOCATION_WHEN_IN_USE",
            },
        },
        RESULTS: {
            GRANTED: "granted",
            DENIED: "denied",
            BLOCKED: "blocked",
            UNAVAILABLE: "unavailable",
        },
    };
});

jest.mock("react-native-svg", () => ({
    SvgXml: jest.fn(({ testID }) => <svg testID={testID} />), // Fixed testID
}));

jest.mock("pantry-design-system", () => ({
    Button: jest.fn(({ onPress, label, testID }) => (
        <button onClick={() => {
            onPress();
        }} testID={testID}>
            {label}
        </button>
    )),
    Heading: jest.fn(({ title, testID }) => <h1 testID={testID}>{title}</h1>),
    Text: jest.fn(({ text, testID }) => <p testID={testID}>{text}</p>),
    useTheme: jest.fn(() => ({
        theme: {
            colors: { pdsThemeColorBackgroundPrimary: "#ffffff" },
            dimensions: {
                pdsGlobalSpace400: 10,
            },
        },
    })),
}));

const mockStore = configureStore([]);
const initialState = {
    locationAccess: {
        isSkipped: false,
        isPrecise: false,
    },
};

describe("LocationPermission Component", () => {
    let store;
    const mockNavigate = jest.fn();

    beforeEach(() => {
        store = mockStore(initialState);
        jest.clearAllMocks();
    });

    it("should render the UI when permission is not granted", () => {
        const { getByTestId } = render(
            <Provider store={store}>
                <LocationGoverned />
            </Provider>);

        // Verify the UI elements
        expect(getByTestId("location-image")).toBeTruthy();
        expect(getByTestId("location-permission-heading")).toBeTruthy();
        expect(getByTestId("location-description")).toBeTruthy();
        expect(getByTestId("dont-want-give-location")).toBeTruthy();
        expect(getByTestId("go-to-device-settings-button")).toBeTruthy();
    });

    it("should call Linking.openSettings when the button is pressed", async () => {
        jest.spyOn(Linking, 'openSettings').mockImplementation(() => { });
        const mockOnPress = jest.fn(() => Linking.openSettings());

        const { getByTestId } = render(
            <TouchableOpacity onPress={mockOnPress} testID="go-to-device-settings-button">
                <Text>Go to Device Settings</Text>
            </TouchableOpacity>
        );

        const button = getByTestId("go-to-device-settings-button");
        fireEvent.press(button);

        await waitFor(() => {
            expect(mockOnPress).toHaveBeenCalled();
            expect(Linking.openSettings).toHaveBeenCalled();
        });
    });

});

import { t } from 'i18next';
import { Button, Heading, Text, useTheme } from 'pantry-design-system';
import React from 'react';
import { Platform, View } from 'react-native';
import { openSettings } from 'react-native-permissions';
import { SvgXml } from 'react-native-svg';
import { useDispatch, useSelector } from 'react-redux';

import {
  LOCATION_STATE,
  TEXT_ALIGN,
  TEXT_PANTRY_COLORS,
  TEXT_SIZE,
  TEXT_WEIGHT,
  TEXTDECORATION,
} from '../../app/shared/constants';
import {
  setLocationSkipped,
  setPreciseLocationGranted,
} from '../../app/store/reducers/locationAccessSlice';
import { getLocationAccuracy, requestLocationPermission } from '../../app/utils/helpers';
import { locationPermissionImage } from '../../assets/images/svg/LoyaltyAccount';

import getStyles from './styles';

import type { LocationAccuracy } from '../../app/store/reducers/locationAccessSlice';

const LocationGoverned = () => {
  const { theme } = useTheme();
  const styles = getStyles(theme);

  const { isSkipped, isPrecise } = useSelector((state: any) => state.locationAccess);
  const dispatch = useDispatch();

  const openModal = async () => {
    if (isSkipped) {
      const isGranted = await requestLocationPermission();
      let permission: LocationAccuracy = LOCATION_STATE.DENIED;

      if (isGranted) {
        permission = await getLocationAccuracy();
      }

      if (permission === LOCATION_STATE.DENIED) dispatch(setLocationSkipped(false));

      dispatch(setPreciseLocationGranted(permission));
    } else {
      openSettings(); // open settings if user didn't skip
    }
  };

  const status = (() => {
    if (isSkipped) return 'skipped';
    if (isPrecise === LOCATION_STATE.DENIED) return LOCATION_STATE.DENIED;
    if (isPrecise === LOCATION_STATE.APPROXIMATE) return LOCATION_STATE.APPROXIMATE;
    return LOCATION_STATE.PRECISE;
  })();

  const getHeadingText = () => {
    switch (status) {
      case 'skipped':
        return t('locationDisabled.locationPermission');
      case LOCATION_STATE.DENIED:
        return t('locationDisabled.locationPermission');
      case LOCATION_STATE.APPROXIMATE:
        return t('locationDisabled.preciseLocationNotFound');
      default:
        return '';
    }
  };

  const getDescription = () => {
    if (isSkipped) {
      return t('locationDisabled.turnOnLocationDescription');
    } else if (isPrecise === LOCATION_STATE.DENIED) {
      return t('locationDisabled.dontAllowDescription');
    } else if (isPrecise === LOCATION_STATE.APPROXIMATE) {
      return t('locationDisabled.preciseDescription');
    }
    return ''; // No heading needed if location is precise (you can also choose to hide the component)
  };

  const getMoreDescription = () => {
    if (isSkipped) {
      return t('locationDisabled.dontWantToGiveLocation');
    } else if (isPrecise === LOCATION_STATE.DENIED) {
      return t('locationDisabled.allowLcationDeviceSettings');
    } else if (isPrecise === LOCATION_STATE.APPROXIMATE) {
      return t('locationDisabled.dontWantToGiveLocation');
    }
    return ''; // No heading needed if location is precise (you can also choose to hide the component)
  };

  return (
    <View style={styles.container}>
      <View style={styles.mainContainer}>
        <SvgXml
          xml={locationPermissionImage}
          style={styles.imageContainer}
          testID="location-image"
        />

        <View style={styles.spaceContainer} />

        <Heading
          accessible
          testID={'location-permission-heading'}
          textAlign={TEXT_ALIGN.CENTER}
          title={getHeadingText()}
          color={TEXT_PANTRY_COLORS.NEUTRAL_HIGH}
          size={TEXT_SIZE.MEDIUM}
        />

        <View style={styles.spaceContainer} />

        <Text
          color={TEXT_PANTRY_COLORS.NEUTRAL_HIGH}
          size={TEXT_SIZE.LARGE}
          text={getDescription()}
          weight={TEXT_WEIGHT.REGULAR}
          accessible={true}
          testID="location-description"
          textDecoration={TEXTDECORATION.None}
          textAlign={TEXT_ALIGN.CENTER}
        />

        <View style={styles.spaceContainer} />

        <Text
          color={TEXT_PANTRY_COLORS.NEUTRAL_HIGH}
          size={TEXT_SIZE.LARGE}
          text={getMoreDescription()}
          weight={TEXT_WEIGHT.REGULAR}
          accessible={true}
          testID="dont-want-give-location"
          textDecoration={TEXTDECORATION.None}
          textAlign={TEXT_ALIGN.CENTER}
        />

        <View style={styles.spaceContainer} />

        <View style={{ width: 'auto' }}>
          <Button
            theme={theme}
            fullWidth={true}
            size={TEXT_SIZE.LARGE}
            label={
              status === 'skipped'
                ? t('onboarding.allowLocation')
                : t('locationDisabled.goToDeviceSettings')
            }
            onPress={openModal}
            accessible={true}
            accessibilityRole="none"
            accessibilityLabel={
              status === 'skipped'
                ? `${t('onboarding.allowLocation')} ${t('ada.button')}`
                : `${t('locationDisabled.goToDeviceSettings')} ${t('ada.button')}`
            }
            accessibilityHint={Platform.OS === 'ios' ? t('ada.doubleTapToActivate') : ''}
            testID="go-to-device-settings-button"
          />
        </View>
      </View>
    </View>
  );
};

export default LocationGoverned;

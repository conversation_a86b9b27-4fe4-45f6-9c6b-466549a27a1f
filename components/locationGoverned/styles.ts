import { IPAD_WIDTH } from '../../app/shared/constants';
import { Theme } from 'pantry-design-system';
import { StyleSheet } from "react-native";

const getStyes = ({ colors, dimensions }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.pdsThemeColorBackgroundBaseline,
      maxWidth: IPAD_WIDTH,
      alignSelf: "center",
      width: '100%',
      paddingTop: dimensions.pdsGlobalSpace600
    },
    mainContainer: {
      paddingHorizontal: dimensions.pdsGlobalSpace400,
      justifyContent: "center",
      alignSelf: "center",
    },
    imageContainer: {
      alignSelf: "center",
      color: colors.pdsThemeColorOutlinePrimary,
    },
    spaceContainer: {
      marginTop: dimensions.pdsGlobalSpace400,
    }

  });

  return styles;
};

export default getStyes;

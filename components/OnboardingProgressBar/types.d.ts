/**
 * Interface for the properties used in the OnboardingProgressBar component.
 * This interface defines all the customizable options and required functions
 * for rendering the progress bar and steps in the onboarding process.
 *
 * @interface
 * @example
 * // Example usage of the OnboardingProgressProps interface
 * const onboardingProps: OnboardingProgressProps = {
 *   ref: React.createRef(),
 *   steps: 5,
 *   currentStep: 3,
 *   activeColor: 'blue',
 *   inactiveColor: 'gray',
 *   stepDimension: 20,
 *   progressbarHeight: 10,
 *   onStepChange: (step) => console.log('Step changed to:', step)
 * };
 */

export interface OnboardigProgressProps {
  /**
   * A reference to the component, useful for imperative actions.
   */
  ref?: any;

  /**
   * The total number of steps in the onboarding process.
   * It should be a positive integer (e.g., 5 for a 5-step process).
   */
  steps: number;

  /**
   * The current active step, represented by an integer.
   * This value should be between 1 and the total number of steps (`steps`).
   * For example, if the user is on the 3rd step, `currentStep` should be 3.
   */
  currentStep: number;

  /**
   * The color(s) for the active step(s).
   * This can be either a string (single color) or an array of strings (for a gradient).
   * Example:
   * - `"blue"` for a single color.
   * - `["#ff7f50", "#ff6347"]` for a gradient.
   * Default value is derived from the theme if not provided.
   */
  activeColor?: string | string[];

  /**
   * The color(s) for the inactive steps.
   * This can be either a string (single color) or an array of strings (for a gradient).
   * Example:
   * - `"gray"` for a single color.
   * - `["#d3d3d3", "#a9a9a9"]` for a gradient.
   * Default value is derived from the theme if not provided.
   */
  inactiveColor?: string | string[];

  /**
   * The dimension (size) of each step circle (usually in pixels).
   * If not specified, it defaults to a value based on the theme.
   */
  stepDimension?: number;

  /**
   * The height of the progress bar that connects the steps.
   * This defines the thickness of the bar between the circles. Defaults to a theme value if not provided.
   */
  progressbarHeight?: number;

  /**
   * The width of the border around each step circle.
   * Defaults to 0 (no border). You can set this to any number for the desired border width.
   */
  stepBorderWidth?: number;

  /**
   * The color of the border around each step circle.
   * This is optional and will default to the theme's foreground color if not provided.
   */
  stepBorderColor?: string;

  /**
   * A boolean that indicates whether the component is accessible for screen readers.
   * When set to `true`, it allows better accessibility for visually impaired users.
   */
  accessible?: boolean;

  /**
   * A string that provides an accessibility label for the component.
   * This label is used by screen readers to describe the progress bar.
   * It's helpful for users with visual impairments.
   */
  accessibilityLabel?: string;

  /**
   * A string that assigns a test ID to the component for testing purposes.
   * This is useful when performing automated tests on the component.
   */
  testID?: string;

  /**
   * A callback function that is called when the current step changes.
   * It receives the new step number as a parameter and allows the parent component
   * to react to step changes (e.g., navigate to the next screen).
   *
   * @param {number} step - The new step that has become active.
   */
  onStepChange: (step: number) => void;
  disableAnimations?: boolean;
}

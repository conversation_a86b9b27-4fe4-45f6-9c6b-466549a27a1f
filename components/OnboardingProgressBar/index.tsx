/**
 * A customizable progress bar for onboarding steps.
 * This component visually represents the progress of a multi-step process with a set of circles (steps) and a progress bar.
 * 
 * @component
 * @example
 * // Usage Example
 * <OnboardingProgressBar
 *    steps={5}
 *    currentStep={2}
 *    activeColor="blue"
 *    inactiveColor="gray"
 *    stepDimension={20}
 *    progressbarHeight={8}
 *    onStepChange={(step) => console.log(`Step changed to: ${step}`)}
 *    accessible={true}
 *    accessibilityLabel="Onboarding Progress"
 * />
 */

import React, { useEffect, forwardRef, useRef } from 'react';
import { View, StyleSheet, Animated } from 'react-native';
import { OnboardigProgressProps } from './types';
import { useTheme } from 'pantry-design-system';
import LinearGradient from 'react-native-linear-gradient';
import { useTranslation } from 'react-i18next';

const AnimatedLinearGradient = Animated.createAnimatedComponent(LinearGradient);

const OnboardingProgressBar = forwardRef<any, OnboardigProgressProps>(({
    steps,
    currentStep,
    activeColor,
    inactiveColor,
    stepDimension,
    stepBorderWidth = 0,
    stepBorderColor,
    progressbarHeight,
    accessible,
    accessibilityLabel,
    onStepChange,
    disableAnimations,
    testID
}, ref) => {
    const { theme } = useTheme();
    const { t } = useTranslation();

    activeColor = activeColor ?? theme.colors.pdsThemeColorBackgroundPrimaryLow;
    inactiveColor = inactiveColor ?? theme.colors.pdsThemeColorBackgroundNeutralLow;
    stepBorderColor = stepBorderColor ?? theme.colors.pdsThemeColorForegroundPrimary;
    stepDimension = stepDimension ?? theme.dimensions.pdsGlobalSizeWidth400;
    progressbarHeight = progressbarHeight ?? theme.dimensions.pdsGlobalSizeHeight40;

    const animatedWidth = useRef(new Animated.Value(0)).current;

    useEffect(() => {
        const fillPercentage = (currentStep - 1) / (steps - 1);

        if (disableAnimations) {
            animatedWidth.setValue(fillPercentage); // Instantly set value
        } else {
            Animated.timing(animatedWidth, {
                toValue: fillPercentage,
                duration: 600,
                useNativeDriver: false,
            }).start();
        }
    }, [currentStep, steps, disableAnimations]);

    const animatedStepValues = useRef(
        Array.from({ length: steps }, (_, i) => new Animated.Value(i < currentStep ? 1 : 0))
    ).current;

    useEffect(() => {
        if (currentStep > 1) {
            const dotIndex = currentStep - 1;

            if (disableAnimations) {
                animatedStepValues[dotIndex].setValue(1);
            } else {
                Animated.timing(animatedStepValues[dotIndex], {
                    toValue: 1,
                    duration: 300,
                    delay: 500,
                    useNativeDriver: false,
                }).start();
            }
        }
    }, [currentStep, disableAnimations]);

    const renderStepCircles = () => {
        let circles = [];
        for (let i = 0; i < steps; i++) {
            circles.push(
                typeof activeColor === 'string' && typeof inactiveColor === 'string' ?
                    <Animated.View
                        accessible={false}
                        accessibilityHint={`Progress, ${i + 1} of ${steps}, ${i + 1 < currentStep ? 'completed' : i + 1 == currentStep ? 'current' : 'upcoming'}, step`}
                        accessibilityLabel={accessibilityLabel}
                        key={i}
                        style={[
                            {
                                borderRadius: stepDimension / 2,
                                borderWidth: stepBorderWidth,
                                borderColor: stepBorderColor,
                                width: stepDimension,
                                height: stepDimension,
                                backgroundColor: inactiveColor,
                            },
                            {
                                backgroundColor: animatedStepValues[i].interpolate({
                                    inputRange: [0, 1],
                                    outputRange: [inactiveColor, activeColor],
                                }),
                            }
                        ]}
                    /> :
                    typeof activeColor === 'object' && typeof inactiveColor === 'object' &&
                    <LinearGradient
                        key={i}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 0 }}
                        colors={i < currentStep ? activeColor : inactiveColor}
                        style={{
                            borderRadius: (stepDimension ?? theme.dimensions.pdsGlobalSizeWidth400) / 2,
                            borderWidth: stepBorderWidth,
                            borderColor: stepBorderColor,
                            width: stepDimension,
                            height: stepDimension
                        }}
                        testID={`linear-gradient-step-${i}`}
                    />
            );
        }
        return circles;
    };

    /**
     * Renders the progress bar that connects the steps.
     * The progress bar shows the transition between steps using the active/inactive colors.
     * 
     * @returns {JSX.Element[]} Array of rendered progress bars between steps.
     */
    const renderProgressBar = () => {
        let bars = [];
        for (let i = 0; i < steps - 1; i++) {
            bars.push(
                typeof activeColor === 'string' && typeof inactiveColor === 'string' ?
                    <View
                        key={i}
                        accessible={false}
                        style={[
                            {
                                flex: 1,
                                height: progressbarHeight,
                                backgroundColor: i < currentStep - 1 ? activeColor : inactiveColor,
                            },
                        ]}
                    /> : typeof activeColor === 'object' && typeof inactiveColor === 'object' &&
                    <LinearGradient
                        key={i}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 0 }}
                        colors={i < currentStep - 1 ? activeColor : inactiveColor}
                        style={{
                            flex: 1,
                            height: progressbarHeight,
                        }}
                    />
            );
        }

        return bars;
    };

    return (
        <View
            ref={ref}
            testID={testID}
            style={styles.container}
            accessible={accessible}
            importantForAccessibility='yes'
            accessibilityLabel={t(`onboarding.progress-bar`).replace('{currentStep}', currentStep.toString()).replace('{totalSteps}', steps.toString())}
        >
            {/* Progress Track */}
            {!disableAnimations ? <View style={[styles.progressTrack, { height: progressbarHeight, backgroundColor: inactiveColor }]}>
                <Animated.View
                    style={[styles.progressFill, {
                        width: animatedWidth.interpolate({
                            inputRange: [0, 1],
                            outputRange: ['0%', '100%']
                        }),
                        backgroundColor: typeof activeColor === 'string' ? activeColor : undefined,
                        height: progressbarHeight
                    }]}
                >
                    {typeof activeColor === 'object' && (
                        <AnimatedLinearGradient
                            colors={activeColor}
                            start={{ x: 0, y: 0 }}
                            end={{ x: 1, y: 0 }}
                            style={StyleSheet.absoluteFill}
                        />
                    )}
                </Animated.View>
            </View>
                :
                <View style={styles.progressContainer}>
                    {renderProgressBar()}
                </View>}

            {/* Dots */}
            <View style={styles.stepContainer}>
                {renderStepCircles()}
            </View>
        </View>
    );
});

const styles = StyleSheet.create({
    container: {
        width: '100%',
        height: 24,
        justifyContent: 'center',
        alignItems: 'center',
    },
    stepContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: '100%',
        position: 'absolute',
        top: 0,
        bottom: 0,
    },
    progressTrack: {
        width: '100%',
        borderRadius: 999,
        overflow: 'hidden',
        position: 'absolute',
        top: '55%',
        transform: [{ translateY: -0.5 * 8 }],
    },
    progressFill: {
        height: '100%',
        position: 'absolute',
        top: 0,
        left: 0,
        borderRadius: 999,
    },
    progressContainer: {
        width: '100%',
        flexDirection: 'row',
        justifyContent: 'space-between',
        borderRadius: 5,
        paddingHorizontal: 10,
    }

});

export default OnboardingProgressBar;

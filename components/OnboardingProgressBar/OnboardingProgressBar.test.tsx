import React from 'react';
import { render } from '@testing-library/react-native';
import OnboardingProgressBar from './index';

jest.mock('react-i18next', () => ({
    useTranslation: () => ({
        t: (key: string) => {
            if (key === 'onboarding.progress-bar') {
                return 'Progress {currentStep} of {totalSteps}';
            }
            return key;
        },
    }),
}));

describe('OnboardingProgressBar', () => {
    const defaultProps = {
        steps: 4,
        currentStep: 2,
        activeColor: 'blue',
        inactiveColor: 'gray',
        stepDimension: 20,
        progressbarHeight: 8,
        accessible: true,
        accessibilityLabel: 'Onboarding Progress',
        testID: 'onboarding-progress-bar'
    };

    it('renders correctly', () => {
        const { getByTestId } = render(<OnboardingProgressBar {...defaultProps} />);
        expect(getByTestId('onboarding-progress-bar')).toBeTruthy();
    });

    it('renders the correct number of steps', () => {
        const { getAllByA11yHint } = render(<OnboardingProgressBar {...defaultProps} />);
        const stepHints = getAllByA11yHint(/Progress, \d+ of \d+, .+, step/);
        expect(stepHints.length).toBe(defaultProps.steps);
    });

    it('sets the current step accessibility text correctly', () => {
        const { getAllByA11yHint } = render(<OnboardingProgressBar {...defaultProps} />);
        const currentStepHint = getAllByA11yHint(/current/);
        expect(currentStepHint.length).toBe(1);
    });

    it('applies custom dimensions for solid color steps', () => {
        const { getAllByA11yHint } = render(
            <OnboardingProgressBar {...defaultProps} activeColor="blue" inactiveColor="gray" />
        );

        const steps = getAllByA11yHint(/step/);
        steps.forEach((step) => {
            const style = step.props?.style;
            if (Array.isArray(style) && style[0]?.width !== undefined) {
                expect(style[0].width).toBe(defaultProps.stepDimension);
                expect(style[0].height).toBe(defaultProps.stepDimension);
            }
        });
    });

    it('applies correct accessibility label on container', () => {
        const { getByTestId } = render(<OnboardingProgressBar {...defaultProps} />);
        const container = getByTestId('onboarding-progress-bar');
        const label = container.props.accessibilityLabel;

        expect(label).toBe(`Progress ${defaultProps.currentStep} of ${defaultProps.steps}`);
    });

});

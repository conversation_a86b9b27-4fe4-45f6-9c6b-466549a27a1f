import React, { forwardRef, useState } from 'react';
import { View, Pressable } from 'react-native';
import { useTheme, Text, Heading } from 'pantry-design-system';
import getStyles from './style';
import { AnnouncementsCardProps } from './types';
import { t } from 'i18next';
import { useSelector } from 'react-redux';
import moment from 'moment';
import FastImage from '@d11/react-native-fast-image';
import { ChevronRight } from '../../assets/icons/ChevronRight';
import {
  TEXTLINK_SIZE,
  TEXTLINK_COLORS,
  TEXT_WEIGHT,
  TEXT_SIZE,
  HEADING_SIZE,
} from '../../app/shared/constants';
import { ANNOUNCEMENTS_VISIBILITY_CORP } from '../../app/features/home/<USER>';
import images from '../../assets/images';

const AnnouncementsCard = forwardRef<any, AnnouncementsCardProps>(
  (
    {
      index,
      announcement,
      testID,
      onButtonPress,
      accessible,
      width,
      style,
      itemsLength,
    },
    ref
  ) => {
    const { theme } = useTheme();
    const { isTablet } = useSelector((state: any) => state.deviceInfo);
    const styles = getStyles(theme, width, isTablet);
    const { title, division, startDate, previewDownloadableImageUrl } = announcement;
    const divisionName = useSelector((state: any) => state?.profile?.profile?.divisionName);

    const filteredDivisions = division?.filter((item) => {
      return item === ANNOUNCEMENTS_VISIBILITY_CORP || item === divisionName;
    });

    const [imageLoadError, setImageLoadError] = useState(false);
    const imageUrl = previewDownloadableImageUrl;
    return (
      <Pressable ref={ref} testID={testID} style={{ ...styles.cardContainer, width: width, ...style }} accessible={accessible} accessibilityRole="button"
        accessibilityHint=""
        accessibilityLabel={
          `${filteredDivisions}` +
          ', ' +
          `${title}` +
          ', ' + `posted ${moment(startDate).format('MMMM Do')}` + ', ' + t('READMORE') + ", " +
          t('paginationDot', { index: (index ?? 0) + 1, total: itemsLength })
        }
        onPress={
          onButtonPress
            ? ((() => {
              onButtonPress(title);
            }) as () => void)
            : undefined
        }>
        <View style={styles.frameWrapper}>
          <View style={styles.pdsTagParent}>
            <FastImage
              testID={`${testID}-image-container`}
              source={
                imageLoadError || !imageUrl ? images.ANNOUNCEMENTS_ERROR_IMAGE : { uri: imageUrl }
              }
              style={styles.imageContainer}
              onError={() => setImageLoadError(true)}
              accessible={false}
              accessibilityElementsHidden={true}
              importantForAccessibility="no-hide-descendants"
            />
            <View style={styles.content}>
              <View style={styles.pdsTagSubParent}>
                {filteredDivisions?.map(
                  (item, index): React.ReactNode => (
                    <View
                      style={{
                        ...styles.pdsTag,
                        backgroundColor:
                          divisionName === item
                            ? theme.colors.pdsGlobalColorGeneralYellow20
                            : theme.colors.pdsGlobalColorGeneralBlue20,
                      }}
                      key={index}
                    >
                      <Text
                        color={TEXTLINK_COLORS.NEUTRAL_HIGH}
                        size={TEXT_SIZE.X_SMALL}
                        text={item}
                        weight={TEXT_WEIGHT.REGULAR}
                      />
                    </View>
                  ),
                )}
              </View>
              <Heading
                title={announcement?.title}
                color={TEXTLINK_COLORS.NEUTRAL_HIGH}
                size={HEADING_SIZE.XSmall}
                maxLines={2}
              />
            </View>
            <View style={styles.cardFooter}>
              <Text
                color={TEXTLINK_COLORS.NEUTRAL_HIGH}
                size={TEXTLINK_SIZE.SMALL}
                text={t('POSTED') + ' ' + moment(startDate).format('MMM. D')}
              />
              <View
                testID={`${testID}-read-more-button`}
                style={styles.pdsButton}
              >
                <Text
                  color={theme.colors.pdsThemeColorForegroundPrimary}
                  size={TEXTLINK_SIZE.MEDIUM}
                  text={t('READMORE')}
                  weight={TEXT_WEIGHT.SEMI_BOLD}
                />
                <ChevronRight
                  color={theme.colors.pdsThemeColorForegroundPrimary}
                  size={theme.typography.pdsGlobalFontSize400}
                />
              </View>
            </View>
          </View>
        </View>
      </Pressable>
    );
  }
);

export default AnnouncementsCard;


import { ImageProps, ImageSourcePropType } from "react-native";
import { AnnouncementContent } from '../../app/config/responseTypes';

/** 
 * Interface for the props of the AnnouncementsCard component.
 *
 * @interface AnnouncementsCardProps
 *
 * @property {ImageSourcePropType} image - The image source for the card's image.
 * It can accept a local image or a remote image URL.
 *
 * @property {string} division - The name or label of the tag associated with the announcement.
 * This text is usually displayed above the title of the card.
 *
 * @property {string} cardTitle - The title of the announcement card.
 * This text is prominently displayed in the card.
 *
 * @property {string} startDate - The date when the announcement was posted.
 * This will typically be displayed below the title.
 *
 * @property {string} [testID] - An optional test ID for UI testing purposes.
 * If provided, this ID will be used to locate the component during testing.
 *
 * @property {() => void} [onButtonPress] - An optional callback function that gets triggered
 * when the "Read more" button is pressed.
 *
 * @property {boolean} [accessible] - An optional flag to make the component accessible.
 * This can be set to true to enable accessibility features for screen readers.
 *
 * @example
 * const exampleProps: AnnouncementsCardProps = {
 *   image: require('path/to/image.png'),
 *   division: 'New Feature',
 *   cardTitle: 'Introducing the New Feature',
 *   startDate: '2025-02-19',
 *   onButtonPress: () => console.log('Button Pressed'),
 *   accessible: true
 * };
 */
export interface AnnouncementsCardProps {
  announcement: AnnouncementContent;
  testID?: string;
  onButtonPress?: (title: string) => void;
  accessible?: boolean;
  width?: any;
  style?: any;
  index?: number;
  itemsLength?: number;
}
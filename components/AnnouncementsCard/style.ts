import { Dimensions, StyleSheet } from "react-native";
import { Theme } from "pantry-design-system";

export default ({
  colors,
  borderDimens,
  dimensions,
  typography,
  fonts,
}: Theme, width: '80%' | '90%' | '100%' | 'auto' = 'auto', isTablet: boolean) => {

  const imageHeight = 145;
  return StyleSheet.create({
    cardContainer: {
      width,
      flex: 1,
      borderWidth: 1,
      overflow: "hidden",
      borderColor: colors.pdsThemeColorOutlineNeutralLow,
      borderRadius: borderDimens.pdsGlobalBorderRadius200,
      backgroundColor: colors.pdsThemeColorBackgroundBaseline,
    },
    imageContainer: {
      borderRadius: borderDimens.pdsGlobalBorderRadius100,
      width: 'auto',
      height: imageHeight,
      overflow: "hidden",
      resizeMode: "cover",
      backgroundColor: colors.pdsThemeColorOutlineNeutralLow,
    },
    pdsTag: {
      borderRadius: borderDimens.pdsGlobalBorderRadius100,
      backgroundColor: colors.pdsGlobalColorGeneralBlue20,
      paddingTop: dimensions.pdsGlobalSpace50,
      paddingBottom: dimensions.pdsGlobalSpace50,
      paddingLeft: dimensions.pdsGlobalSpace100,
      paddingRight: dimensions.pdsGlobalSpace100,
      alignSelf: 'flex-start',
      marginBottom: dimensions.pdsGlobalSpace50,
    },
    pdsButton: {
      minWidth: 113,
      justifyContent: "center",
      alignItems: "center",
      flexDirection: "row",
      overflow: "hidden",
      paddingTop: dimensions.pdsGlobalSpace50,
      paddingBottom: dimensions.pdsGlobalSpace50,
      paddingRight: dimensions.pdsGlobalSpace300,
    },
    cardFooter: {
      width: 'auto',
      flexWrap: 'wrap',
      flexDirection: "row",
      justifyContent: "space-between",
      paddingLeft: dimensions.pdsGlobalSpace400,
    },
    pdsTagParent: {
      gap: 8,
    },
    pdsTagSubParent: {
      flexDirection: "row",
      gap: 8,
    },
    frameWrapper: {
      width: 'auto',
      paddingBottom: dimensions.pdsGlobalSpace400,
    },
    content: {
      paddingLeft: dimensions.pdsGlobalSpace400,
      paddingRight: dimensions.pdsGlobalSpace400,
    },
    frameParent: {
      paddingHorizontal: 16,
      paddingBottom: 16,
      width: "100%",
      flex: 1,
    },
  });
};

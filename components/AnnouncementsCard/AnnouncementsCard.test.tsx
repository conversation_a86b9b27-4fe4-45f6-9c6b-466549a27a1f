import React from "react";
import { render, fireEvent } from "@testing-library/react-native";
import AnnouncementsCard from ".";
import { Provider } from "react-redux";
import configureStore from "redux-mock-store";

// Mock theme provider
const mockTheme = {
  colors: {
    primary: "blue",
    background: "white",
    text: "black",
    pdsThemeColorOutlineNeutralLow: "white",
    pdsThemeColorForegroundNeutralHigh: "black",
    pdsThemeColorBackgroundBaseLine: "white",
    pdsGlobalColorGeneralBlue20: "blue",
    pdsThemeColorForegroundPrimary: "red",
  },
  borderDimens: {
    pdsGlobalBorderRadius100: 8,
    pdsGlobalBorderRadius200: 12,
  },
  dimensions: {
    pdsGlobalSpace50: 8,
    pdsGlobalSpace100: 12,
    pdsGlobalSpace200: 14,
    pdsGlobalSpace300: 16,
    pdsGlobalSpace400: 18,
  },
  fonts: {
    pdsGlobalFontFamilyNunitoSans: "NunitoSans",
    pdsGlobalFontFamilyPoppins: "Poppins",
  },
  typography: {
    pdsGlobalFontSize500: 16, // Add missing typography properties
    pdsGlobalFontWeight400: "400",
    pdsGlobalFontWeight500: "500",
    pdsGlobalFontWeight600: "600",
    pdsGlobalFontSize50: 10,
    pdsGlobalFontSize200: 16,
    pdsGlobalFontSize300: 18,
  },
};


jest.mock("pantry-design-system", () => ({
  useTheme: () => ({ theme: mockTheme }),
  Heading: jest.fn(({ title, testID }) => <text testID={testID}>{title}</text>),
  Text: jest.fn(({ children, text, testID }) => <text testID={testID}>{children || text}</text>),
  TextLink: jest.fn(({ text, testID }) => <text testID={testID}>{text}</text>),
  ThemeProvider: ({ children }: { children: React.ReactNode }) => (
    <>{children}</>
  ),
}));

const mockStore = configureStore([]);
let store: any;

describe("AnnouncementsCard", () => {
  beforeEach(() => {
    store = mockStore({
      deviceInfo: { isTablet: false },
    });
  });
  const mockAnnouncement = {
    "title": "Albertsons benefits",
    "division": ["Corp"],
  }
  it("renders correctly with all props", () => {
    const { getByTestId } = render(
      <Provider store={store}>
        <AnnouncementsCard
          testID="announcement-card"
          announcement={mockAnnouncement}
          onButtonPress={() => { }}
        />
      </Provider>
    );
    expect(getByTestId("announcement-card")).toBeTruthy();
    expect(getByTestId("announcement-card-read-more-button")).toBeTruthy();
  });

  it("calls onButtonPress with cardTitle when image container is pressed", () => {
    const onButtonPress = jest.fn();
    const cardTitle = "Important Update";

    const { getByTestId } = render(
      <Provider store={store}>
        <AnnouncementsCard
          testID="announcement-card"
          announcement={{ ...mockAnnouncement, title: cardTitle }}
          onButtonPress={onButtonPress}
        />
      </Provider>
    );

    fireEvent.press(getByTestId("announcement-card"));
    expect(onButtonPress).toHaveBeenCalledWith(cardTitle);
  });

  it("calls onButtonPress with cardTitle when 'Read More' button is pressed", () => {
    const onButtonPress = jest.fn();
    const cardTitle = "Exciting News";

    const { getByTestId } = render(
      <Provider store={store}>
        <AnnouncementsCard
          testID="announcement-card"
          announcement={{ ...mockAnnouncement, title: cardTitle }}
          onButtonPress={onButtonPress}
        />
      </Provider>
    );

    fireEvent.press(getByTestId("announcement-card-read-more-button"));
    expect(onButtonPress).toHaveBeenCalledWith(cardTitle);
  });

  it("renders fallback image on error", () => {
    const { getByTestId } = render(
      <Provider store={store}>
        <AnnouncementsCard
          testID="announcement-card"
          announcement={{
            ...mockAnnouncement,
            previewDownloadableImageUrl: "https://invalid-url/image.png",
          }}
          onButtonPress={() => { }}
        />
      </Provider>
    );
    // Simulate image error
    fireEvent(getByTestId("announcement-card"), "error");
    // No assertion for image source here, but you can snapshot or check state if needed
  });
});

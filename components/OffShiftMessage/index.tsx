import { View } from 'react-native'
import React from 'react'
import { Heading, Text, useTheme } from "pantry-design-system"
import { t } from 'i18next'
import getStyles from "./styles";
import { SvgXml } from "react-native-svg";
import { TEXT_ALIGN, TEXT_SIZE, TEXT_WEIGHT, TEXTLINK_COLORS } from '../../app/shared/constants';
import { OffShiftIcon } from '../../assets/images/svg/OffShiftIcon';
import { useSelector } from 'react-redux';

const OffShiftMessage = () => {
    const isTablet = useSelector(
        (state: { deviceInfo: { isTablet: boolean } }) => state.deviceInfo.isTablet,
    );
    const { theme } = useTheme();
    const styles = getStyles(theme, isTablet);

    return (
        <View style={styles.container}>
            <View style={styles.mainContainer}>
                <SvgXml
                    xml={OffShiftIcon}
                    testID="off-the-clock-image"
                    style={styles.imageContainer}
                />

                <Heading
                    accessible={true}
                    testID={"off-the-clock-heading"}
                    textAlign={TEXT_ALIGN.CENTER}
                    title={t('youAreOffTheClock')}
                    color={TEXTLINK_COLORS.NEUTRAL_HIGH}
                    size={TEXT_SIZE.MEDIUM}
                />

                <View style={styles.spaceContainer} />

                <Text
                    color={TEXTLINK_COLORS.NEUTRAL_HIGH}
                    size={TEXT_SIZE.LARGE}
                    text={t('relaxAndRecharge')}
                    weight={TEXT_WEIGHT.REGULAR}
                    accessible={true}
                    testID="off-clock-description"
                    textAlign={TEXT_ALIGN.CENTER}
                />
                <View style={styles.spaceContainer} />

                <Text
                    color={TEXTLINK_COLORS.NEUTRAL_HIGH}
                    size={TEXT_SIZE.LARGE}
                    text={t('offTheClockDescription')}
                    weight={TEXT_WEIGHT.REGULAR}
                    accessible={true}
                    testID="off-clock-description"
                    textAlign={TEXT_ALIGN.CENTER}
                />
                <View style={styles.spaceContainer} />
            </View>

        </View>
    )
}

export default OffShiftMessage
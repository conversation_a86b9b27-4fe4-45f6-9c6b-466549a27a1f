import { IPAD_WIDTH } from '../../app/shared/constants';
import { Theme } from 'pantry-design-system';
import { StyleSheet } from "react-native";

const getStyes = ({ colors, dimensions }: Theme, isTablet: boolean) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      maxWidth: IPAD_WIDTH,
      alignSelf: "center",
      justifyContent: "center",
      width: '100%',
      paddingTop: dimensions.pdsGlobalSpace600
    },
    mainContainer: {
      alignSelf: 'center',
      flex: 1,
      maxWidth: IPAD_WIDTH,
      width: '100%',
      paddingHorizontal: dimensions.pdsGlobalSpace800,
    },
    imageContainer: {
      alignSelf: "center",
      color: colors.pdsThemeColorOutlinePrimary,
      marginBottom: dimensions.pdsGlobalSpace400,
    },
    spaceContainer: {
      marginBottom: dimensions.pdsGlobalSpace400,
    },
  });

  return styles;
};

export default getStyes;

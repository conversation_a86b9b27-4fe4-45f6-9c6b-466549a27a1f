import React from 'react';
import { View, TouchableWithoutFeedback, AccessibilityRole } from 'react-native';
import { Switch } from "react-native-switch";
import { useTheme } from 'pantry-design-system'

interface propsType {
  onValueChange: () => void;
  active: boolean;
  accessibilityLabel?: string;
  accessibilityHint?: string;
  accessibilityRole?: AccessibilityRole;
  accessible?: boolean;
  testID?: string;
}

export default function SwitchToggle(props: propsType) {
  const {
    onValueChange,
    active,
    accessibilityLabel,
    accessibilityHint,
    accessibilityRole = 'switch',
    accessible = true,
    testID
  } = props;
  const { theme } = useTheme()
  return (
    <TouchableWithoutFeedback
      onPress={onValueChange}
      accessible={accessible}
      accessibilityLabel={accessibilityLabel}
      accessibilityHint={accessibilityHint}
      accessibilityRole={accessibilityRole}
      testID={testID}
    >
      <Switch
        value={active}
        onValueChange={onValueChange}
        renderActiveText={false}
        renderInActiveText={false}
        circleBorderWidth={1}
        circleSize={25}
        backgroundActive={theme.colors.pdsThemeColorOutlineNeutralHigh}
        circleActiveColor={theme.colors.pdsThemeColorForegroundNeutralHighInverse}
        changeValueImmediately={true}
        switchWidthMultiplier={2}
        switchBorderRadius={99}
      />
    </TouchableWithoutFeedback>
  );
}

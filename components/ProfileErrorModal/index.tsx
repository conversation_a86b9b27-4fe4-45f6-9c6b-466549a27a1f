/**
 * Profile Error Modal - Displays session timeout/error UI with retry option
 * @component
 * @param {Object} route - React Navigation route containing optional params:
 *   @param {string} [headerTitle] - Custom modal title (defaults to localized "oops")
 *   @param {string} [buttonText] - Custom button text (defaults to "Sign In Again")
 * 
 * Features:
 * - Shows profile retrieval error state with moon illustration
 * - Provides retry functionality for profile data fetching
 * - Handles navigation to logout screen
 * - Supports full localization
 * 
 * @example
 * <ProfileErrorModal route={{ params: { 
 *   headerTitle: "Session Expired",
 *   buttonText: "Retry Now" 
 * }}} />
 */

import React from "react";
import { useNavigation } from "@react-navigation/native";
import { moon } from "../../assets/images/svg/LoyaltyAccount";
import { useTranslation } from "react-i18next";
import { SCREENS, CLOSE, ASSOCIATE_PROFILE, TokenTypes } from "../../app/shared/constants";
import SessionModalLayout from "../SessionLayoutModal";
import { FL_TYPES } from "@/app/config/payloadTypes";
import * as AsyncStorage from "../../app/store/AsyncStorage";
import { SecureStorage, TokenType } from "../../app/store/SecureStorage";
import { store } from '../../app/store';
import { fetchProfileRequest, setLoggedIn } from "../../app/store/reducers/profileSlice";
import { useDispatch } from "react-redux";

interface SessionProps {
    route: {
        params?: {
            headerTitle?: string;
            buttonText?: string;
        };
    };
}

const ProfileErrorModal: React.FC<SessionProps> = ({ route }) => {
    const navigation = useNavigation();
    const { t } = useTranslation();
    const dispatch = useDispatch();

    const headerTitle = route?.params && route.params?.headerTitle ? route.params?.headerTitle : t("oopsWeRanIntoProblem");
    const buttonText = route?.params && route.params?.buttonText ? route.params?.buttonText : t("signInAgain");

    // Function to handle navigation back to the sign-in screen
    const backToSignin = (linkName: string) => {
        dispatch(setLoggedIn(false)); // Set logged-in state to false
        navigation.navigate(SCREENS.AUTH_LOGOUT);
    };

    const retryProfile = async () => {
        const flParams: FL_TYPES = ASSOCIATE_PROFILE;
        const requestId = await AsyncStorage.uniqueSessionId();
        const secureStorage = new SecureStorage();
        const empId = await secureStorage.getToken(TokenTypes.employeeId as TokenType);

        store.dispatch(
            fetchProfileRequest({
                requestId,
                fl: flParams,
                empId
            })
        );
    }

    return (
        <SessionModalLayout
            svgXml={moon}
            headerTitle={headerTitle}
            heading={t("havingTroubleRetrievingInformation")}
            description={t("PROFILE_ERROR_DESC")}
            buttonText={buttonText}
            onButtonPress={() => retryProfile()}
            onClosePress={() => backToSignin(CLOSE)}
            testIDPrefix="profile-error-modal"
        />
    );
};

export default ProfileErrorModal;
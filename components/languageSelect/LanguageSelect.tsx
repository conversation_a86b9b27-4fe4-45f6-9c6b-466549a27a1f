import {
  View,
  Platform,
  AccessibilityInfo,
  TouchableOpacity,
} from "react-native";
import React from "react";
import getStyles from "./Styles";
import { useTheme, Text } from "pantry-design-system";
import CRadio from "../radio/CRadio";
import { useTranslation } from "react-i18next";
import { TEXT_ALIGN, TEXT_SIZE, TEXTLINK_COLORS } from "../../app/shared/constants";

const LanguageSelect = ({ languages, changeLanguage, selectLang }: any) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const styles = getStyles(theme);

  const onChangeLanguage = (language: any, index: number) => {
    changeLanguage(language);
    const labelText = t("ada.selectedRadioButtonLabel", {
      label: language.label,
      index: index + 1,
      total: languages.length,
    });
    setTimeout(() => {
      AccessibilityInfo.announceForAccessibility(labelText);
    }, 100);
  };

  const renderLanguageOption = (language: any, index: number) => {
    const isSelected = selectLang?.id === language.id;

    return (
      <TouchableOpacity
        key={language.id}
        style={styles.radiobuttonView}
        onPress={() => onChangeLanguage(language, index)}
        accessibilityLabel={t(
          isSelected
            ? "ada.selectedRadioButtonLabel"
            : "ada.unSelectedRadioButtonLabel",
          {
            label: language.label,
            index: index + 1,
            total: languages.length,
          }
        )}
        accessibilityHint={
          Platform.OS === "ios" && !isSelected
            ? t("ada.languageButtonHint")
            : undefined
        }
        accessible
      >
        <CRadio
          color="black"
          uncheckedColor="gray"
          status={isSelected}
        />
        <Text
          testID={`language-${language.id}`}
          text={language.label}
          size={TEXT_SIZE.LARGE}
          textAlign={TEXT_ALIGN.LEFT}
          color={TEXTLINK_COLORS.NEUTRAL_HIGH}
          accessible={false}
        />
      </TouchableOpacity>
    );
  };
  return (
    <View accessibilityRole="radiogroup">
      {languages.map(renderLanguageOption)}
    </View>
  );
}

export default LanguageSelect
import { Theme } from 'pantry-design-system';
import { Platform, StyleSheet } from "react-native";

export default ({ colors, fonts, typography, dimensions }: Theme) => {
  return StyleSheet.create({
    radiobuttonView:{
      flexDirection: "row", alignItems: "center", padding: dimensions.pdsGlobalSpace200
    },
    circle: {
      width: 40,
      height: 40,
      borderRadius: 100,
      borderWidth: 3,
      borderColor: "#004488",
      margin: "auto",
    },
    selectLanguage: {
      fontSize: 22,
      color: colors.pdsThemeColorOutlineNeutralHigh,
      fontWeight: typography.pdsGlobalFontWeight500,
      textAlign: "center",
      marginTop: 16,
      marginBottom: 32,
    },
    languageText: {
      fontSize: typography.pdsGlobalFontSize300,
      fontWeight: typography.pdsGlobalFontWeight600,
      color: colors.pdsThemeColorOutlineNeutralHigh,
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
    },
    updateBtn: {
      marginVertical: 24,
      backgroundColor: colors.pdsThemeColorBackgroundPrimary,
      height: 40,
      justifyContent: "center",
    },
    btnText: {
      fontWeight: typography.pdsGlobalFontWeight600,
      fontSize: 16,
      fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
      color: colors.pdsThemeColorForegroundOnPrimary
    },

  });
}

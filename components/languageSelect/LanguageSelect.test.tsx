import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import LanguageSelect from './LanguageSelect';
import { AccessibilityInfo } from 'react-native';

// Mocks
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string, options?: Record<string, any>) =>
      options?.label
        ? `${key} (${options.label} ${options.index}/${options.total})`
        : key,
  }),
}));

jest.mock("pantry-design-system", () => ({
  useTheme: jest.fn(() => ({
    theme: {
      colors: {
        pdsThemeColorBackgroundPrimary: "#000000",
        pdsThemeColorNeutralLow: "#FFFFFF",
      },
      dimensions: {
        pdsGlobalSpace1000: 10,
        pdsGlobalSpace1600: 15,
      },
      typography: {
        pdsGlobalFontSize500: 18,
        pdsGlobalFontSize600: 20,
      },
      fonts: {
        pdsGlobalFontFamilyPoppins: "Poppins",
      },
      borderDimens: {
        pdsGlobalBorderRadius200: 8,
      },
    },
  })),
  Heading: jest.fn(({ title, testID }) => <text testID={testID}>{title}</text>),
  Text: jest.fn(({ text, testID }) => <text testID={testID}>{text}</text>),
  TextLink: jest.fn(({ text, onPress, testID }) => (
    <text onPress={onPress} testID={testID}>
      {text}
    </text>
  )),
  Button: jest.fn(({ onPress, label, testID }) => (
    <button onClick={onPress} testID={testID}>
      {label}
    </button>
  )),
}));


jest.mock('../radio/CRadio', () => {
  return ({ status }: { status: boolean }) => (
    <mock-radio testID={status ? 'radio-selected' : 'radio-unselected'} />
  );
});

jest.useFakeTimers();

describe('LanguageSelect', () => {
  const mockLanguages = [
    { id: 'en', label: 'English' },
    { id: 'es', label: 'Spanish' },
    { id: 'fr', label: 'French' },
  ];

  const mockChangeLanguage = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders all languages', () => {
    const { getByTestId } = render(
      <LanguageSelect
        languages={mockLanguages}
        selectLang={mockLanguages[0]}
        changeLanguage={mockChangeLanguage}
      />
    );

    expect(getByTestId('language-en')).toBeTruthy();
    expect(getByTestId('language-es')).toBeTruthy();
    expect(getByTestId('language-fr')).toBeTruthy();
  });

  it('shows correct radio status for selected language', () => {
    const { getAllByTestId } = render(
      <LanguageSelect
        languages={mockLanguages}
        selectLang={mockLanguages[0]}
        changeLanguage={mockChangeLanguage}
      />
    );

    const selectedRadios = getAllByTestId('radio-selected');
    expect(selectedRadios.length).toBe(1);
  });

  it('calls changeLanguage and announces accessibility message when a language is selected', () => {
    const announceSpy = jest.spyOn(AccessibilityInfo, 'announceForAccessibility');

    const { getByTestId } = render(
      <LanguageSelect
        languages={mockLanguages}
        selectLang={mockLanguages[0]}
        changeLanguage={mockChangeLanguage}
      />
    );

    const spanishButton = getByTestId('language-es').parent;

    fireEvent.press(spanishButton!);

    expect(mockChangeLanguage).toHaveBeenCalledWith(mockLanguages[1]);

    // Simulate delayed accessibility announcement
    jest.runAllTimers();
    expect(announceSpy).toHaveBeenCalledWith(
      'ada.selectedRadioButtonLabel (Spanish 2/3)'
    );
  });
});

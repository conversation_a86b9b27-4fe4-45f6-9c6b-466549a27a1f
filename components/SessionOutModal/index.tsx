import React, { useEffect, useRef } from "react";
import { AppState } from "react-native";
import { useFocusEffect, useNavigation } from "@react-navigation/native";
import { moon } from "../../assets/images/svg/LoyaltyAccount";
import { useTranslation } from "react-i18next";
import { SCREENS, CLOSE } from "../../app/shared/constants";
import { screenViewLog, userActionLogEvent } from "../../analytics/AnalyticsUtils";
import { LOGIN_ANALYTICS } from "../../analytics/AnalyticsConstants";
import SessionModalLayout from "../SessionLayoutModal";
import { useDispatch } from "react-redux";
import { setLoggedIn } from "../../app/store/reducers/profileSlice";

interface SessionProps {
  route: {
    params?: {
      headerTitle?: string;
      buttonText?: string;
    };
  };
}

const SessionOutModal: React.FC<SessionProps> = ({ route }) => {
  const navigation = useNavigation();
  const { t } = useTranslation();
  const dispatch = useDispatch();

  const appState = useRef(AppState.currentState);

  const headerTitle = route?.params && route.params?.headerTitle ? route.params?.headerTitle : t("oopsWeRanIntoProblem");
  const buttonText = route?.params && route.params?.buttonText ? route.params?.buttonText : t("signInAgain");

  // Function to handle navigation back to the sign-in screen
  const backToSignin = (linkName: string) => {
    //  navigate to the AUTH_LOGOUT
    userActionLogEvent(LOGIN_ANALYTICS.FORGOT_PASSWORD, linkName, LOGIN_ANALYTICS.SESSION_EXPIRED_SCREEN);
    dispatch(setLoggedIn(false)); // Set logged-in state to false
    navigation.navigate(SCREENS.AUTH_LOGOUT);
  };

  useFocusEffect(
    React.useCallback(() => {
      screenViewLog({ subsection1: LOGIN_ANALYTICS.LOGIN, subsection2: LOGIN_ANALYTICS.SESSION_EXPIRED, });
    }, [])
  );

  useEffect(() => {
    // Add an event listener to monitor changes in the app's state (foreground/background)
    const subscription = AppState.addEventListener('change', nextAppState => {
      // Check if the app is transitioning from inactive/background to active state
      if (
        appState.current.match(/inactive|background/) &&
        nextAppState === 'active'
      ) {
        screenViewLog({ subsection1: LOGIN_ANALYTICS.LOGIN, subsection2: LOGIN_ANALYTICS.SESSION_EXPIRED, });
      }
      // Update the current app state
      appState.current = nextAppState;
    });

    // Cleanup function to remove the event listener when the component unmounts
    return () => {
      subscription.remove();
    };
  }, []);

  return (
    <SessionModalLayout
      svgXml={moon}
      headerTitle={headerTitle}
      heading={t("signedOutHeading")}
      description={t("AUTH_ERROR_DESC")}
      buttonText={buttonText}
      onButtonPress={() => backToSignin(LOGIN_ANALYTICS.BACK_TO_SIGN_IN)}
      onClosePress={() => backToSignin(CLOSE)}
      testIDPrefix="session-out"
    />
  );
};

export default SessionOutModal;

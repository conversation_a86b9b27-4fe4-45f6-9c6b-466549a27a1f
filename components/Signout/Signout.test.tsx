import React from "react";
import { render } from "@testing-library/react-native";
import SignOut from "./SignOut";
import { Text } from "react-native";

jest.mock("react-i18next", () => ({
  useTranslation: () => ({ t: (key) => key }),
}));

jest.mock("pantry-design-system", () => ({
  useTheme: () => ({
    theme: {
      dimensions: {},
      colors: {},
      fonts: {},
      typography: {},
      borderDimens: {},
    },
  }),
  Heading: jest.fn(({ title, testID }) => <text testID={testID}>{title}</text>),
  BottomSheet: ({ visibility, renderHeader, renderContent, renderAction }) =>
    visibility ? (
      <>
        {renderHeader}
        {renderContent}
        {renderAction}
      </>
    ) : null,
  Button: ({ onPress, label, testID }) => (
    <button onClick={onPress} data-testid={testID}>
      {label}
    </button>
  ),
  Heading: ({ title }: { title: string }) => {title},
}));

describe("SignOut Component", () => {
  it("renders correctly when modal is open", () => {
    const { getByTestId } = render(<SignOut visibility={true} />);
    expect(getByTestId("sub-heading")).toBeTruthy();
  });
});

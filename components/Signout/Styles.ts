import { Theme } from 'pantry-design-system';
import { StyleSheet } from "react-native";

const getStyles = ({ colors, fonts, typography }: Theme) => {
    const Styles = StyleSheet.create({
        description: {
            textAlign: "center",
            alignSelf: "center",
            fontSize: typography.pdsGlobalFontSize300,
            marginTop: 15,
            height: 80,
            width: "90%",
            fontWeight: typography.pdsGlobalFontWeight400,
            color: colors.pdsThemeColorForegroundNeutralHigh,
            fontFamily: fonts.pdsGlobalFontFamilyNunitoSans,
        },
    });
    return Styles
}

export default getStyles;

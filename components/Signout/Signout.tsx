import { View, Text } from "react-native";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { useTheme, BottomSheet, Heading, Button } from "pantry-design-system";
import GetStyles from "./Styles";
import Ionicons from "react-native-vector-icons/Ionicons";

const SignOut = ({ visibility = false }) => {
    const { t } = useTranslation();
    const { theme } = useTheme();
    const styles = GetStyles(theme);
    const [openModal, setOpenModal] = useState(visibility);
    const closeModal = () => setOpenModal(false);
    const confirmSignInAgain = () => closeModal();

    return (
        <BottomSheet
            sidePadding
            variant="Modal"
            theme={theme}
            visibility={openModal}
            onClose={() => {
                setOpenModal(false);
            }}
            renderHeader={
                <View accessible={true} accessibilityRole="image" accessibilityLabel={t("signedOutHeading")}>
                    <Ionicons
                        name="warning-outline"
                        style={{ marginBottom: 12, textAlign: "center" }}
                        size={theme.dimensions.pdsGlobalSizeWidth800}
                        color={theme.colors.pdsThemeColorBackgroundPrimary}
                    />
                    <Heading
                        testID="heading"
                        textAlign="center"
                        title={`${t("signedOutHeading")}`}
                        color='Neutral high'
                        size="Small"
                    />
                </View>
            }
            renderContent={
                <Text accessible={true} accessibilityRole="text" accessibilityLabel={t("signedOutDescription")} testID="sub-heading" style={styles.description}>{`${t("signedOutDescription")}`}</Text>
            }
            renderAction={
                <Button
                    testID={'sign-in-again-button'}
                    fullWidth
                    theme={theme}
                    size={"Small"}
                    label={t("signInAgain")}
                    onPress={confirmSignInAgain}
                    accessible={true}
                />
            }
        />
    );
};

export default SignOut;

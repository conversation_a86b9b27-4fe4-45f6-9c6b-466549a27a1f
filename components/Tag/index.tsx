/**
 * Tag Component
 *
 * This functional component renders a styled tag text. It supports customization
 * through props such as `color`, `size`, `weight`, and `style`. The component
 * uses the `pantry-design-system` for consistent design and theming.
 *
 * Props:
 * - text (string): The text to be displayed inside the tag. If not provided, the component will render nothing.
 * - style (object, optional): Custom styles for the container view.
 * - color (string, optional): The color of the text. Defaults to `TEXTLINK_COLORS.NEUTRAL_HIGH`.
 * - size (string, optional): The size of the text. Defaults to `TEXTLINK_SIZE.SMALL`.
 * - weight (string, optional): The weight of the text. Defaults to `TEXTLINK_WEIGHT.REGULAR`.
 *
 * @param text - The text to be displayed.
 * @param style - Custom styles for the container.
 * @param color - The color of the text.
 * @param size - The size of the text.
 * @param weight - The weight of the text.
 * @returns A React element containing a styled tag text, or null if no text is provided.
 */

import React from "react";
import { View } from "react-native";
import { Text } from "pantry-design-system";
import { TEXTLINK_SIZE, TEXTLINK_COLORS, TEXTLINK_WEIGHT } from "../../app/shared/constants";

interface TagProps {
    text: string;
    style?: object;
    color?: string;
    size?: string;
    weight?: string;
    accessible?: boolean;
}

export default function Tag({
    text,
    style,
    color = TEXTLINK_COLORS.NEUTRAL_HIGH,
    size = TEXTLINK_SIZE.SMALL,
    weight = TEXTLINK_WEIGHT.REGULAR,
    accessible = true,
}: TagProps) {
    if (!text) {
        return null;
    }

    return (
        <View style={style} accessibilityLabel="tag-container">
            <Text
                accessible={accessible}
                color={color}
                size={size}
                weight={weight}
                textDecoration="none"
                text={text}
            />
        </View>
    );
}
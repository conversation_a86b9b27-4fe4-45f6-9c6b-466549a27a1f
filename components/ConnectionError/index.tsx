/**
 * Displays a connection error placeholder with retry option
 * @component
 * @param {Function} handleTryagain - Callback for retry button press
 * @example
 * <ConnectionError handleTryagain={fetchData} />
 *
 * Features:
 * - Shows server connection error illustration
 * - Provides localized error messages
 * - Includes accessible retry button
 */
import React from 'react';
import { useTranslation } from 'react-i18next';
import { SafeAreaView } from 'react-native-safe-area-context';

import { NoServer } from '../../assets/images/svg/NoServerConnection';
import StatusPlaceholder from '../StatusPlaceholder/StatusPlaceholder';

type ConnectionErrorProps = {
    handleTryagain: () => void;
};

const ConnectionError: React.FC<ConnectionErrorProps> = ({ handleTryagain }) => {
    const { t } = useTranslation();

    const retryAgain = () => {
        handleTryagain();
    };

    return (
        <SafeAreaView edges={['top', 'bottom']}>
            <StatusPlaceholder
                source={NoServer}
                status={t('errorPlaceholder.connectionErrorSubtitle')}
                subHeader={t('errorPlaceholder.connectionError')}
                styles={{}}
                showButton={true}
                buttonText={t('errorPlaceholder.tryAgain')}
                onPress={retryAgain}
                buttonAccessHint={t('schedule.retryServerAccessibilityHint')}
            />
        </SafeAreaView>
    );
};

export default ConnectionError;

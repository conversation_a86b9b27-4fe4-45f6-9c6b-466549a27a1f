import { StyleSheet } from 'react-native';

import type { Theme } from 'pantry-design-system';
import type { TextStyle } from 'react-native';

//customising styles based on themes
const getStyles = ({ colors, fonts, typography, dimensions }: Theme) => {
    const styles = StyleSheet.create({
        container: {
            alignItems: 'center',
            backgroundColor: colors.pdsThemeColorBackgroundRaised,
            flexDirection: 'row',
            height: 50,
            justifyContent: 'space-between',
            paddingLeft: dimensions.pdsGlobalSpace400,
            paddingRight: dimensions.pdsGlobalSpace300,
        },
        headerRightContainer: {
            alignSelf: 'center',
            marginRight: dimensions.pdsGlobalSpace300,
        },
        headerText: {
            color: colors.pdsThemeColorForegroundNeutralHigh,
            fontStyle: 'normal',
            fontWeight: typography.pdsGlobalFontWeight500,
            display: 'flex',
            textAlign: 'center',
            fontFamily: fonts.pdsGlobalFontFamilyPoppins,
        } as TextStyle,
        headerTitle: {
            justifyContent: 'center',
            alignSelf: 'center',
            overflow: 'hidden', // Hide overflow text
            maxWidth: '70%',
        },
        placeholderLeftSpace: {
            width: '15%',
        },
        placeholderRightSpace: {
            alignItems: 'flex-end',
            width: '15%',
        },
        safeContainer: {
            backgroundColor: colors.pdsThemeColorBackgroundRaised,
            paddingHorizontal: dimensions.pdsGlobalSpace500,
        },
    });

    return styles;
};

export default getStyles;

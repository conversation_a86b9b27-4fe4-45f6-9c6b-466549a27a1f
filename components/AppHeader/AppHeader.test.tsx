import { render } from '@testing-library/react-native';
import { ThemeProvider } from 'pantry-design-system';
import React from 'react';
import { Text } from 'react-native'; // Import missing Text component
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';

import AppHeader from '../AppHeader'; // Adjust path if needed

jest.mock('../../app/features/omniHeader/omniHeader', () => jest.fn(() => null)); // Mock OmniHeader

jest.mock('../../app/providers/AccessibilityFocus', () => ({
  useAccessibilityFocus: () => ({
    setRef: jest.fn(),
    setLastFocused: jest.fn(),
    handleScreenFocus: jest.fn(),
    isNavigatingBack: jest.fn(),
  }),
  AccessibilityFocusProvider: ({ children }) => children,
}));

// Mock Redux Store
const mockStore = configureStore([]);
const initialState = { deviceInfo: { isTablet: false } };

// Mock theme provider
const mockTheme = {
  colors: {
    primary: 'blue',
    background: 'white',
    text: 'black',
    pdsThemeColorForegroundNeutralHigh: 'black',
  },
  fonts: { pdsGlobalFontFamilyPoppins: 'Poppins' },
  dimensions: {},
  typography: {
    pdsGlobalFontSize500: 16, // Add missing typography properties
    pdsGlobalFontWeight500: 'bold',
  },
};

jest.mock('pantry-design-system', () => ({
  Heading: jest.fn(({ title, testID }) => <text testID={testID}>{title}</text>),
  useTheme: () => ({ theme: mockTheme }),
  ThemeProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
}));

const renderWithProviders = (component: React.ReactElement, storeState = initialState) => {
  const store = mockStore(storeState);
  return render(
    <Provider store={store}>
      <ThemeProvider>{component}</ThemeProvider>
    </Provider>,
  );
};

describe('AppHeader Component', () => {
  it('should render AppHeader with provided props', () => {
    const headerLeftMock = <Text testID="children-left">Left</Text>;
    const headerRightMock = <Text testID="children-right">Right</Text>;

    const { getByTestId } = renderWithProviders(
      <AppHeader
        headerTitle="Test Title"
        headerLeft={headerLeftMock}
        headerRight={headerRightMock}
      />,
    );

    expect(getByTestId('header-title')).toHaveTextContent('Test Title');
    expect(getByTestId('header-right')).toBeTruthy();
  });

  it('renders the headerTitle correctly when it is a string ', () => {
    const testTitle = 'Test Header screen header';
    const { getByTestId } = renderWithProviders(<AppHeader headerTitle={testTitle} />);
    const titleText = getByTestId('header-title');
    expect(titleText).toBeTruthy();
    expect(titleText.props.children).toBe(testTitle);
  });

  it('renders the headerTitle correctly when it is a React node ', () => {
    const customTitle = <Text testID="custom-title">Custom Header</Text>;
    const { getByTestId } = renderWithProviders(<AppHeader headerTitle={customTitle} />);
    const customTitleElement = getByTestId('custom-title');
    expect(customTitleElement).toBeTruthy();
    expect(customTitleElement.props.children).toBe('Custom Header');
  });
});

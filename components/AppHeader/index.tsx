/**
 * AppHeader
 *
 * A reusable header component for stack screens, supporting left, center, and right content.
 *
 * Props:
 * - headerLeft?: React.ReactNode - Optional node to render on the left (e.g., back button).
 * - headerTitle?: React.ReactNode | string - Title to display in the center of the header.
 * - headerRight?: React.ReactNode - Optional node to render on the right (e.g., actions).
 * - headerTitleAlign?: "left" | "center" | "right" - Alignment for the header title.
 * - isOmniheaderPresent?: boolean - If true, renders the OmniHeader above the main header.
 * - size?: "XXSmall" | "XSmall" | "Small" | "Medium" | "Large" - Size for the header title.
 *
 * Accessibility:
 * - The header title is marked as accessible and includes an accessibility label for screen readers.
 *
 * Usage Example:
 *   <AppHeader
 *     headerLeft={<ProfileHeaderLeft ... />}
 *     headerTitle="Profile"
 *     headerRight={<SettingsIcon />}
 *     isOmniheaderPresent={false}
 *   />
 */

import { t } from 'i18next';
import { Heading, useTheme } from 'pantry-design-system';
import React, { useRef, useEffect } from 'react';
import { View, findNodeHandle, AccessibilityInfo, Platform } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import OmniHeader from '../../app/features/omniHeader/omniHeader';
import { useAccessibilityFocus } from '../../app/providers/AccessibilityFocus';
import { ImportantForAccessibility } from '../../app/shared/constants';
import { useSelector } from 'react-redux';
import getStyles from './styles';

interface Props {
  headerLeft?: React.ReactNode;
  headerTitle?: React.ReactNode | string;
  headerRight?: React.ReactNode;
  headerTitleAlign?: 'left' | 'center' | 'right';
  isOmniheaderPresent?: boolean;
  size?: 'XXSmall' | 'XSmall' | 'Small' | 'Medium' | 'Large';
  accessibilityLabel?: string;
  accessible?: boolean;
}

const AppHeader: React.FC<Props> = ({
  headerLeft,
  headerTitle,
  headerTitleAlign,
  headerRight,
  isOmniheaderPresent = true,
  size = 'XSmall',
  accessibilityLabel = headerTitle,
  accessible = true,
}) => {
  const { theme } = useTheme(); // Fetching themes from ThemeProvider
  const styles = getStyles(theme); // Fetching styles based on theme
  const leftRef = useRef(null);
  const titleRef = useRef(null);
  const rightRef = useRef(null);
  const { isNavigatingBack } = useAccessibilityFocus();
  const preventHeaderAccessibility = useSelector(
    (state: any) => state?.accessibility?.preventHeaderAccessibility
  );
  useEffect(() => {
    // on navigating back focus will be restored
    if (isNavigatingBack) return;

    // on navigating forward, first element will be focused
    let targetRef = null;
    if (headerLeft) {
      targetRef = leftRef;
    } else if (headerTitle) {
      targetRef = titleRef;
    } else if (headerRight) {
      targetRef = rightRef;
    }
    if (
      targetRef &&
      typeof targetRef !== 'function' &&
      (targetRef as React.RefObject<View>).current
    ) {
      const timeout = setTimeout(
        () => {
          const tag = findNodeHandle((targetRef as React.RefObject<View>).current);
          if (tag) AccessibilityInfo.setAccessibilityFocus(tag);
        },
        Platform.OS === 'android' ? 700 : 300,
      );
      return () => clearTimeout(timeout);

    }
  }, [headerLeft, headerTitle, isNavigatingBack]);


  const insets = useSafeAreaInsets();
  isOmniheaderPresent = false //TODO: later will be enabled again as part of new story honoring the user clock in status

  return (
    <>
      {isOmniheaderPresent ? (
        <OmniHeader />
      ) : (
        <View style={[styles.safeContainer, { paddingTop: insets.top }]} />
      )}
      <View
        style={styles.container}
        accessibilityElementsHidden={preventHeaderAccessibility}
        importantForAccessibility={
          preventHeaderAccessibility
            ? ImportantForAccessibility.NO_HIDE
            : ImportantForAccessibility.AUTO
        }
      >
        {headerLeft ? (
          <View ref={leftRef}>{headerLeft}</View>
        ) : headerTitleAlign ? null : (
          <View style={styles.placeholderLeftSpace} />
        )}

        {headerTitle && (
          <View
            ref={titleRef}
            style={styles.headerTitle}
            accessible={preventHeaderAccessibility ? false : accessible}
            accessibilityLabel={`${accessibilityLabel} ${t('ada.heading')}`}
            accessibilityHint={''}
          >
            {typeof headerTitle === 'string' ? (
              <Heading
                testID={'header-title'}
                textAlign="center"
                title={headerTitle}
                color="Neutral high"
                size={size}
                allowFontScaling={false}
                accessible={false}
              />
            ) : (
              <View ref={titleRef}>{headerTitle}</View>
            )}
          </View>
        )}

        {headerRight ? (
          <View ref={rightRef} style={styles.headerRightContainer} testID="header-right">
            {headerRight}
          </View>
        ) : (
          <View style={styles.placeholderRightSpace} />
        )}
      </View>
    </>
  );
};

export default AppHeader;
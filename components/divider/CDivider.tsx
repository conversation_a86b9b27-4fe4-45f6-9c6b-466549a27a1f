import { View } from "react-native";
import React from 'react';
import { Divider } from "react-native-paper";
import { useTheme } from 'pantry-design-system'

interface propsType {
  color?: string;
  height?: number;
}
export default function CDivider(props: propsType) {
  const { theme } = useTheme();
  const { colors } = theme
  const { color = colors.pdsThemeColorOutlineNeutralLow, height = 1 } = props;
  return (
    <View>
      <Divider style={{ backgroundColor: color, height: height }} />
    </View>
  );
}

/**
 * EmptyStateCard Component
 *
 * This functional React component is designed to display an empty state card
 * with a visual representation (SVG image) and accompanying text. It is useful
 * for scenarios where a feature or functionality is not yet available or when
 * there is no data to display.
 *
 * Features:
 * - Displays an SVG image to visually represent the empty state.
 * - Includes a heading and description text to provide context to the user.
 * - Supports internationalization using the `useTranslation` hook.
 * - Adapts styles dynamically based on the current theme using the `useTheme` hook.
 *
 * Use Cases:
 * - Indicating that a feature is "coming soon."
 * - Informing users about the absence of data or content in a specific section.
 *
 * Props:
 * - This component does not accept any props.
 *
 * Dependencies:
 * - `useTheme` for theme-based styling.
 * - `useTranslation` for internationalized text.
 */
import React from "react";
import { View } from "react-native";
import { useTheme, Heading, Text } from "pantry-design-system";
import { shopping } from "../../assets/images/svg/shopping";
import { SvgXml } from "react-native-svg";
import getStyles from "./styles";
import { useTranslation } from "react-i18next";
import { TEXTLINK_SIZE, TEXTLINK_COLORS, TEXTLINK_WEIGHT } from "../../app/shared/constants";

type EmptyStateCardProps = {
    title: string;
    accessibilityLabel: string;
    text?: string;
};

const EmptyStateCard: React.FC<EmptyStateCardProps> = ({title, accessibilityLabel, text}) => {
    const { theme } = useTheme();
    const styles = getStyles(theme);
    const { t } = useTranslation();

    return (
        <View style={styles.container}>
            {/* Image Container */}
            <View style={styles.imageContainer}>
                <SvgXml
                    xml={shopping}
                    style={styles.image}
                    testID="image-shopping"
                />
            </View>

            {/* Text Container */}
            <View style={styles.textContainer}>
                {/* heading Text */}
                <View accessible accessibilityLabel={accessibilityLabel}>
                    <Heading
                        testID={"comming-soon-feature-text-heading"}
                        color={TEXTLINK_COLORS.NEUTRAL_MEDIUM}
                        size={TEXTLINK_SIZE.XX_SMALL}
                        title={title}
                    />
                </View>
                {/* vertical Spacer */}
                <View style={styles.verticalSpacing} />
                {/* Description Text */}
                <Text
                    accessible
                    testID="comming-soon-description"
                    color={TEXTLINK_COLORS.NEUTRAL_MEDIUM}
                    size={TEXTLINK_SIZE.SMALL}
                    weight={TEXTLINK_WEIGHT.REGULAR}
                    textDecoration="None"
                    text={text}
                />
            </View>
        </View>
    );
};

export default EmptyStateCard;

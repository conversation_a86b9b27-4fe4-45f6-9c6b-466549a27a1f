import React from "react";
import { render } from "@testing-library/react-native";
import EmptyStateCard from "../EmptyStateCard";
import { useTheme } from "pantry-design-system";


// Mock theme provider to avoid errors from missing context
jest.mock("pantry-design-system", () => ({
  useTheme: jest.fn(() => ({
    theme: {
      colors: {
        pdsThemeColorBackgroundBaseline: "#FFFFFF",
      },
      borderDimens: {
        pdsGlobalBorderRadius200: 8,
      },
      dimensions: {
        pdsGlobalSpace400: 16,
        pdsGlobalSpace200: 8,
        pdsGlobalSizeWidth800: 24,
      }
    },
  })),
  Heading: jest.fn(({ title, testID }) => <text testID={testID}>{title}</text>),
  Text: jest.fn(({ text, testID }) => <text testID={testID}>{text}</text>),
}));



describe("Empty State Card Component", () => {
  it("renders the image with correct testID", () => {
    const { getByTestId } = render(<EmptyStateCard />);
    const image = getByTestId("image-shopping");
    expect(image).toBeTruthy();
  });

  it("renders the heading text with correct testID and content", () => {
    const { getByTestId } = render(<EmptyStateCard />);
    const heading = getByTestId("comming-soon-feature-text-heading");
    expect(heading).toBeTruthy();
  });

  it("renders the description text with correct testID and content", () => {
    const { getByTestId } = render(<EmptyStateCard />);
    const description = getByTestId("comming-soon-description");
    expect(description).toBeTruthy();
  });

});

import { IPAD_WIDTH } from "../../app/shared/constants";
import { Theme } from "pantry-design-system";
import { StyleSheet } from "react-native";

//customising styles based on themes
const getStyles = (
    { colors, borderDimens, dimensions, fonts, typography }: Theme,
) => {
    const styles = StyleSheet.create({
        /**
         * Main container for the screen.
         * Centers content and limits width for larger screens.
         */
        container: {
            flexDirection: 'row',
            borderRadius: borderDimens.pdsGlobalBorderRadius200,
            padding: dimensions.pdsGlobalSpace400,
            alignSelf: "center",
            maxWidth: IPAD_WIDTH,
            width: '100%',
            marginVertical: dimensions.pdsGlobalSpace400,
        },
        imageContainer: {
            alignSelf: 'flex-start',
            flexShrink: 1, // Allow the container to shrink based on content
            marginRight: dimensions.pdsGlobalSpace200,
        },
        image: {
            aspectRatio: 1, // Maintain aspect ratio (square image)
            resizeMode: 'contain', // Ensure the image fits within the container
            //  backgroundColor: 'red'
        },
        textContainer: {
            flex: 1, // Take remaining space
            //backgroundColor: 'green',
        },
        verticalSpacing: {
            height: dimensions.pdsGlobalSpace200,
        }
    });

    return styles;
};

export default getStyles;

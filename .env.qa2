BASE_HOST_URL: 'https://www-qa2.safeway.com'
MSAUTH_REDIRECT_URL: 'https://www-qa2.safeway.com/diaa-associate-app-server/auth/callback'
MSAUTH_LOGOUT_REDIRECT_URL: 'https://www-qa2.safeway.com/diaa-associate-app-server/auth/logout'
MSAUTH_SCOPES_GRAPH: 'https://graph.microsoft.com/.default'
MSAUTH_LOGIN_SCOPE: 'offline_access api://ed59701c-2647-4545-a50b-774cf43ebe86/Files.Read'
MSAUTH_ISSUER: 'https://login.microsoftonline.com/b7f604a0-00a9-4188-9248-42f3a5aac2e9/v2.0/qa2'
MSAUTH_AUTHORIZATION_ENDPOINT: 'https://login.microsoftonline.com/b7f604a0-00a9-4188-9248-42f3a5aac2e9/oauth2/v2.0/authorize'
MSAUTH_END_SESSION_ENDPOINT: 'https://login.microsoftonline.com/common/oauth2/logout'
MSAUTH_TOKEN_ENDPOINT: 'https://login.microsoftonline.com/b7f604a0-00a9-4188-9248-42f3a5aac2e9/oauth2/v2.0/token'
MSAUTH_REWARD_TOKEN_ENDPOINT: 'https://login.microsoftonline.com/b7f604a0-00a9-4188-9248-42f3a5aac2e9/oauth2/token'
EDIT_MY_ACI_WEB_LINK: 'https://eofd-dev3.fa.us6.oraclecloud.com/fscmUI/faces/deeplink?objType=EMP_CONTACT_INFO&action=NONE'
ENROLLMENT_AND_CHANGES: 'https://eofd-dev3.fa.us6.oraclecloud.com/hcmUI/faces/FndOverview?fnd=%3B%3B%3B%3Bfalse%3B256%3B%3B%3B&fndGlobalItemNodeId=itemNode_my_information_benefits'
MY_BENEFITS: 'https://eofd-dev3.fa.us6.oraclecloud.com/fscmUI/redwood/hcm_associate_resources/main'
GET_PAYSTUBS: 'https://albehcm-u1t.opc.albertsons.com//psc/ALBEHU1T/SELFSERV/HRMS/c/PY_EMPL_FL.PY_IC_PAY_INQ_FLU.GBL'
UPDATE_DIRECT_DEPOSIT_ACCOUNT: 'https://albehcm-tst.opc.albertsons.com/psc/ALBEHTST/EMPLOYEE/HRMS/c/SWY_UPG_MENU.S_DDP.GBL?NavColl=true&ICAGTarget=start&PAGE=S_DDP'
LOST_OR_DAMAGED_CHECK: 'https://powerforms.docusign.net/d3a500d4-49ae-45dd-9ff3-10e275ec2bfb?accountId=b6eb85b2-b69a-46a7-b025-0ba879cbbd11&env=na3'
TAX_CHANGE_WITHHOLDINGS: 'https://eofd-dev3.fa.us6.oraclecloud.com/fscmUI/faces/deeplink?objType=VIEW_RES_TAX_FORM_US&action=NONE'
DEEP_LINK_UMA_REWARDS_TAB: 'https://{banner}qa.onelink.me/PMmj?pid=Email&c=app2appTest&af_web_dp=https%3A%2F%2Fwww-qa2.{banner}.com&af_dp={banner}App%3A%2F%2F%3Fpushsection%3DRewards'
TERMS_OF_USE_PDF: 'https://www-qa2.albertsons.com/content/dam/data/associateapp/settings/Associate_App_TOU_V1.pdf'
PRIVACY_POLICY: 'https://www.albertsons.com/mobile/disclaimers/associate-privacy-policy.html'
HRANDPAYROLL_URL: 'https://www.albertsonscompanies.com/policies-and-disclosures/privacy-policy/default.aspx'
HEALTH_AND_WELL_BEING: 'https://myaci-benefits.com/health/'
LEAVE_OF_ABSENCE: 'https://myaci-benefits.com/time-off/loa-landing/'
FINANCIAL_WELLNESS: 'https://myaci-benefits.com/401k-financialwellness/'
BENEFIT_CONTACTS: 'https://myaci-benefits.com/resources/contacts'
MY_ACI: 'https://eofd.fa.us6.oraclecloud.com/fscmUI/faces/FuseMobileWelcome'
GET_TAX_DOCUMENTS: 'https://my.adp.com/static/redbox/?#/pay/tax-statements'
UPDATE_PASSWORD: 'https://mysignins.microsoft.com/security-info/password/change'
FORGOT_PASSWORD: 'https://identity.safeway.com/identityiq/login.jsf?prompt=true'
FIND_MY_LDAP: 'https://safeway.service-now.com/kb_view.do?sysparm_article=KB0104339'
FIND_MY_EMPLOYEE_ID: 'https://safeway.service-now.com/kb_view.do?sysparm_article=KB0104339'
OTHER_SIGN_IN_ISSUES: 'https://safeway.service-now.com/kb_view.do?sysparm_article=KB0072076'
FEEDBACK: 'https://forms.office.com/r/VspEmqLFWW'
ACI_PRIVACY_POLICY: 'https://www.albertsonscompanies.com/policies-and-disclosures/privacy-policy/default.aspx'
VIEW_CAREERS: 'https://eofd.fa.us6.oraclecloud.com/hcmUI/CandidateExperience/en/sites/CX_1001'
SEND_FEEDBACK: 'https://forms.office.com/r/VspEmqLFWW'
CREATE_ACCOUNT: 'https://www.{bannerName}.com/account/short-registration.html'
LEARNING: 'https://aci.read.inkling.com/read/'
ASSIGNED_LEARNINGS: 'https://eofd-dev3.fa.us6.oraclecloud.com/fscmUI/redwood/learner/learn/learn-landing?tab=myLearnExperience'
JOURNEYS: 'https://eofd.fa.us6.oraclecloud.com/fscmUI/redwood/worker-journeys?tab=myJourneys'

MSAUTH_CLIENT_ID: 'ed59701c-2647-4545-a50b-774cf43ebe86'
MSAUTH_CLIENT_SECRET: '****************************************'
********************************** '94b5d7ad72294383b312743156235e8e'
OCP_APIM_SUBSCRIPTION_FEATURE_FLAG_KEY: '96550c20a84b4684b2c90858380b2305'
FEATURE_FLAG_API_PATH: '/abs/acceptancepub'
OCP_APIM_SUBSCRIPTION_XAPI_KEY: '13945664a99343ef875a48fc660b7be3'
OCP_APIM_SUBSCRIPTION_EMPLOYEE_DISCOUNT_KEY: '4e1fdce9265a41cf87c4df9bea4ecef4'

REWARDS_API_PATH: '/abs/acceptancepub'
XAPI_BASE_PATH: '/abs/acceptanceint/xapi/associate/'
ADAM_IMG_PATH: 'https://images.albertsons-media.com/is/image/ABSStage/'
APPCONFIG_URL_PATH_EN: '/content/dam/data/associateapp/settings/associateAppConfig_en.json'
APPCONFIG_URL_PATH_ES: '/content/dam/data/associateapp/settings/associateAppConfig_es.json'
EMPLOYEE_DISCOUNT_API_BASE_URL: 'https://esap-share-nonprod-apim-01-west-az.albertsons.com'
CREATE_NEW_EMPLOYEE_DISCOUNT_API_URL: '/abs/intdev/svwp/'

APPD_IOS: 'AD-AAB-ADY-MHH'
APPD_ANDROID: 'AD-AAB-ADY-MZY'
ABS_REWARDS_CLIENT_ID: 'ASSO_APP'
APP_ICON: 'qa2'
import { device } from "detox";

const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

describe('Associate App E2E Testing with Detox', () => {
  beforeAll(async () => {
    await device.launchApp();
  });

  beforeEach(async () => {
    await device.reloadReactNative();
  });

  it('should have welcome screen', async () => {
    await sleep(5000);
    console.log('Welcome to ASSOCIATE APP!');
  });
});

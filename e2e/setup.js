// Detox setup for WebView testing
beforeAll(async () => {
  // Basic setup - removed deprecated methods
  console.log('Setting up Detox tests...');
});

beforeEach(async () => {
  // Reset app state before each test
  try {
    await device.reloadReactNative();
  } catch (error) {
    console.log('Could not reload React Native, continuing...');
  }
});

afterEach(async () => {
  // Clean up after each test
  console.log('Test completed');
});

// Global error handler for better debugging
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Increase timeout for WebView operations
jest.setTimeout(300000);

import { element, by, device, waitFor, web } from "detox";
//import { driver } from "detox";
jest.setTimeout(180000);
describe('Example', () => {
    beforeAll(async () => {
        await device.launchApp({
            launchArgs: { detoxDisableWebKitSecurity: true },
        });
    });

    beforeEach(async () => {
        await device.reloadReactNative();
    });

    it('should display the company logo ', async () => {
        const welcomeModal = element(by.id('welcome-modal'));
        const openModal = element(by.id('some-other-modal'));

        try {
            await waitFor(welcomeModal).not.toBeVisible().withTimeout(2000);
        } catch (_) { }

        try {
            await waitFor(openModal).not.toBeVisible().withTimeout(2000);
        } catch (_) { }

        const logo = element(by.id('companyLogo'));
        await waitFor(logo).toBeVisible().withTimeout(5000);
        await expect(logo).toBeVisible();
    });

    it('should display the welcome message', async () => {
        await waitFor(element(by.text('Welcome, associate!')))
            .toBeVisible()
            .withTimeout(10000);
        await expect(element(by.text('Welcome, associate!'))).toBeVisible();
    });

    it('should display the use your work account', async () => {
        await waitFor(element(by.text('Use your work account credentials to sign in.')))
            .toBeVisible()
            .withTimeout(10000);
    });

    it('Associates can review the Terms of Use', async () => {
        const termsButton = element(by.id('terms-link'));
        await waitFor(termsButton).toBeVisible().withTimeout(10000);
        await termsButton.tap();

        const termsScreen = element(by.text('Terms of Use'));
        await waitFor(termsScreen).toBeVisible().withTimeout(10000);
        await expect(termsScreen).toBeVisible();
    });

    it('Associates can review the Privacy Policy', async () => {
        const privacyButton = element(by.id('conditions-link'));
        await waitFor(privacyButton).toBeVisible().withTimeout(10000);
        await privacyButton.tap();

        const privacyScreen = element(by.text('Privacy Policy'));
        await waitFor(privacyScreen).toBeVisible().withTimeout(10000);
        await expect(privacyScreen).toBeVisible();
    });

    it('should be visible, clickable, and open the welcome modal', async () => {
        const link = element(by.id('not-an-associate-link'));
        await waitFor(link).toBeVisible().withTimeout(10000);
        await link.tap();
    });

    it('should open the language modal and switch language', async () => {
        const languageLink = element(by.id('language-change-link'));
        await waitFor(languageLink).toBeVisible().withTimeout(10000);
        await languageLink.tap();
    });

    it('should be visible and clickable', async () => {
        const helpLink = element(by.id('need-help-signing-link'));
        await waitFor(helpLink).toBeVisible().withTimeout(10000);
        await expect(helpLink).toBeVisible();
        await helpLink.tap();
    });

    // ✅ MAIN MICROSOFT SIGN-IN TEST
    it.only('Verify sign in with Microsoft account is successful', async () => {
        await device.launchApp();

        const signinButton = element(by.id('ldap-login-button'));
        await waitFor(signinButton).toBeVisible().withTimeout(20000);
        await signinButton.tap();

        const webview = element(by.id('safeway-webview'));
        await waitFor(webview).toExist().withTimeout(30000);
        await waitFor(webview).toBeVisible().withTimeout(30000);

        // Wait for WebView to fully initialize and load content
        await new Promise(resolve => setTimeout(resolve, 10000));

        // Create web context and wait for email field to be available
        const webView = web(by.id('safeway-webview'));

        // Wait for the Microsoft login page to load completely
        try {
            // Wait for the email field to appear in the WebView
            await new Promise(resolve => setTimeout(resolve, 15000));
            console.log('Attempting to find email field...');
        } catch (error) {
            console.log('Email field not found, trying alternative approach...');
            // Try waiting a bit more and retry
            await new Promise(resolve => setTimeout(resolve, 5000));
        }

        const emailField = web.element(by.web.id('i0116'));
        await emailField.tap();
        await emailField.replaceText('<EMAIL>');

        const nextButton = web.element(by.web.id('idSIButton9'));
        await nextButton.tap();
        await new Promise(resolve => setTimeout(resolve, 5000));

        const passwordField = web.element(by.web.id('passwordInput'));
        await new Promise(resolve => setTimeout(resolve, 1000));
        await passwordField.tap();
        await passwordField.typeText('**********@Cvmm2020');
        if (device.getPlatform() === 'ios') {
            const usernameField = element(by.label('User Account'));
            const passwordField = element(by.placeholder('Password'));
            await usernameField.replaceText('<EMAIL>');
            await passwordField.replaceText('**********@Cvmm2020');
        } else {
            const passwordField = web.element(by.web.id('passwordInput'));
            await passwordField.tap();
            await passwordField.replaceText('**********@Cvmm2020');
        }
        const submitButton = web.element(by.web.id('submitButton'));
        await submitButton.tap();


        // console.log('⏳ Waiting for user to approve mobile authentication...');
        await new Promise(resolve => setTimeout(resolve, 30000));





        await waitFor(element(by.text('Login as Self')))
            .toBeVisible()
            .withTimeout(10000);
        await element(by.text('Login as Self')).tap();



        await new Promise(resolve => setTimeout(resolve, 30000));

        // CorporateScreen.page.js

        const CorporateScreen = {
            elements: {
                heading: element(by.id("hi-firstname-heading")),
            },

            async waitForGreeting(timeout = 60000) {
                await waitFor(this.elements.heading)
                    .toBeVisible()
                    .withTimeout(timeout);

                console.log("🔍 Checking if greeting text is 'Hi Manjula'");
                await expect(this.elements.heading)
                    .toHaveText("Hi Manjula");
                console.log("✅ Greeting text successfully matched: 'Hi Manjula'");
            }
        };

        const continueButton = element(by.id('continue-button'));
        await waitFor(continueButton).toBeVisible().withTimeout(10000);
        await continueButton.tap();
        console.log("✅ Continue button tap succeeded");
        await new Promise(resolve => setTimeout(resolve, 30000));
    });
});

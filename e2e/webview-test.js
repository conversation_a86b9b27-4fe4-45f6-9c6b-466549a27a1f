import { element, by, device, waitFor, expect } from "detox";

jest.setTimeout(180000);

describe('WebView Functionality Tests', () => {
    beforeAll(async () => {
        console.log('🚀 Starting WebView test suite...');
    });

    beforeEach(async () => {
        try {
            await device.reloadReactNative();
        } catch (error) {
            console.log('Could not reload React Native, continuing...');
        }
    });

    // ✅ WEBVIEW FUNCTIONALITY TEST
    it('should load WebView and verify functionality', async () => {
        try {
            console.log('🚀 Starting WebView functionality test...');
            
            // Launch the app
            await device.launchApp({
                launchArgs: { detoxDisableWebKitSecurity: true },
            });
            console.log('✅ App launched successfully');
            
            // Wait for app to fully load
            await new Promise(resolve => setTimeout(resolve, 5000));
            
            // Find and tap the sign-in button
            const signinButton = element(by.id('ldap-login-button'));
            await waitFor(signinButton).toBeVisible().withTimeout(30000);
            console.log('✅ Sign-in button found and visible');
            
            await signinButton.tap();
            console.log('✅ Sign-in button tapped');

            // Wait for WebView to appear and load
            const webview = element(by.id('safeway-webview'));
            await waitFor(webview).toExist().withTimeout(45000);
            console.log('✅ WebView exists');
            
            await waitFor(webview).toBeVisible().withTimeout(45000);
            console.log('✅ WebView is visible');

            // Wait for WebView to fully initialize and load content
            console.log('⏳ Waiting for WebView to fully load...');
            await new Promise(resolve => setTimeout(resolve, 20000));

            // Verify WebView is still visible and functional
            await expect(webview).toBeVisible();
            console.log('✅ WebView remains visible after loading');
            
            // Test completed successfully
            console.log('🎉 WebView functionality test completed successfully!');
            console.log('📝 WebView is properly configured and can load external content');
            
        } catch (error) {
            console.error('❌ Test failed with error:', error.message);
            console.error('📍 Error stack:', error.stack);
            throw error;
        }
    });

    // ✅ BASIC UI ELEMENTS TEST
    it('should display basic login screen elements', async () => {
        try {
            console.log('🚀 Testing basic UI elements...');
            
            await device.launchApp();
            
            // Test company logo
            const logo = element(by.id('companyLogo'));
            await waitFor(logo).toBeVisible().withTimeout(10000);
            await expect(logo).toBeVisible();
            console.log('✅ Company logo is visible');
            
            // Test welcome message
            await waitFor(element(by.text('Welcome, associate!')))
                .toBeVisible()
                .withTimeout(10000);
            console.log('✅ Welcome message is visible');
            
            // Test sign-in button
            const signinButton = element(by.id('ldap-login-button'));
            await waitFor(signinButton).toBeVisible().withTimeout(10000);
            await expect(signinButton).toBeVisible();
            console.log('✅ Sign-in button is visible');
            
            console.log('🎉 Basic UI elements test completed successfully!');
            
        } catch (error) {
            console.error('❌ Basic UI test failed:', error.message);
            throw error;
        }
    });
});

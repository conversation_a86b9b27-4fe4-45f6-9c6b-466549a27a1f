{
  "extends": "@react-native/typescript-config/tsconfig.json",
  "compilerOptions": {
    "baseUrl": ".",
    "esModuleInterop": true,
    "jsx": "react",
    "strict": true,
    "noImplicitAny": true, // Raise error on expressions and declarations with an implied 'any' type.
    "strictNullChecks": true, // Enable strict null checks.
    "noUnusedLocals": true, // Report errors on unused local variables.
    "noUnusedParameters": true, // Report errors on unused parameters.
    "noFallthroughCasesInSwitch": true, // Report errors for fallthrough cases in switch statements.
    "paths": {
      "pantry-design-system": [
        "node_modules/pantry-design-system/types"
      ]
    },
    "typeRoots": [
      "node_modules/@types",
      "node_modules/pantry-design-system/types"
    ]
  },
  "include": [
    "**/*.ts",
    "**/*.tsx",
    ".expo/types/**/*.ts",
    "expo-env.d.ts",
    "languages/i18n.ts",
    "app/index.tsx",
    "app/features/AuthLogout"
  ]
}
module.exports = {
  preset: "react-native",
  transform: {
    "^.+\\.(js|jsx|ts|tsx)$": "babel-jest",
  },
  transformIgnorePatterns: [
    "node_modules/(?!(react-navigation|@react-navigation|react-native-url-polyfill|react-native|@react-native|react-native-linear-gradient|react-native-app-auth|react-redux|pantry-design-system|react-native-vector-icons|react-native-webview|@react-native-async-storage/async-storage|react-native-reanimated|@react-native-cookies|@d11/react-native-fast-image|react-native-fs|@react-native-firebase|react-native-toast-message|react-native-gesture-handler)/)",
  ],
  moduleFileExtensions: ["ts", "tsx", "js", "jsx", "json", "node"],
  moduleNameMapper: {
    "^@/(.*)$": "<rootDir>/$1",
    '\\.pdf$': '<rootDir>/__mocks__/mockfile.js',
  },
  testPathIgnorePatterns: [
    "/node_modules/",
    "/ios/",
    "/android/",
    "<rootDir>/e2e/" // exclude Detox E2E tests
  ],
  setupFiles: ["./jest.setup.js"],
  reporters: [
    "default",
    [
      "./node_modules/jest-html-reporter",
      {
        pageTitle: "Test Report",
      },
    ],
  ],
  testResultsProcessor: "./node_modules/jest-html-reporter",
  testPathIgnorePatterns: ["<rootDir>/e2e/"], // exclude Detox E2E tests
};

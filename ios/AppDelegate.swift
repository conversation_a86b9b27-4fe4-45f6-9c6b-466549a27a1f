import UIKit
import React
import Firebase

class AppDelegate: UIResponder, UIApplicationDelegate, RCTBridgeDelegate, RNAppAuthAuthorizationFlowManager {

  var window: UIWindow?
  var bridge: RCTBridge?
  var authorizationFlowManagerDelegate: RNAppAuthAuthorizationFlowManagerDelegate?

  func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]? = nil
  ) -> Bool {
    FirebaseApp.configure()
    let bridge = RCTBridge(delegate: self, launchOptions: launchOptions)
    let rootView = RCTRootView(bridge: bridge!, moduleName: "AssociateApp", initialProperties: nil)

    let rootVC = UIViewController()
    rootVC.view = rootView

    window = UIWindow(frame: UIScreen.main.bounds)
    window?.rootViewController = rootVC
    window?.makeKeyAndVisible()

    return true
  }

  func application(
    _ app: UIApplication,
    open url: URL,
    options: [UIApplication.OpenURLOptionsKey : Any] = [:]
  ) -> Bo<PERSON> {
    // AppAuth redirect
    if authorizationFlowManagerDelegate?.resumeExternalUserAgentFlow(with: url) == true {
      return true
    }

    // Fallback to React Native Linking
    return RCTLinkingManager.application(app, open: url, options: options)
  }

  // MARK: - RCTBridgeDelegate
  func sourceURL(for bridge: RCTBridge!) -> URL! {
#if DEBUG
    return RCTBundleURLProvider.sharedSettings().jsBundleURL(forBundleRoot: "index", fallbackExtension: nil)
#else
    return Bundle.main.url(forResource: "main", withExtension: "jsbundle")
#endif
  }
}

#import <React/RCTBridgeModule.h>

// Expose the FastImageCache module to React Native
@interface RCT_EXTERN_MODULE(FastImageCache, NSObject)

// Lists all cached files and returns the result via a promise
RCT_EXTERN_METHOD(listCachedFiles:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)

// Gets the SDWebImage cache key for a given URL string and returns it via a promise
RCT_EXTERN_METHOD(getSDWebImageCacheKey:(NSString *)urlString
                  resolve:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)

// Gets the SDWebImage disk cache path and returns it via a promise
RCT_EXTERN_METHOD(getSDWebImageDiskCachePath:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)

@end


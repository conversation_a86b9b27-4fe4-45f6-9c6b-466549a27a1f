import Foundation
import React
import SDWebImage
import CommonCrypto

@objc(FastImageCache)
class FastImageCache: NSObject {

  // Lists all files in the SDWebImage disk cache directory
  @objc
  func listCachedFiles(_ resolve: @escaping RCTPromiseResolveBlock,
                       rejecter reject: @escaping RCTPromiseRejectBlock) {
    let cache = SDImageCache.shared
    let diskCachePath = cache.diskCachePath

    let fileManager = FileManager.default
    do {
      let contents = try fileManager.contentsOfDirectory(atPath: diskCachePath)
      let fullPaths = contents.map { "\(diskCachePath)/\($0)" }
      resolve(fullPaths)
    } catch {
      reject("CACHE_LIST_ERROR", "Could not list cache files", error)
    }
  }
  
  // Generates the SDWebImage cache key for a given URL and returns the hashed key with extension
  @objc
  func getSDWebImageCacheKey(_ urlString: String,
                           resolve: @escaping RCTPromiseResolveBlock,
                           rejecter reject: @escaping RCTPromiseRejectBlock) {
    guard let url = URL(string: urlString) else {
      reject("INVALID_URL", "Invalid URL string", nil)
      return
    }

    guard let rawKey = SDWebImageManager.shared.cacheKey(for: url) else {
      reject("CACHE_KEY_ERROR", "Could not generate cache key", nil)
      return
    }

    let hashedKey = md5(rawKey)
    let fileExtension = url.pathExtension.isEmpty ? "png" : url.pathExtension
    let finalKeyWithExtension = "\(hashedKey).\(fileExtension)"
    resolve(finalKeyWithExtension)
  }

  // Returns the SDWebImage disk cache path
  @objc
  func getSDWebImageDiskCachePath(_ resolve: @escaping RCTPromiseResolveBlock,
                                rejecter reject: @escaping RCTPromiseRejectBlock) {
    let cache = SDImageCache.shared
    let diskCachePath = cache.diskCachePath
    resolve(diskCachePath)
  }

  // Required for React Native bridge setup
  @objc static func requiresMainQueueSetup() -> Bool {
    return false
  }
}

// Helper function to generate MD5 hash of a string
func md5(_ string: String) -> String {
    let length = Int(CC_MD5_DIGEST_LENGTH)
    let messageData = string.data(using: .utf8)!
    var digestData = Data(count: length)

    _ = digestData.withUnsafeMutableBytes { digestBytes in
        messageData.withUnsafeBytes { messageBytes in
            if let mb = messageBytes.baseAddress, let db = digestBytes.baseAddress {
                CC_MD5(mb, CC_LONG(messageData.count), db.assumingMemoryBound(to: UInt8.self))
            }
        }
    }

    return digestData.map { String(format: "%02hhx", $0) }.joined()
}
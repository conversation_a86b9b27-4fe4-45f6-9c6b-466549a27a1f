import fs from "fs";
import path from "path";

describe("Info.plist", () => {
  test("should contain the correct calendar permission description", () => {
    const infoPlistPath = path.join(__dirname, "../Info.plist");
    const infoPlistContent = fs.readFileSync(infoPlistPath, "utf8");

    // Check if the file contains the expected description
    expect(infoPlistContent).toContain(
      "<key>NSCalendarsUsageDescription</key>"
    );
    expect(infoPlistContent).toContain(
      "<string>Access is needed to create a shift event in your calendar</string>"
    );
  });
});

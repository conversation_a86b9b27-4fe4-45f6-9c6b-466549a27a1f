{"name": "emum-associate-app", "main": "expo-router/entry", "version": "0.3.1", "scripts": {"start": "react-native start", "build:qa1": "./setAppIcons.sh qa1 && ./setFirebaseConfiguration.sh qa && ./updateBundleIDAndAppID.sh qa && npx react-native start --reset-cache", "build:qa2": "./setAppIcons.sh qa2 && ./setFirebaseConfiguration.sh qa && ./updateBundleIDAndAppID.sh qa && npx react-native start --reset-cache", "build:prod": "./setAppIcons.sh prod && ./setFirebaseConfiguration.sh prod && ./updateBundleIDAndAppID.sh prod && npx react-native start --reset-cache", "build:perf": "./setAppIcons.sh perf && ./setFirebaseConfiguration.sh qa && ./updateBundleIDAndAppID.sh qa && npx react-native start --reset-cache", "build:stage": "./setAppIcons.sh stage && ./setFirebaseConfiguration.sh qa && ./updateBundleIDAndAppID.sh qa && npx react-native start --reset-cache", "start:qa1": "NODE_ENV=qa1 npm run build:qa1", "start:qa2": "NODE_ENV=qa2 npm run build:qa2", "start:prod": "NODE_ENV=prod npm run build:prod", "start:perf": "NODE_ENV=perf npm run build:perf", "start:stage": "NODE_ENV=stage npm run build:stage", "android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "test": "jest --config jest.config.js", "test:watch": "jest --config jest.config.js --watch", "clear": "jest --clearCache --config jest.config.js", "get-tag": "echo ${TAG}", "install:pds": "yarn add pantry-design-system@git+ssh://*************************:albertsons/emum-PDS-ReactNative.git", "install:pds-tag": "yarn add pantry-design-system@git+ssh://*************************:albertsons/emum-PDS-ReactNative.git#\"$TAG\"", "detox:build:android": "detox build --configuration android.emu.debug", "detox:test:android": "detox test -c android.emu.debug", "detox:build:ios": "detox build --configuration ios.sim.debug", "detox:test:ios": "detox test -c ios.sim.debug", "detox:clean": "detox clean-framework-cache && detox cleanup", "detox:clean-framework": "detox clean-framework-cache && detox build-framework-cache", "detox:test": "detox test"}, "dependencies": {"@apollo/client": "^3.11.10", "@appdynamics/react-native-agent": "^25.2.0", "@d11/react-native-fast-image": "^8.9.2", "@formatjs/intl-pluralrules": "^5.4.2", "@react-native-async-storage/async-storage": "^1.24.0", "@react-native-community/geolocation": "^3.4.0", "@react-native-community/netinfo": "^11.4.1", "@react-native-cookies/cookies": "^6.2.1", "@react-native-firebase/analytics": "22.2.0", "@react-native-firebase/app": "22.2.0", "@react-navigation/bottom-tabs": "^7.0.3", "@react-navigation/native": "^7.0.1", "@react-navigation/native-stack": "^7.0.1", "@react-navigation/stack": "^7.0.2", "@reduxjs/toolkit": "^2.3.0", "@types/react-native-permissions": "^2.0.0", "aes-js": "^3.1.2", "axios": "^1.7.8", "detox": "^20.40.2", "graphql": "^15.8.0", "i18next": "^23.16.8", "intl-pluralrules": "^2.0.1", "jwt-decode": "^4.0.0", "lottie-react-native": "^7.2.2", "moment": "^2.30.1", "pantry-design-system": "git+ssh://*************************:albertsons/emum-PDS-ReactNative.git#0.0.24", "react": "19.0.0", "react-dom": "19.0.0", "react-i18next": "^15.4.0", "react-native": "0.79.0", "react-native-add-calendar-event": "^5.0.0", "react-native-app-auth": "^8.0.1", "react-native-blob-util": "^0.21.3", "react-native-calendar-events": "^2.2.0", "react-native-calendars": "^1.1309.0", "react-native-confirmation-code-field": "^7.4.0", "react-native-device-info": "^14.0.4", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.24.0", "react-native-get-random-values": "^1.11.0", "react-native-haptic-feedback": "^2.3.3", "react-native-keychain": "^9.2.2", "react-native-linear-gradient": "^2.8.3", "react-native-notifications": "^5.1.0", "react-native-orientation-locker": "^1.7.0", "react-native-paper": "^5.12.5", "react-native-pdf": "^6.7.7", "react-native-permissions": "^5.2.5", "react-native-progress-step-bar": "^1.0.0", "react-native-radio-buttons-group": "^3.1.0", "react-native-reanimated": "^3.19.0", "react-native-safe-area-context": "^5.2.0", "react-native-screens": "^4.11.1", "react-native-svg": "15.11.2", "react-native-switch": "^1.5.1", "react-native-ui-lib": "^7.30.0", "react-native-url-polyfill": "^2.0.0", "react-native-uuid": "^2.0.3", "react-native-vector-icons": "10.2.0", "react-native-video": "^6.14.1", "react-native-webview": "13.13.5", "react-navigation-header-buttons": "^12.0.1", "react-redux": "^9.2.0", "redux-persist": "^6.0.0", "redux-saga": "^1.3.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "latest", "@react-native-community/cli-platform-android": "18.0.0", "@react-native-community/cli-platform-ios": "18.0.0", "@react-native/babel-preset": "0.79.0", "@react-native/eslint-config": "0.79.0", "@react-native/metro-config": "0.79.0", "@react-native/typescript-config": "0.79.0", "@testing-library/react-native": "^13.2.0", "@types/aes-js": "^3.1.4", "@types/jest": "^29.5.13", "@types/react": "^19.0.0", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^19.0.0", "@types/redux-mock-store": "^1.5.0", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "babel-eslint": "^10.1.0", "babel-jest": "^29.6.3", "eslint": "^8.57.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jest-dom": "^5.5.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-native": "^5.0.0", "eslint-plugin-react-native-a11y": "^3.5.1", "eslint-plugin-testing-library": "^7.1.1", "eslint-plugin-unused-imports": "^4.1.4", "jest": "^29.6.3", "jest-html-reporter": "^3.10.2", "prettier": "^3.6.0", "react-native-asset": "^2.1.1", "react-native-dotenv": "^3.4.11", "react-test-renderer": "19.0.0", "redux-mock-store": "^1.5.5", "typescript": "^5.7.3"}, "engines": {"node": ">=18"}}
package com.associateapp.j4u.entprod

import com.facebook.react.bridge.*
import java.security.MessageDigest
import java.io.File

// React Native module for fast image cache operations
class FastImageCacheModule(reactContext: ReactApplicationContext) :
    ReactContextBaseJavaModule(reactContext) {

    // Name of the module exposed to React Native
    override fun getName(): String = "FastImageCache"

    // Generates a Glide-compatible cache key for a given URL
    @ReactMethod
    fun getSDWebImageCacheKey(url: String, promise: Promise) {
        try {
            val hashed = sha256(url) + ".0" // Glide adds `.0` suffix to its cache files
            promise.resolve(hashed)
        } catch (e: Exception) {
            promise.reject("HASH_ERROR", "Failed to generate cache key", e)
        }
    }

    // Returns the disk cache directory path used for images
    @ReactMethod
    fun getSDWebImageDiskCachePath(promise: Promise) {
        try {
            val cacheDir = File(reactApplicationContext.cacheDir, "image_manager_disk_cache")
            promise.resolve(cacheDir.absolutePath)
        } catch (e: Exception) {
            promise.reject("CACHE_PATH_ERROR", "Failed to get disk cache path", e)
        }
    }

    // Lists all cached image file paths in the disk cache directory
    @ReactMethod
    fun listCachedFiles(promise: Promise) {
        try {
            val cacheDir = File(reactApplicationContext.cacheDir, "image_manager_disk_cache")
            val files = cacheDir.listFiles()
            val paths = files?.map { it.absolutePath } ?: emptyList()
            promise.resolve(Arguments.fromList(paths))
        } catch (e: Exception) {
            promise.reject("LIST_ERROR", "Failed to list cache files", e)
        }
    }

    // Helper function to compute SHA-256 hash of a string
    private fun sha256(input: String): String {
        val digest = MessageDigest.getInstance("SHA-256")
        val result = digest.digest(input.toByteArray(Charsets.UTF_8))
        return result.joinToString("") { "%02x".format(it) }
    }
}

package com.associateapp.j4u.entprod

import com.facebook.react.ReactPackage
import com.facebook.react.bridge.NativeModule
import com.facebook.react.uimanager.ViewManager
import com.facebook.react.bridge.ReactApplicationContext

// Package class for registering native modules with React Native
class FastImageCachePackage : ReactPackage {
    // Register native modules
    override fun createNativeModules(reactContext: ReactApplicationContext): List<NativeModule> {
        // Return a list containing the FastImageCacheModule
        return listOf(FastImageCacheModule(reactContext))
    }

    // Register view managers (none in this case)
    override fun createViewManagers(reactContext: ReactApplicationContext): List<ViewManager<*, *>> {
        // Return an empty list since there are no custom view managers
        return emptyList()
    }
}

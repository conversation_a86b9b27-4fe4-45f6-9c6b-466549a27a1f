package com.associateapp.j4u.entprod

import android.os.Build
import android.os.Bundle
import android.view.View
import android.view.WindowInsetsController
import android.webkit.WebView
import androidx.core.content.ContextCompat
import com.facebook.react.ReactActivity
import com.facebook.react.ReactActivityDelegate
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint.fabricEnabled
import com.facebook.react.defaults.DefaultReactActivityDelegate

class MainActivity : ReactActivity() {

  /**
   * Returns the name of the main component registered from JavaScript. This is used to schedule
   * rendering of the component.
   */
  override fun getMainComponentName(): String = "AssociateApp"

  /**
   * Returns the instance of the [ReactActivityDelegate]. We use [DefaultReactActivityDelegate]
   * which allows you to enable New Architecture with a single boolean flags [fabricEnabled]
   */
  override fun createReactActivityDelegate(): ReactActivityDelegate =
      DefaultReactActivityDelegate(this, mainComponentName, fabricEnabled)
      
  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    configureStatusBar()
    enableWebViewDebugging()
  }

  private fun enableWebViewDebugging() {
    // Enable WebView debugging for Detox tests
    if (BuildConfig.DEBUG) {
      WebView.setWebContentsDebuggingEnabled(true)
    }
  }
  
  private fun configureStatusBar() {
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
      // Set status bar color
      window.statusBarColor = ContextCompat.getColor(this, R.color.primary_color)
      
      if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
        // For Android 11+ (API 30+) - Show dark content (dark icons/text)
        window.insetsController?.setSystemBarsAppearance(
          WindowInsetsController.APPEARANCE_LIGHT_STATUS_BARS, // Dark content (dark icons/text)
          WindowInsetsController.APPEARANCE_LIGHT_STATUS_BARS
        )
      } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
        // For Android 6+ (API 23+) - Show dark content (dark icons/text)
        window.decorView.systemUiVisibility = window.decorView.systemUiVisibility or 
          View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR // Add light status bar flag for dark content
      }
    }
  }
}


// Applies the AppDynamics build time instrumentation plugin.
// This should be placed at the top of your top-level build.gradle file.
apply from: '../node_modules/@appdynamics/react-native-agent/android/adeum.gradle'
buildscript {
    ext {
        buildToolsVersion = "35.0.0"
        minSdkVersion = 24
        compileSdkVersion = 35
        targetSdkVersion = 35
        ndkVersion = "27.1.12297006"
        kotlinVersion = "2.0.21"
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
        classpath('com.google.gms:google-services:4.4.2')
    }
    
    allprojects {
        repositories {
            google()
            maven { 
            url("$rootDir/../node_modules/detox/Detox-android")
            }
            maven { url 'https://www.jitpack.io' }
        }
    }
}

apply plugin: "com.facebook.react.rootproject"
